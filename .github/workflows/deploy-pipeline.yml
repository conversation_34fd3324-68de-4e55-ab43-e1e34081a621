name: Deploy Pipeline

on:
  repository_dispatch:
    types:
      - deploy-pipeline-hook
  workflow_dispatch:
    inputs:
      deploy_infra:
        type: boolean
        description: Deploy infrastructure
        default: false
      deploy_app:
        type: boolean
        description: Deploy application
        default: true

run-name: Deploy Pipeline (${{ github.event.client_payload.version || github.ref_name }})

jobs:
  deploy:
    name: Deploy
    uses: flexapp/flex-gha-workflows/.github/workflows/pipeline-workflow.yml@main
    secrets: inherit
    with:
      app: ${{ github.event.client_payload.app || vars.SERVICE_NAME }}
      version: ${{ github.event.client_payload.version || github.ref_name }}
      deploy-infra: ${{ github.event.client_payload.deploy_infra || inputs.deploy_infra }}
      deploy-app: ${{ github.event.client_payload.deploy_app || inputs.deploy_app }}