name: Deploy

on:
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: The environment to deploy to.
        options:
          - dev
          - int
          - stg
          - qa
          - prod
      deploy_infra:
        type: boolean
        description: Deploy infrastructure
        default: false
      deploy_app:
        type: boolean
        description: Deploy application
        default: true

  workflow_call:
    inputs:
      env:
        type: string
        description: The environment to deploy to.
      deploy_infra:
        type: boolean
        description: Deploy infrastructure
        default: false
      deploy_app:
        type: boolean
        description: Deploy application
        default: true

jobs:
  deploy:
    name: Deploy Bundle (${{ inputs.env }})
    uses: flexapp/flex-gha-workflows/.github/workflows/deploy.yml@main
    secrets: inherit
    with:
      app: ${{ vars.SERVICE_NAME }}
      version: ${{ github.ref_name }}
      env: ${{ inputs.env }}
      deploy_infra: ${{ inputs.deploy_infra }}
      deploy_app: ${{ inputs.deploy_app }}
      # # Override the default Terraform workspace
      # terraform-workspace:
