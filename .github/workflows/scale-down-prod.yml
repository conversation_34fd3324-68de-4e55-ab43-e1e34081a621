name: Scale down - Prod
on:
  workflow_dispatch:
  schedule:
    - cron: '59 4 7 * *'    # 11:59PM ET on the 6th day of every month
jobs:
  scale_down:
    uses: flexapp/flex-gha-workflows/.github/workflows/patch-hpa.yml@main
    with:
      cluster: shared-tuna
      namespace: flex2
      hpa: feature-store-hpa
      min_replicas: 10
      max_replicas: 20
    secrets:
      AWS_IAM_ROLE: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID_PRODUCTION }}:role/github
      NETWORK_CONNECT_CONFIG: ${{ secrets.NETWORK_CONNECT_CONFIG }}
