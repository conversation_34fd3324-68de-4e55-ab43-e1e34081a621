name: Scale up - Prod
on:
  workflow_dispatch:
  schedule:
    - cron: '0 5 30 1,3,5,7,8,10,12 *'    # 31-day months
    - cron: '0 5 29 4,6,9,11 *'    # 30-day months
    - cron: '0 5 27 2 *'    # February (leap year not considered)
jobs:
  scale_up:
    uses: flexapp/flex-gha-workflows/.github/workflows/patch-hpa.yml@main
    with:
      cluster: shared-tuna
      namespace: flex2
      hpa: feature-store-hpa
      min_replicas: 30
      max_replicas: 50
    secrets:
      AWS_IAM_ROLE: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID_PRODUCTION }}:role/github
      NETWORK_CONNECT_CONFIG: ${{ secrets.NETWORK_CONNECT_CONFIG }}
