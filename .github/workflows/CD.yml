name: CD

on:
  push:
    branches:
      - main
  workflow_dispatch:


jobs:
  push:
    uses: flexapp/flex-gha-workflows/.github/workflows/push-gradle-docker-image-v2.yml@main
    # with:
    #   # Run tests before pushing the image
    #   run_tests: true
    #   # Build the Flyway Docker image
    #   flyway: true
    #   # Publish the gradle package
    #   publish_gradle_package: false

  deploy_dev:
    needs:
      - push

    name: Deploy Bundle (dev)
    uses: flexapp/flex-gha-workflows/.github/workflows/deploy.yml@main
    secrets: inherit
    with:
      app: ${{ vars.SERVICE_NAME }}
      version: ${{ needs.push.outputs.new_tag }}
      env: dev
      deploy_infra: false
      # # Deploy terraform infrastructure
      # deploy_infra: true
      # # Deploy kubernetes application
      # deploy_app: true
      # # Override the default Terraform workspace
      # terraform-workspace:
  trigger-deploy-pipeline:
    runs-on: ubuntu-latest
    needs: push
    steps:
      - uses: actions/create-github-app-token@v2
        id: app-token
        with:
          app-id: ${{ vars.FLEX_GH_APP_ID }}
          private-key: ${{ secrets.FLEX_GH_APP_KEY }}
          owner: ${{ github.repository_owner }}
      - name: Trigger Deploy Pipeline
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |-
          curl -X POST https://api.github.com/repos/${{ github.repository }}/dispatches \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer $GITHUB_TOKEN" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            -d '{"event_type":"deploy-pipeline-hook","client_payload":{"app":"${{ vars.SERVICE_NAME }}","version":"${{ needs.push.outputs.new_tag }}","deploy_infra":true,"deploy_app":true}}'
