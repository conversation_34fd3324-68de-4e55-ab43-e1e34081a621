name: Terraform Plan All

on:
  pull_request:
    branches:
      - main
    paths:
      # Run workflow when terraform files change
      - terraform/**
  workflow_dispatch:

jobs:
  terraform_plan_dev:
    strategy:
      matrix:
        env: [ dev, qa, int, stg, prod ]
    name: Terraform Plan ${{ matrix.env }}
    uses: flexapp/flex-gha-workflows/.github/workflows/terraform-plan-v2.yml@main
    with:
      environment: ${{ matrix.env }}
    secrets: inherit
