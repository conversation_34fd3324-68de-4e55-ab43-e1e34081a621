name: Publish OpenApi Client

on:
  workflow_dispatch:

jobs:
  deploy-openapi-client:
    permissions:
      id-token: write
      contents: read
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: Configure AWS Dev credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID_DEVELOPMENT_LEGACY }}:role/github
          aws-region: us-east-1

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          server-id: github
          settings-path: ${{ github.workspace }}

      - name: Publish Java Client to Code Artifact
        uses: gradle/gradle-build-action@v2
        with:
          arguments: :api:generateFeatureStoreClientCode :api:jar :api:publish
        env:
          AWS_REGION: us-east-1

  # Temporary until added to argo workflow or risk-api-service is deprecated
  upload_python_client:
    name: upload python client
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read

    outputs:
      tag: ${{ steps.tag_version.outputs.new_tag }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          server-id: github # Value of the distributionManagement/repository/id field of the pom.xml
          settings-path: ${{ github.workspace }}

      - name: Configure AWS
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ vars.AWS_ACCOUNT_ID_SHARED }}:role/github
          aws-region: ${{ vars.AWS_REGION }}

      - name: Get code artifact token
        run: |
          echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain flex-development --domain-owner ${{ vars.AWS_ACCOUNT_ID_SHARED }} --query authorizationToken --output text)" >> $GITHUB_ENV
        shell: bash

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Setup Gradle
        uses: gradle/gradle-build-action@v3

      - name: Build Python Package with Gradle
        run: ./gradlew :api:generatePythonClient
        env:
          AWS_REGION: us-east-1

      - name: Upload Python Packages to CodeArtifact
        working-directory: ./api/build/generated-python
        run: |
          python3 -m pip install --upgrade build
          python3 -m pip install --upgrade twine
          aws codeartifact login --tool twine --domain flex-development --domain-owner ${{ secrets.AWS_ACCOUNT_ID_DEVELOPMENT_LEGACY }} --repository flex-development
          for PKG in *; do
            cd $PKG;
            echo "Building Python package $PKG";
            python3 -m build;
            echo "Uploading Python package $PKG";
            python3 -m twine upload --repository codeartifact dist/*;
            cd ..;
          done
