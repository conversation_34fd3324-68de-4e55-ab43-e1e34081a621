version: 2

registries:
  getflex:
    type: terraform-registry
    url: https://app.terraform.io/getflex
    token: ${{ secrets.TF_API_TOKEN }}
  github:
    type: git
    url: https://github.com
    username: x-access-token
    password: ${{ secrets.GH_TOKEN }}
  ecr-docker:
    type: docker-registry
    url: https://775586162990.dkr.ecr.us-east-1.amazonaws.com
    username: ${{secrets.ECR_READONLY_KEY_ID}}
    password: ${{secrets.ECR_READONLY_ACESS_KEY}}

updates:
- package-ecosystem: "terraform"
  directory: "terraform"
  registries:
  - getflex
  schedule:
    interval: "daily"
- package-ecosystem: "github-actions"
  directory: "/"
  registries:
  - github
  schedule:
    interval: "weekly"
- package-ecosystem: "docker"
  directory: "/"
  registries:
  - ecr-docker
  schedule:
    interval: "daily"
