resources:
  - ../base

patches:
  - path: ./socure-credentials-patch.yaml
  - path: ./zendesk-credentials-patch.yaml

helmCharts:
- name: java-helm-base
  includeCRDs: false
  valuesInline:
    flex:
      serviceName: feature-store
      clusterName: shared-tuna
      env: prod
      serviceAccountArn: arn:aws:iam::************:role/feature-store20240129195752496500000002
      rdsClusterName: risk-ltv
    createDatadogSecret: true
    auroraSecretVersionId: terraform-20240129195753031700000003
    deployment:
      maxSurge: 100%
      maxUnavailable: 20%
      resources:
        limits:
          memory: 4Gi
        requests:
          cpu: 1
          memory: 4Gi
      livenessProbe:
        periodSeconds: 30
        timeoutSeconds: 5
        failureThreshold: 6
      readinessProbe:
        periodSeconds: 15
        timeoutSeconds: 5
      env:
        - name: CIPHER_KEY_ARN
          valueFrom:
            secretKeyRef:
              name: cipher-secret
              key: cipher_master_key
        - name: SOCURE_API_KEY
          valueFrom:
            secretKeyRef:
              key: api_key
              name: feature-store-socure-credentials
        - name: ZENDESK_API_KEY
          valueFrom:
            secretKeyRef:
              key: api_key
              name: feature-store-zendesk-credentials
    flyway:
      create: true
      secretName: flyway-ltv # risk-ltv-flyway/rds/credentials
      tableName: feature_store_flyway_schema_history
    hpa:
      maxReplicas: 20
  version: v0.3.7

  releaseName: java-helm-base
  repo: oci://775586162990.dkr.ecr.us-east-1.amazonaws.com

namespace: flex2  
