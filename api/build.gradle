import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    id 'java'
    id "org.openapi.generator" version "7.4.0"
    id 'maven-publish'
}

repositories {
    mavenCentral()
}

def codeArtifactToken = System.getenv().getOrDefault("CODEARTIFACT_AUTH_TOKEN", null)
if (codeArtifactToken == null) {
    // token wasn't passed in, lets generate it via AWS
    logger.info("Generating codeartifact token via aws cli")
    codeArtifactToken = "aws codeartifact get-authorization-token --domain flex-development --domain-owner 775586162990 --query authorizationToken --output text".execute().text
}

def BUILD_NUMBER = System.getenv().getOrDefault("GITHUB_RUN_NUMBER", "0")
def ARTIFACT_VERSION = "1.0.${BUILD_NUMBER}"

dependencies {
    annotationProcessor 'org.projectlombok:lombok:1.18.30'

    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.13.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.3'
    implementation 'joda-time:joda-time:2.12.6'
    implementation 'jakarta.annotation:jakarta.annotation-api:2.1.1'
    implementation 'org.projectlombok:lombok:1.18.30'
}

tasks.register('generateFeatureStoreClientCode', GenerateTask) {
    generatorName = "java"
    library = "native"
    inputSpec = layout.projectDirectory.dir("spec").file("spec.yml").toString()
    outputDir = layout.buildDirectory.dir("generated/client").get().toString()
    apiPackage = 'com.getflex.featurestore.api'
    invokerPackage = 'com.getflex.featurestore.client'
    modelPackage = 'com.getflex.featurestore.model'
    generateModelTests = false
    generateApiTests = false
    configOptions = [
            dateLibrary: "java8",
            useJakartaEe: "true",
            openApiNullable: 'false',
            useTags: "true",
    ]
}

tasks.register('generateFeatureStorePythonCode', GenerateTask) {
    generatorName = "python-pydantic-v1"
    inputSpec = layout.projectDirectory.dir("spec").file("spec.yml").toString()
    outputDir = layout.buildDirectory.dir("generated-python/feature-store-client").get().toString()
    configOptions = [
            packageName: "flex2_feature_store_client",
            packageVersion: ARTIFACT_VERSION
    ]
}

tasks.register('generatePythonClient') {
    dependsOn 'generateFeatureStorePythonCode'
}

tasks.register('copyEventModelFiles', Copy) {
    dependsOn ":api:generateFeatureStoreClientCode"
    from layout.projectDirectory.dir("..").dir("src/main/java/com/getflex/featurestore/dao/model/event")
    into layout.buildDirectory.dir("generated/client/src/main/java/com/getflex/featurestore/dao/model").get()
}

sourceSets {
    main {
        java {
            srcDirs 'build/generated/client/src/main/java'
        }
    }
}

configure(generateFeatureStoreClientCode) {
    group = "flex"
    description = "Generate the Java client"
}

compileJava.dependsOn ":api:generateFeatureStoreClientCode", ":api:copyEventModelFiles"

publishing {
    repositories {
        maven {
            url 'https://flex-development-775586162990.d.codeartifact.us-east-1.amazonaws.com/maven/flex-development/'
            credentials {
                username "aws"
                password codeArtifactToken
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            from components.java
            groupId = 'com.getflex.flex2'
            artifactId = 'feature-store-api-client'
            version = ARTIFACT_VERSION
        }
    }
}
