---
features:
  get:
    tags:
      - Features
    operationId: GetFeatures
    description: Get features
    parameters:
      - in: query
        name: eval_param_keys
        schema:
          type: array
          uniqueItems: true
          items:
            $ref: "common.yml#/components/schemas/eval_param_key"
          style: form
          explode: false
        description: |
          A set of feature eval keys that defines which features are returned in the response.
          If empty, the endpoint returns all features by default
    responses:
      '200':
        description: 'Success'
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/get_features_response"

  post:
    tags:
      - Features
    operationId: FetchFeatureValues
    description: Fetch feature values (Deprecated, use /feature-values)
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/fetch_feature_values_request"

    responses:
      '200':
        description: 'Success'
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/fetch_feature_values_response"
      '207':
        description: 'Some features failed to evaluate, check feature value error message'
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/fetch_feature_values_response"

components:
  schemas:
    get_features_response:
      type: array
      items:
        $ref: "common.yml#/components/schemas/feature"

    fetch_feature_values_request:
      type: object
      properties:
        feature_names:
          description: a List of feature names to fetch value for
          type: array
          items:
            type: string
        eval_params:
          description: a map of evaluation parameters
          $ref: "common.yml#/components/schemas/eval_params"

    fetch_feature_values_response:
      type: object
      properties:
        feature_values:
          description: List of evaluated feature objects
          type: array
          items:
            $ref: "common.yml#/components/schemas/feature_value"


