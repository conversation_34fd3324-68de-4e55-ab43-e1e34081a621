events:
  post:
    tags:
      - Events
    operationId: PostEvent
    description: PostEvent to be persisted in events table
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/post_event_request"
    responses:
      '200':
        description: 'Success'
        content:
          application/json:
            schema:
              type: object
              properties: {}

components:
  schemas:
    post_event_request:
      type: object
      description: Event request containing event information (entity_id, entity_type, and event_metadata)
      properties:
        name:
          type: string
          description: Event name enum (enforced in events-sdk)
        category:
          type: string
          description: Event category enum (enforced in events-sdk)
        entity_id:
          type: string
          nullable: true
        entity_type:
          type: string
          nullable: true
          description: Entity type enum (enforced in events-sdk)
        customer_id:
          type: string
          nullable: true
        metadata:
          type: string
          description: JSON string containing metadata fields (enforced in events-sdk)
        dt_arrived:
          type: string
          format: date-time
          description: Timestamp when data arrives in Flex cloud
      required:
        - name
        - category
        - event_metadata
