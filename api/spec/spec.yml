---
openapi: 3.0.3
info:
  title: Feature Store Spec
  version: 1.0.0
servers:
  - url: https://feature-store.{subdomain}.getflex.org:{version}
    description: The Feature Store server
    variables:
      subdomain:
        default: 'prod'
      version:
        default: /v1
  - url: http://localhost:8000/v1
tags:
  - name: Feature Store
    description: Feature Store domain methods

paths:
  /events:
    $ref: 'events.yml#/events'
  /features:
    $ref: 'features.yml#/features'
  /feature-values:
    $ref: 'feature_values.yml#/feature_values'

# Add feature metadata structure below so the java class will be generated
components:
  schemas:
    flex_score_metadata:
      type: object
      description: |
        Applies to FlexScoreFiveFeature (likely FlexScoreSixFeature in the future too).
      properties:
        input_features:
          type: object
          additionalProperties:
            type: number
        vantage_score:
          type: integer
        decile:
          type: integer
        reason_codes:
          type: array
          items:
            type: string
    cmm_metadata:
      type: object
      description: |
        Applies to CMM V3 Feature
      properties:
        model_reason_codes:
          type: array
          description: Top 4 model reason codes based on SHAP values.
          items:
            type: string
