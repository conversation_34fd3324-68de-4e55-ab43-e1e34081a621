---
feature_values:
  post:
    tags:
      - FetchFeatureValues
    operationId: FetchFeatureValues
    description: Fetch feature values
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/fetch_feature_values_request"

    responses:
      '200':
        description: 'Success'
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/fetch_feature_values_response"
      '207':
        description: 'Some features failed to evaluate, check feature value error message'
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/fetch_feature_values_response"

components:
  schemas:
    fetch_feature_values_request:
      type: object
      properties:
        feature_names:
          description: a List of feature names to fetch value for
          type: array
          items:
            type: string
        eval_params:
          description: a map of evaluation parameters
          $ref: "common.yml#/components/schemas/eval_params"

    fetch_feature_values_response:
      type: object
      properties:
        feature_values:
          description: List of evaluated feature objects
          type: array
          items:
            $ref: "common.yml#/components/schemas/feature_value"
