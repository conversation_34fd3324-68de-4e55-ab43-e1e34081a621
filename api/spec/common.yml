components:
  schemas:
    eval_param_key:
      type: string
      nullable: false
      description: The evaluation parameter key, should match to query params in the getFeatureValues request
      enum:
        - customer_id
        - biller_account_id
        - biller_id
        - device_id
        - ip_address
        - alloy_report_url
        - stripe_payment_full_name
        - stripe_zip_code
        - stripe_address_line_1
        - stripe_avs_street_check
        - stripe_avs_zip_check
        - stripe_state
        - card_fingerprint
        - card_type
        - card_issuer
        - offer_version
        - offer_id
        - bill_transaction_id
        - batch_memo
        - transaction_amount_cents
        - verification_id
        - ssn_cipher
        - dob_cipher
        - dispute_id
        - estimated_rent_amount_cent
        - kyc_id
        - hub_user_kyc_id
        - onboarding_domain
        - payment_method_id
        - product_name
        - ssn_hmac
        - checkpoint_name
        - business_id
        - ein_hmac

    eval_params:
      type: object
      properties:
        customer_id:
          type: integer
          format: int64
        biller_account_id:
          type: integer
          format: int64
        biller_id:
          type: integer
          format: int64
        device_id:
          type: string
        ip_address:
          type: string
        alloy_report_url:
          type: string
        stripe_payment_full_name:
          type: string
        stripe_zip_code:
          type: string
        stripe_address_line_1:
          type: string
        stripe_avs_street_check:
          type: string
          enum:
            - pass
            - fail
            - unavailable
            - unchecked
        stripe_avs_zip_check:
          type: string
          enum:
            - pass
            - fail
            - unavailable
            - unchecked
        stripe_state:
          type: string
        card_fingerprint:
          type: string
        card_type:
          type: string
        card_issuer:
          type: string
        offer_version:
          type: integer
          format: int64
        offer_id:
          type: string
        bill_transaction_id:
          type: string
        batch_memo:
          type: string
        transaction_amount_cents:
          type: integer
          format: int64
        verification_id:
          type: string
        ssn_cipher:
          type: string
          format: password
        dob_cipher:
          type: string
          format: password
        estimated_rent_amount_cent:
          type: integer
          format: int64
        dispute_id:
          type: integer
          format: int64
        kyc_id:
          type: string
          description: |
            This is entity type agnostic (could be customer or hub_user).
        hub_user_kyc_id:
          type: string
          description: |
            Dedicated to hub_user entity type. This is needed when both customer and landlord KYC are used in the same
            checkpoint.
        onboarding_domain:
          type: string
          enum:
            - credit
            - fraud
        payment_method_id:
          type: integer
          format: int64
        product_name:
          type: string
          enum:
            - SplitRent
            - CreditBuilder
        ssn_hmac:
          type: string
        checkpoint_name:
          type: string
        business_id:
          type: integer
          format: int64
          description: business_id field from identity.business table
        ein_hmac:
          type: string
          description: HMAC of the business EIN

    feature:
      type: object
      properties:
        name:
          type: string
        type:
          $ref: "#/components/schemas/feature_type_enum"
        description:
          type: string

    feature_type_enum:
      type: string
      nullable: false
      enum:
        - int
        - double
        - string
        - boolean
        - array
        - long
        - object

    feature_value:
      type: object
      nullable: false
      properties:
        name:
          description: Feature name
          type: string
        type:
          description: The feature value type
          $ref: "#/components/schemas/feature_type_enum"
        value:
          description: The evaluated feature value
          type: object
        metadata:
          description: |
            Additional values formatted in a json string.
            Certain features have modeled metadata structured defined in spec.yml file.
          type: string
        error_message:
          description: Populated if there is an error or exception thrown while getting the feature value
          type: string
