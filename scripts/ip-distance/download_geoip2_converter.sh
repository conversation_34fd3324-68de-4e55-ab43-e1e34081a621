#!/bin/bash

# Script to download the geoip2-csv-converter tool
# Based on MaxMind documentation: https://dev.maxmind.com/geoip/importing-databases/mysql/

set -e  # Exit immediately if a command exits with a non-zero status

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOWNLOAD_DIR="${SCRIPT_DIR}"
VERSION="1.4.1"  # Latest version as of March 2025

echo "Downloading geoip2-csv-converter version ${VERSION}..."

# Create OS-specific download URL
OS="$(uname -s | tr '[:upper:]' '[:lower:]')"
ARCH="$(uname -m)"

if [ "$ARCH" = "x86_64" ]; then
    ARCH="amd64"
elif [ "$ARCH" = "aarch64" ] || [ "$ARCH" = "arm64" ]; then
    ARCH="arm64"
fi

CONVERTER_URL="https://github.com/maxmind/geoip2-csv-converter/releases/download/v${VERSION}/geoip2-csv-converter_${VERSION}_${OS}_${ARCH}.tar.gz"

echo "Downloading from: ${CONVERTER_URL}"
curl -L "${CONVERTER_URL}" -o "${DOWNLOAD_DIR}/geoip2-csv-converter.tar.gz"

echo "Listing archive contents:"
tar -tvf "${DOWNLOAD_DIR}/geoip2-csv-converter.tar.gz"

TEMP_DIR="${DOWNLOAD_DIR}/temp_extract"
mkdir -p "${TEMP_DIR}"

echo "Extracting archive..."
tar -xzf "${DOWNLOAD_DIR}/geoip2-csv-converter.tar.gz" -C "${TEMP_DIR}"

echo "Finding and moving binary..."
find "${TEMP_DIR}" -type f -name "geoip2-csv-converter" -exec mv {} "${DOWNLOAD_DIR}/" \;

echo "Cleaning up..."
rm -rf "${TEMP_DIR}"
rm "${DOWNLOAD_DIR}/geoip2-csv-converter.tar.gz"

if [ -f "${DOWNLOAD_DIR}/geoip2-csv-converter" ]; then
    chmod +x "${DOWNLOAD_DIR}/geoip2-csv-converter"
    echo "Download complete. The geoip2-csv-converter is now available at: ${DOWNLOAD_DIR}/geoip2-csv-converter"
    echo "You can use it to convert MaxMind CSV files to SQL for importing into a database."
else
    echo "Error: Could not find geoip2-csv-converter binary in the extracted files."
    echo "Contents of the extracted directory:"
    find "${TEMP_DIR}" -type f -ls 2>/dev/null || echo "No files found."
    exit 1
fi
