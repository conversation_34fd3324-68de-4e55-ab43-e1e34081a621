#!/usr/bin/env python3
"""
GeoIP CSV Converter

This script uses the geoip2-csv-converter tool to convert MaxMind GeoIP CSV files
to a format suitable for further processing.

Usage:
    python geoip_converter.py [--ipv4-file IPV4_FILE] [--ipv6-file IPV6_FILE] [--output-dir OUTPUT_DIR]
"""

import argparse
import os
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional

import structlog

structlog.configure(
    processors=[
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer(),
    ]
)
logger = structlog.get_logger("geoip_converter")


class GeoIPConverter:
    """Handles the conversion of MaxMind GeoIP CSV data."""

    def __init__(self, input_dir: str = "inputs", output_dir: Optional[str] = None):
        """Initialize the converter with the given configuration."""
        self.input_dir = input_dir
        self.converter_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "geoip2-csv-converter")
        self.temp_dir = tempfile.mkdtemp()

        if not os.path.isfile(self.converter_path):
            logger.error("converter_not_found", path=self.converter_path)
            raise FileNotFoundError(f"geoip2-csv-converter not found at {self.converter_path}")

        os.chmod(self.converter_path, 0o755)

        if output_dir:
            self.output_dir = output_dir
        else:
            self.output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "outputs")

        os.makedirs(self.output_dir, exist_ok=True)

        logger.info("converter_initialized", converter_path=self.converter_path, output_dir=self.output_dir)

    def convert_csv_file(self, input_file: str) -> str:
        """
        Convert a CSV file using the geoip2-csv-converter tool.

        Args:
            input_file: Path to the input CSV file

        Returns:
            str: Path to the converted file
        """
        if not os.path.isfile(input_file):
            logger.error("input_file_not_found", file=input_file)
            raise FileNotFoundError(f"Input file not found: {input_file}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_base = os.path.basename(input_file)

        if "IPv4" in file_base:
            output_prefix = "ipv4"
        elif "IPv6" in file_base:
            output_prefix = "ipv6"
        elif "Locations" in file_base:
            output_prefix = "locations"
        else:
            output_prefix = "geoip"

        # Temporary files for different conversion formats
        temp_hex_file = os.path.join(self.temp_dir, f"{output_prefix}_{timestamp}_hex.csv")
        temp_cidr_file = os.path.join(self.temp_dir, f"{output_prefix}_{timestamp}_cidr.csv")
        temp_merged_file = os.path.join(self.temp_dir, f"{output_prefix}_{timestamp}_merged.csv")

        final_output_file = os.path.join(self.output_dir, f"{output_prefix}_{timestamp}.csv")

        # Run converter with hex range format
        hex_cmd = [self.converter_path, "-block-file", input_file, "-include-hex-range", "-output-file", temp_hex_file]

        # Run converter with CIDR format to get the original network column
        cidr_cmd = [self.converter_path, "-block-file", input_file, "-include-cidr", "-output-file", temp_cidr_file]

        logger.info(
            "converting_csv",
            input_file=input_file,
            hex_output=temp_hex_file,
            cidr_output=temp_cidr_file,
            final_output=final_output_file,
        )

        try:
            subprocess.run(hex_cmd, check=True, capture_output=True, text=True)

            subprocess.run(cidr_cmd, check=True, capture_output=True, text=True)

            self.merge_hex_and_cidr_files(temp_hex_file, temp_cidr_file, temp_merged_file)

            self.rename_columns(temp_merged_file, final_output_file)

            return final_output_file

        except subprocess.CalledProcessError as e:
            logger.error("conversion_failed", error=str(e), stdout=e.stdout, stderr=e.stderr)
            raise RuntimeError(f"Failed to convert file: {e}")

    def merge_hex_and_cidr_files(self, hex_file: str, cidr_file: str, output_file: str) -> None:
        """
        Merge the hex range file with the CIDR file to include the original network column.

        Args:
            hex_file: Path to the hex range CSV file
            cidr_file: Path to the CIDR CSV file
            output_file: Path to the output merged CSV file
        """
        import csv

        networks = {}
        with open(cidr_file, "r", newline="", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Create a unique key based on other columns to match with hex file
                key = f"{row.get('geoname_id', '')}-{row.get('registered_country_geoname_id', '')}-{row.get('latitude', '')}-{row.get('longitude', '')}"
                networks[key] = row.get("network", "")

        with open(hex_file, "r", newline="", encoding="utf-8") as f_in, open(
            output_file, "w", newline="", encoding="utf-8"
        ) as f_out:
            reader = csv.DictReader(f_in)
            fieldnames = list(reader.fieldnames) + ["network"]

            writer = csv.DictWriter(f_out, fieldnames=fieldnames)
            writer.writeheader()

            for row in reader:
                key = f"{row.get('geoname_id', '')}-{row.get('registered_country_geoname_id', '')}-{row.get('latitude', '')}-{row.get('longitude', '')}"
                row["network"] = networks.get(key, "")
                writer.writerow(row)

        logger.info("files_merged", hex_file=hex_file, cidr_file=cidr_file, output_file=output_file)

    def rename_columns(self, input_file: str, output_file: str) -> None:
        """
        Rename columns in the CSV file to match the database schema.

        Args:
            input_file: Path to the input CSV file
            output_file: Path to the output CSV file
        """
        import csv

        with open(input_file, "r", newline="", encoding="utf-8") as f_in, open(
            output_file, "w", newline="", encoding="utf-8"
        ) as f_out:
            reader = csv.DictReader(f_in)
            fieldnames = [
                col.replace("network_start_hex", "network_start").replace("network_last_hex", "network_end")
                for col in reader.fieldnames
            ]

            writer = csv.DictWriter(f_out, fieldnames=fieldnames)
            writer.writeheader()

            for row in reader:
                writer.writerow({fieldnames[i]: row[col] for i, col in enumerate(reader.fieldnames)})

    def process_network_data(self, ipv4_file: Optional[str] = None, ipv6_file: Optional[str] = None):
        try:
            if ipv4_file:
                converted_ipv4 = self.convert_csv_file(ipv4_file)
                logger.info("ipv4_conversion_complete", output_file=converted_ipv4)

            if ipv6_file:
                converted_ipv6 = self.convert_csv_file(ipv6_file)
                logger.info("ipv6_conversion_complete", output_file=converted_ipv6)

            logger.info("network_data_conversion_success")
        except Exception as e:
            error_msg = str(e)
            logger.error("network_data_conversion_failed", error=error_msg)
            raise

    def cleanup(self):
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info("temp_dir_removed", temp_dir=self.temp_dir)


def parse_args():
    parser = argparse.ArgumentParser(description="GeoIP CSV Converter")
    parser.add_argument("--ipv4-file", help="Path to IPv4 blocks CSV file")
    parser.add_argument("--ipv6-file", help="Path to IPv6 blocks CSV file")
    parser.add_argument("--input-dir", default="inputs", help="Directory containing input files")
    parser.add_argument("--output-dir", help="Directory to store output files")

    return parser.parse_args()


def main():
    args = parse_args()

    try:
        converter = GeoIPConverter(input_dir=args.input_dir, output_dir=args.output_dir)

        if args.ipv4_file or args.ipv6_file:
            converter.process_network_data(args.ipv4_file, args.ipv6_file)
        else:
            logger.warning("no_network_files_specified", message="No IPv4 or IPv6 files were specified")

        converter.cleanup()

        logger.info("conversion_completed_successfully")
    except Exception as e:
        logger.error("conversion_failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
