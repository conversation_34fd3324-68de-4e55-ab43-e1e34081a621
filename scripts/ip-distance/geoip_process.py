#!/usr/bin/env python3
"""
GeoIP Processing Script

This script processes GeoIP location and network files using the geoip_converter.py script
and generates a CSV file for the geoip_import_log table.

Usage:
    python geoip_process.py --loc-file <LOCATION_FILE>
                           [--ipv4-file <IPV4_FILE>] [--ipv6-file <IPV6_FILE>]
                           [--output-dir <OUTPUT_DIR>] [--ipv4-import-id <ID>] [--ipv6-import-id <ID>] [--loc-import-id <ID>]
"""

import argparse
import csv
import os
import re
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple

import structlog

# Configure logging
structlog.configure(
    processors=[
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer(),
    ]
)
logger = structlog.get_logger("geoip_process")


def add_import_id_to_csv(csv_file: str, import_id: int) -> None:
    """
    Add an import_id column to a CSV file.

    Args:
        csv_file: Path to the CSV file
        import_id: Import ID to add
    """
    if not os.path.isfile(csv_file):
        logger.error("csv_file_not_found", file=csv_file)
        raise FileNotFoundError(f"CSV file not found: {csv_file}")

    temp_file = tempfile.NamedTemporaryFile(delete=False, mode="w", newline="", encoding="utf-8")

    try:
        with open(csv_file, "r", newline="", encoding="utf-8") as input_file, open(
            temp_file.name, "w", newline="", encoding="utf-8"
        ) as output_file:

            reader = csv.reader(input_file)
            writer = csv.writer(output_file)

            header = next(reader)
            header.append("import_id")
            writer.writerow(header)

            for row in reader:
                row.append(str(import_id))
                writer.writerow(row)

        os.replace(temp_file.name, csv_file)
        logger.info("added_import_id_to_csv", file=csv_file, import_id=import_id)
    except Exception as e:
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        logger.error("failed_to_add_import_id", file=csv_file, error=str(e))
        raise


def find_latest_file_by_prefix(directory: str, prefix: str) -> Optional[str]:
    """
    Find the most recently created file with the given prefix in the directory.

    Args:
        directory: Directory to search in
        prefix: Prefix of the file to find

    Returns:
        Optional[str]: Path to the latest file or None if not found
    """
    if not os.path.isdir(directory):
        logger.error("directory_not_found", directory=directory)
        return None

    matching_files = []
    for filename in os.listdir(directory):
        if filename.startswith(prefix) and os.path.isfile(os.path.join(directory, filename)):
            matching_files.append(os.path.join(directory, filename))

    if not matching_files:
        return None

    matching_files.sort(key=lambda x: os.path.getctime(x), reverse=True)
    return matching_files[0]


def run_converter(
    ipv4_file: Optional[str] = None,
    ipv6_file: Optional[str] = None,
    loc_file: Optional[str] = None,
    output_dir: Optional[str] = None,
    ipv4_import_id: Optional[int] = None,
    ipv6_import_id: Optional[int] = None,
    loc_import_id: Optional[int] = None,
) -> dict:
    """
    Run the geoip_converter.py script on the specified files.

    Args:
        ipv4_file: Path to the IPv4 blocks CSV file
        ipv6_file: Path to the IPv6 blocks CSV file
        loc_file: Path to the locations CSV file
        output_dir: Directory to store output files
        ipv4_import_id: Import ID for the IPv4 file
        ipv6_import_id: Import ID for the IPv6 file
        loc_import_id: Import ID for the location file

    Returns:
        dict: Dictionary containing information about the processed files
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    converter_script = os.path.join(script_dir, "geoip_converter.py")

    if not os.path.isfile(converter_script):
        logger.error("converter_script_not_found", path=converter_script)
        raise FileNotFoundError(f"Converter script not found at {converter_script}")

    results = {}

    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    else:
        output_dir = os.path.join(script_dir, "outputs")
        os.makedirs(output_dir, exist_ok=True)

    if ipv4_file:
        if not os.path.isfile(ipv4_file):
            logger.error("ipv4_file_not_found", file=ipv4_file)
            raise FileNotFoundError(f"IPv4 file not found: {ipv4_file}")

        cmd = [sys.executable, converter_script, "--ipv4-file", ipv4_file]
        if output_dir:
            cmd.extend(["--output-dir", output_dir])

        logger.info("running_converter_ipv4", cmd=cmd)
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("ipv4_conversion_completed")

            output_file = find_latest_file_by_prefix(output_dir, "ipv4_")

            if output_file and os.path.exists(output_file):
                if ipv4_import_id is not None:
                    add_import_id_to_csv(output_file, ipv4_import_id)

                with open(output_file, "r", newline="", encoding="utf-8") as f:
                    record_count = sum(1 for _ in csv.reader(f)) - 1  # account for header

                results["ipv4"] = {
                    "file_name": os.path.basename(ipv4_file),
                    "output_file": output_file,
                    "record_count": record_count,
                    "import_type": "network",
                    "import_id": ipv4_import_id if ipv4_import_id is not None else None,
                }
                logger.info("ipv4_conversion_result", record_count=record_count, output_file=output_file)
            else:
                logger.error("ipv4_output_file_not_found", output_dir=output_dir)
        except subprocess.CalledProcessError as e:
            logger.error("ipv4_conversion_failed", error=str(e), stdout=e.stdout, stderr=e.stderr)
            raise RuntimeError(f"Failed to convert IPv4 file: {e}")

    if ipv6_file:
        if not os.path.isfile(ipv6_file):
            logger.error("ipv6_file_not_found", file=ipv6_file)
            raise FileNotFoundError(f"IPv6 file not found: {ipv6_file}")

        cmd = [sys.executable, converter_script, "--ipv6-file", ipv6_file]
        if output_dir:
            cmd.extend(["--output-dir", output_dir])

        logger.info("running_converter_ipv6", cmd=cmd)
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info("ipv6_conversion_completed")

            output_file = find_latest_file_by_prefix(output_dir, "ipv6_")

            if output_file and os.path.exists(output_file):
                if ipv6_import_id is not None:
                    add_import_id_to_csv(output_file, ipv6_import_id)

                with open(output_file, "r", newline="", encoding="utf-8") as f:
                    record_count = sum(1 for _ in csv.reader(f)) - 1  # account for header

                results["ipv6"] = {
                    "file_name": os.path.basename(ipv6_file),
                    "output_file": output_file,
                    "record_count": record_count,
                    "import_type": "network",
                    "import_id": ipv6_import_id if ipv6_import_id is not None else None,
                }
                logger.info("ipv6_conversion_result", record_count=record_count, output_file=output_file)
            else:
                logger.error("ipv6_output_file_not_found", output_dir=output_dir)
        except subprocess.CalledProcessError as e:
            logger.error("ipv6_conversion_failed", error=str(e), stdout=e.stdout, stderr=e.stderr)
            raise RuntimeError(f"Failed to convert IPv6 file: {e}")

    if loc_file:
        if not os.path.isfile(loc_file):
            logger.error("loc_file_not_found", file=loc_file)
            raise FileNotFoundError(f"Location file not found: {loc_file}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"locations_{timestamp}.csv")

        try:
            shutil.copy2(loc_file, output_file)
            logger.info("location_file_copied", source=loc_file, destination=output_file)

            if loc_import_id is not None:
                add_import_id_to_csv(output_file, loc_import_id)

            with open(output_file, "r", newline="", encoding="utf-8") as f:
                record_count = sum(1 for _ in csv.reader(f)) - 1  # account for header

            results["locations"] = {
                "file_name": os.path.basename(loc_file),
                "output_file": output_file,
                "record_count": record_count,
                "import_type": "location",
                "import_id": loc_import_id if loc_import_id is not None else None,
            }
            logger.info("location_processing_complete", record_count=record_count, output_file=output_file)
        except Exception as e:
            logger.error("location_processing_failed", error=str(e))
            raise RuntimeError(f"Failed to process location file: {e}")

    return results


def generate_import_log_csv(results: dict, output_dir: Optional[str] = None) -> str:
    """
    Generate a CSV file for the geoip_import_log table.

    Args:
        results: Dictionary containing information about the processed files
        output_dir: Directory to store the output file

    Returns:
        str: Path to the generated CSV file
    """
    if not results:
        logger.error("no_results_to_generate_csv")
        raise ValueError("No results to generate CSV")

    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    else:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(script_dir, "outputs")
        os.makedirs(output_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = os.path.join(output_dir, f"import_log_{timestamp}.csv")

    with open(csv_file, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["import_id", "file_name", "import_date", "record_count", "import_type"])

        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        for import_type, data in results.items():
            import_id = data.get("import_id")
            if import_id is None:
                logger.error("missing_import_id", import_type=import_type)
                raise ValueError(f"Import ID is required for {import_type} but was not provided")

            writer.writerow([import_id, data["file_name"], current_date, data["record_count"], data["import_type"]])

    logger.info("import_log_csv_generated", file=csv_file, entries=len(results))
    return csv_file


def parse_args():
    parser = argparse.ArgumentParser(description="GeoIP Processing Script")
    parser.add_argument("--ipv4-file", help="Path to IPv4 blocks CSV file")
    parser.add_argument("--ipv6-file", help="Path to IPv6 blocks CSV file")
    parser.add_argument("--loc-file", help="Path to locations CSV file")
    parser.add_argument("--output-dir", help="Directory to store output files")
    parser.add_argument("--ipv4-import-id", type=int, help="Import ID for the IPv4 file")
    parser.add_argument("--ipv6-import-id", type=int, help="Import ID for the IPv6 file")
    parser.add_argument("--loc-import-id", type=int, help="Import ID for the location file")

    return parser.parse_args()


def main():
    args = parse_args()

    try:
        results = run_converter(
            ipv4_file=args.ipv4_file,
            ipv6_file=args.ipv6_file,
            loc_file=args.loc_file,
            output_dir=args.output_dir,
            ipv4_import_id=args.ipv4_import_id,
            ipv6_import_id=args.ipv6_import_id,
            loc_import_id=args.loc_import_id,
        )

        csv_file = generate_import_log_csv(results, args.output_dir)

        logger.info("processing_completed_successfully", csv_file=csv_file)
    except Exception as e:
        logger.error("processing_failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
