# Maxmind GeoIP CSV to MySQL Importer

A collection of scripts to import Maxmind GeoIP CSV files into Risk-LTV MySQL database.

## Usage

### Preparing the import data.

- Install python dependencies.
```
pip install -r requirements.txt
```
- Download the geoip2-csv-converter tool. [download_geoip2_converter.sh](./download_geoip2_converter.sh)
```
. ./download_geoip2_converter.sh
```

- Download the GeoIP CSV files from MaxMind. See [inputs/README.md](./inputs/README.md) for details.
- Check `feature_store.geoip_import_log` table for the latest import IDs.
- Prepare the import CSVs for IPv4 and Locations CSVs, passing in the input files and the next consecutive import IDs.

```bash
python geoip_process.py --ipv4-file inputs/GeoLite2-City-Blocks-IPv4.csv --loc-file inputs/GeoLite2-City-Locations-en.csv --ipv6-file inputs/GeoLite2-City-Blocks-IPv6.csv --ipv4-import-id 123 --loc-import-id 124 --ipv6-import-id 125
```
- 4 new files should be prepared in `outputs` directory ready to be loaded into the Risk LTV RDS cluster.
  - `import_log_{timestamp}.csv`
    - This contains metadata about the import, such as the import ID, row counts, timestamp, and file paths, targeting the `feature_store.geoip_import_log` table.
  - `ipv4_{timestamp}.csv`
    - This contains IPv4 data, targeting the `feature_store.geoip_network` table.
  - `ipv6_{timestamp}.csv`
    - This contains IPv6 data, targeting the `feature_store.geoip_network` table.
  - `locations_{timestamp}.csv`
    - This contains location data, targeting the `feature_store.geoip_location` table.
- If your command completed successfully, you should see an output in your terminal like this (up until this point, we are still working locally):
```
flex2-feature-store/scripts/ip-distance on  ip-distance [$!?] via 🐍 v3.9.12 on ☁️  flex2-dev (us-east-1)
❯ python geoip_process.py --ipv4-file inputs/GeoLite2-City-Blocks-IPv4.csv --loc-file inputs/GeoLite2-City-Locations-en.csv --ipv6-file inputs/GeoLite2-City-Blocks-IPv6.csv --ipv4-import-id 123 --loc-import-id 124 --ipv6-import-id 125
2025-03-31T20:55:36.181326Z [info     ] running_converter_ipv4         cmd=['/Users/<USER>/.asdf/installs/python/3.9.12/bin/python', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/geoip_converter.py', '--ipv4-file', 'inputs/GeoLite2-City-Blocks-IPv4.csv', '--output-dir', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs']
2025-03-31T20:56:49.356007Z [info     ] added_import_id_to_csv         file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/ipv4_20250331_165536.csv import_id=123
2025-03-31T20:56:52.348878Z [info     ] ipv4_conversion_result         output_file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/ipv4_20250331_165536.csv record_count=3293945
2025-03-31T20:56:52.348953Z [info     ] running_converter_ipv6         cmd=['/Users/<USER>/.asdf/installs/python/3.9.12/bin/python', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/geoip_converter.py', '--ipv6-file', 'inputs/GeoLite2-City-Blocks-IPv6.csv', '--output-dir', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs']
2025-03-31T20:58:04.800620Z [info     ] added_import_id_to_csv         file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/ipv6_20250331_165734.csv import_id=125
2025-03-31T20:58:07.088449Z [info     ] ipv6_conversion_result         output_file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/ipv6_20250331_165734.csv record_count=1739309
2025-03-31T20:58:07.088528Z [info     ] running_converter_location     cmd=['/Users/<USER>/.asdf/installs/python/3.9.12/bin/python', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/geoip_converter.py', '--loc-file', 'inputs/GeoLite2-City-Locations-en.csv', '--output-dir', '/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs']
2025-03-31T20:58:08.002533Z [info     ] added_import_id_to_csv         file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/locations_20250331_165807.csv import_id=124
2025-03-31T20:58:08.084826Z [info     ] location_conversion_result     output_file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/locations_20250331_165807.csv record_count=76428
2025-03-31T20:58:08.086119Z [info     ] import_log_csv_generated       entries=3 file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/import_log_20250331_165808.csv
2025-03-31T20:58:08.086165Z [info     ] processing_completed_successfully csv_file=/Users/<USER>/dev/flex/flex2-feature-store/scripts/ip-distance/outputs/import_log_20250331_165808.csv
```

- Ingesting data from our local machines into RDS will take too long, so we will use [S3 to load this directly](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/AuroraMySQL.Integrating.LoadFromS3.html).

### Ingesting data into RDS.

- Authenticate using AWS SSO and generate and RDS auth token.
```
aws sso login --profile <AWS_PROFILE>
```

```
DB_HOST="risk-ltv.cluster-<CLUSTER_ID>.us-east-1.rds.amazonaws.com"
DB_PORT=3306
DB_USER="<YOUR_FLEX_HANDLE>@getflex.com"
AWS_PROFILE="flex2-dev"
REGION="us-east-1"

TOKEN=$(aws rds generate-db-auth-token \
  --hostname $DB_HOST \
  --port $DB_PORT \
  --region $REGION \
  --username $DB_USER \
  --profile $AWS_PROFILE)

mysql -h $DB_HOST \
      -P $DB_PORT \
      -u $DB_USER \
      --enable-cleartext-plugin \
      -p"$TOKEN"
```

- An easy way to retrieve the `DB_HOST` is through AWS Toolkit, which you should have installed if you're using Intellij IDEs. Feel free to reach out if you have questions on this step.
- Gather the 4 output files prepared earlier and upload them to `s3://<ENV>-rds-risk-ltv`. You can use the AWS CLI or S3 console for this.
- ⚠️⚠️⚠️ USE CAUTION WHEN RUNNIN QUERIES IN PRODUCTION. ⚠️⚠️⚠️
- Run the following queries directly in MySQL CLI.

```
use feature_store;

load data from s3 's3://dev-rds-risk-ltv/import_log_<TIMESTAMP>.csv'

    into table geoip_import_log

    fields terminated by ',' enclosed by '"' lines terminated by '\n' ignore 1 rows

    (@import_id, @file_name, @import_date, @record_count, @import_type)

set import_id = nullif(@import_id, ''),
    file_name = nullif(@file_name, ''),
    import_date = nullif(@import_date, ''),
    record_count = nullif(@record_count, ''),
    import_type = nullif(@import_type, '')
;



load data from s3 's3://dev-rds-risk-ltv/locations_<TIMESTAMP>.csv'

    into table geoip_location

    fields terminated by ',' enclosed by '"' lines terminated by '\n' ignore 1 rows

    (@geoname_id, @locale_code, @continent_code, @continent_name, @country_iso_code, @country_name,
     @subdivision_1_iso_code, @subdivision_1_name, @subdivision_2_iso_code, @subdivision_2_name,
     @city_name, @metro_code, @time_zone, @is_in_european_union, @import_id)

set geoname_id = nullif(@geoname_id, ''),
    locale_code = nullif(@locale_code, ''),
    continent_code = nullif(@continent_code, ''),
    continent_name = nullif(@continent_name, ''),
    country_iso_code = nullif(@country_iso_code, ''),
    country_name = nullif(@country_name, ''),
    subdivision_1_iso_code = nullif(@subdivision_1_iso_code, ''),
    subdivision_1_name = nullif(@subdivision_1_name, ''),
    subdivision_2_iso_code = nullif(@subdivision_2_iso_code, ''),
    subdivision_2_name = nullif(@subdivision_2_name, ''),
    city_name = nullif(@city_name, ''),
    metro_code = nullif(@metro_code, ''),
    time_zone = nullif(@time_zone, ''),
    is_in_european_union = nullif(@is_in_european_union, ''),
    import_id = nullif(@import_id, '')
;



load data from s3 's3://dev-rds-risk-ltv/ipv4_<TIMESTAMP>.csv'
    into table geoip_network

    fields terminated by ',' enclosed by '"' lines terminated by '\n' ignore 1 rows

    (@network_start, @network_end, @geoname_id, @registered_country_geoname_id, @represented_country_geoname_id,
     @is_anonymous_proxy, @is_satellite_provider, @postal_code, @latitude, @longitude, @accuracy_radius, @is_anycast, @network, @import_id)

set network_start = unhex(@network_start),
    network_end = unhex(@network_end),
    geoname_id = nullif(@geoname_id, ''),
    registered_country_geoname_id = nullif(@registered_country_geoname_id, ''),
    represented_country_geoname_id = nullif(@represented_country_geoname_id, ''),
    is_anonymous_proxy = nullif(@is_anonymous_proxy, ''),
    is_satellite_provider = nullif(@is_satellite_provider, ''),
    postal_code = nullif(@postal_code, ''),
    latitude = nullif(@latitude, ''),
    longitude = nullif(@longitude, ''),
    accuracy_radius = nullif(@accuracy_radius, ''),
    is_anycast = nullif(@is_anycast, ''),
    network = nullif(@network, ''),
    import_id = nullif(@import_id, '')
;



load data from s3 's3://dev-rds-risk-ltv/ipv6_<TIMESTAMP>.csv'
    into table geoip_network

    fields terminated by ',' enclosed by '"' lines terminated by '\n' ignore 1 rows

    (@network_start, @network_end, @geoname_id, @registered_country_geoname_id, @represented_country_geoname_id,
     @is_anonymous_proxy, @is_satellite_provider, @postal_code, @latitude, @longitude, @accuracy_radius, @is_anycast, @network, @import_id)

set network_start = unhex(@network_start),
    network_end = unhex(@network_end),
    geoname_id = nullif(@geoname_id, ''),
    registered_country_geoname_id = nullif(@registered_country_geoname_id, ''),
    represented_country_geoname_id = nullif(@represented_country_geoname_id, ''),
    is_anonymous_proxy = nullif(@is_anonymous_proxy, ''),
    is_satellite_provider = nullif(@is_satellite_provider, ''),
    postal_code = nullif(@postal_code, ''),
    latitude = nullif(@latitude, ''),
    longitude = nullif(@longitude, ''),
    accuracy_radius = nullif(@accuracy_radius, ''),
    is_anycast = nullif(@is_anycast, ''),
    network = nullif(@network, ''),
    import_id = nullif(@import_id, '')
;



```

## References

- [MaxMind Developer Documentation](https://dev.maxmind.com/geoip/importing-databases/mysql/) for import guidelines
