version: '3.9'

services:
  flyway:
    build:
      dockerfile: ./flyway/flyway.Dockerfile
      context: .
      target: FLYWAY_MIGRATION_IMAGE
    container_name: feature_store.flyway
    command: -url=********************************************************************************************** -user=feature_store -password=password -connectRetries=5 -connectRetriesInterval=60 -table=feature_store_flyway_schema_history -cleanDisabled=true -baselineOnMigrate="true" migrate
    network_mode: "host"
    volumes:
      - ./flyway/db:/flyway/sql
  datadog:
    image: gcr.io/datadoghq/agent:latest
    container_name: feature_store_datadog
    ports:
      - "8124:8125/udp"
    environment:
      - DD_API_KEY=${DD_API_KEY}
      - DD_APM_ENABLED=true
      - DD_DOGSTATSD_NON_LOCAL_TRAFFIC=true
      - DD_HOSTNAME=FEATURE_STORE.LOCAL
      - DD_DOGSTATSD_ORIGIN_DETECTION=true
      - DD_AC_INCLUDE="image:*"
    volumes:
      - ./flyway/db:/flyway/sql
    network_mode: "host"
    depends_on:
      - app_db
  app_db:
    container_name: feature_store.mysql.local
    image: mysql:8.0.30
    hostname: app_db
    environment:
      MYSQL_DATABASE: 'feature_store'
      MYSQL_USER: 'feature_store'
      MYSQL_PASSWORD: 'password'
      MYSQL_ROOT_PASSWORD: 'password'
    ports:
      - '3411:3306'
    volumes:
      - db-data:/var/lib/mysql
    networks:
      - flex2-network

volumes:
  db-data:
    driver: local

networks:
  flex2-network:
    driver: bridge


