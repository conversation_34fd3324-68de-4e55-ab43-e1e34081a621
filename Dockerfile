# in mac with m1 series chips, use arm64v8/gradle:7.4.1-jdk17 as duild image https://hub.docker.com/r/arm64v8/gradle
# arm64v8/amazoncorretto:17-alpine as runtime image https://hub.docker.com/r/arm64v8/amazoncorretto

# Build image docker build --target BUILD_IMAGE
FROM 775586162990.dkr.ecr.us-east-1.amazonaws.com/flex/gradle-openjdk17-alpine:********.8 as BUILD_IMAGE

ARG CODEARTIFACT_AUTH_TOKEN
ENV CODEARTIFACT_AUTH_TOKEN=${CODEARTIFACT_AUTH_TOKEN}

WORKDIR /app
COPY . /app
RUN echo "$VERSION"
RUN gradle build -x test --no-daemon

# Service image docker build --target SERVICE_IMAGE
FROM 775586162990.dkr.ecr.us-east-1.amazonaws.com/flex/openjdk-17-alpine:*********.******** as RUNTIME_IMAGE

ARG JAR_VERSION
WORKDIR /app
# install shadow to add group and user
# hopefully we will have a alpine amazon corretto arm64v8 base image in the future https://github.com/corretto/corretto-docker/issues/46#issuecomment-1198776157
RUN if cat /etc/*release | grep -q "Amazon" ; \
    then yum -y install shadow-utils && yum clean all ; \
    else apk --no-cache add shadow ; \
    fi
RUN groupadd -r --gid 9999 flex2 && useradd -r --gid 9999 --uid 9999 --home /app flex2
USER flex2
RUN echo "$VERSION"
COPY --from=BUILD_IMAGE /app/build/libs/feature-store-latest.jar .
COPY --from=BUILD_IMAGE /app/docker-run.sh .
EXPOSE 8080

# CMD ["tail", "-f", "/dev/null"]
CMD [ "/bin/sh", "/app/docker-run.sh", "feature-store-latest.jar"]
