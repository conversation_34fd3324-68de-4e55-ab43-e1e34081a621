package com.getflex.featurestore;

import com.getflex.o11y.ObservabilityLibrary;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@ComponentScan(basePackages = "com.getflex")
@Import(ObservabilityLibrary.class)
public class FeatureStoreApplication {

  public static void main(String[] args) {
    SpringApplication.run(FeatureStoreApplication.class, args);
  }

}
