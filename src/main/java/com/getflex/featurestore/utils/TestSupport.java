package com.getflex.featurestore.utils;

import com.google.common.collect.ImmutableSet;
import java.util.Arrays;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;


@Component
public class TestSupport {

  public static final ImmutableSet<String> VALID_ENV_LIST = ImmutableSet.of("prod", "stg", "qa", "int", "dev");

  private final Environment environment;

  public TestSupport(Environment environment) {
    this.environment = environment;
  }

  public boolean isProdEnvironment() {
    return Arrays.asList(environment.getActiveProfiles()).contains("prod");
  }

  public String getEnvironment() {
    for (String profile : environment.getActiveProfiles()) {
      if (VALID_ENV_LIST.contains(profile)) {
        return profile;
      }
    }
    return "dev";
  }
}
