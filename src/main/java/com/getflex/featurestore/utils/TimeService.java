package com.getflex.featurestore.utils;

import java.time.Clock;
import java.time.OffsetDateTime;

public class TimeService {
  private final Clock clock;

  // Constructor injection of the Clock dependency
  public TimeService(Clock clock) {
    this.clock = clock;
  }

  // Method that returns the current OffsetDateTime
  public OffsetDateTime getCurrentOffsetDateTime() {
    return OffsetDateTime.now(clock);
  }
}
