package com.getflex.featurestore.utils;

import static com.getflex.featurestore.config.CacheConfig.ALLOY_REPORT_CACHE;
import static com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature.OBJECT_MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureBoolean;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

@Component
public class AlloyUtils {

  private final S3Client s3Client;

  public static final Set<AlloyExtractedFeatureNumber> ALLOY_ONLY_NUMBER_FEATURES =
      Arrays.stream(AlloyExtractedFeatureNumber.values())
          .filter(AlloyExtractedFeatureNumber::isAlloyReportOnly).collect(Collectors.toUnmodifiableSet());

  public static final Set<AlloyExtractedFeatureBoolean> ALLOY_ONLY_BOOLEAN_FEATURES = Arrays.stream(
          AlloyExtractedFeatureBoolean.values())
      .filter(AlloyExtractedFeatureBoolean::isAlloyReportOnly).collect(Collectors.toUnmodifiableSet());

  public AlloyUtils(S3Client s3Client) {
    this.s3Client = s3Client;
  }

  @Cacheable(value = ALLOY_REPORT_CACHE, key = "#alloyReportUrl")
  public AlloyReport downloadAlloyReport(String alloyReportUrl) {
    try {
      URI uri = new URI(alloyReportUrl);
      String host = uri.getHost();
      String bucket = host.split("\\.")[0]; // Extract bucket name
      String key = uri.getPath().substring(1); // Extract key (strip leading '/')

      String report = s3Client.getObjectAsBytes(GetObjectRequest.builder().bucket(bucket).key(key).build())
          .asUtf8String();

      return OBJECT_MAPPER.readValue(report, AlloyReport.class);
    } catch (URISyntaxException | JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

}
