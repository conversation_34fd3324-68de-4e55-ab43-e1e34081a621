package com.getflex.featurestore.utils;

import java.time.LocalDate;

public class FulfillmentUtils {

  public static LocalDate getPayDate() {
    // This method returns the bp date given the current date(y-m-d). The rule is:
    // (1) If d <= 27, returns y-m-1
    // (2) If d > 27, returns y-(m+1)-1
    // For example, 2024-05-10 returns 2024-05-01; 2024-08-29 returns 2024-09-01
    LocalDate currentDate = LocalDate.now();
    int year = currentDate.getYear();
    int month = currentDate.getMonthValue();
    int day = currentDate.getDayOfMonth();

    LocalDate payDate;
    // Determine the pay date
    if (day <= 27) {
      payDate = LocalDate.of(year, month, 1);
    } else {
      // Handle month transition
      if (month == 12) {
        payDate = LocalDate.of(year + 1, 1, 1);
      } else {
        payDate = LocalDate.of(year, month + 1, 1);
      }
    }
    return payDate;
  }
}
