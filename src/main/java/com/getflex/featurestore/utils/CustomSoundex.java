package com.getflex.featurestore.utils;

import java.util.Locale;
import org.apache.commons.codec.language.Soundex;

public class CustomSoundex extends Soundex {

  private static final char[] US_ENGLISH_MAPPING = "01230120022455012623010202".toCharArray();

  private final char[] soundexMapping;
  private final boolean specialCaseHw;

  public CustomSoundex() {
    this.soundexMapping = US_ENGLISH_MAPPING;
    this.specialCaseHw = true;
  }

  @Override
  public String encode(String str) {
    if (str == null) {
      return null;
    } else {
      str = clean(str);
      if (str.isEmpty()) {
        return str;
      } else {
        char[] out = new char[]{'0', '0', '0', '0'};
        int count = 0;
        char first = str.charAt(0);
        out[count++] = first;
        char lastDigit = getCharMapping(first);

        for (int i = 1; i < str.length() && count < out.length; ++i) {
          char ch = str.charAt(i);
          if (!this.specialCaseHw || ch != 'H' && ch != 'W') {
            char digit = this.getCharMapping(ch);
            if (digit != '-') {
              if (digit != '0' && digit != lastDigit) {
                out[count++] = digit;
              }

              lastDigit = digit;
            }
          }
        }

        return new String(out);
      }
    }
  }

  private char getCharMapping(char c) {
    if (Character.isDigit(c)) {
      return c; // Allow digits to pass through
    }

    int index = Character.toUpperCase(c) - 'A';
    if (index >= 0 && index < this.soundexMapping.length) {
      return this.soundexMapping[index];
    }
    return '0'; // Default to '0' for non-alphabetic characters (not needed if no special chars)
  }

  static String clean(String str) {
    if (isEmpty(str)) {
      return str;
    } else {
      int len = str.length();
      char[] chars = new char[len];
      int count = 1;
      chars[0] = str.charAt(0);
      for (int i = 1; i < len; ++i) {
        if (Character.isLetter(str.charAt(i))) {
          chars[count++] = str.charAt(i);
        }
      }

      if (count == len) {
        return str.toUpperCase(Locale.ENGLISH);
      } else {
        return (new String(chars, 0, count)).toUpperCase(Locale.ENGLISH);
      }
    }
  }

  static boolean isEmpty(CharSequence cs) {
    return cs == null || cs.length() == 0;
  }

}
