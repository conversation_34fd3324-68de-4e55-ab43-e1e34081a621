package com.getflex.featurestore.utils;

public class AnnualTaxAmountHelper {

  // https://www.irs.gov/newsroom/irs-releases-tax-inflation-adjustments-for-tax-year-2025
  // Tax brackets and rates for 2025 (Married Filing Jointly)
  private static final long[] bracketsCents = {0, 2385000, 9695000, 20670000, 39460000, 50105000, 75160000,
      Long.MAX_VALUE};
  private static final double[] rates = {0.10, 0.12, 0.22, 0.24, 0.32, 0.35, 0.37};

  public static Long estimateAnnualTax(Long monthlyGrossIncomeCent) {
    // Calculate annual gross income
    long annualGrossIncomeCent = monthlyGrossIncomeCent * 12;

    // Initialize tax calculation variables
    long tax = 0;
    long previousBracket = 0;

    // Calculate tax based on brackets
    for (int i = 0; i < bracketsCents.length - 1; i++) {
      if (annualGrossIncomeCent > bracketsCents[i]) {
        long taxableIncome = Math.min(annualGrossIncomeCent, bracketsCents[i + 1]) - previousBracket;
        tax += Math.round(taxableIncome * rates[i]);
        previousBracket = bracketsCents[i + 1];
      } else {
        break;
      }
    }
    return tax;
  }

}
