package com.getflex.featurestore.utils;

import com.getflex.o11y.helper.MetricsUtils;
import com.timgroup.statsd.StatsDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class Metrics {
  private final StatsDClient statsdClient;

  @Autowired
  public Metrics(StatsDClient statsdClient) {
    this.statsdClient = statsdClient;
  }

  public void increment(Class<?> component, String metricsName, String... metricsTags) {
    statsdClient.increment(MetricsUtils.getFullMetricName(component, metricsName), convertTags(metricsTags));
  }

  public void distribution(Class<?> component, String metricsName, double value, String... metricsTags) {
    statsdClient.distribution(MetricsUtils.getFullMetricName(component, metricsName), value, convertTags(metricsTags));
  }

  /**
   * Fold input string with separated key and value into array of tags in format "key:value", then append default tags.
   *
   * @param metricsTags Even number of strings, even index is always key and odd index is always value.
   * @return Array of tags in format "key:value"
   */
  static String[] convertTags(String[] metricsTags) {
    if (metricsTags.length % 2 != 0) {
      throw new IllegalArgumentException("tags must be always a key string followed by a value string");
    }
    int inputTagCount = metricsTags.length / 2;
    String[] result = new String[inputTagCount + MetricsUtils.getDefaultTags().length];
    for (int i = 0; i < metricsTags.length; i += 2) {
      result[i / 2] = buildTag(metricsTags[i], metricsTags[i + 1]);
    }
    for (int i = inputTagCount; i < result.length; i++) {
      result[i] = MetricsUtils.getDefaultTags()[i - inputTagCount];
    }

    return result;
  }

  /**
   * Assemble key & value into one tag string.
   *
   * @param key
   * @param value
   * @return
   */
  static String buildTag(String key, String value) {
    if (key.contains(":")) {
      throw new IllegalArgumentException("tag key must not contain ':'");
    }
    return "%s:%s".formatted(key, value);
  }
}
