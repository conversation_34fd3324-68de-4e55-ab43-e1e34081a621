package com.getflex.featurestore.utils;

public class ObservabilityConstants {

  public static final String SUCCESS_METRIC = "%s_success";
  public static final String ERROR_METRIC = "%s_error";

  public static String formatMetricName(final String methodName, final boolean isError) {
    return String.format(isError ? ERROR_METRIC : SUCCESS_METRIC, methodName);
  }

  // Metric tag keys
  public static final String FEATURE_NAME = "feature_name";
  public static final String CUSTOMER_ID = "customer_id";
  public static final String CUSTOMER_PUBLIC_ID = "customer_public_id";

}
