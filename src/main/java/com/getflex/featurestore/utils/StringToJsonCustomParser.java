package com.getflex.featurestore.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * A simple parser that converts a custom Map-style string into a Jackson JsonNode.
 *
 * <p>Example input:
 * <pre>
 * {key1=value1, key2={nestedKey=[item1, item2], number=123}, flag=true}
 * </pre>
 *
 * <p>This parser handles:
 * <ul>
 *   <li>Nested objects delimited by { and }</li>
 *   <li>Arrays delimited by [ and ]</li>
 *   <li>Primitive values: integers, decimals, booleans, and unquoted strings</li>
 * </ul>
 */
public class StringToJsonCustomParser {
  // mutable parsing state
  private String input;
  private int pos;

  /** Public entry point */
  public JsonNode parseAsJsonNode(String str) throws IOException {
    this.input = str.trim();
    this.pos = 0;
    Object parsed = parseValue();
    return new ObjectMapper().valueToTree(parsed);
  }

  /** Parse any value: object, array or primitive */
  private Object parseValue() {
    skipWhitespace();
    char c = peek();
    if (c == '{') {
      return parseObject();
    } else if (c == '[') {
      return parseArray();
    } else {
      return parsePrimitive();
    }
  }

  /** Parse { key=value, ... } */
  private Map<String, Object> parseObject() {
    Map<String, Object> map = new LinkedHashMap<>();
    pos++; // skip '{'
    skipWhitespace();
    // handle empty object {}
    if (peek() == '}') {
      pos++; // consume '}'
      return map;
    }
    while (true) {
      skipWhitespace();
      String key = parseKey();
      skipWhitespace();
      pos++; // skip '='
      Object val = parseValue();
      map.put(key, val);
      skipWhitespace();
      char c = peek();
      if (c == ',') {
        pos++;
        continue;
      } else if (c == '}') {
        pos++;
        break;
      } else {
        throw new IllegalStateException("Expected ',' or '}', got '" + c + "'");
      }
    }
    return map;
  }

  /** Parse [ v1, v2, ... ] */
  private List<Object> parseArray() {
    List<Object> list = new ArrayList<>();
    pos++; // skip '['
    while (true) {
      skipWhitespace();
      if (peek() == ']') {
        pos++;
        break;
      }
      Object val = parseValue();
      list.add(val);
      skipWhitespace();
      char c = peek();
      if (c == ',') {
        pos++;
      } else if (c == ']') {
        pos++;
        break;
      } else {
        throw new IllegalStateException("Expected ',' or ']', got '" + c + "'");
      }
    }
    return list;
  }

  /** Read an unquoted key up to '=' */
  private String parseKey() {
    int start = pos;
    while (pos < input.length() && input.charAt(pos) != '=') {
      pos++;
    }
    return input.substring(start, pos).trim();
  }

  /** Parse a “primitive”: number, boolean or bare string */
  private Object parsePrimitive() {
    int start = pos;
    while (pos < input.length()) {
      char c = input.charAt(pos);
      if (c == ',' || c == '}' || c == ']') {
        break;
      }
      pos++;
    }
    String tok = input.substring(start, pos).trim();
    // number?
    if (tok.matches("-?\\d+")) {
      return Long.parseLong(tok);
    }
    if (tok.matches("-?\\d+\\.\\d+")) {
      return Double.parseDouble(tok);
    }
    // boolean?
    if ("true".equalsIgnoreCase(tok) || "false".equalsIgnoreCase(tok)) {
      return Boolean.parseBoolean(tok);
    }
    // otherwise treat as string
    return tok;
  }

  private void skipWhitespace() {
    while (pos < input.length() && Character.isWhitespace(input.charAt(pos))) {
      pos++;
    }
  }

  private char peek() {
    if (pos >= input.length()) {
      throw new IllegalStateException("Unexpected end of input");
    }
    return input.charAt(pos);
  }
}
