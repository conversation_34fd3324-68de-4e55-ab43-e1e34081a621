package com.getflex.featurestore.utils;

import com.getflex.featurestore.integration.socure.exception.InvalidPiiException;
import com.getflex.featurestore.integration.socure.exception.InvalidPiiException.InputField;
import com.google.common.collect.ImmutableBiMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AddressStateAbbreviation {
  private static final ImmutableBiMap<String, String> ADDRESS_STATE_ABBREVIATIONS;

  //  https://www.faa.gov/air_traffic/publications/atpubs/cnt_html/appendix_a.html
  static {
    ADDRESS_STATE_ABBREVIATIONS = ImmutableBiMap.<String, String>builder()
        .put("alabama", "AL")
        .put("kentucky", "KY")
        .put("ohio", "OH")
        .put("alaska", "AK")
        .put("louisiana", "LA")
        .put("oklahoma", "OK")
        .put("arizona", "AZ")
        .put("maine", "ME")
        .put("oregon", "OR")
        .put("arkansas", "AR")
        .put("maryland", "MD")
        .put("pennsylvania", "PA")
        .put("massachusetts", "MA")
        .put("california", "CA")
        .put("michigan", "MI")
        .put("colorado", "CO")
        .put("minnesota", "MN")
        .put("connecticut", "CT")
        .put("mississippi", "MS")
        .put("delaware", "DE")
        .put("missouri", "MO")
        .put("tennessee", "TN")
        .put("montana", "MT")
        .put("texas", "TX")
        .put("florida", "FL")
        .put("nebraska", "NE")
        .put("georgia", "GA")
        .put("nevada", "NV")
        .put("utah", "UT")
        .put("guam", "GU")
        .put("vermont", "VT")
        .put("hawaii", "HI")
        .put("virginia", "VA")
        .put("idaho", "ID")
        .put("illinois", "IL")
        .put("washington", "WA")
        .put("indiana", "IN")
        .put("iowa", "IA")
        .put("wisconsin", "WI")
        .put("kansas", "KS")
        .put("wyoming", "WY")
        // States with multiple words
        .put("trustterritories", "TT")
        .put("newhampshire", "NH")
        .put("newjersey", "NJ")
        .put("newmexico", "NM")
        .put("newyork", "NY")
        .put("northcarolina", "NC")
        .put("northdakota", "ND")
        .put("rhodeisland", "RI")
        .put("southcarolina", "SC")
        .put("southdakota", "SD")
        .put("westvirginia", "WV")
        .put("districtofcolumbia", "DC")
        .put("americansamoa", "AS")
        .put("northernmarianaislands", "MP")
        .put("puertorico", "PR")
        .put("unitedstatesminoroutlyingislands", "UM")
        .put("usvirginislands", "VI")
        .build()
    ;
  }

  public static String getAbbreviatedState(String addressState) {
    addressState = addressState.replaceAll("[^A-Za-z]", "");

    if (ADDRESS_STATE_ABBREVIATIONS.inverse().containsKey(addressState.toUpperCase())) {
      return addressState.toUpperCase();
    }
    if (!ADDRESS_STATE_ABBREVIATIONS.containsKey(addressState.toLowerCase())) {
      String errorMsg = String.format("Invalid address state provided: %s", addressState);
      log.error(errorMsg);
      throw new InvalidPiiException(InputField.STATE);
    }
    return ADDRESS_STATE_ABBREVIATIONS.get(addressState.toLowerCase());
  }

  public static boolean statesMatch(String state1, String state2) {
    if (state1 == null || state2 == null) {
      return false;
    }
    String stateCode1 = getStateCode(state1);
    String stateCode2 = getStateCode(state2);
    return stateCode1.equalsIgnoreCase(stateCode2);
  }

  private static String getStateCode(String stateName) {
    if (stateName == null) {
      return null;
    }
    if (stateName.length() == 2) {
      return stateName.toUpperCase();
    }
    String parsedStateName = stateName.replaceAll("[^A-Za-z]", "").toLowerCase();
    if (ADDRESS_STATE_ABBREVIATIONS.containsKey(parsedStateName)) {
      return ADDRESS_STATE_ABBREVIATIONS.get(parsedStateName);
    }
    return stateName;
  }
}
