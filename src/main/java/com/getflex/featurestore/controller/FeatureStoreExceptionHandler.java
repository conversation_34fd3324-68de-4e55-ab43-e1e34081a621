package com.getflex.featurestore.controller;

import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.exception.UnexpectedFeatureStateException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class FeatureStoreExceptionHandler {

  @ExceptionHandler(FeatureNotFoundException.class)
  public ResponseEntity<String> handleFeatureNotFoundException(FeatureNotFoundException e) {
    return ResponseEntity.status(e.getCode()).body(e.getMessage());
  }

  @ExceptionHandler(EventMetadataParsingException.class)
  public ResponseEntity<String> handleEventMetadataParsingException(EventMetadataParsingException e) {
    return ResponseEntity.status(e.getCode()).body(e.getMessage());
  }

  @ExceptionHandler(IllegalArgumentException.class)
  public ResponseEntity<String> handleIllegalArgumentException(IllegalArgumentException e) {
    return ResponseEntity.status(400).body(e.getMessage());
  }

  @ExceptionHandler(UnexpectedFeatureStateException.class)
  public ResponseEntity<String> handleInvalidOfferLoanStateException(UnexpectedFeatureStateException e) {
    return ResponseEntity.status(409).body(e.getMessage());
  }
}
