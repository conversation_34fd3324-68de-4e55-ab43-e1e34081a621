package com.getflex.featurestore.controller;

import com.getflex.featurestore.controller.stub.FeaturesApi;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.Feature;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.FetchFeatureValuesRequest;
import com.getflex.featurestore.model.FetchFeatureValuesResponse;
import com.getflex.featurestore.service.FeatureService;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/v1")
public class FeaturesController implements FeaturesApi {

  private final FeatureService featureService;

  @Autowired
  public FeaturesController(FeatureService featureService) {
    this.featureService = featureService;
  }

  @Override
  public ResponseEntity<List<Feature>> getFeatures(Set<EvalParamKey> evalParamKeys) {
    List<Feature> response = featureService.getFeatures(evalParamKeys);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<FetchFeatureValuesResponse> fetchFeatureValues(
      FetchFeatureValuesRequest fetchFeatureValuesRequest) {
    List<FeatureValue> featureValues =
        featureService.getFeatureValues(Set.copyOf(fetchFeatureValuesRequest.getFeatureNames()
        ), fetchFeatureValuesRequest.getEvalParams());
    FetchFeatureValuesResponse response = new FetchFeatureValuesResponse();
    response.setFeatureValues(featureValues);
    boolean hasError = featureValues.stream().anyMatch(featureValue -> featureValue.getErrorMessage() != null);
    if (hasError) {
      return new ResponseEntity<>(response, HttpStatus.MULTI_STATUS);
    }
    return ResponseEntity.ok(response);
  }
}
