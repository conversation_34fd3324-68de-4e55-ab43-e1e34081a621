package com.getflex.featurestore.controller;

import com.getflex.featurestore.controller.stub.FeatureValuesApi;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.FetchFeatureValuesRequest;
import com.getflex.featurestore.model.FetchFeatureValuesResponse;
import com.getflex.featurestore.service.FeatureService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/v1")
public class FeatureValuesController implements FeatureValuesApi {

  private final FeatureService featureService;

  @Autowired
  public FeatureValuesController(FeatureService featureService) {
    this.featureService = featureService;
  }

  @Override
  public ResponseEntity<FetchFeatureValuesResponse> fetchFeatureValues(
      FetchFeatureValuesRequest fetchFeatureValuesRequest) {
    List<FeatureValue> featureValues =
        featureService.getFeatureValues(Set.copyOf(fetchFeatureValuesRequest.getFeatureNames()
        ), fetchFeatureValuesRequest.getEvalParams());
    FetchFeatureValuesResponse response = new FetchFeatureValuesResponse();
    response.setFeatureValues(featureValues);
    boolean hasError = featureValues.stream().anyMatch(featureValue -> featureValue.getErrorMessage() != null);
    if (hasError) {
      return new ResponseEntity<>(response, HttpStatus.MULTI_STATUS);
    }
    return ResponseEntity.ok(response);
  }
}
