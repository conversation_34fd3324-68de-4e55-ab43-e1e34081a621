package com.getflex.featurestore.controller;

import com.getflex.featurestore.controller.stub.EventsApi;
import com.getflex.featurestore.dao.utils.PostEventRequestConverter;
import com.getflex.featurestore.model.PostEventRequest;
import com.getflex.featurestore.service.EventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1")
public class EventsController implements EventsApi {
  private final EventService eventService;

  @Autowired
  public EventsController(EventService eventService) {
    this.eventService = eventService;
  }

  @Override
  public ResponseEntity<Object> postEvent(PostEventRequest postEventRequest) {
    eventService.postEvent(PostEventRequestConverter.convertToEvent(postEventRequest));
    return ResponseEntity.ok("{}");
  }
}
