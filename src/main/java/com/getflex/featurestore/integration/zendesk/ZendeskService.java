package com.getflex.featurestore.integration.zendesk;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUser;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUserSearchResponse;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUserTicketsResponse;
import com.getflex.featurestore.utils.Metrics;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ZendeskService {

  private static final String USER_SEARCH_ENDPOINT = "api/v2/search.json";
  private static final String USER_TICKETS_ENDPOINT = "api/v2/users/%s/tickets/requested.json";
  private static final String CONTENT_TYPE = "application/json";

  private final String baseUrl;
  private final String encodedAuth;
  private final HttpClient httpClient;
  private final Metrics metrics;
  private final ObjectMapper objectMapper = new ObjectMapper();

  public ZendeskService(ServiceConfig serviceConfig, @Qualifier("zendeskHttpClient") HttpClient httpClient,
      Metrics metrics) {
    this.baseUrl = serviceConfig.getZendeskUrl();
    this.encodedAuth = Base64.getEncoder()
        .encodeToString(serviceConfig.getZendeskApiKey().getBytes(StandardCharsets.UTF_8));
    this.httpClient = httpClient;
    this.metrics = metrics;
  }

  public ZendeskUser getUserByEmail(String email) {
    String query = String.format("email:%s", email);
    String url = String.format("%s/%s?query=%s", baseUrl, USER_SEARCH_ENDPOINT, query);

    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create(url))
        .GET()
        .header("Authorization", "Basic " + encodedAuth)
        .header("Accept", CONTENT_TYPE)
        .build();

    try {
      HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
      if (response.statusCode() != 200) {
        throw new RuntimeException(response.body());
      }

      ZendeskUserSearchResponse userSearchResponse = objectMapper.readValue(
          response.body(),
          ZendeskUserSearchResponse.class
      );
      List<ZendeskUser> users = userSearchResponse.getResults();

      if (users == null || users.isEmpty()) {
        return null;
      }
      if (users.size() > 1) {
        log.warn("Multiple ({}) Zendesk users found by email={}", users.size(), email);
      }
      metrics.increment(
          getClass(),
          formatMetricName("getUserByEmail", false)
      );
      return users.get(0);
    } catch (Exception e) {
      // This happens if user does not have a Zendesk user associated with their email
      metrics.increment(
          getClass(),
          formatMetricName("getUserByEmail", true)
      );
      return null;
    }
  }

  public ZendeskUserTicketsResponse getTicketsByUserId(String zendeskUserId) {
    String formattedEndpoint = String.format(USER_TICKETS_ENDPOINT, zendeskUserId);
    String url = String.format("%s/%s", baseUrl, formattedEndpoint);

    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create(url))
        .GET()
        .header("Authorization", "Basic " + encodedAuth)
        .header("Accept", CONTENT_TYPE)
        .build();

    try {
      HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
      if (response.statusCode() != 200) {
        throw new RuntimeException(response.body());
      }

      metrics.increment(
          getClass(),
          formatMetricName("getTicketsByUserId", false)
      );
      return objectMapper.readValue(
          response.body(),
          ZendeskUserTicketsResponse.class
      );
    } catch (Exception e) {
      log.error("Failed to get Zendesk tickets by zendeskUserId={}", zendeskUserId, e);
      metrics.increment(
          getClass(),
          formatMetricName("getTicketsByUserId", true)
      );
      return null;
    }
  }
}
