package com.getflex.featurestore.integration.sagemaker.model;

import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchObjectValueFromFeature;
import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchValueFromFeature;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.CreditReportFeature;
import com.getflex.featurestore.model.feature.IsInNetworkFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.proxyrent.CityNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.CityNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.PropertyBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.PropertyRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ProxyRentUtils;
import com.getflex.featurestore.model.feature.proxyrent.StateNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StateNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StreetNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StreetNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ZipCodeBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ZipCodeRentMeanFeature;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import java.time.LocalDate;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.IntFunction;
import java.util.stream.Stream;
import lombok.Getter;

@Getter
public enum ProxyRentModelInput implements BaseModelInput {

  VANTAGE_SCORE(((featureFactory, evalParams) -> ProxyRentUtils.extractCreditReportFeature(
            fetchObjectValueFromFeature(featureFactory, evalParams, CreditReportFeature.class.getSimpleName()),
            AlloyExtractedFeatureNumber.VANTAGE30_SCORE.name()).stream().mapToInt(Integer::valueOf).boxed()
      // If vantage score is -1 return null
      .filter(v -> v > -1).findFirst().orElse(null))),

  UW_MONTH(((featureFactory, evalParams) -> LocalDate.now().getMonthValue())),

  ACQUISITION_CHANNEL(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      IsInNetworkFeature.class.getSimpleName()))),

  CREDIT_HISTORY_DURATION(((featureFactory, evalParams) -> ProxyRentUtils.extractCreditReportFeature(
        fetchObjectValueFromFeature(featureFactory, evalParams, CreditReportFeature.class.getSimpleName()),
        AlloyExtractedFeatureNumber.MONTHS_ON_FILE.name()).stream().mapToInt(Integer::valueOf).sum())),

  COLLECTIONS_BALANCE_NO_MEDICAL(((featureFactory, evalParams) -> ProxyRentUtils.extractCreditReportFeature(
        fetchObjectValueFromFeature(featureFactory, evalParams, CreditReportFeature.class.getSimpleName()),
        AlloyExtractedFeatureNumber.COLLECTIONS_BALANCE_NO_MEDICAL.name()).stream().mapToLong(Long::valueOf).sum())),

 TU_DEBT(((featureFactory, evalParams) -> ProxyRentUtils.extractCreditReportFeature(
     fetchObjectValueFromFeature(featureFactory, evalParams, CreditReportFeature.class.getSimpleName()),
     AlloyExtractedFeatureNumber.EADS11_CV23.name(),
     AlloyExtractedFeatureNumber.TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT.name())
     .stream().mapToDouble(Double::valueOf).sum())),

  TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING(((featureFactory, evalParams) -> ProxyRentUtils.extractCreditReportFeature(
      fetchObjectValueFromFeature(featureFactory, evalParams, CreditReportFeature.class.getSimpleName()),
      AlloyExtractedFeatureNumber.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING.name())
      .stream().mapToLong(Long::valueOf).sum())),

  PROPERTY_MEAN_RENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      PropertyRentMeanFeature.class.getSimpleName()))),

  STREET_MEAN_RENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StreetNameRentMeanFeature.class.getSimpleName()))),

  ZIP5_MEAN_RENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      ZipCodeRentMeanFeature.class.getSimpleName()))),

  CITY_MEAN_RENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      CityNameRentMeanFeature.class.getSimpleName()))),

  STATE_MEAN_RENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StateNameRentMeanFeature.class.getSimpleName()))),

  PROPERTY_MEAN_BP(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      PropertyBpMeanFeature.class.getSimpleName()))),

  STREET_MEAN_BP(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StreetNameBpMeanFeature.class.getSimpleName()))),

  ZIP5_MEAN_BP(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      ZipCodeBpMeanFeature.class.getSimpleName()))),

  CITY_MEAN_BP(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      CityNameBpMeanFeature.class.getSimpleName()))),

  STATE_MEAN_BP(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StateNameBpMeanFeature.class.getSimpleName())));

  private final BiFunction<FeatureFactory, EvalParams, Number> modelInputValueFunction;

  ProxyRentModelInput(BiFunction<FeatureFactory, EvalParams, Number> inputValueFunction) {
    this.modelInputValueFunction = inputValueFunction;
  }

  @Override
  public BiFunction<FeatureFactory, EvalParams, Number> getModelInputValueFunction() {
    return modelInputValueFunction;
  }
}
