package com.getflex.featurestore.integration.sagemaker.model.base;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import java.util.function.BiFunction;

public interface BaseModelInput {

  BiFunction<FeatureFactory, EvalParams, Number> getModelInputValueFunction();

  static Number fetchValueFromFeature(FeatureFactory featureFactory, EvalParams evalParams,
      String featureName) {
    BaseFeature feature = featureFactory.getFeature(featureName);
    FeatureValue featurevalue = feature.fetchFeatureValue(evalParams);
    FeatureTypeEnum featureType = featurevalue.getType();
    Object value = featurevalue.getValue();
    if (value == null) {
      return null;
    } else if (FeatureTypeEnum.INT.equals(featureType)) {
      return (Integer) value;
    } else if (FeatureTypeEnum.LONG.equals(featureType)) {
      return (Long) value;
    } else if (FeatureTypeEnum.BOOLEAN.equals(featureType)) {
      return (Boolean) value ? 1 : 0;
    } else if (FeatureTypeEnum.DOUBLE.equals(featureType)) {
      return (Double) value;
    }

    throw new IllegalArgumentException("Unsupported feature value for feature=" + featureName);
  }

  static Object fetchObjectValueFromFeature(FeatureFactory featureFactory, EvalParams evalParams,
      String featureName) {
    BaseFeature feature = featureFactory.getFeature(featureName);
    FeatureValue featurevalue = feature.fetchFeatureValue(evalParams);
    if (!FeatureTypeEnum.OBJECT.equals(featurevalue.getType())) {
      throw new IllegalArgumentException("Feature value is not an object");
    }
    return featurevalue.getValue();
  }

}
