package com.getflex.featurestore.integration.sagemaker.model;

import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchObjectValueFromFeature;
import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchValueFromFeature;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.CustomerFailed3DsCountFeature;
import com.getflex.featurestore.model.feature.CustomerRatioBillOverwrittenRentFeature;
import com.getflex.featurestore.model.feature.CustomerTotalCardDeclinesTransactionNotAllowedFeature;
import com.getflex.featurestore.model.feature.CustomerUnderwritingScoresMaxFeature;
import com.getflex.featurestore.model.feature.DistinctPaymentMethodsFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFirstNameFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentLastNameFeature;
import com.getflex.featurestore.model.feature.RealtimeCardLinkageCountFeature;
import com.getflex.featurestore.model.feature.StripeAddressLine1JarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeFirstNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeLastNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeNameMismatchFeature;
import com.getflex.featurestore.model.feature.StripeStateMatchFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchV2Feature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesInvalidAccountFeature;
import com.getflex.featurestore.model.feature.TotalDisputesFeature;
import com.getflex.featurestore.model.feature.TotalSuccessfulPaymentsFeature;
import com.getflex.featurestore.model.feature.ZipCheckFailFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInsufficientFundsLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInvalidAccountLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.BpRateLast12MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.BpRateLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.CprEads142At24sFeature;
import com.getflex.featurestore.model.feature.billpay.CprVantage40ScoreFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast12MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsSinceFirstSignupFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsSinceLastSignupFeature;
import com.getflex.featurestore.model.feature.billpay.PaySuccessRateLast6MonthsFeature;
import com.getflex.featurestore.model.feature.output.CustomerUnderwritingScoresOutput;
import com.getflex.featurestore.model.feature.parameterized.CardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.CardVelocityExceededCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.FundsOutFundsInDifferentCardCountFeature;
import com.getflex.featurestore.model.feature.parameterized.FundsOutFundsInSameCardCountFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfAddCardAttemptsFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfAddCardSuccessFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfDeclinedFundsInFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfEmailChangesFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfFundsInAttemptsFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfFundsOutAttemptsFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfHighAmountFundsInFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfPhoneChangesFeature;
import com.getflex.featurestore.model.feature.parameterized.RiskyCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.TotalFundsInAmountFeature;
import com.getflex.featurestore.model.feature.parameterized.TotalFundsOutAmountFeature;
import java.util.Objects;
import java.util.function.BiFunction;
import lombok.Getter;

@Getter
public enum FraudTransactionModelV3Input implements BaseModelInput {

  TOTAL_SUCESSFUL_PAYMENT((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          TotalSuccessfulPaymentsFeature.class.getSimpleName())
  ),

  BP_RATE_LAST_6MONTHS((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          BpRateLast6MonthsFeature.class.getSimpleName())
  ),

  BP_RATE_LAST_12MONTHS((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          BpRateLast12MonthsFeature.class.getSimpleName())
  ),

  PAY_SUCCESS_RATE_6MON((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          PaySuccessRateLast6MonthsFeature.class.getSimpleName())
  ),

  AVG_DECLINE_INSUFF_FUNDS_6MON((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          AvgCardDeclinedInsufficientFundsLast6MonthsFeature.class.getSimpleName()
      )
  ),

  MONTHS_SINCE_FIRST_SIGNUP((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          MonthsSinceFirstSignupFeature.class.getSimpleName())
  ),

  MONTHS_SINCE_LAST_SIGNUP((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          MonthsSinceLastSignupFeature.class.getSimpleName())
  ),

  AVG_DECLINED_CARD_6MON((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          AvgCardDeclinedLast6MonthsFeature.class.getSimpleName())
  ),

  AVG_INVALID_ACCT_6MON((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          AvgCardDeclinedInvalidAccountLast6MonthsFeature.class.getSimpleName()
      )
  ),

  MONTHS_INACTIVE_6MON((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          MonthsInactiveLast6MonthsFeature.class.getSimpleName())
  ),

  MONTHS_INACTIVE_12MON((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          MonthsInactiveLast12MonthsFeature.class.getSimpleName())
  ),

  MAX_SOCURE_SIGMA_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getSigmaScore());
  }),

  MAX_SYNTHETIC_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getSyntheticScore());
  }),

  MAX_PHONERISK_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getPhoneRiskScore());
  }),

  MAX_SENTILINK_ID_THEFT_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );

    return output.getSentilinkScores() != null
        ? checkNullOrZero(output.getSentilinkScores().getIdTheftScore())
        : null;
  }),

  MAX_SENTILINK_ABUSE_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return output.getSentilinkScores() != null
        ? checkNullOrZero(output.getSentilinkScores().getAbuseScore())
        : null;
  }),

  MAX_SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return output.getSentilinkScores() != null
        ? checkNullOrZero(output.getSentilinkScores().getFirstPartySyntheticScore())
        : null;
  }),

  MAX_EMAILRISK_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getEmailRiskScore());
  }),

  MAX_SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return output.getSentilinkScores() != null
        ? checkNullOrZero(output.getSentilinkScores().getThirdPartySyntheticScore())
        : null;
  }),

  MAX_NAMEPHONECORRELATION((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getNamePhoneCorrelationScore());
  }),

  MAX_SOCURE_R617((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getFraudReasonCodeR617());
  }),

  MAX_RISKY_CARRIER_IND((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getFraudReasonCodeR021());
  }),

  MAX_SOCURE_R665((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getFraudReasonCodeR665());
  }),

  MAX_NAMEEMAILCORRELATION((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getNamePhoneCorrelationScore());
  }),

  MAX_SOCURE_I161((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName()
    );
    return checkNullOrZero(output.getFraudReasonCodeI161());
  }),

  TOTAL_DISPUTE_PRE((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams, TotalDisputesFeature.class.getSimpleName())
  ),

  NUM_ADDCARDATTEMPT_60D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfAddCardAttemptsFeature.class.getSimpleName() + "_P60D"
      )
  ),

  NUM_ADDCARD_SUCCESS_60D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfAddCardSuccessFeature.class.getSimpleName() + "_P60D"
      )
  ),

  NUM_ADDCARDATTEMPT_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfAddCardAttemptsFeature.class.getSimpleName() + "_P7D"
      )
  ),

  NUM_ADDCARDATTEMPT_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfAddCardAttemptsFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  NUM_ADDCARDATTEMPT_6H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfAddCardAttemptsFeature.class.getSimpleName() + "_PT6H"
      )
  ),

  POSTAL_MATCH_FLAG_V2((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeZipCodeMatchV2Feature.class.getSimpleName());

    if (value == null) {
      return 3;
    }
    return value.equals(1) ? 1 : 2;
  }),

  STATEMATCHFLAG((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          StripeStateMatchFeature.class.getSimpleName())
  ),

  NAME_MATCH_FLAG((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          StripeNameMismatchFeature.class.getSimpleName())
  ),

  ADDRESS1_JAROW((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeAddressLine1JarowSimilarityScoreFeature.class.getSimpleName());
    return value == null || value.equals(-1.0) ? null : value;
  }),

  FNAME_EDITDISTANCE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        EditDistanceToStripePaymentFirstNameFeature.class.getSimpleName());
    return Objects.equals(value, -1) ? null : value;
  }),

  FNAME_JAROW((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(
        featureFactory,
        evalParams,
        StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName()
    );
    return value == null || value.equals(-1.0) ? null : value;
  }),

  LNAME_EDITDISTANCE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(
        featureFactory,
        evalParams,
        EditDistanceToStripePaymentLastNameFeature.class.getSimpleName()
    );
    return Objects.equals(value, -1) ? null : value;
  }),

  LNAME_JAROW((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(
        featureFactory,
        evalParams,
        StripeLastNameJarowSimilarityScoreFeature.class.getSimpleName()
    );
    return value == null || value.equals(-1.0) ? null : value;
  }),

  ZIPCHECK_FAIL((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams, ZipCheckFailFeature.class.getSimpleName())
  ),

  CARD_FUNDING_FLAG(((featureFactory, evalParams) -> {
    String cardType = evalParams.getCardType();
    return "credit".equalsIgnoreCase(cardType) ? 1 : 0;
  })),

  NUM_BANKDECLINE_7D((featureFactory, evalParams) -> {
    Number output = fetchValueFromFeature(
        featureFactory,
        evalParams,
        CardDeclinesCountFeature.class.getSimpleName() + "_P7D"
    );
    return checkNullOrZero(output);
  }),

  NUM_RISKYTRANS_DECLINE_7D((featureFactory, evalParams) -> {
    Number output = fetchValueFromFeature(
        featureFactory,
        evalParams,
        RiskyCardDeclinesCountFeature.class.getSimpleName() + "_P7D"
    );
    return checkNullOrZero(output);
  }),

  NUM_RISKYTRANS_DECLINE_24H((featureFactory, evalParams) -> {
    Number output = fetchValueFromFeature(
        featureFactory,
        evalParams,
        RiskyCardDeclinesCountFeature.class.getSimpleName() + "_PT24H"
    );
    return checkNullOrZero(output);
  }),

  NUM_VELOCITY_DECLINE_7D((featureFactory, evalParams) -> {
    Number output = fetchValueFromFeature(
        featureFactory,
        evalParams,
        CardVelocityExceededCardDeclinesCountFeature.class.getSimpleName() + "_P7D"
    );
    return checkNullOrZero(output);
  }),

  NUM_BANKDECLINE_24H((featureFactory, evalParams) -> {
    Number output = fetchValueFromFeature(
        featureFactory,
        evalParams,
        CardDeclinesCountFeature.class.getSimpleName() + "_PT24H"
    );
    return checkNullOrZero(output);
  }),

  NUM_FUNDSIN_ATTEMPTS_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfFundsInAttemptsFeature.class.getSimpleName() + "_P7D"
      )
  ),

  NUM_FUNDSIN_ATTEMPTS_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfFundsInAttemptsFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  NUM_FUNDSIN_FAILED_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfDeclinedFundsInFeature.class.getSimpleName() + "_P7D"
      )
  ),

  TOT_FUNDSIN_AMT_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          TotalFundsInAmountFeature.class.getSimpleName() + "_P7D"
      )
  ),

  TOT_FUNDSIN_AMT_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          TotalFundsInAmountFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  NUM_FUNDSIN_FAILED_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfDeclinedFundsInFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  NUM_HIGHAMT_FUNDSIN_30D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfHighAmountFundsInFeature.class.getSimpleName() + "_P30D"
      )
  ),

  NUM_FUNDSOUT_ATTEMPTS_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfFundsOutAttemptsFeature.class.getSimpleName() + "_P7D"
      )
  ),

  TOT_FUNDSOUT_AMT_7D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          TotalFundsOutAmountFeature.class.getSimpleName() + "_P7D"
      )
  ),

  NUM_FUNDSINOUT_DIFFERENTCARD_30D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          FundsOutFundsInDifferentCardCountFeature.class.getSimpleName() + "_P30D"
      )
  ),

  NUM_FUNDSINOUT_SAMECARD_30D((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          FundsOutFundsInSameCardCountFeature.class.getSimpleName() + "_P30D"
      )
  ),

  NUM_FUNDSOUT_ATTEMPTS_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          NumberOfFundsOutAttemptsFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  TOT_FUNDSOUT_AMT_24H((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          TotalFundsOutAmountFeature.class.getSimpleName() + "_PT24H"
      )
  ),

  NUM_PHONE_CHANGE((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          NumberOfPhoneChangesFeature.class.getSimpleName())
  ),

  NUM_EMAIL_CHANGE((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          NumberOfEmailChangesFeature.class.getSimpleName())
  ),

  TOT_DIST_CARD((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          DistinctPaymentMethodsFeature.class.getSimpleName())
  ),

  CARD_DECLINE_COUNT((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          TotalCardDeclinesFeature.class.getSimpleName())
  ),

  NUM_3DS_FAILED_ALL((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          CustomerFailed3DsCountFeature.class.getSimpleName())
  ),

  RATIO_BILL_RENT((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          CustomerRatioBillOverwrittenRentFeature.class.getSimpleName())
  ),

  CARD_SUPPORT_FAIL_COUNT_V2((featureFactory, evalParams) ->
      fetchValueFromFeature(
          featureFactory,
          evalParams,
          CustomerTotalCardDeclinesTransactionNotAllowedFeature.class.getSimpleName()
      )
  ),

  INVALID_ACCOUNT_COUNT((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          TotalCardDeclinesInvalidAccountFeature.class.getSimpleName())
  ),

  CPR_EADS142_AT24S((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          CprEads142At24sFeature.class.getSimpleName())
  ),

  CPR_VANTAGE40_SCORE((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          CprVantage40ScoreFeature.class.getSimpleName())
  ),

  NUMCARDSLINK((featureFactory, evalParams) ->
      fetchValueFromFeature(featureFactory, evalParams,
          RealtimeCardLinkageCountFeature.class.getSimpleName())
  ),

  FUNDSIN_FLAG((featureFactory, evalParams) -> {
    String checkpointName = evalParams.getCheckpointName();
    return "WalletAddFundCheckpoint".equalsIgnoreCase(checkpointName) ? 1 : 0;
  });

  private final BiFunction<FeatureFactory, EvalParams, Number> modelInputValueFunction;

  FraudTransactionModelV3Input(BiFunction<FeatureFactory, EvalParams, Number> inputValueFunction) {
    this.modelInputValueFunction = inputValueFunction;
  }

  private static Number checkNullOrZero(Float value) {
    return value == null || value == 0.0 ? null : value;
  }

  private static Number checkNullOrZero(Double value) {
    return value == null || value == 0.0 ? null : value;
  }

  private static Number checkNullOrZero(Integer value) {
    return value == null || value == 0 ? null : value;
  }

  private static Number checkNullOrZero(Number value) {
    return value == null || value.doubleValue() == 0.0 ? null : value;
  }

}
