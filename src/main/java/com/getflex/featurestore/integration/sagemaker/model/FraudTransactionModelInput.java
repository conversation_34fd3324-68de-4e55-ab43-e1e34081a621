package com.getflex.featurestore.integration.sagemaker.model;

import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchValueFromFeature;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsStreetCheckEnum;
import com.getflex.featurestore.model.feature.CmmScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkAbuseScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkFirstPartySyntheticScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkIdentityTheftScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkThirdPartySyntheticScoreFeature;
import com.getflex.featurestore.model.feature.DistinctPaymentMethodsFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFirstNameFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentLastNameFeature;
import com.getflex.featurestore.model.feature.HasSentilinkCarrierIndicatorFeature;
import com.getflex.featurestore.model.feature.IsCustomerFlexAnywhereFeature;
import com.getflex.featurestore.model.feature.SocureMobileNumberScoreFeature;
import com.getflex.featurestore.model.feature.StripeNameMismatchFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesCardNotSupportedFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesInsufficientFundsFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesInvalidAccountFeature;
import com.getflex.featurestore.model.feature.TotalDisputesFeature;
import com.getflex.featurestore.model.feature.TotalSuccessfulPaymentsFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import java.util.Objects;
import java.util.function.BiFunction;
import lombok.Getter;

@Getter
public enum FraudTransactionModelInput implements BaseModelInput {

  ABUSE_SCORE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        CustomerSentilinkAbuseScoreFeature.class.getSimpleName());
    return Objects.equals(value, 0) ? null : value;
  }),

  IDENTITY_THEFT_SCORE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        CustomerSentilinkIdentityTheftScoreFeature.class.getSimpleName());
    return Objects.equals(value, 0) ? null : value;
  }),

  NAME_MATCH_FLAG(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StripeNameMismatchFeature.class.getSimpleName()))),

  FLEX_CUST_SCORE(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams, CmmScoreFeature.class.getSimpleName());
    if (value == null || value.equals(-1.0)) {
      return null;
    }
    return value;
  })),

  TOTAL_INSUFFICIENT_DECL(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesInsufficientFundsFeature.class.getSimpleName()))),

  CARD_DECLINE_COUNT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesFeature.class.getSimpleName()))),

  CARD_SUPPORT_FAIL_COUNT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesCardNotSupportedFeature.class.getSimpleName()))),

  INVALID_ACCOUNT_COUNT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesInvalidAccountFeature.class.getSimpleName()))),

  TOT_DIST_CARD(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      DistinctPaymentMethodsFeature.class.getSimpleName()))
  ),

  TOTAL_SUCCESSFUL_PAYMENT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalSuccessfulPaymentsFeature.class.getSimpleName()))
  ),

  CARD_FUNDING_FLAG(((featureFactory, evalParams) -> {
    String cardType = evalParams.getCardType();
    return "credit".equalsIgnoreCase(cardType) ? 1 : 0;
  })),

  ADDRESS_CHECK_FLAG(((featureFactory, evalParams) -> {
    StripeAvsStreetCheckEnum addressCheck = evalParams.getStripeAvsStreetCheck();
    if (StripeAvsStreetCheckEnum.PASS.equals(addressCheck)) {
      return 1;
    } else if (StripeAvsStreetCheckEnum.FAIL.equals(addressCheck)) {
      return 2;
    } else if (StripeAvsStreetCheckEnum.UNAVAILABLE.equals(addressCheck)) {
      return 3;
    } else if (StripeAvsStreetCheckEnum.UNCHECKED.equals(addressCheck)) {
      return 4;
    } else {
      return 5;
    }
  })),

  POSTAL_MATCH_FLAG(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeZipCodeMatchFeature.class.getSimpleName());
    if (value == null) {
      return 3;
    }
    return (Integer) value == 1 ? 1 : 2;
  })),

  MOBILE_VERIFY(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        SocureMobileNumberScoreFeature.class.getSimpleName());
    if (value == null || value.equals(1D)) {
      return null;
    }
    return value;
  })),

  FA_IND(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      IsCustomerFlexAnywhereFeature.class.getSimpleName()))),

  TOTAL_DISPUTE_PRE(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalDisputesFeature.class.getSimpleName()))),

  FNAME_EDIT_DISTANCE(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        EditDistanceToStripePaymentFirstNameFeature.class.getSimpleName());
    return Objects.equals(value, -1) ? null : value;
  })),

  LNAME_EDIT_DISTANCE(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        EditDistanceToStripePaymentLastNameFeature.class.getSimpleName());
    return Objects.equals(value, -1) ? null : value;
  }
  )),

  SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory,
      evalParams, CustomerSentilinkFirstPartySyntheticScoreFeature.class.getSimpleName()))),

  SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory,
      evalParams, CustomerSentilinkThirdPartySyntheticScoreFeature.class.getSimpleName()))),

  RISKY_CARRIER_IND(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      HasSentilinkCarrierIndicatorFeature.class.getSimpleName())));


  private final BiFunction<FeatureFactory, EvalParams, Number> modelInputValueFunction;

  FraudTransactionModelInput(BiFunction<FeatureFactory, EvalParams, Number> modelInputValueFunction) {
    this.modelInputValueFunction = modelInputValueFunction;
  }
}
