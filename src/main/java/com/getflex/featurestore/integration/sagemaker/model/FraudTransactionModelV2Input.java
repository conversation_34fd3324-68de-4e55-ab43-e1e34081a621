package com.getflex.featurestore.integration.sagemaker.model;

import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchObjectValueFromFeature;
import static com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput.fetchValueFromFeature;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsStreetCheckEnum;
import com.getflex.featurestore.model.feature.CmmV2ScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkAbuseScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkFirstPartySyntheticScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkIdentityTheftScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkThirdPartySyntheticScoreFeature;
import com.getflex.featurestore.model.feature.CustomerTotalCardDeclinesTransactionNotAllowedFeature;
import com.getflex.featurestore.model.feature.CustomerUnderwritingScoresMaxFeature;
import com.getflex.featurestore.model.feature.DistinctPaymentMethodsFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFirstNameFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentLastNameFeature;
import com.getflex.featurestore.model.feature.HasSentilinkCarrierIndicatorFeature;
import com.getflex.featurestore.model.feature.IsCustomerFlexAnywhereFeature;
import com.getflex.featurestore.model.feature.SocureMobileNumberScoreFeature;
import com.getflex.featurestore.model.feature.StripeAddressLine1JarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeFirstNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeLastNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeNameMismatchFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchV2Feature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesInsufficientFundsFeature;
import com.getflex.featurestore.model.feature.TotalCardDeclinesInvalidAccountFeature;
import com.getflex.featurestore.model.feature.TotalDisputesFeature;
import com.getflex.featurestore.model.feature.TotalSuccessfulPaymentsFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.output.CustomerUnderwritingScoresOutput;
import java.util.Objects;
import java.util.function.BiFunction;
import lombok.Getter;

@Getter
public enum FraudTransactionModelV2Input implements BaseModelInput {

  IDENTITY_THEFT_SCORE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        CustomerSentilinkIdentityTheftScoreFeature.class.getSimpleName());
    return Objects.equals(value, 0) ? null : value;
  }),

  ABUSE_SCORE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        CustomerSentilinkAbuseScoreFeature.class.getSimpleName());
    return Objects.equals(value, 0) ? null : value;
  }),

  NAME_MATCH_FLAG((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      StripeNameMismatchFeature.class.getSimpleName())),

  CMM2_SCORE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams, CmmV2ScoreFeature.class.getSimpleName());
    return value == null || value.equals(-1.0) ? null : value;
  }),

  TOTAL_INSUFFICIENT_DECL((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesInsufficientFundsFeature.class.getSimpleName())),

  CARD_DECLINE_COUNT(((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesFeature.class.getSimpleName()))),

  CARD_SUPPORT_FAIL_COUNT_V2((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      CustomerTotalCardDeclinesTransactionNotAllowedFeature.class.getSimpleName())),

  INVALID_ACCOUNT_COUNT((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalCardDeclinesInvalidAccountFeature.class.getSimpleName())),

  TOT_DIST_CARD((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      DistinctPaymentMethodsFeature.class.getSimpleName())),

  TOTAL_SUCESSFUL_PAYMENT((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalSuccessfulPaymentsFeature.class.getSimpleName())),

  CARD_FUNDING_FLAG(((featureFactory, evalParams) -> {
    String cardType = evalParams.getCardType();
    return "credit".equalsIgnoreCase(cardType) ? 1 : 0;
  })),

  ADDRESS_CHECK_FLAG((featureFactory, evalParams) -> {
    StripeAvsStreetCheckEnum addressCheck = evalParams.getStripeAvsStreetCheck();
    if (StripeAvsStreetCheckEnum.PASS.equals(addressCheck)) {
      return 1;
    } else if (StripeAvsStreetCheckEnum.FAIL.equals(addressCheck)) {
      return 2;
    } else if (StripeAvsStreetCheckEnum.UNAVAILABLE.equals(addressCheck)) {
      return 3;
    } else if (StripeAvsStreetCheckEnum.UNCHECKED.equals(addressCheck)) {
      return 4;
    } else {
      return 5;
    }
  }),

  POSTAL_MATCH_FLAG_V2((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeZipCodeMatchV2Feature.class.getSimpleName());

    if (value == null) {
      return 3;
    }
    return value.equals(1) ? 1 : 2;
  }),

  MOBILE_VERIFY((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        SocureMobileNumberScoreFeature.class.getSimpleName());

    return value == null || value.equals(1D) ? null : value;
  }),

  FA_IND((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      IsCustomerFlexAnywhereFeature.class.getSimpleName())),

  TOTAL_DISPUTE_PRE((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      TotalDisputesFeature.class.getSimpleName())),

  FNAME_EDIT_DISTANCE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        EditDistanceToStripePaymentFirstNameFeature.class.getSimpleName());
    return Objects.equals(value, -1) ? null : value;
  }),

  LNAME_EDITDISTANCE((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        EditDistanceToStripePaymentLastNameFeature.class.getSimpleName());
    return Objects.equals(value, -1) ? null : value;
  }),

  SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory,
      evalParams, CustomerSentilinkFirstPartySyntheticScoreFeature.class.getSimpleName())),

  SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory,
      evalParams, CustomerSentilinkThirdPartySyntheticScoreFeature.class.getSimpleName())),

  RISKY_CARRIER_IND((featureFactory, evalParams) -> fetchValueFromFeature(featureFactory, evalParams,
      HasSentilinkCarrierIndicatorFeature.class.getSimpleName())),

  SIGMA_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getSigmaScore());
  }),

  ADDRESSRISK_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getAddressRiskScore());
  }),

  EMAILRISK_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getEmailRiskScore());
  }),

  NAMEADDRESSCORRELATION((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getNameAddressCorrelationScore());
  }),

  NAMEEMAILCORRELATION((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getNameEmailCorrelationScore());
  }),

  NAMEPHONECORRELATION((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getNamePhoneCorrelationScore());
  }),

  PHONERISK_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getPhoneRiskScore());
  }),

  SYNTHETIC_SCORE((featureFactory, evalParams) -> {
    CustomerUnderwritingScoresOutput output = (CustomerUnderwritingScoresOutput) fetchObjectValueFromFeature(
        featureFactory, evalParams, CustomerUnderwritingScoresMaxFeature.class.getSimpleName());
    return checkNullOrZero(output.getSyntheticScore());
  }),

  // Not available for add card checkpoint but included in model for future use
  PAYMENT_SEG(((featureFactory, evalParams) -> null)),

  LNAME_JAROW((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeLastNameJarowSimilarityScoreFeature.class.getSimpleName());
    return value == null || value.equals(-1.0) ? null : value;
  }),

  FNAME_JAROW(((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName());
    return value == null || value.equals(-1.0) ? null : value;
  })),

  ADDRESS1_JAROW((featureFactory, evalParams) -> {
    Number value = fetchValueFromFeature(featureFactory, evalParams,
        StripeAddressLine1JarowSimilarityScoreFeature.class.getSimpleName());
    return value == null || value.equals(-1.0) ? null : value;
  });


  private final BiFunction<FeatureFactory, EvalParams, Number> modelInputValueFunction;

  FraudTransactionModelV2Input(BiFunction<FeatureFactory, EvalParams, Number> inputValueFunction) {
    this.modelInputValueFunction = inputValueFunction;
  }

  private static Number checkNullOrZero(Float value) {
    return value == null || value == 0.0 ? null : value;
  }

  private static Number checkNullOrZero(Double value) {
    return value == null || value == 0.0 ? null : value;
  }

}
