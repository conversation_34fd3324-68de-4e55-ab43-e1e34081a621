package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.autopay.api.AutopayApi;
import com.getflex.autopay.api.AutopayApi.APIqueryAutopayTaskRequest;
import com.getflex.autopay.api.AutopayApi.APIqueryAutopayTasksByBillPayDateRequest;
import com.getflex.autopay.client.ApiException;
import com.getflex.autopay.model.EpisodicToggleResponse;
import com.getflex.autopay.model.GetBpWindowResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.autopay.model.WalletConfigurationResponse;
import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import io.github.resilience4j.retry.annotation.Retry;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AutopayService {

  private final AutopayApi api;
  private final Metrics metrics;

  @Autowired
  public AutopayService(AutopayApi api, Metrics metrics) {
    this.api = api;
    this.metrics = metrics;
  }


  @Cacheable(value = CacheConfig.AUTOPAY_CACHE, key = "#customerId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Boolean getCustomerWalletConfigured(Long customerId) {
    try {
      final WalletConfigurationResponse response = api.getWalletConfiguration(customerId);
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerWalletConfiguration", false)
      );
      return response.getWalletConfigured();
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerWalletConfiguration", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch the wallet configuration for customer id %s".formatted(
              customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.AUTOPAY_CACHE, key = "#billTransactionId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetBpWindowResponse getBpWindowByBillTransactionId(String billTransactionId) {
    try {
      return api.getBpWindow(billTransactionId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch the bp window for btx id %s".formatted(
              billTransactionId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public SearchAutopayTasksResponse getCurrentAutoPay(String customerPublicId, LocalDate bpDate) {
    try {
      APIqueryAutopayTasksByBillPayDateRequest request = new APIqueryAutopayTasksByBillPayDateRequest.Builder()
          .customerPublicId(customerPublicId)
          .billpayDate(bpDate)
          .build();
      return api.queryAutopayTasksByBillPayDate(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch autopay for customerPublicId %s".formatted(customerPublicId), e
      );
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public EpisodicToggleResponse getCustomerEpisodicToggle(Long customerId) {
    try {
      return api.getCustomerEpisodicToggle(customerId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch the episodic toggle for customer id %s".formatted(
              customerId), e);
    }
  }
}
