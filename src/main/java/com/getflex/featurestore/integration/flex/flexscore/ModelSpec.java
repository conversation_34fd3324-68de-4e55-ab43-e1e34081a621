package com.getflex.featurestore.integration.flex.flexscore;

import static com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA002;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA006;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA021;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA022;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA027;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA029;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF038;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF053;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF068;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF069;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF090;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF107;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF115;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF145;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF166;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF167;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF176;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF181;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF185;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF193;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.ACCTS_30_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.ACCTS_DEFAULT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.COLLECTIONS_BALANCE_NO_MEDICAL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADM09_P02E;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADM09_P02F;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG205;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG218;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG504;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG602;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG801;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS06_TRV07;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS06_TRV08;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS07_BALMAG01;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS10_PAYMNT07;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS10_PAYMNT10;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS11_CV23;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS11_CV28;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT104S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT24S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT28B;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BC103S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BR02S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BR34S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_FI34S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G208B;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G224C;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G242F;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G411S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_RE28S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_S061S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_S071A;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_ST24S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_AGGS106;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ1;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ2;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ3;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ4;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_ALL252;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_REV255;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_STD255;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC320;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC322;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC326;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_REV322;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52PR_AUT225;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EAPR1P_PLATTR04;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.INSTALLMENT_ACCTS_30_DQ_L12M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.INSTALLMENT_ACCTS_30_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.INTEGRATION_IND;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.IN_NETWORK_IND;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.IS_FA_OR_P2P;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.IS_FROM_YARDI;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.IS_RETURNING_CUSTOMER;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_OLDEST_OPEN_REVOLVING_TRADE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_OLDEST_TRADE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_ON_FILE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_SINCE_DEFAULT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NETWORK_IND;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_ACCTS_REVOLVING_GOOD;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_COLLECTIONS;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_OPEN_RETAIL_TRADES;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_ACCTS_60_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_100_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_60_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_85_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.UTILIZATION_AUTHORIZED_USER;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.VANTAGE30_SCORE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.VANTAGE40_SCORE;

import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * This class stores constants that defines behavior of each different {@link Version}.
 */
public class ModelSpec {

  public static final List<FlexScoreModelExtractedFeature> V6_FEATURE_LIST = List.of(
      EADM09_P02E,
      EADS05_AGG602,
      VANTAGE30_SCORE,
      EADS52OB_REV322,
      REVOLVING_TRADES_100_UTIL,
      TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING,
      REVOLVING_ACCTS_60_DQ_L24M,
      REVOLVING_TRADES_UTIL,
      TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING,
      AADM86_LINKF193,
      EADS05_AGG218,
      INSTALLMENT_ACCTS_30_DQ_L24M,
      MONTHS_SINCE_DEFAULT,
      NUM_COLLECTIONS,
      TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT,
      TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT,
      MONTHS_ON_FILE,
      AADM86_LINKA027,
      EADS07_BALMAG01,
      EADS142_AT104S,
      EADS142_G208B,
      NUM_OPEN_RETAIL_TRADES,
      ACCTS_30_DQ_L24M,
      NUM_ACCTS_REVOLVING_GOOD,
      COLLECTIONS_BALANCE_NO_MEDICAL,
      REVOLVING_TRADES_85_UTIL,
      AADM86_LINKA029,
      MONTHS_OLDEST_OPEN_REVOLVING_TRADE,
      ACCTS_DEFAULT,
      INTEGRATION_IND,
      NETWORK_IND,
      MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES,
      EADS05_AGG504,
      EADS142_AT28B,
      EADS52OB_BKC320,
      EADS52EP_REV255,
      EADS142_AT24S,
      EADS142_G411S,
      EADS10_PAYMNT07,
      EADS142_G224C,
      AADM86_LINKA006,
      AADM86_LINKF115,
      AADM86_LINKF181,
      AADM86_LINKF069,
      EADS142_S061S,
      EADS15_INDEXQ1,
      EADS15_INDEXQ2,
      EADS15_INDEXQ3,
      EADS15_INDEXQ4,
      TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT,
      UTILIZATION_AUTHORIZED_USER,
      EADS142_FI34S,
      AADM86_LINKF038,
      AADM86_LINKA022,
      EADS52PR_AUT225,
      AADM86_LINKF167,
      AADM86_LINKA021,
      EADS142_BR02S,
      AADM86_LINKA002,
      EADS142_ST24S
  );

  public static final List<FlexScoreModelExtractedFeature> V5_FEATURE_LIST = List.of(
      EADM09_P02E,
      EADS05_AGG602,
      VANTAGE30_SCORE,
      EADS52OB_REV322,
      REVOLVING_TRADES_100_UTIL,
      TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING,
      REVOLVING_ACCTS_60_DQ_L24M,
      REVOLVING_TRADES_UTIL,
      TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING,
      AADM86_LINKF193,
      EADS05_AGG218,
      INSTALLMENT_ACCTS_30_DQ_L24M,
      MONTHS_SINCE_DEFAULT,
      NUM_COLLECTIONS,
      TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT,
      TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT,
      MONTHS_ON_FILE,
      AADM86_LINKA027,
      EADS07_BALMAG01,
      EADS142_AT104S,
      EADS142_G208B,
      NUM_OPEN_RETAIL_TRADES,
      ACCTS_30_DQ_L24M,
      NUM_ACCTS_REVOLVING_GOOD,
      COLLECTIONS_BALANCE_NO_MEDICAL,
      REVOLVING_TRADES_85_UTIL,
      AADM86_LINKA029,
      MONTHS_OLDEST_OPEN_REVOLVING_TRADE,
      ACCTS_DEFAULT,
      IS_FROM_YARDI,
      IN_NETWORK_IND,
      MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES,
      EADS05_AGG504,
      EADS142_AT28B,
      EADS52OB_BKC320,
      EADS52EP_REV255,
      EADS142_AT24S,
      EADS142_G411S,
      EADS10_PAYMNT07,
      EADS142_G224C
  );

  public static final List<FlexScoreModelExtractedFeature> V7_FEATURE_LIST = List.of(
      IS_FA_OR_P2P,
      EADS05_AGG602,
      VANTAGE40_SCORE,
      IS_FROM_YARDI,
      IS_RETURNING_CUSTOMER,
      EADM09_P02F,
      EADM09_P02E,
      EADS52OB_BKC320,
      EADS52OB_BKC326,
      EADS11_CV23,
      EADS142_G224C,
      EADS142_S061S,
      EADS142_S071A,
      EADS142_BC103S,
      EADS52OB_REV322,
      EADS142_BR02S,
      EADS05_AGG205,
      EADS05_AGG504,
      EADS52EP_ALL252,
      EADS52EP_REV255,
      EADS142_AT28B,
      EADS142_G411S,
      EADS10_PAYMNT07,
      EADS05_AGG218,
      EADS142_AT24S,
      EADS142_BR34S,
      EAPR1P_PLATTR04,
      EADS52PR_AUT225,
      EADS10_PAYMNT10,
      EADS06_TRV08,
      EADS06_TRV07,
      EADS15_AGGS106,
      EADS142_RE28S,
      EADS142_G242F,
      EADS05_AGG801,
      EADS52EP_STD255,
      EADS52OB_BKC322,
      TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING,
      NUM_COLLECTIONS,
      COLLECTIONS_BALANCE_NO_MEDICAL,
      ACCTS_DEFAULT,
      MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES,
      TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING,
      INSTALLMENT_ACCTS_30_DQ_L24M,
      INSTALLMENT_ACCTS_30_DQ_L12M,
      MONTHS_SINCE_DEFAULT,
      NUM_ACCTS_REVOLVING_GOOD,
      REVOLVING_TRADES_60_UTIL,
      REVOLVING_TRADES_UTIL,
      MONTHS_OLDEST_OPEN_REVOLVING_TRADE,
      TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT,
      REVOLVING_ACCTS_60_DQ_L24M,
      REVOLVING_TRADES_85_UTIL,
      TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT,
      TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT,
      EADS11_CV28,
      EADS142_G208B,
      EADS07_BALMAG01,
      NUM_OPEN_RETAIL_TRADES,
      MONTHS_OLDEST_TRADE,
      REVOLVING_TRADES_100_UTIL,
      AADM86_LINKF181,
      AADM86_LINKF068,
      AADM86_LINKF107,
      AADM86_LINKF145,
      AADM86_LINKF038,
      AADM86_LINKF176,
      AADM86_LINKF053,
      AADM86_LINKF090,
      AADM86_LINKF115,
      AADM86_LINKF185,
      AADM86_LINKF193,
      AADM86_LINKF166
  );

  /**
   * MUST ALWAYS be ascending order.
   */
  static final List<Double> V5_SCORE_DECILE_THRESHOLD = List.of(
      0.026756,
      0.040660,
      0.056562,
      0.074639,
      0.095708,
      0.119252,
      0.149153,
      0.189817,
      0.252896,
      1.0
  );

  /**
   * MUST ALWAYS be ascending order.
   */
  static final List<Double> V6_SCORE_DECILE_THRESHOLD = List.of(
      0.011777,
      0.017716,
      0.026146,
      0.037287,
      0.050650,
      0.066246,
      0.085017,
      0.108884,
      0.147379,
      1.0
  );


  static final List<Double> V7_SCORE_DECILE_THRESHOLD = List.of(
      0.012756,
      0.019412,
      0.027999,
      0.039356,
      0.053234,
      0.069753,
      0.089916,
      0.117446,
      0.161161,
      1.0
  );

  /**
   * Both flexscore-5 and flexscore-6 in Sagemaker share the same csv based input.
   * Note: null value is replaced with <code>-99999999</code>
   *
   * @param inputFeatures
   * @param selectedFeatures
   * @return
   */
  public static String toCsv(Map<FlexScoreModelExtractedFeature, Number> inputFeatures,
      List<FlexScoreModelExtractedFeature> selectedFeatures) {
    return selectedFeatures.stream().map(f -> {
      Number featureValue = inputFeatures.get(f);
      if (featureValue != null) {
        return featureValue.toString();
      }
      return "-99999999";
    }).collect(Collectors.joining(","));
  }
}
