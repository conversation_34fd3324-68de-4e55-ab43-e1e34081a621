package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.rentcredibility.api.RentCredibilityApi;
import com.getflex.rentcredibility.client.ApiException;
import com.getflex.rentcredibility.model.ScoreRequest;
import com.getflex.rentcredibility.model.ScoreResponse;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class DsScoreService {

  private final RentCredibilityApi api;
  private final Metrics metrics;

  @Autowired
  public DsScoreService(RentCredibilityApi api, Metrics metrics) {
    this.api = api;
    this.metrics = metrics;
  }

  @Cacheable(value = CacheConfig.DS_SCORE_CACHE, key = "#batchMemo")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Integer getBatchMemoRentCredibilityScore(String batchMemo) {
    try {
      ScoreRequest request = new ScoreRequest().batchMemo(batchMemo);
      final ScoreResponse response = api.scoreRequestV1ScorePost(request);
      metrics.increment(
          getClass(),
          formatMetricName("getBatchMemoRentCredibilityScore", false)
      );
      return response.getScore();
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getBatchMemoRentCredibilityScore", true)
      );
      throw new InternalDependencyFailureException(
          "Could not get RentCredibilityScore for batchMemo %s".formatted(batchMemo), e);
    }
  }
}
