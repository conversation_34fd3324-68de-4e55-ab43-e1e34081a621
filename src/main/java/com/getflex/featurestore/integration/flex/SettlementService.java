package com.getflex.featurestore.integration.flex;

import com.getflex.settlement.api.SettlementApi;
import com.getflex.settlement.api.SettlementApi.APIgetDdaDecisionsForCustomerRequest;
import com.getflex.settlement.client.ApiException;
import com.getflex.settlement.model.DdaDecision;
import com.getflex.settlement.model.GetDdaDecisionsResponse;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SettlementService {

  private final SettlementApi api;

  @Autowired
  public SettlementService(SettlementApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<DdaDecision> getDdaDecisionsByBillTransactionId(String billTransactionId) {
    try {
      APIgetDdaDecisionsForCustomerRequest r = APIgetDdaDecisionsForCustomerRequest.newBuilder()
          .billTransactionId(billTransactionId).build();
      GetDdaDecisionsResponse response = api.getDdaDecisionsForCustomer(r);
      if (response == null) {
        return null;
      }
      return response.getDdaDecisions();
    } catch (ApiException e) {
      log.error("Failed to fetch settlement btxId={}", billTransactionId);
      return null;
    }
  }
}
