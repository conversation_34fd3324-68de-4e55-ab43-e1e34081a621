package com.getflex.featurestore.integration.flex.utils;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class LedgerUtils {

  private final LedgerService ledgerService;

  public LedgerUtils(LedgerService ledgerService) {
    this.ledgerService = ledgerService;
  }

  public List<RecordLedger> getFundsOutRecords(Long customerId, OffsetDateTime dtCreatedStartRange) {
    List<RecordLedger> fundsOutRecords = ledgerService.retrieveLedgerByCustomerId(
        APIretrieveLedgerByCustomerIdRequest.newBuilder()
            .customerId(customerId)
            .dtCreatedStartRange(dtCreatedStartRange)
            .paymentStatusId(PaymentState.SETTLED.getValue())
            .includeRefund(IncludeRefundEnum.INCLUDE)
            .build()
    );
    if (fundsOutRecords == null) {
      return List.of();
    }
    return fundsOutRecords.stream().filter(this::validFundsOut).toList();
  }

  public List<RecordLedger> getFundsInRecords(Long customerId, OffsetDateTime dtCreatedStartRange) {
    List<RecordLedger> fundsInRecords = ledgerService.retrieveLedgerByCustomerId(
        APIretrieveLedgerByCustomerIdRequest.newBuilder()
            .customerId(customerId)
            .dtCreatedStartRange(dtCreatedStartRange)
            .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
            .paymentStatusId(PaymentState.SETTLED.getValue())
            .includeRefund(IncludeRefundEnum.EXCLUDE)
            .build()
    );
    if (fundsInRecords == null) {
      return List.of();
    }
    return fundsInRecords.stream()
        .filter(fundsIn -> fundsIn.getPaymentCategoryId() != null
            && fundsIn.getPaymentCategoryId().equals(MovementCategory.CHARGE.getValue())
        ).toList();
  }

  private Boolean validFundsOut(RecordLedger recordLedger) {
    return isRegularFundsOut(recordLedger) || isOctFundsOut(recordLedger);
  }

  private Boolean isOctFundsOut(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() != null
        && recordLedger.getPaymentCategoryId() == MovementCategory.CHARGE.getValue()
        && recordLedger.getMoneyMovementTypeId() != null
        && recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_OUT.getValue();
  }

  private Boolean isRegularFundsOut(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() != null
        && recordLedger.getPaymentCategoryId() == MovementCategory.REFUND.getValue()
        && recordLedger.getMoneyMovementTypeId() != null
        && recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_IN.getValue();
  }

}
