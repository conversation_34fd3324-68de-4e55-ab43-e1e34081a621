package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.verification.api.KycApi;
import com.getflex.verification.api.VerificationApi;
import com.getflex.verification.api.VerificationApi.APIgetLatestIncomeVerificationRequest;
import com.getflex.verification.client.ApiException;
import com.getflex.verification.model.FindDuplicateTinRequest;
import com.getflex.verification.model.FindDuplicateTinResponse;
import com.getflex.verification.model.Kyc;
import com.getflex.verification.model.RiskProfileDto;
import com.getflex.verification.model.SearchKyc200Response;
import com.getflex.verification.model.Verification;
import com.google.common.base.Strings;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VerificationService {

  private final VerificationApi api;

  private final KycApi kycApi;

  @Autowired
  public VerificationService(VerificationApi api, KycApi kycApi) {
    this.api = api;
    this.kycApi = kycApi;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Verification getVerification(String verificationId) {
    try {
      APIgetLatestIncomeVerificationRequest request = APIgetLatestIncomeVerificationRequest.newBuilder()
            .verificationId(verificationId).build();

      return api.getLatestIncomeVerification(request);
    } catch (ApiException e) {
      // This can happen if the customer does not have an offer yet
      throw new InternalDependencyFailureException(
          "Could not fetch verification for verificationId=%s".formatted(verificationId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Kyc getKyc(String kycId) {
    try {
      return kycApi.getKyc(kycId);
    } catch (ApiException e) {
      if (e.getCode() == 404) {
        throw new InternalServiceBadDataException("KYC not found");
      }
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Kyc> findKycSharingSameSsn(Kyc kyc) {
    if (Strings.isNullOrEmpty(kyc.getVerifiedSsnHmac512())) {
      throw new IllegalArgumentException("Input KYC doesn't have verified_ssn_hmac_512");
    }

    List<Kyc> result = new ArrayList<>();
    String nextToken = null;
    do {
      try {
        SearchKyc200Response page = kycApi.searchKyc(kyc.getVerifiedSsnHmac512(), nextToken, null);
        result.addAll(
            page.getItems().stream().filter(k -> !Objects.equals(k.getEntityName(), kyc.getEntityName())).toList());
        nextToken = page.getNextToken();
      } catch (ApiException e) {
        throw new InternalDependencyFailureException(e);
      }
    } while (nextToken != null);

    return result;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RiskProfileDto> findDuplicateRiskProfilesByTin(String tinHash) {
    try {
      FindDuplicateTinRequest request = new FindDuplicateTinRequest().tinHash(tinHash);
      FindDuplicateTinResponse response = api.findDuplicateTin(request);
      return response.getRiskProfileList();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

}
