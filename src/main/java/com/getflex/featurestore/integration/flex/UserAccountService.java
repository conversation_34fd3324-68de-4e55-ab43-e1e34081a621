package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.useraccount.api.UserAccountApi;
import com.getflex.useraccount.client.ApiException;
import com.getflex.useraccount.model.CustomerAccountUpdateHistory;
import com.getflex.useraccount.model.UserAccount;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserAccountService {

  private final UserAccountApi api;

  public enum UserAccountUpdateType {
    UPSERT_CREATE("UPSERT_CREATE"),
    UPSERT_UPDATE("UPSERT_UPDATE"),
    UPDATE("UPDATE");

    private final String name;

    UserAccountUpdateType(String name) {
      this.name = name;
    }

    @Override
    public String toString() {
      return name;
    }
  }

  @Autowired
  public UserAccountService(UserAccountApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public UserAccount getUserAccountByPublicId(String customerPublicId) {
    try {
      return api.getUserAccountByPublicId(customerPublicId).getUserAccount();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch the user account from customer public Id %s".formatted(customerPublicId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public CustomerAccountUpdateHistory getUserAccountUpdateHistory(String customerPublicId) {
    try {
      return api.customerAccountUpdateHistory(customerPublicId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch the user account update history from customer public Id %s".formatted(customerPublicId), e);
    }
  }

}
