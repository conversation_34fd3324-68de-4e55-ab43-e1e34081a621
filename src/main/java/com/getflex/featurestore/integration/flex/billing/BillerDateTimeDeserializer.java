package com.getflex.featurestore.integration.flex.billing;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

public class BillerDateTimeDeserializer extends JsonDeserializer<OffsetDateTime> {

  private static final Pattern LOCAL_DATE_TIME_FORMAT = Pattern.compile("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}");

  @Override
  public OffsetDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
    if (LOCAL_DATE_TIME_FORMAT.matcher(p.getText()).matches()) {
      return LocalDateTime
          .parse(p.getText(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"))
          .atOffset(ZoneOffset.UTC);
    } else {
      return OffsetDateTime.parse(p.getText());
    }
  }
}
