package com.getflex.featurestore.integration.flex.billing;

import com.getflex.billing.api.v2.api.BillingServicePublicApi;
import com.getflex.billing.api.v2.client.ApiException;
import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaContainerPortalPortalContainer;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseBillPayRestrictionResult;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseGetBillerAccountResponse;
import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BillingService {

  private final BillingServicePublicApi billingApi;

  public BillingService(BillingServicePublicApi billingApi) {
    this.billingApi = billingApi;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public PropertyInfo getPropertyByIdentityBillerId(Long billerId) {
    try {
      ComGetflexBillingControllerV2PropertyControllerPropertyResponse property = billingApi.getPropertyByBillerId(
          billerId);
      return new PropertyInfo(property.getBillingIntegrationType(), property.getIsOutOfNetwork(),
          property.getLocation());
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public ComGetflexBillingControllerV2PropertyControllerPropertyResponse getPropertyById(
      Long billerId) {
    try {
      List<ComGetflexBillingControllerV2PropertyControllerPropertyResponse> resp = billingApi.getProperties(
          billerId);
      if (resp.isEmpty()) {
        throw new InternalDependencyFailureException(
            "Billing service hasn't returned any property info");
      }
      if (resp.size() > 1) {
        log.warn("Biller service has returned more than one property info, using the first one");
      }
      return resp.get(0);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch property by id %s".formatted(billerId), e);
    }
  }

  @Cacheable(value = CacheConfig.PORTAL_CACHE)
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<ComGetflexBillingAuroraJpaContainerPortalPortalContainer> getPortals() {
    try {
      return billingApi.getPortals(1L, 200L, null, null, null);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public ComGetflexBillingControllerV2PropertyControllerPropertyResponse getPropertyByBillerId(
      Long billerId) {
    try {
      return billingApi.getPropertyByBillerId(billerId);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.error("no property found for biller id {}", billerId);
        return null;
      }
      log.error("Error fetching property for biller id {}, exception {}", billerId, e.getMessage());
    }
    return null;
  }

  public ComGetflexBillingDtoResponseGetBillerAccountResponse getBillerAccountByBillerIdAndBillerAccountId(
      Long billerId, Long billerAccountId) {
    try {
      return billingApi.getBillerAccount2(billerId, billerAccountId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  public ComGetflexBillingDtoResponseBillPayRestrictionResult getBillPayRestrictionStatusWithBillerId(
      Long billerId, Long billerAccountId) {
    try {
      return billingApi.getBillPayRestrictionStatusWithBillerId(billerId, billerAccountId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public ComGetflexBillingControllerV2PropertyControllerPropertyResponse getBillerInfoByBillerId(Long billerId) {
    try {
      return billingApi.getPropertyByBillerId(billerId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }
}
