package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.cipher.util.CipherUtil;
import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.identity.api.IdentityApi;
import com.getflex.identity.api.v2.IdentityV2Api;
import com.getflex.identity.client.ApiException;
import com.getflex.identity.model.AccountSearchData;
import com.getflex.identity.model.AccountSearchFilters;
import com.getflex.identity.model.AccountSearchResult;
import com.getflex.identity.model.AgType;
import com.getflex.identity.model.BillerAccountRecord;
import com.getflex.identity.model.BillerAccountStatusEnumDto;
import com.getflex.identity.model.BillerAccountStatusRecord;
import com.getflex.identity.model.BillerDto;
import com.getflex.identity.model.CustomerAgreementRecord;
import com.getflex.identity.model.CustomerLookup;
import com.getflex.identity.model.CustomerLookupWithNullableFields;
import com.getflex.identity.model.CustomerStatusRecord;
import com.getflex.identity.model.DuplicateAddress;
import com.getflex.identity.model.FinancialPartnerRecord;
import com.getflex.identity.model.FinancialPartnerType;
import com.getflex.identity.model.GetAccountResponse;
import com.getflex.identity.model.GetBillerAccountStatusHistoryResponse;
import com.getflex.identity.model.GetCustomerAgreementFilters;
import com.getflex.identity.model.GetCustomerAgreementRequest;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.identity.model.GetPropertyNameResponse;
import com.getflex.identity.model.LookupCustomerResponse;
import com.getflex.identity.model.SearchFinancialPartnerRequest;
import com.getflex.identity.model.SearchFinancialPartnerResponse;
import com.getflex.identity.model.SearchFor;
import com.getflex.identity.model.v2.BillerAccountSearchRequest;
import com.getflex.identity.model.v2.BillerAccountSearchResponse;
import com.getflex.identity.model.v2.CustomerDataV2;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.identity.model.v2.SearchCustomerRequest;
import com.google.common.collect.Iterables;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IdentityService {

  private final IdentityApi api;
  private final IdentityV2Api v2Api;
  private final Metrics metrics;
  private final CipherUtil cipherUtil;
  private final IdentityV2Api identityV2Api;

  public enum BillerAccountCategoryStatus {
    PENDING,
    ACTIVE,
    CANCELED
  }

  @Autowired
  public IdentityService(IdentityApi api, IdentityV2Api v2Api, Metrics metrics, CipherUtil cipherUtil,
      IdentityV2Api identityV2Api) {
    this.api = api;
    this.v2Api = v2Api;
    this.metrics = metrics;
    this.cipherUtil = cipherUtil;
    this.identityV2Api = identityV2Api;
  }

  @Cacheable(value = CacheConfig.IDENTITY_CACHE, key = "{ #root.methodName, #customerId }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetCustomerResponse getCustomer(Long customerId) {
    try {
      final GetCustomerResponse response = api.getCustomer(customerId);
      metrics.increment(
          getClass(),
          formatMetricName("getCustomer", false)
      );
      return response;
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getCustomer", true)
      );
      throw new InternalDependencyFailureException("Could not fetch customer of id %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.IDENTITY_CACHE, key = "{ #root.methodName, #customerId }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public CustomerDataV2 getCustomerById(Long customerId) {
    SearchCustomerRequest request = new SearchCustomerRequest().customerId(customerId);
    try {
      return Iterables.getOnlyElement(identityV2Api.searchCustomer(request));
    } catch (com.getflex.identity.client.v2.ApiException e) {
      log.error("Could not fetch customer by id {}", customerId, e);
      throw new InternalDependencyFailureException("Could not fetch customer by id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<CustomerDataV2> searchCustomer(SearchCustomerRequest request) {
    try {
      return identityV2Api.searchCustomer(request);
    } catch (com.getflex.identity.client.v2.ApiException e) {
      log.error("Error fetching customer(s) from IdentityV2 api.", e);
      throw new InternalDependencyFailureException("Error fetching customer(s) from IdentityV2 api",  e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public ExtendedBillerAccountData getBillerAccount(Long billerAccountId) {
    try {
      return Iterables.getOnlyElement(
          v2Api.searchBillerAccounts(new BillerAccountSearchRequest().billerAccountId(billerAccountId))
              .getBillerAccounts());
    } catch (com.getflex.identity.client.v2.ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch biller account of id %d".formatted(billerAccountId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<ExtendedBillerAccountData> getBillerAccountsByCustomerId(Long customerId) {
    try {
      BillerAccountSearchResponse response = v2Api.searchBillerAccounts(new BillerAccountSearchRequest()
          .customerId(customerId));
      return response.getBillerAccounts();
    } catch (com.getflex.identity.client.v2.ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("Could not fetch biller accounts for customer id {}", customerId, e);
        return List.of();
      }
      log.error("Error fetching biller accounts for customer id {}", customerId, e);
      throw new InternalDependencyFailureException(
          "Error fetching biller accounts for customer id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<BillerAccountStatusRecord> getBillerAccountStatusHistory(Long billerAccountId) {
    try {
      GetBillerAccountStatusHistoryResponse response = api.getBillerAccountStatusHistory(billerAccountId);
      return response.getBillerAccountStatusRecords();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("Could not fetch biller account history for biller account id {}", billerAccountId, e);
        return List.of();
      }
      log.error("Error fetching biller account history for biller account id {}", billerAccountId, e);
      throw new InternalDependencyFailureException(
          "Error fetching biller account history for biller account id %s".formatted(billerAccountId), e);
    }
  }

  @Cacheable(value = CacheConfig.IDENTITY_CACHE, key = "{ #root.methodName, #billerAccountId }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public BillerDto getBillerByBillerAccountId(Long billerAccountId) {
    try {
      GetAccountResponse billerAccount = api.getBillerAccountById(billerAccountId);
      return billerAccount.getCurrentAccount().getBiller();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Integer getCustomerFlexAnywhereDuplicatedAddress(Long customerId) {
    try {
      CustomerLookup cl = new CustomerLookup();
      cl.setCustomerId(customerId);
      List<DuplicateAddress> customerList = api.customersWithDuplicateAddresses(cl);
      return customerList.size();
    } catch (ApiException e) {
      log.warn("Failed to fetch duplicated address for customerId={}", customerId);
      return 0;
    }
  }

  /**
   * Get all typical unencrypted PII info plus decrypted DOB & SSN info.
   *
   * @param evalParams Must contain {@link EvalParams#getCustomerId()}, {@link EvalParams#getDobCipher()} and
   *                   {@link EvalParams#getSsnCipher()}
   * @return
   */
  @Retry(name = "{#root.name}#{#root.methodName}")
  public CustomerInfo getCompleteCustomerInfo(EvalParams evalParams) {
    CustomerInfo customerInfo = getCustomerInfo(evalParams);
    customerInfo.setDateOfBirth(cipherUtil.decryptWithCache(evalParams.getDobCipher()));
    customerInfo.setSsn(cipherUtil.decryptWithCache(evalParams.getSsnCipher()));
    return customerInfo;
  }

  /**
   * Get all typical unencrypted PII info.
   *
   * @param evalParams Must contain {@link EvalParams#getCustomerId()}.
   * @return
   */
  @Retry(name = "{#root.name}#{#root.methodName}")
  public CustomerInfo getCustomerInfo(EvalParams evalParams) {
    try {
      LookupCustomerResponse resp = api.lookupCustomer(
          new CustomerLookupWithNullableFields().customerId(evalParams.getCustomerId()));
      return CustomerInfo.builder()
          .customerId(resp.getCustomerId())
          .firstName(resp.getFirstName())
          .lastName(resp.getLastName())
          .addressLine1(resp.getAddressLine1())
          .addressLine2(resp.getAddressLine2())
          .city(resp.getCity())
          .state(resp.getState())
          .postalCode(resp.getZip())
          .emailAddress(resp.getEmail())
          .phoneNumber(resp.getPhone())
          .build();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch customer of id %s".formatted(evalParams.getCustomerId()), e);
    }
  }

  /**
   * Get property name by biller account id
   *
   * @param billerAccountId
   * @return
   */
  @Retry(name = "{#root.name}#{#root.methodName}")
  public String getPropertyNameByBillerAccountId(Long billerAccountId) {
    try {
      GetPropertyNameResponse res = api.getPropertyName(billerAccountId, null);
      if (res == null) {
        return "";
      }
      return res.getPropertyName();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("no property name for ba_id: {}", billerAccountId, e);
        return "";
      }
      log.error("Exception fetching property name for ba_id: {}", billerAccountId, e);
      throw new InternalDependencyFailureException(
          "Could not fetch property name for ba_id %s".formatted(billerAccountId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public BillerAccountRecord getActiveBillerAccountByCustomerId(Long customerId) {
    try {
      // request body
      AccountSearchData accountSearchData = new AccountSearchData();
      SearchFor searchFor = new SearchFor();
      searchFor.setCustomerLookup(new CustomerLookup().customerId(customerId));
      accountSearchData.setSearchValues(searchFor);

      AccountSearchFilters accountSearchFilters = new AccountSearchFilters();
      accountSearchFilters.setStatus(List.of(BillerAccountStatusEnumDto.ACTIVE));
      accountSearchData.setFilters(accountSearchFilters);

      AccountSearchResult accountSearchResult = api.accountSearch(accountSearchData);
      if (accountSearchResult.getAccounts().isEmpty()) {
        return null;
      }
      return accountSearchResult.getAccounts().get(0);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public FinancialPartnerType getFinancialPartner(Long customerId) {
    try {
      SearchFinancialPartnerRequest searchFinancialPartnerRequest = new SearchFinancialPartnerRequest()
          .lookup(
              new CustomerLookup().customerId(customerId)
          );
      SearchFinancialPartnerResponse response = api.searchFinancialPartner(searchFinancialPartnerRequest);
      if (response.getFinancialPartners() != null && !response.getFinancialPartners().isEmpty()) {
        return response.getFinancialPartners().stream()
            .filter(partner -> partner.getHistorical() != null)
            .filter(partner -> !partner.getHistorical())
            .findFirst()
            .map(FinancialPartnerRecord::getFinancialPartnerType)
            .orElse(FinancialPartnerType.NULL);
      }
      return FinancialPartnerType.NULL;
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<CustomerAgreementRecord> getCustomerAgreements(Long customerId, List<AgType> agreementTypes) {
    try {
      GetCustomerAgreementRequest getCustomerAgreementRequest = new GetCustomerAgreementRequest()
          .customerLookup(
              new CustomerLookup()
                  .customerId(customerId)
          ).filters(
              new GetCustomerAgreementFilters()
                  .agreementTypes(agreementTypes)
                  .newestCustomerAgreementsOnly(true)
          );
      return api.getCustomerAgreements(getCustomerAgreementRequest).getCustomerAgreements();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No customer agreements found for customerId {}", customerId);
        return List.of();
      }
      throw new InternalDependencyFailureException(e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<CustomerStatusRecord> getCustomerStatusHistory(Long customerId) {
    try {
      return api.getCustomerStatusHistory(customerId).getCustomerStatusRecords();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No customer status history found for customerId {}", customerId);
        return List.of();
      }
      throw new InternalDependencyFailureException(e);
    }
  }
}
