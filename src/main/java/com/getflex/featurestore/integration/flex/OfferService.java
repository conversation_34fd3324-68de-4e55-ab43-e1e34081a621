package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.offerv2.api.OfferApi;
import com.getflex.offerv2.api.OfferApi.APIfindOffersBySsnRequest;
import com.getflex.offerv2.api.OfferApi.APIgetCustomerOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIgetFirstEverAcceptedOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIgetOfferByBillerAccountRequest;
import com.getflex.offerv2.api.OfferApi.APIgetRootOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIsearchOfferRequest;
import com.getflex.offerv2.client.ApiException;
import com.getflex.offerv2.model.DelinquencyStatusResponse;
import com.getflex.offerv2.model.InternalOffer;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OfferService {

  private final OfferApi api;

  @Autowired
  public OfferService(OfferApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getFirstEverAcceptedOffer(Long customerId) {
    try {
      APIgetFirstEverAcceptedOfferRequest request = APIgetFirstEverAcceptedOfferRequest.newBuilder()
          .customerId(customerId).build();
      return api.getFirstEverAcceptedOffer(request);
    } catch (ApiException e) {
      // This can happen if the customer does not have an offer yet
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch first ever accepted offer for customerId %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.OFFER_CACHE, key = "{ #root.methodName, #customerId, false }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<InternalOffer> searchOffer(Long customerId) {
    try {
      APIsearchOfferRequest request = APIsearchOfferRequest.newBuilder().customerId(customerId)
          .allVersions(Boolean.FALSE).build();
      return api.searchOffer(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers from customerId %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.OFFER_CACHE, key = "{ #root.methodName, #customerId, #allVersions }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<InternalOffer> searchOffer(Long customerId, Boolean allVersions) {
    try {
      APIsearchOfferRequest request = APIsearchOfferRequest.newBuilder().customerId(customerId)
          .allVersions(allVersions).build();
      return api.searchOffer(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers from customerId %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getOfferByBillerAccountId(Long billerAccountId) {
    try {
      APIgetOfferByBillerAccountRequest request = APIgetOfferByBillerAccountRequest.newBuilder()
          .billerAccountId(billerAccountId).build();
      return api.getOfferByBillerAccount(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers from billerAccountId %s".formatted(billerAccountId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getRootOffer(String offerId, Long offerVersion) {
    try {
      APIgetRootOfferRequest request = APIgetRootOfferRequest.newBuilder()
          .offerId(offerId).offerVersion(offerVersion).build();
      return api.getRootOffer(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch root offer from offerId %s and offerVersion %s".formatted(offerId, offerVersion), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getLastSuspension(Long customerId) {
    OfferApi.APIgetSuspensionHistoryRequest req = OfferApi.APIgetSuspensionHistoryRequest.newBuilder()
        .customerId(customerId)
        .lastSuspension(Boolean.TRUE)
        .build();
    try {
      var offers = api.getSuspensionHistory(req).getOfferVersions();
      if (offers.isEmpty()) {
        return null;
      }
      if (offers.size() > 1) {
        log.warn("Multiple suspended offers returned customerId={}", customerId);
      }
      return offers.get(0);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch last suspended offer for customerId %d".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getOfferByBillerAccountIdAndOfferVersion(Long billerAccountId, @Nullable Long offerVersion) {
    try {
      APIgetOfferByBillerAccountRequest.Builder requestBuilder = APIgetOfferByBillerAccountRequest.newBuilder()
          .billerAccountId(billerAccountId);
      if (offerVersion != null) {
        requestBuilder.offerVersion(offerVersion);
      }
      APIgetOfferByBillerAccountRequest request = requestBuilder.build();
      return api.getOfferByBillerAccount(request);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch offers using billerAccountId %s".formatted(billerAccountId), e);
    }
  }

  @Cacheable(value = CacheConfig.OFFER_CACHE, key = "{ #root.methodName, #customerId }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getOfferByCustomerId(Long customerId) {
    try {
      APIgetCustomerOfferRequest request = APIgetCustomerOfferRequest.newBuilder()
          .customerId(customerId).build();
      return api.getCustomerOffer(request);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers using customerId %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public DelinquencyStatusResponse getDqStatus(Long customerId) {
    try {
      return api.getDelinquencyStatus(customerId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers using customerId %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public InternalOffer getOffer(String offerId, @Nullable Long offerVersion) {
    try {
      return api.getOffer(offerId, offerVersion);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return null;
      }
      throw new InternalDependencyFailureException("Could not fetch offers using ID %s".formatted(offerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<InternalOffer> getOfferBySsnHmac(Long customerId, String ssnHmac) {
    try {
      APIfindOffersBySsnRequest request = APIfindOffersBySsnRequest
          .newBuilder()
          .customerId(customerId)
          .ssnHmac(ssnHmac)
          .build();
      return api.findOffersBySsn(request).getOffers();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException("Could not fetch offers for customer %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.OFFER_CACHE, 
      key = "{ #root.methodName, #customerId, #productIds, #productCategoryIds, #allVersions }")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<InternalOffer> searchOfferByProduct(Long customerId, Set<Long> productIds,
      Set<Long> productCategoryIds, @Nullable Boolean allVersions) {
    try {
      APIsearchOfferRequest.Builder requestBuilder = APIsearchOfferRequest.newBuilder()
          .customerId(customerId)
          .productIds(productIds)
          .productCategoryIds(productCategoryIds);

      if (allVersions != null) {
        requestBuilder.allVersions(allVersions);
      } else {
        requestBuilder.allVersions(false);
      }

      return api.searchOffer(requestBuilder.build());
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Could not fetch offers from customerId %s".formatted(customerId), e);
    }
  }
}
