package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.ledger.api.LedgerApi.APIgetDownpayByTransactionIdRequest;
import com.getflex.ledger.api.LedgerApi.APIgetWalletRecordLedgerRequest;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByBillTransactionIdRequest;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.client.ApiException;
import com.getflex.ledger.model.GetCreditLineBalanceResponse;
import com.getflex.ledger.model.GetLedgerResponse;
import com.getflex.ledger.model.GetLedgerWalletResponse;
import com.getflex.ledger.model.GetOutstandingBalanceResponse;
import com.getflex.ledger.model.GetWalletBalanceResponse;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.PaymentAuthStatus;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.ledger.model.RecordLedgerWallet;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LedgerService {

  private final LedgerApi api;
  private final Metrics metrics;

  public static final Long PARENT_ID_FLEX = 625L;
  public static final List<Long> REVERSEAL_PAYMENT_CATEGORIES = List.of(
      MovementCategory.REFUND.getValue(),
      MovementCategory.REVERSAL.getValue(),
      MovementCategory.WIRE_REVERSAL.getValue(),
      MovementCategory.ACH_RETURN.getValue(),
      MovementCategory.INTRA_BANK_RETURN.getValue(),
      MovementCategory.CHECK_REVERSAL.getValue()
  );

  public static final List<Long> INCOMING_PAYMENT_CATEGORIES = List.of(
      MovementCategory.CAPTURE.getValue(),
      MovementCategory.CHARGE.getValue(),
      MovementCategory.ACH_TRANSFER.getValue(),
      MovementCategory.CHECK_CLEARED.getValue(),
      MovementCategory.INTRA_BANK_TRANSFER.getValue(),
      MovementCategory.WIRE_TRANSFER.getValue()
  );

  public static final List<Long> INCOMING_PAYMENT_TYPES = List.of(
      MoneyMovementType.DOWNPAYMENT.getValue(),
      MoneyMovementType.REPAYMENT.getValue(),
      MoneyMovementType.REPAYMENT_REVX.getValue(),
      MoneyMovementType.REPAYMENT_CHECK.getValue()
  );

  @Getter
  public enum PaymentState {
    SETTLED(1L),
    DECLINED(2L),
    CANCELED(3L),
    BILL_PAID(4L),
    INITIATED(5L);

    private final long value;

    PaymentState(long value) {
      this.value = value;
    }

    public static PaymentState fromValue(long value) {
      for (PaymentState state : PaymentState.values()) {
        if (state.getValue() == value) {
          return state;
        }
      }
      throw new IllegalArgumentException("Unknown value: " + value);
    }
  }

  @Getter
  public enum MoneyMovementType {
    DOWNPAYMENT(1L),
    REPAYMENT(2L),
    MEMBERSHIP_FEE(3L),
    PAY_BILLER(4L),
    PROCESSING_FEE(5L),
    //REFUND = 6 # Deprecated, do NOT use
    CUSTOMER_CREDIT(9L),
    REPAYMENT_REVX(10L),
    REPAYMENT_CHECK(11L),
    REVX_MEMBERSHIP_FEE(12L),
    MEMBERSHIP_FEE_CHECK(13L),
    REPAYMENT_TRUEACCORD(14L),
    MEMBERSHIP_FEE_TRUEACCORD(15L),
    PROCESSING_FEE_DOWNPAYMENT(16L),
    PROCESSING_FEE_REPAYMENT(17L),
    FUNDS_IN(18L),
    FUNDS_OUT(19L);

    private final long value;

    MoneyMovementType(long value) {
      this.value = value;
    }

    public static MoneyMovementType fromValue(long value) {
      for (MoneyMovementType t : MoneyMovementType.values()) {
        if (t.getValue() == value) {
          return t;
        }
      }
      throw new IllegalArgumentException("Unknown value: " + value);
    }
  }

  @Getter
  public enum MovementCategory {
    AUTH(1L),
    CAPTURE(2L),
    CHARGE(3L),
    REFUND(4L),
    REVERSAL(5L),  //similar to refund, but the money moves back via 3rd party, such as Revx
    ACH_RETURN(6L),
    ACH_TRANSFER(7L),
    CHECK_CLEARED(8L),
    WIRE_TRANSFER(9L),
    WIRE_REVERSAL(10L),
    //11 and 12 are intra bank transfers
    INTRA_BANK_TRANSFER(11L),
    INTRA_BANK_RETURN(12L),
    CHECK_REVERSAL(13L),
    //14 is RESERVED
    CREDIT_LINE_ADJUSTMENT(15L),
    DISPUTE(16L);

    private final long value;

    MovementCategory(long value) {
      this.value = value;
    }

    public static MovementCategory fromValue(long value) {
      for (MovementCategory c : MovementCategory.values()) {
        if (c.getValue() == value) {
          return c;
        }
      }
      throw new IllegalArgumentException("Unknown value: " + value);
    }
  }

  @Autowired
  public LedgerService(LedgerApi api, Metrics metrics) {
    this.api = api;
    this.metrics = metrics;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Long getCustomerCreditBalance(Long customerId) {
    try {
      final GetCreditLineBalanceResponse response = api.getCreditLineBalance(customerId);
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerCLBalance", false)
      );
      return response.getCreditLineBalance();
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerCLBalance", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer credit line balance %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.LEDGER_CACHE, key = "#customerId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Long getCustomerCreditLinePendingBalance(Long customerId) {
    try {
      final GetCreditLineBalanceResponse response = api.getCreditLineBalance(customerId);
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerCreditLinePendingBalance", false)
      );
      return response.getCreditLinePendingBalance();
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerCreditLinePendingBalance", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer credit line pending balance %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetOutstandingBalanceResponse getOutstandingBalanceRaw(String customerPublicId, boolean forceLedgerBalance) {
    try {
      final GetOutstandingBalanceResponse response = api.getOutstandingBalance(customerPublicId, forceLedgerBalance);
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerOutstandingBalance", false)
      );
      return response;
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getCustomerOutstandingBalance", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer outstanding balance %s".formatted(customerPublicId), e);
    }
  }

  @Cacheable(value = CacheConfig.LEDGER_CACHE, key = "#customerPublicId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Long getCustomerOutstandingBalance(String customerPublicId) {
    final GetOutstandingBalanceResponse response = getOutstandingBalanceRaw(customerPublicId, false);
    return response.getTotalAmount();
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RecordLedger> getLedgersByCustomerId(Long customerId, boolean includeRefund) {
    try {
      APIretrieveLedgerByCustomerIdRequest request = LedgerApi.APIretrieveLedgerByCustomerIdRequest.newBuilder()
          .customerId(customerId)
          .paymentStatusId(PaymentState.SETTLED.getValue())
          .includeRefund(includeRefund ? IncludeRefundEnum.INCLUDE : IncludeRefundEnum.EXCLUDE)
          .build();
      final GetLedgerResponse response = api.retrieveLedgerByCustomerId(request);
      if (response.getTransactions() == null) {
        return List.of();
      }
      return response.getTransactions();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      metrics.increment(
          getClass(),
          formatMetricName("getLedgersForCustomerId", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer transactions for customer id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RecordLedgerWallet> getWalletLedgersByCustomerId(Long customerId, boolean includeRefund) {
    try {
      LedgerApi.APIgetLedgerWithWalletByCustomerIdRequest request =
          LedgerApi.APIgetLedgerWithWalletByCustomerIdRequest.newBuilder()
          .customerId(customerId)
          .paymentStatusId(PaymentState.SETTLED.getValue())
          .includeRefund(includeRefund ? IncludeRefundEnum.INCLUDE : IncludeRefundEnum.EXCLUDE)
          .includeMetadata(Boolean.TRUE)
          .build();
      final GetLedgerWalletResponse response = api.getLedgerWithWalletByCustomerId(request);
      return response.getTransactions();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      metrics.increment(
          getClass(),
          formatMetricName("getLedgersForCustomerId", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer transactions for customer id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RecordLedger> getLedgersByBillTransactionId(String billTransactionId,
      PaymentState state,
      MoneyMovementType moneyMovementType) {
    try {
      APIretrieveLedgerByBillTransactionIdRequest request
          = LedgerApi.APIretrieveLedgerByBillTransactionIdRequest.newBuilder()
          .billTransactionId(billTransactionId)
          .paymentStatusId(state.getValue())
          .moneyMovementTypeId(moneyMovementType.getValue())
          .includeRefund(IncludeRefundEnum.EXCLUDE)
          .build();
      final GetLedgerResponse response = api.retrieveLedgerByBillTransactionId(request);
      return response.getTransactions();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      metrics.increment(
          getClass(),
          formatMetricName("getLedgersByBillTransactionId", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer transactions for btxId %s, type %s".formatted(billTransactionId,
              moneyMovementType.getValue()), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RecordLedger> getDownpaymentRecords(String billTransactionId,
      MovementCategory category) {
    try {
      APIgetDownpayByTransactionIdRequest request = LedgerApi.APIgetDownpayByTransactionIdRequest.newBuilder()
          .billTransactionId(billTransactionId)
          .paymentCategoryId(category.getValue()).authStatus(PaymentAuthStatus.ALL)
          .build();
      final GetLedgerResponse response = api.getDownpayByTransactionId(request);
      return response.getTransactions();
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getDownpaymentByBillTransactionId", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch customer transactions for btxId %s, category %s".formatted(
              billTransactionId,
              category.getValue()), e);
    }
  }

  @Cacheable(value = CacheConfig.LEDGER_CACHE, key = "#customerId")
  public List<RecordLedgerWallet> getWalletRecordLedgerByCustomerId(Long customerId) {
    try {
      APIgetWalletRecordLedgerRequest request
          = LedgerApi.APIgetWalletRecordLedgerRequest.newBuilder()
          .customerId(customerId)
          .build();
      final GetLedgerWalletResponse response = api.getWalletRecordLedger(request);
      return response.getTransactions();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      metrics.increment(
          getClass(),
          formatMetricName("getSettledWalletRecordLedger", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch settled wallet record ledger for customer id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<RecordLedger> retrieveLedgerByCustomerId(APIretrieveLedgerByCustomerIdRequest request) {
    try {
      final GetLedgerResponse response = api.retrieveLedgerByCustomerId(request);
      return response.getTransactions();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.info("No ledger records found for customer id {}", request.customerId());
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Could not fetch withdraw funds records for customer id %s".formatted(request.customerId()), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetWalletBalanceResponse getWalletBalance(Long customerId) {
    try {
      return api.getWalletBalance(customerId);
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getWalletBalance", true)
      );
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.error("Wallet balance not found for customer id {}", customerId);
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch wallet balance for customer id %s".formatted(customerId), e);
    }
  }
}
