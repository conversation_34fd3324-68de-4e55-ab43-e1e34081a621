package com.getflex.featurestore.integration.flex;

import com.getflex.dispute.api.DisputeApi;
import com.getflex.dispute.client.ApiException;
import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import io.github.resilience4j.retry.annotation.Retry;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DisputeService {

  private final DisputeApi api;

  public static final List<String> ALL_STATUSES =
      List.of("Lost", "NeedsResponse", "UnderReview", "Won", "PartialLost");

  @Autowired
  public DisputeService(DisputeApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Dispute> getLostDisputes(Long customerId) {
    try {
      return api.disputesByCustomer(customerId, Collections.emptyList(), List.of("Lost"), null, null).getDisputes();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Could not fetch lost disputes from customerId %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Dispute getLatestDisputeEventByCustomerId(Long customerId) {
    try {
      return api.latestDisputeEventByCustomer(customerId);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch latest dispute from customerId %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public int getTotalNumberOfDispute(Long customerId, OffsetDateTime dtCreatedStart) {
    try {
      List<Dispute> disputes =
          api.disputesByCustomer(
              customerId, Collections.emptyList(), ALL_STATUSES,
              dtCreatedStart, null).getDisputes();
      if (disputes == null) {
        return 0;
      }
      return disputes.size();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return 0;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch disputes from customerId %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.DISPUTE_CACHE, key = "#customerId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Dispute> getAllDisputesByCustomerId(Long customerId) {
    try {
      return api.disputesByCustomer(customerId,
          Collections.emptyList(), ALL_STATUSES,
              null, null).getDisputes();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Could not fetch disputes from customerId %s".formatted(customerId), e);
    }
  }

  @Cacheable(value = CacheConfig.DISPUTE_CACHE, key = "#disputeId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Dispute getDisputeById(Long disputeId) {
    try {
      return api.getDisputeById(disputeId);
    } catch (Exception e) {
      throw new InternalDependencyFailureException(
          "Could not fetch dispute for disputeId %s".formatted(disputeId), e);
    }
  }
}
