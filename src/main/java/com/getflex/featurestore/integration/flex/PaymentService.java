package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.payment.api.PaymentApi;
import com.getflex.payment.client.ApiException;
import com.getflex.payment.model.DeclineCodeEnum;
import com.getflex.payment.model.Get3dsAuthenticationsResponse;
import com.getflex.payment.model.GetCustomerBillResponse;
import com.getflex.payment.model.Model3dsAuthRecord;
import io.github.resilience4j.retry.annotation.Retry;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PaymentService {

  private final PaymentApi api;

  @Autowired
  public PaymentService(PaymentApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Integer getTotalUserPaymentsDeclined(Long customerId, DeclineCodeEnum declineCode) {
    try {
      return api.getTotalUserPaymentsDeclined(customerId, declineCode)
          .getTotalUserPaymentsDeclined().intValue();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No declined payments found for customer {}", customerId);
        return 0;
      }

      throw new InternalDependencyFailureException(
          "Failed to get total user payments declined for customer " + customerId, e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Integer getTotalUserPaymentsDeclinedV2(
      Long customerId,
      List<DeclineCodeEnum> declineCodes,
      OffsetDateTime dtCreatedStart
  ) {
    try {
      return api.getTotalUserPaymentsDeclinedV2(customerId, declineCodes, dtCreatedStart)
          .getTotalUserPaymentsDeclined()
          .intValue();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return 0;
      }
      throw new InternalDependencyFailureException(
          "Failed to get total user payments declined v2 for customer " + customerId, e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetCustomerBillResponse getCustomerBill(String customerPublicId, String billTransactionId) {
    try {
      PaymentApi.APIgetCustomerBillByTransactionIdRequest request = PaymentApi
          .APIgetCustomerBillByTransactionIdRequest.newBuilder()
          .customerPublicId(customerPublicId)
          .billTransactionId(billTransactionId)
          .build();
      return api.getCustomerBillByTransactionId(request);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn(
            "No bill found from payment, cid {}, btxId {}", customerPublicId, billTransactionId
        );
        return null;
      }
      throw new InternalDependencyFailureException(
          "No bill posted for billTransactionId" + billTransactionId, e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Model3dsAuthRecord> get3dsAuthRecords(String customerPublicId) {
    try {
      Get3dsAuthenticationsResponse response = api.get3dsAuth(customerPublicId);
      if (response == null) {
        return List.of();
      }
      return response.getData();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Failed to get 3ds auth records for customerPublicId: " + customerPublicId, e
      );
    }
  }

}
