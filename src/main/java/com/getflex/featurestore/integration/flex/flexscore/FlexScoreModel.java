package com.getflex.featurestore.integration.flex.flexscore;

import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA002;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA006;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA021;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA022;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA027;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA029;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF038;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF053;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF068;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF069;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF090;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF107;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF115;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF145;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF166;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF167;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF176;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF181;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF185;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKF193;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.ACCTS_30_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.ACCTS_DEFAULT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.COLLECTIONS_BALANCE_NO_MEDICAL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADM09_P02E;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADM09_P02F;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG205;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG218;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG504;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG602;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS05_AGG801;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS06_TRV07;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS06_TRV08;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS07_BALMAG01;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS10_PAYMNT07;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS10_PAYMNT10;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS11_CV23;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS11_CV28;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT104S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT24S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_AT28B;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BC103S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BR02S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_BR34S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_FI34S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G208B;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G224C;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G242F;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_G411S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_RE28S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_S061S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_S071A;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS142_ST24S;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_AGGS106;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ1;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ2;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ3;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS15_INDEXQ4;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_ALL252;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_REV255;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52EP_STD255;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC320;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC322;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_BKC326;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52OB_REV322;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EADS52PR_AUT225;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.EAPR1P_PLATTR04;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.INSTALLMENT_ACCTS_30_DQ_L12M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.INSTALLMENT_ACCTS_30_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_OLDEST_OPEN_REVOLVING_TRADE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_ON_FILE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.MONTHS_SINCE_DEFAULT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_ACCTS_REVOLVING_GOOD;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_COLLECTIONS;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.NUM_OPEN_RETAIL_TRADES;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_ACCTS_60_DQ_L24M;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_100_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_60_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_85_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.REVOLVING_TRADES_UTIL;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.UTILIZATION_AUTHORIZED_USER;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.VANTAGE30_SCORE;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.VANTAGE40_SCORE;
import static java.util.Map.entry;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.flex.flexscore.model.ModelOutput;
import com.getflex.featurestore.integration.flex.flexscore.model.ShapElement;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelFeatureFormulaParams;
import com.getflex.featurestore.utils.Metrics;
import com.google.common.annotations.VisibleForTesting;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@Slf4j
@Service
public class FlexScoreModel {

  private static final String FLEX_SCORE_MODEL_AVAILABILITY_METRIC_NAME = "flex_score_model_availability";
  private final ObjectMapper objectMapper;
  private final SageMakerRuntimeClient sageMaker;
  private final Map<Version, String> endpointName;
  private final Metrics metrics;

  public FlexScoreModel(ServiceConfig config, SageMakerRuntimeClient sageMakerRuntimeClient, Metrics metrics) {
    sageMaker = sageMakerRuntimeClient;
    endpointName = Collections.unmodifiableMap(config.getFlexScoreEndpoint());
    this.metrics = metrics;
    objectMapper = new ObjectMapper();
  }

  /**
   * @param flexScoreVersion
   * @param formulaParams
   * @return
   */
  public ModelOutput execute(Version flexScoreVersion, FlexScoreModelFeatureFormulaParams formulaParams) {
    Map<FlexScoreModelExtractedFeature, Number> inputFeatures = extractFeatures(formulaParams,
        flexScoreVersion.getFeatureList());
    return toModelOutput(formulaParams.getAlloyReport(), sageMaker.invokeEndpoint(buildRequest(flexScoreVersion,
            inputFeatures)),
        inputFeatures, flexScoreVersion);
  }

  private InvokeEndpointRequest buildRequest(Version flexScoreVersion, Map<FlexScoreModelExtractedFeature,
      Number> featureMap) {
    return InvokeEndpointRequest.builder()
        .endpointName(endpointName.get(flexScoreVersion))
        .contentType(flexScoreVersion.getContentType())
        .body(SdkBytes.fromUtf8String(flexScoreVersion.getInputSerializer().apply(featureMap)))
        .enableExplanations("`true`")
        .build();
  }

  @VisibleForTesting
  public Map<FlexScoreModelExtractedFeature, Number> extractFeatures(FlexScoreModelFeatureFormulaParams formulaParams,
      List<FlexScoreModelExtractedFeature> featureList) {
    Map<FlexScoreModelExtractedFeature, Number> result = new HashMap<>(featureList.size() + 1);
    featureList.forEach(f -> {
      Number value = f.apply(formulaParams);
      result.put(f, value);
      metrics.distribution(this.getClass(), "flex_score_feature_availability", value != null ? 1.0 : 0.0,
          "feature_name", f.name(), "member_code", getMemberCode(formulaParams.getAlloyReport()));
    });
    metrics.increment(this.getClass(), "flex_score_feature_count");
    return result;
  }

  private ModelOutput toModelOutput(AlloyReport alloyReport, InvokeEndpointResponse invokeEndpointResponse,
      Map<FlexScoreModelExtractedFeature, Number> inputFeatures, Version flexScoreVersion) {
    Boolean success = false;
    try {
      if ("application/json".equalsIgnoreCase(invokeEndpointResponse.contentType())) {
        JsonNode outputJson = objectMapper.readTree(invokeEndpointResponse.body().asUtf8String());
        final Number vantageScore = flexScoreVersion == Version.V7
            ? inputFeatures.get(FlexScoreModelExtractedFeature.VANTAGE40_SCORE)
            : inputFeatures.get(FlexScoreModelExtractedFeature.VANTAGE30_SCORE);

        // figure out score and decile
        double score = outputJson.at("/predictions/data").asDouble();
        Integer decile = null;
        if (vantageScore == null) {
          score = -5.0;
        } else if (vantageScore.intValue() == 1 || vantageScore.intValue() == 4) {
          score = -1.0;
        } else {
          decile = getDecile(flexScoreVersion, score);
        }
        success = true;
        return new ModelOutput(inputFeatures, vantageScore, score, decile, getRejectionCodes(decile, outputJson));
      }
      throw new RuntimeException("Unsupported Sagemaker output content type");
    } catch (IOException e) {
      throw new RuntimeException(e);
    } finally {
      String memberCode = getMemberCode(alloyReport) == null ? "unknown" : getMemberCode(alloyReport);
      Double metricValue = success ? 1.0 : 0.0;
      metrics.distribution(this.getClass(), FLEX_SCORE_MODEL_AVAILABILITY_METRIC_NAME, metricValue,
          "member_code", memberCode);
    }
  }

  public String getMemberCode(AlloyReport alloyReport) {
    try {
      return alloyReport.getRawResponses().getTransunionCredit().get(0).getCreditBureau().getTransactionControl()
          .getSubscriber().getMemberCode();
    } catch (Exception e) {
      log.warn("Failed to extract member code", e);
      return null;
    }
  }

  /**
   * Map flexscore to decile based on {@link Version#getScoreDecileThreshold()}.
   *
   * @param flexScoreVersion
   * @param score
   * @return
   * @throws RuntimeException if input score is greater than the last (greatest) threshold
   */
  private static int getDecile(Version flexScoreVersion, double score) {
    int decile;
    int index = Collections.binarySearch(flexScoreVersion.getScoreDecileThreshold(), score);
    if (index < 0) {
      index = Math.negateExact(index + 1);
    }
    decile = 10 - index;
    if (decile <= 0) {
      throw new RuntimeException("Invalid flex_score=" + score);
    }
    return decile;
  }

  static List<Double> validateDecileThreshold(List<Double> scoreDecileThreshold) {
    if (scoreDecileThreshold.size() != 10) {
      throw new IllegalArgumentException("Must have exactly 10 threshold");
    }
    for (int i = 1; i < scoreDecileThreshold.size(); i++) {
      if ((double) scoreDecileThreshold.get(i) <= scoreDecileThreshold.get(i - 1)) {
        throw new IllegalArgumentException("Threshold must be monotonically increasing");
      }
    }

    return scoreDecileThreshold;
  }

  /**
   * Return top {@link this#REJECTION_CODE_COUNT} reasons (count limit first then deduped) explaining the score.
   *
   * @param decile
   * @param outputJson
   * @return
   */
  @VisibleForTesting
  Set<String> getRejectionCodes(Integer decile, JsonNode outputJson) {
    if (decile == null) {
      return Set.of("MR003");
    }

    List<ShapElement> shaps = objectMapper.convertValue(outputJson.at("/explanations/kernel_shap/0"),
        objectMapper.getTypeFactory().constructCollectionType(List.class, ShapElement.class));
    return shaps.stream()
        .filter(s -> MODEL_REASON_CODE_MAPPING.containsKey(s.feature()))
        .filter(s -> s.feature() != FlexScoreModelExtractedFeature.EADS142_ST24S || s.value() >= 0.0)
        .sorted(Comparator.comparing(ShapElement::value, Comparator.reverseOrder()))
        .limit(REJECTION_CODE_COUNT)
        .map(s -> MODEL_REASON_CODE_MAPPING.get(s.feature()))
        .collect(Collectors.toSet());
  }

  static final int REJECTION_CODE_COUNT = 4;

  /**
   * Flex score versions.
   * <br>
   * This enum also defines 3 aspects of the flexscore model behavior (all about request at the moment, can be
   * extended):
   * <ul>
   *   <li>A subset list of {@link FlexScoreModelExtractedFeature} to be consumed by the model</li>
   *   <li>Request body content type</li>
   *   <li>(related to previous item) How the features are serialized to the specific content type</li>
   * </ul>
   */
  @Getter
  public enum Version {
    V5(ModelSpec.V5_FEATURE_LIST, ModelSpec.V5_SCORE_DECILE_THRESHOLD),
    V6(ModelSpec.V6_FEATURE_LIST, ModelSpec.V6_SCORE_DECILE_THRESHOLD),
    V7(ModelSpec.V7_FEATURE_LIST, ModelSpec.V7_SCORE_DECILE_THRESHOLD);

    /**
     * Content type of Sagemaker request body.
     */
    private final String contentType;
    /**
     * Features to be consumed by the specific model. Order of features may or may not matter, depending on the version
     * of model and serializer.
     */
    private final List<FlexScoreModelExtractedFeature> featureList;
    /**
     * Request body serializer.
     */
    private final Function<Map<FlexScoreModelExtractedFeature, Number>, String> inputSerializer;

    private final List<Double> scoreDecileThreshold;

    /**
     * @param featureList          Features to be consumed by the specific model
     * @param scoreDecileThreshold
     */
    Version(List<FlexScoreModelExtractedFeature> featureList, List<Double> scoreDecileThreshold) {
      this.contentType = "text/csv";
      this.featureList = featureList;
      this.inputSerializer = input -> ModelSpec.toCsv(input, featureList);
      this.scoreDecileThreshold = validateDecileThreshold(scoreDecileThreshold);
    }
  }

  /**
   * Note that the keys of this map is indeed 1:1 mapping to {@link FlexScoreModelExtractedFeature}, but the name might
   * be slightly different.
   * <br>
   * {@link FlexScoreModelExtractedFeature} completely honors credit-features package naming so that it's easier
   * to test against old data.
   * <br>
   * Naming of keys in this map is defined by new model in Sagemaker.
   */
  static final Map<FlexScoreModelExtractedFeature, String> MODEL_REASON_CODE_MAPPING = Map.ofEntries(
      entry(EADM09_P02E, "MR001"),
      entry(EADS05_AGG602, "MR019"),
      entry(VANTAGE30_SCORE, "MR006"),
      entry(EADS52OB_REV322, "MR007"),
      entry(REVOLVING_TRADES_100_UTIL, "MR007"),
      entry(TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING, "MR003"),
      entry(REVOLVING_ACCTS_60_DQ_L24M, "MR002"),
      entry(REVOLVING_TRADES_UTIL, "MR007"),
      entry(TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING, "MR019"),
      entry(AADM86_LINKF193, "MR004"),
      entry(EADS05_AGG218, "MR019"),
      entry(INSTALLMENT_ACCTS_30_DQ_L24M, "MR002"),
      entry(MONTHS_SINCE_DEFAULT, "MR002"),
      entry(NUM_COLLECTIONS, "MR002"),
      entry(TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT, "MR012"),
      entry(TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT, "MR012"),
      entry(MONTHS_ON_FILE, "MR008"),
      entry(AADM86_LINKA027, "MR010"),
      entry(EADS07_BALMAG01, "MR009"),
      entry(EADS142_AT104S, "MR013"),
      entry(EADS142_G208B, "MR014"),
      entry(NUM_OPEN_RETAIL_TRADES, "MR003"),
      entry(ACCTS_30_DQ_L24M, "MR002"),
      entry(NUM_ACCTS_REVOLVING_GOOD, "MR003"),
      entry(COLLECTIONS_BALANCE_NO_MEDICAL, "MR002"),
      entry(REVOLVING_TRADES_85_UTIL, "MR007"),
      entry(AADM86_LINKA029, "MR004"),
      entry(MONTHS_OLDEST_OPEN_REVOLVING_TRADE, "MR008"),
      entry(ACCTS_DEFAULT, "MR002"),
      entry(MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES, "MR019"),
      entry(EADS05_AGG504, "MR003"),
      entry(EADS142_AT28B, "MR019"),
      entry(EADS52OB_BKC320, "MR007"),
      entry(EADS52EP_REV255, "MR011"),
      entry(EADS142_AT24S, "MR003"),
      entry(EADS142_G411S, "MR004"),
      entry(EADS10_PAYMNT07, "MR011"),
      entry(EADS142_G224C, "MR002"),
      entry(AADM86_LINKA006, "MR004"),
      entry(AADM86_LINKF115, "MR004"),
      entry(AADM86_LINKF181, "MR016"),
      entry(AADM86_LINKF069, "MR004"),
      entry(EADS142_S061S, "MR015"),
      entry(EADS15_INDEXQ1, "MR003"),
      entry(EADS15_INDEXQ2, "MR003"),
      entry(EADS15_INDEXQ3, "MR003"),
      entry(EADS15_INDEXQ4, "MR003"),
      entry(TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT, "MR003"),
      entry(UTILIZATION_AUTHORIZED_USER, "MR007"),
      entry(EADS142_FI34S, "MR007"),
      entry(AADM86_LINKF038, "MR004"),
      entry(AADM86_LINKA022, "MR004"),
      entry(EADS52PR_AUT225, "MR011"),
      entry(AADM86_LINKF167, "MR001"),
      entry(AADM86_LINKA021, "MR004"),
      entry(EADS142_BR02S, "MR003"),
      entry(AADM86_LINKA002, "MR004"),
      entry(EADS142_ST24S, "MR017"),
      
      // New V7 entries
      entry(VANTAGE40_SCORE, "MR006"),
      entry(EADM09_P02F, "MR001"),
      entry(AADM86_LINKF145, "MR003"),
      entry(EADS52OB_BKC326, "MR007"),
      entry(AADM86_LINKF107, "MR004"),
      entry(AADM86_LINKF176, "MR016"),
      entry(AADM86_LINKF068, "MR004"),
      entry(AADM86_LINKF166, "MR001"),
      entry(EADS06_TRV07, "MR003"),
      entry(EADS06_TRV08, "MR003"),
      entry(AADM86_LINKF090, "MR004"),
      entry(EADS05_AGG205, "MR019"),
      entry(REVOLVING_TRADES_60_UTIL, "MR007"),
      entry(EADS11_CV23, "MR003"),
      entry(EAPR1P_PLATTR04, "MR018"),
      entry(EADS142_BR34S, "MR007"),
      entry(AADM86_LINKF053, "MR004"),
      entry(EADS52EP_ALL252, "MR011"),
      entry(INSTALLMENT_ACCTS_30_DQ_L12M, "MR002"),
      entry(EADS52EP_STD255, "MR011"),
      entry(EADS142_BC103S, "MR003"),
      entry(EADS142_G242F, "MR004"),
      entry(EADS142_S071A, "MR002"),
      entry(EADS142_RE28S, "MR019"),
      entry(AADM86_LINKF185, "MR004"),
      entry(EADS52OB_BKC322, "MR007"),
      entry(EADS10_PAYMNT10, "MR011"),
      entry(EADS11_CV28, "MR007"),
      entry(EADS05_AGG801, "MR011"),
      entry(EADS15_AGGS106, "MR003")
  );

}
