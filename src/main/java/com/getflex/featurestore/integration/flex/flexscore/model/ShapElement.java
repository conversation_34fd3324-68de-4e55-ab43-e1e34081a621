package com.getflex.featurestore.integration.flex.flexscore.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import java.util.List;
import lombok.Setter;

@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShapElement {

  private String featureHeader;
  private List<ShapAttribution> attributions;

  /**
   * Translate {@link this#featureHeader} to {@link FlexScoreModelExtractedFeature}.
   * <br>
   * For the majority of features, {@link this#featureHeader} should match
   * {@link FlexScoreModelExtractedFeature#name()} (case-insensitive). But there are a few cases need special mapping.
   *
   * @return
   */
  public FlexScoreModelExtractedFeature feature() {
    try {
      return FlexScoreModelExtractedFeature.valueOf(featureHeader.toUpperCase());
    } catch (IllegalArgumentException e) {
      return switch (featureHeader.toUpperCase()) {
        // flexscore model v5/v6 used shorter name, FlexScoreInputFeature inherited naming from credit-features repo
        case "AGG504" -> FlexScoreModelExtractedFeature.EADS05_AGG504;
        case "AT28B" -> FlexScoreModelExtractedFeature.EADS142_AT28B;
        case "BKC320" -> FlexScoreModelExtractedFeature.EADS52OB_BKC320;
        case "REV255" -> FlexScoreModelExtractedFeature.EADS52EP_REV255;
        case "AT24S" -> FlexScoreModelExtractedFeature.EADS142_AT24S;
        case "G411S" -> FlexScoreModelExtractedFeature.EADS142_G411S;
        case "PAYMNT07" -> FlexScoreModelExtractedFeature.EADS10_PAYMNT07;
        case "G224C" -> FlexScoreModelExtractedFeature.EADS142_G224C;

        case "EADS520B_REV322" -> FlexScoreModelExtractedFeature.EADS52OB_REV322; //typo in the model

        case "VANTAGE_SCORE" -> FlexScoreModelExtractedFeature.VANTAGE30_SCORE;

        default -> throw e;
      };
    }
  }

  public double value() {
    return attributions.get(0).getAttribution().get(0).doubleValue();
  }
}
