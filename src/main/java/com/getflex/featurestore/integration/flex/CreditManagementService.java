package com.getflex.featurestore.integration.flex;

import com.getflex.credit.client.ApiException;
import com.getflex.credit.client.LoanManagementApi;
import com.getflex.credit.client.model.Loan;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CreditManagementService {

  private final LoanManagementApi api;

  @Autowired
  public CreditManagementService(LoanManagementApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Loan getLoanById(Long loanId) {
    try {
      return api.getLoanById(loanId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          "Failed to fetch loan for loanId=" + loanId, e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Loan findLoan(String offerId, Long billerAccountId, Long offerVersion) {
    try {
      return api.findLoan(offerId, billerAccountId, offerVersion);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(
          String.format("Failed to find loan for offerId=%s billerAccountId=%s offerVersion=%s", 
              offerId, billerAccountId, offerVersion), e);
    }
  }
}
