package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.exception.IndefiniteHubUserException;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.partnerhub.client.ApiException;
import com.getflex.partnerhub.model.User;
import com.getflex.partnerhub.openapi.PrivateApi;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

@Service
public class PartnerHubService {

  private final PrivateApi privateApi;

  public PartnerHubService(PrivateApi privateApi) {
    this.privateApi = privateApi;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public User getHubUser(Long hubUserId) {
    try {
      return privateApi.getUser(hubUserId);
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }

  /**
   * Find the active hub user per pmcId.
   *
   * @param pmcId
   * @return
   * @throws IndefiniteHubUserException More than 1 active hub user found
   */
  @Retry(name = "{#root.name}#{#root.methodName}")
  public Optional<User> getHubUserByPmcId(Long pmcId) {
    try {
      //TODO exclude inactive user later after test
      List<User> users = privateApi.getUsersForPmc(pmcId, true);
      if (users.size() > 1) {
        throw new IndefiniteHubUserException();
      }
      return users.stream().findAny();
    } catch (ApiException e) {
      throw new InternalDependencyFailureException(e);
    }
  }
}
