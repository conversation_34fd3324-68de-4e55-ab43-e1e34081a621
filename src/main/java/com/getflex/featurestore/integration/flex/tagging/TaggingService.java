package com.getflex.featurestore.integration.flex.tagging;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.tagging.api.TaggingApi;
import com.getflex.tagging.client.ApiException;
import com.getflex.tagging.model.CustomerTagDataItem;
import com.getflex.tagging.model.GetCustomerTagListResponse;
import com.getflex.tagging.model.GetCustomerTagResponse;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaggingService {

  private static final String METRIC_GET_FRAUD_TAGS = "getFraudTags";
  private static final String METRIC_CHECK_CUSTOMER_TAGS = "getCheckCustomerTags";
  private static final Long CUSTOMER_ACCOUNT_TYPE_ID = 1L;

  private final TaggingApi api;
  private final Metrics metrics;
  private final ServiceConfig serviceConfig;

  @Autowired
  public TaggingService(TaggingApi api, Metrics metrics, ServiceConfig serviceConfig) {
    this.api = api;
    this.metrics = metrics;
    this.serviceConfig = serviceConfig;
  }

  public enum TaggingNamespace {
    DEFAULT,
    RISK_FRAUD
  }

  @Cacheable(value = CacheConfig.FRAUD_TAG_CACHE, key = "#customerId")
  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<CustomerTagDataItem> getFraudTags(Long customerId) {
    try {
      List<Long> namespaceIds = List.of(serviceConfig.getTaggingNamespaceIds().get(TaggingNamespace.RISK_FRAUD));
      GetCustomerTagListResponse response = api.getAllTagsForCustomer(customerId,
          CUSTOMER_ACCOUNT_TYPE_ID, namespaceIds, true);
      metrics.increment(
          getClass(),
          formatMetricName(METRIC_GET_FRAUD_TAGS, false)
      );
      if (response.getTags() != null) {
        return new ArrayList<>(response.getTags());
      } else {
        return List.of();
      }
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return List.of();
      }
      metrics.increment(
          getClass(),
          formatMetricName(METRIC_GET_FRAUD_TAGS, true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch fraud tags for customer of id %s".formatted(customerId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public boolean customerHasTag(Long customerId, String tagName) {
    try {
      GetCustomerTagResponse response = api.checkCustomerTagByName(customerId, CUSTOMER_ACCOUNT_TYPE_ID, tagName,
          true);

      metrics.increment(
          getClass(),
          formatMetricName(METRIC_CHECK_CUSTOMER_TAGS, false)
      );
      return response.getCustomerTag() != null;
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        return false;
      }
      metrics.increment(
          getClass(),
          formatMetricName(METRIC_CHECK_CUSTOMER_TAGS, true)
      );
      throw new InternalDependencyFailureException(
          "Failed to get the tag for customer of id %s".formatted(customerId), e);
    }
  }
}