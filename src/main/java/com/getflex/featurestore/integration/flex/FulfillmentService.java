package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.FulfillmentUtils;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.fulfillment.api.FulfillmentsApi;
import com.getflex.fulfillment.client.ApiException;
import com.getflex.fulfillment.model.FulfillmentInfo;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FulfillmentService {

  private final FulfillmentsApi api;
  private final Metrics metrics;

  @Autowired
  public FulfillmentService(FulfillmentsApi api,
                            Metrics metrics) {
    this.api = api;
    this.metrics = metrics;
  }


  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetStatusResponseV2 getStatusCode(String billTransactionId) {
    try {
      FulfillmentsApi.APIgetStatus2Request request = FulfillmentsApi.APIgetStatus2Request.newBuilder()
          .billTransactionId(billTransactionId).build();
      return api.getStatus2(request);
    } catch (ApiException e) {
      log.error("Failed to fetch fulfillmentStatus btxId={}, error={}", billTransactionId,
          e.getMessage());
      throw new InternalDependencyFailureException("Failed to fetch fulfillmentStatus", e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public FulfillmentInfo getFulfillmentByCustomerInBp(Long customerId) {
    try {
      FulfillmentInfo fulfillmentInfo = api.queryByCustomerInBP(customerId, FulfillmentUtils.getPayDate(), true, null);
      metrics.increment(getClass(), formatMetricName("getFulfillmentByCustomerInBp", false));
      return fulfillmentInfo;
    } catch (ApiException e) {
      metrics.increment(getClass(), formatMetricName("getFulfillmentByCustomerInBp", true));
      log.error("Failed to fetch fulfillmentInfo for customerId={}, error={}", customerId,
          e.getMessage());
      throw new InternalDependencyFailureException("Failed to fetch fulfillmentInfo", e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetStatusResponseV2 getFulfillmentStatusByBillTransactionIdV2(
          String billTransactionId, Long fulfillmentId) {
    try {
      GetStatusResponseV2 getStatusResponseV2 = api.getStatus2(billTransactionId, fulfillmentId, null);
      metrics.increment(
              getClass(), formatMetricName("getFulfillmentStatusByBillTransactionIdV2", false));
      return getStatusResponseV2;
    } catch (ApiException e) {
      metrics.increment(
              getClass(), formatMetricName("getFulfillmentStatusByBillTransactionIdV2", true));
      log.error(
              "Failed to fetch getStatus2 for billTransactionId={}, fulfillmentId={}, error={}",
              billTransactionId, fulfillmentId, e.getMessage());
      throw new InternalDependencyFailureException("Failed to fetch getStatus2", e);
    }
  }

}
