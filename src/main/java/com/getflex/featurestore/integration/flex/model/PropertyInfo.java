package com.getflex.featurestore.integration.flex.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerLocationResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import java.util.List;
import java.util.Objects;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public record PropertyInfo(BillingIntegrationTypeEnum integrationType, Boolean isOutOfNetwork,
    ComGetflexBillingControllerV2PropertyControllerLocationResponse location) {

  /**
   * Get {@link AlloyExtractedFeatureNumber#NETWORK_IND}. Number mapping is defined by data scientist in <a
   * href="https://docs.google.com/spreadsheets/d/1ijfGplDCA4NRD7vbwZsekPM0SejeXcm7KxnGG9qoU9w/edit?usp=sharing">this
   * doc</a>.
   *
   * @return
   */
  @JsonIgnore
  public int getFlexScoreSixNetworkIndicator() {
    if (integrationType == BillingIntegrationTypeEnum.P2P) {
      return 3;
    }
    if (Objects.equals(Boolean.FALSE, isOutOfNetwork)) {
      return 1;
    }
    if (integrationType == BillingIntegrationTypeEnum.FLEX_ANYWHERE) {
      return 3;
    }
    return 2;
  }

  @JsonIgnore
  public int getInNetworkIndicator() {
    if (integrationType != null) {
      if (List.of(
              BillingIntegrationTypeEnum.FLEX_ANYWHERE,
              BillingIntegrationTypeEnum.PORTAL,
              BillingIntegrationTypeEnum.P2P
          ).contains(integrationType)) {
        return 0;
      }
    }
    if (Objects.equals(Boolean.TRUE, isOutOfNetwork)) {
      return 0;
    }
    return 1;
  }

  /**
   * Get {@link AlloyExtractedFeatureNumber#INTEGRATION_IND} Number mapping is defined by data scientist in <a
   * href="https://docs.google.com/spreadsheets/d/1ijfGplDCA4NRD7vbwZsekPM0SejeXcm7KxnGG9qoU9w/edit?usp=sharing">this
   * doc</a>.
   *
   * @return
   */
  @JsonIgnore
  public int getFlexScoreSixIntegrationIndicator() {
    if (integrationType == null) {
      return 5;
    }
    return switch (integrationType) {
      case FLEX_ANYWHERE, P2P -> 1;
      case PORTAL -> 2;
      case RENTMANAGER -> 3;
      case YARDI -> 4;
      default -> 5;
    };
  }
}
