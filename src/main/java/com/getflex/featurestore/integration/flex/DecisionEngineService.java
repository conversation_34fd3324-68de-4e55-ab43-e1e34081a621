package com.getflex.featurestore.integration.flex;

import com.getflex.decisionengine.async.api.GetCheckpointDecisionLogsApi;
import com.getflex.decisionengine.async.client.ApiException;
import com.getflex.decisionengine.async.model.GetCheckpointDecisionLogsResponse;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DecisionEngineService {
  private final GetCheckpointDecisionLogsApi decisionLogsApi;

  @Autowired
  public DecisionEngineService(GetCheckpointDecisionLogsApi api) {
    this.decisionLogsApi = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public CompletableFuture<GetCheckpointDecisionLogsResponse> getCheckpointDecisionLogs(
      Long customerId, String checkpointName, String checkpointDecision) {
    try {
      return decisionLogsApi.getCheckpointDecisionLogs(customerId, checkpointName, checkpointDecision);
    } catch (ApiException e) {
      return CompletableFuture.failedFuture(e);
    }
  }
}
