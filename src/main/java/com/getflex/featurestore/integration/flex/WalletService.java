package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.wallet.api.WalletApi;
import com.getflex.wallet.api.WalletApi.APIgetCardsRequest;
import com.getflex.wallet.client.ApiException;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetCardsByFingerprintResponse;
import com.getflex.wallet.model.GetCardsResponse;
import com.getflex.wallet.model.GetDefaultCardResponse;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WalletService {

  private final WalletApi api;

  @Autowired
  public WalletService(WalletApi api) {
    this.api = api;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<String> getDistinctPaymentMethodByCustomerId(Long customerId) {
    try {
      return api.getDistinctPaymentMethodByCustomer(customerId).getPaymentMethodStripeIds();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No payment methods found for customer {}", customerId);
        return List.of();
      }

      throw new InternalDependencyFailureException(
          "Could not fetch distinct payment methods for customer " + customerId, e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Boolean hasStripeConnectAccount(String customerPublicId) {
    try {
      return api.getConnectAccount(customerPublicId) != null;
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No stripe connect account for customer public id {}", customerPublicId);
        return false;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch stripe connect account for customer public id %s".formatted(customerPublicId), e);
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public Card getCard(Long paymentMethodId) {
    try {
      return api.getCardDetails(paymentMethodId);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No card found for payment method id: {}", paymentMethodId);
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch card for payment method id: %s".formatted(paymentMethodId), e
      );
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public GetDefaultCardResponse getDefaultCard(String customerPublicId) {
    try {
      return api.getDefaultCard(customerPublicId);
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No card found for customer public id: {}", customerPublicId);
        return null;
      }
      throw new InternalDependencyFailureException(
          "Could not fetch card for customer public id: %s".formatted(customerPublicId), e
      );
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Card> getCards(String customerPublicId) {
    try {
      return api.getCards(
          APIgetCardsRequest.newBuilder()
              .customerPublicId(customerPublicId)
              .isActive(false)
              .build()
      ).getCards();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.warn("No cards found for customer public id: {}", customerPublicId);
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Could not fetch cards for customer public id: %s".formatted(customerPublicId), e
      );
    }
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public List<Card> getCardsByFingerprint(String fingerprint) {
    try {
      return api.getCardsByFingerprint(fingerprint).getCards();
    } catch (ApiException e) {
      if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
        log.error("No cards found for fingerprint: {}", fingerprint);
        return List.of();
      }
      throw new InternalDependencyFailureException(
          "Could not fetch cards for fingerprint: %s".formatted(fingerprint),
          e
      );
    }
  }
}
