package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.billPaymentMethod.client.ApiException;
import com.getflex.billPaymentMethod.model.BillPaymentMethodResponse;
import com.getflex.billPaymentMethod.model.MethodType;
import com.getflex.billPaymentMethod.openapi.BillPaymentMethodApi;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BillPaymentService {

  private final BillPaymentMethodApi api;
  private final Metrics metrics;

  @Autowired
  public BillPaymentService(BillPaymentMethodApi api, Metrics metrics) {
    this.api = api;
    this.metrics = metrics;
  }

  @Retry(name = "{#root.name}#{#root.methodName}")
  public MethodType getFlexAnywhereBillPaymentType(String customerPublicId, String billTransactionId) {
    try {
      final BillPaymentMethodResponse response = api.getBillPaymentMethod(
          customerPublicId,
          1L,
          billTransactionId
      );
      metrics.increment(
          getClass(),
          formatMetricName("getFlexAnywhereBillPaymentType", false)
      );
      if (response.getMethodType() != null && response.getMethodType() == MethodType.VC) {
        return MethodType.VC;
      }
      return MethodType.DDA;
    } catch (ApiException e) {
      metrics.increment(
          getClass(),
          formatMetricName("getFlexAnywhereBillPaymentType", true)
      );
      throw new InternalDependencyFailureException(
          "Could not fetch the flex anywhere bill payment type for customer %s, btxId %s".formatted(
              customerPublicId, billTransactionId), e);
    }
  }
}
