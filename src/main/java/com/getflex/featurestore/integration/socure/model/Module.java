package com.getflex.featurestore.integration.socure.model;

import com.fasterxml.jackson.annotation.JsonValue;

public enum Module {
  KYC("kyc"),
  WATCHLIST_PLUS("watchlistplus"),
  DECISION("decision"),
  SIGMA_IDENTITY_FRAUD("fraud"),
  SYNTHETIC("synthetic"),
  DEVICERISK("devicerisk"),
  ADDRESSRISK("addressrisk"),
  EMAILRISK("emailrisk"),
  PHONERISK("phonerisk"),
  ALERTLIST("alertlist"),
  KYC_PLUS("kycplus");

  private final String name;

  Module(String name) {
    this.name = name;
  }

  @JsonValue
  public String getName() {
    return name;
  }
}
