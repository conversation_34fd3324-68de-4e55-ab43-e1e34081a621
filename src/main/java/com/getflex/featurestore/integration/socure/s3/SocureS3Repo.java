package com.getflex.featurestore.integration.socure.s3;

import com.getflex.featurestore.config.ServiceConfig;
import java.net.URI;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Uri;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@Slf4j
@Service
public class SocureS3Repo {

  private final S3Client s3Client;
  private final String socureBucketName;

  public SocureS3Repo(S3Client s3Client, ServiceConfig serviceConfig) {
    this.s3Client = s3Client;
    socureBucketName = serviceConfig.getSocureBucket();
  }

  /**
   * Save Socure result (raw JSON string) into S3 bucket.
   *
   * @param customerId
   * @param requestSha512 SHA512 in base64url, of Socure request
   * @param rawJson       Raw JSON returned by Socure
   * @return S3 URI of the saved object
   */
  public String save(Long customerId, String requestSha512, String rawJson) {
    String objectKey = buildS3Key(customerId, requestSha512);
    s3Client.putObject(
        PutObjectRequest.builder().bucket(socureBucketName).key(objectKey).build(),
        RequestBody.fromString(rawJson)
    );
    return "s3://%s/%s".formatted(socureBucketName, objectKey);
  }

  /**
   * Load Socure result.
   *
   * @param customerId
   * @param requestSha512 SHA512 in base64url, of Socure request
   * @return Raw JSON string
   */
  public Optional<String> load(Long customerId, String requestSha512) {
    String objectKey = buildS3Key(customerId, requestSha512);
    try {
      return Optional.of(s3Client.getObjectAsBytes(
          GetObjectRequest.builder().bucket(socureBucketName).key(objectKey).build()
      ).asUtf8String());
    } catch (NoSuchKeyException e) {
      return Optional.empty();
    }
  }

  private static String buildS3Key(Long customerId, String requestSha512) {
    return String.format("customer:%d/%s.json", customerId, requestSha512);
  }
}
