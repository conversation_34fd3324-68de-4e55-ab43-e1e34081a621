package com.getflex.featurestore.integration.socure.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.integration.socure.model.SocureRequest;
import com.getflex.featurestore.integration.socure.s3.SocureS3Repo;
import com.google.common.hash.Hashing;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;
import java.util.concurrent.Callable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

/**
 * Custom Spring cache implementation for KYC(Socure). It uses S3 as the storage.
 */
@Slf4j
public class SocureCache implements Cache {

  private final String cacheName;

  private final ObjectMapper objectMapper = new ObjectMapper();

  private final SocureS3Repo socureS3Repo;

  public SocureCache(String cacheName, SocureS3Repo socureS3Repo) {
    this.cacheName = cacheName;
    this.socureS3Repo = socureS3Repo;
  }


  @Override
  public String getName() {
    return cacheName;
  }

  @Override
  public Object getNativeCache() {
    return socureS3Repo;
  }

  @Override
  public ValueWrapper get(Object key) {
    SocureRequest request = (SocureRequest) key;
    String hash = sha512AndBase64UrlEncode(request);

    Optional<String> cache = socureS3Repo.load(Long.parseLong(request.getCustomerUserId()), hash);

    if (cache.isEmpty()) {
      return null;
    }

    log.info("Reusing Socure cache for customer {}, cache sha512={}", request.getCustomerUserId(), hash);
    try {
      return new SimpleValueWrapper(objectMapper.readTree(cache.get()));
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public <T> T get(Object key, Class<T> type) {
    return (T) Optional.ofNullable(get(key)).map(ValueWrapper::get).orElse(null);
  }

  @Override
  public <T> T get(Object key, Callable<T> valueLoader) {
    ValueWrapper valueWrapper = get(key);
    if (valueWrapper == null) {
      try {
        return valueLoader.call();
      } catch (Exception e) {
        throw new ValueRetrievalException(key, valueLoader, e);
      }
    }
    return (T) valueWrapper.get();
  }

  @Override
  public void put(Object key, Object value) {
    SocureRequest request = (SocureRequest) key;
    JsonNode report = (JsonNode) value;

    try {
      String s3Uri = socureS3Repo.save(Long.parseLong(request.getCustomerUserId()), sha512AndBase64UrlEncode(request),
          objectMapper.writeValueAsString(report));
      log.info("Socure cache updated: {}", s3Uri);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public void evict(Object key) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void clear() {
    throw new UnsupportedOperationException();
  }

  private String sha512AndBase64UrlEncode(SocureRequest request) {
    try {
      return Base64.getUrlEncoder().encodeToString(
          Hashing.sha512().hashString(objectMapper.writeValueAsString(request), StandardCharsets.UTF_8).asBytes());
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
