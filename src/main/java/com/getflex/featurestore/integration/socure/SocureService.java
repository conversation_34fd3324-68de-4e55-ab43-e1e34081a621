package com.getflex.featurestore.integration.socure;

import static com.getflex.featurestore.utils.AddressStateAbbreviation.getAbbreviatedState;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.socure.exception.InvalidPiiException;
import com.getflex.featurestore.integration.socure.exception.InvalidPiiException.InputField;
import com.getflex.featurestore.integration.socure.exception.SocureIoFailureException;
import com.getflex.featurestore.integration.socure.exception.SocureServerErrorException;
import com.getflex.featurestore.integration.socure.exception.SocureThrottlingException;
import com.getflex.featurestore.integration.socure.exception.UnknownSocureFailureException;
import com.getflex.featurestore.integration.socure.model.Module;
import com.getflex.featurestore.integration.socure.model.SocureBadRequestResponse;
import com.getflex.featurestore.integration.socure.model.SocureRequest;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.o11y.annotation.TimeMetric;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublisher;
import java.net.http.HttpResponse;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SocureService {

  public static final String SOCURE_WIRE_PROTOCOL = "application/json";

  /**
   * Pointing to the complete entity information returned by Socure KYC+ module.
   */
  public static final String SOCURE_BEST_MATCHED_ENTITY_PATH = "/kycPlus/bestMatchedEntity";
  /**
   * A field under {@link this#SOCURE_BEST_MATCHED_ENTITY_PATH} that provides entity SSN.
   */
  public static final String SOCURE_SSN_FIELD = "ssn";
  /**
   * A field under {@link this#SOCURE_BEST_MATCHED_ENTITY_PATH} that provides entity DOB.
   */
  public static final String SOCURE_DOB_FIELD = "dob";

  private static final List<Module> MODULES = ImmutableList.of(Module.KYC_PLUS, Module.WATCHLIST_PLUS,
      Module.DECISION, Module.SIGMA_IDENTITY_FRAUD, Module.ALERTLIST, Module.EMAILRISK, Module.ADDRESSRISK,
      Module.PHONERISK, Module.SYNTHETIC);
  public static final String CUSTOMER_COUNTRY = "US";

  private final HttpClient httpClient;
  private final URI socureUrl;

  private final String authorization;
  private final ObjectMapper objectMapper = new ObjectMapper();

  public SocureService(@Qualifier("socureHttpClient") HttpClient httpClient, ServiceConfig config) {
    this.httpClient = httpClient;
    socureUrl = URI.create(config.getSocureUrl());
    authorization = config.getSocureApiKey();
  }

  /**
   * Invoke Socure to run KYC and OFAC checks.
   *
   * @param socureRequest
   * @return
   * @throws InvalidPiiException           Certain part of the input is rejected by Socure (400)
   * @throws SocureThrottlingException     Throttled by Socure (429)
   * @throws UnknownSocureFailureException Any http status code other than 200/400/429
   * @throws SocureIoFailureException      Wrapping IO exception, the common example is http2 GOAWAY error
   */
  @TimeMetric
  @Retryable(include = {SocureThrottlingException.class, SocureIoFailureException.class,
      SocureServerErrorException.class}, backoff = @Backoff(random = true, maxDelay = 3000L, multiplier = 1.3))
  @Cacheable(value = "socureCache", cacheManager = "socureCacheManager")
  public JsonNode runKyc(SocureRequest socureRequest) {

    try {
      HttpRequest request = HttpRequest.newBuilder()
          .uri(socureUrl)
          .header("accept", SOCURE_WIRE_PROTOCOL)
          .header("content-type", SOCURE_WIRE_PROTOCOL)
          .header("authorization", authorization)
          .method("POST", toRequestBody(socureRequest))
          .build();
      HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

      if (response.statusCode() == 400) {
        throwIncompleteInputException(response.body());
      }
      if (response.statusCode() == 429) {
        throw new SocureThrottlingException();
      }
      if (response.statusCode() >= 500) {
        throw new SocureServerErrorException(response.body());
      }
      if (response.statusCode() != 200) {
        log.warn("Socure failed unexpectedly - {}:{}", response.statusCode(), response.body());
        throw new UnknownSocureFailureException(response.statusCode());
      }
      return objectMapper.readTree(response.body());

    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    } catch (IOException e) {
      throw new SocureIoFailureException(e);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException(e);
    }
  }

  private void throwIncompleteInputException(String body) {
    try {
      SocureBadRequestResponse resp = objectMapper.readValue(body, SocureBadRequestResponse.class);
      String msg = resp.getMsg().trim().toLowerCase();
      if (msg.contains("invalid email")) {
        throw new InvalidPiiException(InputField.EMAIL);
      }
      if (msg.contains("invalid zip code")) {
        throw new InvalidPiiException(InputField.ZIP);
      }
      throw new InvalidPiiException(InputField.OTHER);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  BodyPublisher toRequestBody(SocureRequest socureRequest) {
    try {
      return HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(socureRequest));
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  public static SocureRequest buildSocureRequest(CustomerInfo customerInfo, String ipAddress) {
    String state = Objects.requireNonNull(getAbbreviatedState(customerInfo.getState()));
    return SocureRequest.builder()
        .modules(MODULES)
        .firstName(customerInfo.getFirstName())
        .surName(customerInfo.getLastName())
        .dob(customerInfo.getDateOfBirth())
        .nationalId(customerInfo.getSsn())
        .email(customerInfo.getEmailAddress())
        .mobileNumber(customerInfo.getPhoneNumber())
        .physicalAddress(customerInfo.getAddressLine1())
        .physicalAddress2(Strings.emptyToNull(customerInfo.getAddressLine2()))
        .city(customerInfo.getCity())
        .state(state)
        .zip(customerInfo.getPostalCode())
        .country(CUSTOMER_COUNTRY)
        .ipAddress(ipAddress)
        .customerUserId(Long.toString(customerInfo.getCustomerId()))
        .build();
  }
}
