package com.getflex.featurestore.integration.socure.model;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class SocureRequest implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private List<Module> modules;
  private String firstName;
  private String surName;
  private String dob;
  private String nationalId;
  private String mobileNumber;
  private String email;
  private String physicalAddress;
  private String physicalAddress2;
  private String city;
  private String state;
  private String zip;
  private String country;
  private String ipAddress;
  @Builder.Default
  private String disclosurePurpose = "GLBA_502(e)";
  private String customerUserId;
}
