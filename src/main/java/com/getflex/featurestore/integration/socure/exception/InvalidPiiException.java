package com.getflex.featurestore.integration.socure.exception;

/**
 * Exception thrown when <PERSON><PERSON> is not happy with certain PII data.
 */
public class InvalidPiiException extends RuntimeException {

  private final InputField invalidField;

  public InvalidPiiException(InputField invalidField) {
    super(invalidField.getMessage());
    this.invalidField = invalidField;
  }

  public InvalidPiiException(InputField invalidField, Exception cause) {
    super(cause);
    this.invalidField = invalidField;
  }

  public InputField getInvalidField() {
    return invalidField;
  }

  public enum InputField {
    STATE(400, "Invalid State Name"),
    ZIP(455, "Invalid Zip Code"),
    EMAIL(454, "Invalid Email Address"),
    OTHER(400, "Invalid Socure Input");

    private final int statusCode;
    private final String message;

    InputField(int statusCode, String message) {

      this.statusCode = statusCode;
      this.message = message;
    }

    public int getStatusCode() {
      return statusCode;
    }

    public String getMessage() {
      return message;
    }
  }


}
