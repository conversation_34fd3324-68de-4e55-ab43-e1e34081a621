package com.getflex.featurestore.service;

import static com.getflex.featurestore.utils.ObservabilityConstants.FEATURE_NAME;
import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.Feature;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.utils.Metrics;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FeatureService {

  private final FeatureFactory featureFactory;
  private final Metrics metrics;
  private final ExecutorService executorService;

  @Autowired
  public FeatureService(FeatureFactory featureFactory, Metrics metrics, ExecutorService executorService) {
    this.featureFactory = featureFactory;
    this.metrics = metrics;
    this.executorService = executorService;
  }

  /**
   * @param evalParamKeys set of eval param keys (if empty, default to returning all feature names)
   * @return a list of feature objects
   */
  public List<Feature> getFeatures(Set<EvalParamKey> evalParamKeys) {
    if (evalParamKeys == null || evalParamKeys.isEmpty()) {
      evalParamKeys = Set.of(EvalParamKey.class.getEnumConstants());
    }
    log.info("Received call to getFeatureNames. evalParamKeys={}", evalParamKeys);

    List<BaseFeature> features = featureFactory.getFeatures(evalParamKeys);
    return features.stream()
        .map(this::getFeatureResponse)
        .peek(feature -> metrics
            .increment(
                getClass(),
                formatMetricName("getFeatures", false),
                FEATURE_NAME,
                feature.getName()
            )
        )
        .collect(Collectors.toList());
  }

  /**
   * @param featureNames set of feature names to be evaluated
   * @param evalParams   object containing evaluation params for the feature names to be evaluated against
   * @return a list of FeatureValue objects containing the evaluated feature values
   */
  public List<FeatureValue> getFeatureValues(Set<String> featureNames, EvalParams evalParams) {
    log.info("Received call to getFeatureValues. featureNames=[{}], evalParams={}", featureNames,
        evalParams != null ? evalParams.toString().replace("\n", "").replace("    ", " ") : ""
    );

    List<CompletableFuture<FeatureValue>> futures = submitTasks(featureNames, evalParams);

    // Combine all the futures into a single CompletableFuture, wait for all tasks to complete
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

    return futures.stream()
        .map(CompletableFuture::join)
        .toList();
  }

  public List<CompletableFuture<FeatureValue>> submitTasks(Set<String> featureNames, EvalParams evalParams) {
    return featureNames.stream()
        .map(featureName -> CompletableFuture.supplyAsync(() -> {
          BaseFeature feature = featureFactory.getFeature(featureName);
          FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
          metrics.increment(
              getClass(),
              formatMetricName("getFeatureValues", featureValue.getErrorMessage() != null),
              FEATURE_NAME, feature.getName()
          );
          return featureValue;
        }, executorService))
        .toList();
  }

  private Feature getFeatureResponse(BaseFeature feature) {
    Feature featureResponse = new Feature();
    featureResponse.setName(feature.getName());
    featureResponse.setType(feature.getType());
    featureResponse.setDescription(feature.getDescription());
    return featureResponse;
  }
}
