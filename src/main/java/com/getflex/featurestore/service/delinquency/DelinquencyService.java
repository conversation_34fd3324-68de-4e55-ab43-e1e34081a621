package com.getflex.featurestore.service.delinquency;

import static com.getflex.featurestore.integration.flex.LedgerService.INCOMING_PAYMENT_CATEGORIES;
import static com.getflex.featurestore.integration.flex.LedgerService.INCOMING_PAYMENT_TYPES;
import static com.getflex.featurestore.integration.flex.LedgerService.PARENT_ID_FLEX;
import static com.getflex.featurestore.integration.flex.LedgerService.REVERSEAL_PAYMENT_CATEGORIES;
import static com.getflex.featurestore.utils.FlexConstant.FLEX_TIMEZONE;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.LedgerRecordInterface;
import com.getflex.featurestore.model.feature.base.WalletLedgerRecordAdapter;
import java.time.Clock;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DelinquencyService {

  private final LedgerService ledgerService;
  private final Clock clock;

  private static final Long MOVE_IN_PRODUCT_CATEGORY_ID = 2L;

  /**
   * Valid payment categories for DOWNPAYMENT and REPAYMENT transactions.
   * These categories represent different payment methods that are considered
   * valid for delinquency calculations.
   */
  public static final List<Long> VALID_CATEGORY_LIST = Arrays.asList(
      MovementCategory.CAPTURE.getValue(),
      MovementCategory.CHARGE.getValue(),
      MovementCategory.REFUND.getValue(),
      MovementCategory.CHECK_CLEARED.getValue(),
      MovementCategory.CHECK_REVERSAL.getValue(),
      MovementCategory.WIRE_TRANSFER.getValue(),
      MovementCategory.WIRE_REVERSAL.getValue(),
      MovementCategory.ACH_TRANSFER.getValue(),
      MovementCategory.ACH_RETURN.getValue(),
      MovementCategory.INTRA_BANK_TRANSFER.getValue(),
      MovementCategory.INTRA_BANK_RETURN.getValue()
  );

  /**
   * Valid movement types for transactions that should be included in delinquency calculations.
   * These represent the core transaction types that affect a customer's balance.
   */
  public static final List<Long> VALID_MOVEMENT_TYPE_LIST = Arrays.asList(
      MoneyMovementType.PAY_BILLER.getValue(),
      MoneyMovementType.CUSTOMER_CREDIT.getValue(),
      MoneyMovementType.REPAYMENT_REVX.getValue(),
      MoneyMovementType.REPAYMENT_CHECK.getValue()
  );

  public DelinquencyService(LedgerService ledgerService, Clock clock) {
    this.ledgerService = ledgerService;
    this.clock = clock;
  }

  /**
   * Determines if a customer is delinquent based on their payment history.
   *
   * @param customerId The ID of the customer to check
   * @param dqMonths The number of months to be considered delinquent (e.g., 0 for DQ0, 1 for DQ30, 6 for DQ180)
   * @param lookBackMonths How many months back from today to check for delinquencies
   * @param excludeCb Whether to exclude CreditBuilder transactions from the analysis
   * @return DelinquencyInfo containing whether the customer is delinquent and the first delinquency date
   */
  public DelinquencyInfo getDelinquencyInfo(
      Long customerId, Integer dqMonths, Integer lookBackMonths, Boolean excludeCb) {
    List<WalletLedgerRecordAdapter> transactions;
    transactions = this.ledgerService.getWalletLedgersByCustomerId(customerId, true).stream().map(
        WalletLedgerRecordAdapter::new
    ).toList();

    // Filter transactions to only include settled transactions with valid movement types and categories
    // Filter out move in related transactions
    List<WalletLedgerRecordAdapter> validTransactions = transactions.stream().filter(
        t -> {
          if (!Objects.equals(t.getPaymentStatusId(), PaymentState.SETTLED.getValue())) {
            return false;
          }
          if (Objects.equals(t.getProductCategoryId(), MOVE_IN_PRODUCT_CATEGORY_ID)) {
            return false;
          }
          if (t.getMoneyMovementTypeId() == MoneyMovementType.DOWNPAYMENT.getValue()
              || t.getMoneyMovementTypeId() == MoneyMovementType.REPAYMENT.getValue()) {
            return VALID_CATEGORY_LIST.contains(t.getPaymentCategoryId());
          }
          return VALID_MOVEMENT_TYPE_LIST.contains(t.getMoneyMovementTypeId());
        }
    ).toList();
    
    Map<String, List<WalletLedgerRecordAdapter>> transactionMap = validTransactions.stream()
        .collect(Collectors.groupingBy(WalletLedgerRecordAdapter::getBillTransactionId,
            Collectors.collectingAndThen(
                Collectors.toList(),
                list -> {
                  list.sort(Comparator.comparing(WalletLedgerRecordAdapter::getDtCreated));
                  return list;
                }
            )));
    
    List<String> dqBtxIds = new ArrayList<>();
    OffsetDateTime firstBpDqDate = null;
    
    for (Map.Entry<String, List<WalletLedgerRecordAdapter>> entry : transactionMap.entrySet()) {
      String btxId = entry.getKey();
      List<WalletLedgerRecordAdapter> transactionList = entry.getValue();
      //transactionList should already be sorted by dtCreated
      
      Optional<WalletLedgerRecordAdapter> firstBpRecord = transactionList.stream()
          .filter(r -> r.getMoneyMovementTypeId() == MoneyMovementType.PAY_BILLER.getValue())
          .findFirst();
      
      if (excludeCb) {
        if (transactionList.stream().anyMatch(WalletLedgerRecordAdapter::hasCreditBuilderTransactions)) {
          log.info("CreditBuilder transaction found for btxId {}", btxId);
          continue;
        }
      }
      
      if (firstBpRecord.isEmpty() || firstBpRecord.get().getDtCreated() == null) {
        continue;
      }
      
      OffsetDateTime bpDate = firstBpRecord.get().getDtCreated();
      OffsetDateTime originalBpDate = bpDate;
      
      // Adjust BP date based on when in the month it occurred
      // If the bill payment was made in the last 5 days of the month, we consider it as
      // belonging to the next month for delinquency calculation purposes
      bpDate = normalizeBpDate(bpDate);
      
      // Calculate the cutoff date - we only look at transactions within the lookback period
      // The cutoff date is the first day of the month, lookBackMonths ago, minus 5 days
      // This 5-day buffer accounts for the end-of-month BP date adjustment
      ZonedDateTime cutOffDate = ZonedDateTime.now(clock);
      cutOffDate = cutOffDate.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
      // subtract 5 days to account for possible early BP start
      cutOffDate = cutOffDate.minusMonths(lookBackMonths).minusDays(5);
      ZonedDateTime now = ZonedDateTime.now(clock);
      
      if (bpDate.isBefore(cutOffDate.toOffsetDateTime()) || now.isBefore(cutOffDate)) {
        // do include if bp date is out of look back month, or it's not cutOffDate yet
        continue;
      }
      
      if (isDq(transactionList, dqMonths, bpDate)) {
        dqBtxIds.add(btxId);
        if (firstBpDqDate == null || originalBpDate.isBefore(firstBpDqDate)) {
          firstBpDqDate = bpDate;
        }
      }
    }
    
    log.warn("dq customerId {}, btxIds {}", customerId, dqBtxIds);
    return DelinquencyInfo.builder()
        .firstBpDqDate(firstBpDqDate)
        .isDq(!dqBtxIds.isEmpty())
        .build();
  }

  /**
   * Normalizes a BP date to the first day of the appropriate month.
   * If the date is in the last 5 days of the month, it's normalized to the first day of the next month.
   * Otherwise, it's normalized to the first day of the current month.
   *
   * @param bpDate The BP date to normalize
   * @return The normalized date as the first day of the appropriate month
   */
  public static OffsetDateTime normalizeBpDate(OffsetDateTime bpDate) {
    if (isInLast5DaysOfMonth(bpDate)) {
      return bpDate.with(TemporalAdjusters.firstDayOfNextMonth());
    } else {
      return bpDate.with(TemporalAdjusters.firstDayOfMonth());
    }
  }

  private static boolean isInLast5DaysOfMonth(OffsetDateTime dateTime) {
    int lastDayOfMonth = dateTime.toLocalDate().with(TemporalAdjusters.lastDayOfMonth())
        .getDayOfMonth();
    return dateTime.getDayOfMonth() >= lastDayOfMonth - 4;
  }

  private boolean isDq(List<WalletLedgerRecordAdapter> txs, Integer dqMonths, OffsetDateTime bpDate) {
    // count pay biller first
    Long balance = txs.stream().mapToLong((tx) -> {
      if (tx.getMoneyMovementTypeId() == MoneyMovementType.PAY_BILLER.getValue()) {
        if (tx.getPaymentCategoryId() != null && REVERSEAL_PAYMENT_CATEGORIES.contains(
            tx.getPaymentCategoryId())) {
          return tx.getAmount();
        } else {
          return -1 * tx.getAmount();
        }
      }
      return 0L;
    }).sum();
    
    // calculate due date on UTC, set to 12:00pm
    OffsetDateTime dueDateUtc = bpDate.plusMonths(dqMonths + 1)
        .with(TemporalAdjusters.firstDayOfMonth()).withHour(12).withMinute(0).withSecond(0);
    // actual due date is 12:00 EST
    ZonedDateTime dueDate = dueDateUtc.atZoneSameInstant(FLEX_TIMEZONE).withHour(0)
        .withMinute(0).withSecond(0);
    ZonedDateTime now = ZonedDateTime.now(clock);
    
    if (dueDate.isAfter(now)) {
      return false;
    }
    
    // creditAmount is what we gave customer to offset their balance, so most of the cases, they are
    // counted as money in, and to_parent_id should be set to customer_id. If it's set to Flex (625), then
    // it is counted as money out, and we should add - sign when calculating balance
    // assuming credit are issued with correct btx id
    Long creditAmounts = txs.stream().mapToLong(
        (tx) -> {
          if (tx.getMoneyMovementTypeId() == MoneyMovementType.CUSTOMER_CREDIT.getValue()) {
            if (Objects.equals(tx.getToParentIdentityId(), PARENT_ID_FLEX)) {
              return -1 * tx.getAmount();
            } else {
              return tx.getAmount();
            }
          }
          return 0L;
        }).sum();
    
    balance += creditAmounts;
    Long[] accumulatedBalance = {balance};
    List<LedgerRecordInterface> payOffTx = new ArrayList<>();
    
    txs.stream().takeWhile(
        tx -> {
          if (INCOMING_PAYMENT_TYPES.contains(tx.getMoneyMovementTypeId())) {
            if (tx.getPaymentCategoryId() != null && REVERSEAL_PAYMENT_CATEGORIES.contains(
                tx.getPaymentCategoryId())) {
              accumulatedBalance[0] -= tx.getAmount();
            } else if (tx.getPaymentCategoryId() != null && INCOMING_PAYMENT_CATEGORIES.contains(
                tx.getPaymentCategoryId())) {
              accumulatedBalance[0] += tx.getAmount();
            } else {
              log.warn("Invalid payment category for btxId {},  categoryId {}",
                  tx.getBillTransactionId(), tx.getPaymentCategoryId());
            }
          }
          if (accumulatedBalance[0] >= 0L) {
            payOffTx.add(tx);
            return false;
          }
          return true;
        }
    ).forEach(
        tx -> {
        }
    );
    if (payOffTx.size() == 1 && payOffTx.get(0) != null && payOffTx.get(0).getDtCreated() != null) {
      return payOffTx.get(0).getDtCreated().isAfter(dueDate.toOffsetDateTime());
    }
    return true;
  }
}
