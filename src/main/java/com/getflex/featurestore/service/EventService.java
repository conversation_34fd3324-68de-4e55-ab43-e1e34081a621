package com.getflex.featurestore.service;

import static com.getflex.featurestore.utils.ObservabilityConstants.formatMetricName;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.utils.Metrics;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;

@Service
@Slf4j
@EnableScheduling
public class EventService {
  private static final List<String> DDB_ELIGIBLE_EVENT_CATEGORY =
      List.of(EventCategory.DEVICE_DATA.toString());
  public static final String CATEGORY_TIMESTAMP_ID_INDEX =
      "customerId_category_timestamp_id_index";

  private final EventRepository eventRepository;
  private final Metrics metrics;
  private final ObjectMapper objectMapper;
  private final SqsClient sqsClient;
  private final String eventsQueueUrl;
  private final DynamoDbEnhancedClient enhancedClient;
  private final DynamoDbTable<DdbEvent> ddbTable;

  @Autowired
  public EventService(
      EventRepository eventRepository,
      Metrics metrics,
      SqsClient sqsClient,
      ServiceConfig serviceConfig,
      DynamoDbEnhancedClient enhancedClient,
      DynamoDbTable<DdbEvent> ddbTable) {
    this.eventRepository = eventRepository;
    this.metrics = metrics;
    this.sqsClient = sqsClient;
    this.objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    this.eventsQueueUrl = serviceConfig.getEventsQueueUrl();
    this.enhancedClient = enhancedClient;
    this.ddbTable = ddbTable;
  }

  public void postEvent(Event event) {
    if (event.getCategory() != null
        && DDB_ELIGIBLE_EVENT_CATEGORY.contains(event.getCategory().name())) {
      postDdbEvent(event);
    } else {
      postSqlEvent(event);
    }
  }

  public void postSqlEvent(Event event) {
    try {
      eventRepository.save(event);
      metrics.increment(getClass(), formatMetricName("postEvent", false));
    } catch (Exception e) {
      log.error(
          "Error in postEvent request: name={}, category={}, entityId={}, entityType={}, "
              + "customerId={}, metadata={}, dtArrived={}",
          event.getName(),
          event.getCategory(),
          event.getEntityId(),
          event.getEntityType(),
          event.getCustomerId(),
          event.getMetadata(),
          event.getDtArrived(),
          e);
      metrics.increment(getClass(), formatMetricName("postEvent", true));
      throw e;
    }
  }

  public void postDdbEvent(Event event) {
    try {
      DdbEvent ddbEvent = toDdbEvent(event);
      TransactWriteItemsEnhancedRequest.Builder requestBuilder =
          TransactWriteItemsEnhancedRequest.builder();
      requestBuilder.addPutItem(ddbTable, ddbEvent);
      enhancedClient.transactWriteItems(requestBuilder.build());
      metrics.increment(getClass(), formatMetricName("postDdbEvent", false));
    } catch (Exception e) {
      log.error(
          "Error in event: name={}, category={}, entityId={}, entityType={}, "
              + "customerId={}, metadata={}, dtArrived={}",
          event.getName(),
          event.getCategory(),
          event.getEntityId(),
          event.getEntityType(),
          event.getCustomerId(),
          event.getMetadata(),
          event.getDtArrived(),
          e);
      metrics.increment(getClass(), formatMetricName("postDdbEvent", true));
      throw e;
    }
  }

  public List<DdbEvent> getCustomerEventsByCategory(Long customerId, String category) {
    return getCustomerEventsByCategoryWithTimestamp(customerId, category, null);
  }

  public List<DdbEvent> getCustomerEventsByCategoryWithTimestamp(
      Long customerId, String category, OffsetDateTime dtCreatedTimestamp) {
    DynamoDbIndex<DdbEvent> gsi = ddbTable.index(CATEGORY_TIMESTAMP_ID_INDEX);

    StringBuilder sortkey = new StringBuilder(category);
    if (dtCreatedTimestamp != null) {
      sortkey.append("#").append(dtCreatedTimestamp);
    }

    QueryConditional query =
        QueryConditional.sortGreaterThanOrEqualTo(
            Key.builder()
                .partitionValue(customerId.toString())
                .sortValue(sortkey.toString())
                .build()
        );

    SdkIterable<Page<DdbEvent>> results = gsi.query(r -> r.queryConditional(query));

    List<DdbEvent> events = new ArrayList<>();
    for (Page<DdbEvent> result : results) {
      events.addAll(result.items());
    }

    return events;
  }

  @Scheduled(fixedDelay = 1) // Long poll with minimal delay of 1 ms
  public void postEventAsync() {
    ReceiveMessageRequest receiveMessageRequest =
        ReceiveMessageRequest.builder()
            .queueUrl(eventsQueueUrl)
            .maxNumberOfMessages(10)
            .waitTimeSeconds(5)
            .build();

    ReceiveMessageResponse receiveMessageResponse = sqsClient.receiveMessage(receiveMessageRequest);
    List<Message> messages = receiveMessageResponse.messages();
    for (Message message : messages) {
      try {
        // Process the message and delete it from the queue
        JsonNode rootNode = objectMapper.readTree(message.body());
        JsonNode detailNode = rootNode.get("detail");
        Event event = objectMapper.readValue(detailNode.toString(), Event.class);
        postEvent(event);
      } catch (JsonProcessingException e) {
        log.error("Error reading event from SQS queue. message={}", message);
        throw new RuntimeException(e);
      } finally {
        sqsClient.deleteMessage(
            b -> b.queueUrl(eventsQueueUrl).receiptHandle(message.receiptHandle()));
      }
    }
  }

  private DdbEvent toDdbEvent(Event event) {
    return DdbEvent.builder()
        .name(event.getName().toString())
        .category(event.getCategory().toString())
        .entityId(event.getEntityId())
        .entityType(event.getEntityType().toString())
        .metadata(event.getMetadata())
        .customerId(event.getCustomerId())
        .build();
  }
}
