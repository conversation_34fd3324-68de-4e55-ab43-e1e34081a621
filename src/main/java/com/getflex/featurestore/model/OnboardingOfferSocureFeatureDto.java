package com.getflex.featurestore.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OnboardingOfferSocureFeatureDto {

  // The full Socure JSON blob (the object under "SocureFeature").
  @JsonIgnore
  private JsonNode raw;

  public static OnboardingOfferSocureFeatureDto fromRaw(JsonNode raw) {
    return new OnboardingOfferSocureFeatureDto(raw);
  }

  public static OnboardingOfferSocureFeatureDto fromEvaluationContext(InternalOfferAllOfEvaluationContext ctx) {
    ObjectNode n = JsonNodeFactory.instance.objectNode();

    n.putNull("referenceId");
    n.put("addressRiskScore",
        ctx.getSocureAddressRiskScore() != null
            ? ctx.getSocureAddressRiskScore()
            : 0.0);
    n.put("emailRiskScore",
        ctx.getSocureEmailRiskScore() != null
            ? ctx.getSocureEmailRiskScore()
            : 0.0);
    n.put("phoneRiskScore",
        ctx.getSocurePhoneRiskScore() != null
            ? ctx.getSocurePhoneRiskScore()
            : 0.0);
    n.put("syntheticScore",
        ctx.getSocureSyntheticScore() != null
            ? ctx.getSocureSyntheticScore()
            : 0.0);

    // build the “fraudReasonCodes” array from ctx.getSocureFraudReasonCodes()
    ArrayNode fraud = n.putArray("fraudReasonCodes");
    Set<String> codes = ctx.getSocureFraudReasonCodes();
    if (codes != null) {
      codes.stream().forEach(fraud::add);
    }
    return new OnboardingOfferSocureFeatureDto(n);
  }

  @JsonProperty("referenceId")
  public String getReferenceId() {
    return raw.path("referenceId").asText(null);
  }

  @JsonProperty("addressRiskScore")
  public double getAddressRiskScore() {
    if (raw.has("addressRiskScore")) {
      return raw.get("addressRiskScore").asDouble();
    }
    return raw.at("/addressRisk/scores/0/score").asDouble();
  }

  @JsonProperty("emailRiskScore")
  public double getEmailRiskScore() {
    if (raw.has("emailRiskScore")) {
      return raw.get("emailRiskScore").asDouble();
    }
    return raw.at("/emailRisk/scores/0/score").asDouble();
  }

  @JsonProperty("phoneRiskScore")
  public double getPhoneRiskScore() {
    if (raw.has("phoneRiskScore")) {
      return raw.get("phoneRiskScore").asDouble();
    }
    return raw.at("/phoneRisk/scores/0/score").asDouble();
  }

  @JsonProperty("syntheticScore")
  public double getSyntheticScore() {
    if (raw.has("syntheticScore")) {
      return raw.get("syntheticScore").asDouble();
    }
    return raw.at("/synthetic/scores/0/score").asDouble();
  }

  @JsonProperty("firstPartyFraudScore")
  public double getFirstPartyFraudScore() {
    if (raw.has("firstPartyFraudScore")) {
      return raw.get("firstPartyFraudScore").asDouble();
    }
    return raw.at("/firstPartyFraud/scores/0/score").asDouble();
  }

  @JsonProperty("fraudReasonCodes")
  public Set<String> getFraudReasonCodes() {
    try {
      JsonNode reasonCodesNode;
      if (raw.has("fraudReasonCodes")) {
        reasonCodesNode =  raw.get("fraudReasonCodes");
      } else {
        reasonCodesNode = raw.at("/fraud/reasonCodes");
      }
      if (reasonCodesNode.isArray()) {
        Set<String> reasonCodes = new HashSet<>();
        for (JsonNode codeNode : reasonCodesNode) {
          reasonCodes.add(codeNode.asText());
        }
        return reasonCodes;
      } else {
        return Collections.emptySet();
      }
    } catch (Exception e) {
      return Collections.emptySet();
    }
  }
}
