package com.getflex.featurestore.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferSocureFeatureDto {

  @JsonIgnore
  private JsonNode rawSocureFeature;

  public static OfferSocureFeatureDto fromRaw(JsonNode rawSocureFeature) {
    return new OfferSocureFeatureDto(rawSocureFeature);
  }

  @JsonProperty("sigmaScore")
  public Double getSigmaScore() {
    if (rawSocureFeature.has("sigmaScore")) {
      return rawSocureFeature.get("sigmaScore").asDouble();
    }
    return rawSocureFeature.at("/fraud/scores/0/score").asDouble();
  }

  @JsonProperty("addressRiskScore")
  public Float getAddressRiskScore() {
    if (rawSocureFeature.has("addressRiskScore")) {
      return rawSocureFeature.get("addressRiskScore").floatValue();
    }
    return rawSocureFeature.at("/addressRisk/scores/0/score").floatValue();
  }

  @JsonProperty("emailRiskScore")
  public Float getEmailRiskScore() {
    if (rawSocureFeature.has("emailRiskScore")) {
      return rawSocureFeature.get("emailRiskScore").floatValue();
    }
    return rawSocureFeature.at("/emailRisk/scores/0/score").floatValue();
  }

  @JsonProperty("nameAddressCorrelationScore")
  public Float getNameAddressCorrelationScore() {
    if (rawSocureFeature.has("nameAddressCorrelationScore")) {
      return rawSocureFeature.get("nameAddressCorrelationScore").floatValue();
    }
    return rawSocureFeature.at("/nameAddressCorrelation/score").floatValue();
  }

  @JsonProperty("nameEmailCorrelationScore")
  public Float getNameEmailCorrelationScore() {
    if (rawSocureFeature.has("nameEmailCorrelationScore")) {
      return rawSocureFeature.get("nameEmailCorrelationScore").floatValue();
    }
    return rawSocureFeature.at("/nameEmailCorrelationScore/score").floatValue();
  }

  @JsonProperty("namePhoneCorrelationScore")
  public Float getNamePhoneCorrelationScore() {
    if (rawSocureFeature.has("namePhoneCorrelationScore")) {
      return rawSocureFeature.get("namePhoneCorrelationScore").floatValue();
    }
    return rawSocureFeature.at("/namePhoneCorrelation/score").floatValue();
  }

  @JsonProperty("phoneRiskScore")
  public Float getPhoneRiskScore() {
    if (rawSocureFeature.has("phoneRiskScore")) {
      return rawSocureFeature.get("phoneRiskScore").floatValue();
    }
    return rawSocureFeature.at("/phoneRisk/scores/0/score").floatValue();
  }

  @JsonProperty("syntheticScore")
  public Float getSyntheticScore() {
    if (rawSocureFeature.has("syntheticScore")) {
      return rawSocureFeature.get("syntheticScore").floatValue();
    }
    return rawSocureFeature.at("/synthetic/scores/0/score").floatValue();
  }

}
