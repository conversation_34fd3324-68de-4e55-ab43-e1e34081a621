package com.getflex.featurestore.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(SnakeCaseStrategy.class)
public class CustomerInfo {
  Long customerId;
  String firstName;
  String lastName;
  String addressLine1;
  String addressLine2;
  String city;
  String state;
  String postalCode;
  String dateOfBirth;
  String ssn;
  String emailAddress;
  String phoneNumber;
}
