package com.getflex.featurestore.model.flexscore.alloy;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.flexscore.alloy.transunionraw.AddOnProduct;
import com.getflex.featurestore.model.flexscore.alloy.transunionraw.Credit;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import lombok.Getter;

/**
 * This defines all features to be extracted from credit file & property info to run various versions of flexscore
 * model.
 * <br>
 * Formula function MUST stay true to the input data, no default value should be specified by the formula, null should
 * be returned instead. That is because different score model may use different default value and having default value
 * at this layer makes it impossible to monitor data availability.
 * <br>
 * If feature is solely based on alloy report, the enum value must use the constructor with {@link Function} parameter.
 */
@Getter
public enum AlloyExtractedFeatureNumber {
  EADM09_P02E(alloyReport ->
      alloyReport.getRawResponses().transunion().getAddOnProduct("00WBO")
          .map(AddOnProduct::getScoreModel)
          .map(scoreModel -> scoreModel.getLongCharacteristic("P02E"))
          .orElse(null)
  ),
  EADS05_AGG602(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H86", "AGG602")
  ),
  EADS11_CV23(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V26", "CV23")
  ),
  VANTAGE30_SCORE(alloyReport ->
      alloyReport.getFormattedResponses().getTransunionCredit().getData()
          .getVantagescore30().getScore()
  ),
  EADS52OB_REV322(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WH1", "REV322")
  ),
  REVOLVING_TRADES_100_UTIL(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(1.0)).orElse(null)
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceFromAllOpenGoodRevolvingTrades()).orElse(null)
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceFromAllOpenRevolvingTrades()).orElse(null)
  ),
  REVOLVING_ACCTS_60_DQ_L24M(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingDqAccounts(3, 24)).orElse(null)
  ),
  REVOLVING_TRADES_UTIL(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesUtilization()).orElse(null)
  ),
  TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalHighCreditOpenGoodRevolving()).orElse(null)
  ),
  AADM86_LINKF193(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF193")
  ),
  EADS05_AGG218(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H86", "AGG218")
  ),
  INSTALLMENT_ACCTS_30_DQ_L24M(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.installmentDqAccounts(2, 24)).orElse(null)
  ),
  MONTHS_SINCE_DEFAULT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.monthsSinceDefault()).orElse(null)
  ),
  NUM_COLLECTIONS(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.numCollections()).orElse(null)
  ),
  TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalOriginalBalanceOpenGoodInstallment()).orElse(null)
  ),
  TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalOriginalBalanceOpenInstallment()).orElse(null)
  ),
  MONTHS_ON_FILE(alloyReport ->
      alloyReport.getRawResponses().transunion().creditHistoryLength()
  ),
  AADM86_LINKA027(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA027")
  ),
  EADS07_BALMAG01(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H88", "BALMAG01")
  ),
  EADS142_AT104S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "AT104S")
  ),
  EADS142_G208B(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "G208B")
  ),
  NUM_OPEN_RETAIL_TRADES(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.numOpenRetailTrades()).orElse(null)
  ),
  ACCTS_30_DQ_L24M(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.dqAccounts(2, 24)).orElse(null)
  ),
  NUM_ACCTS_REVOLVING_GOOD(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.numAccountsRevolvingGood()).orElse(null)
  ),
  COLLECTIONS_BALANCE_NO_MEDICAL(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(Credit::collectionsBalanceNoMedical).orElse(null)
  ),
  REVOLVING_TRADES_85_UTIL(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(0.85)).orElse(null)
  ),
  AADM86_LINKA029(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA029")
  ),
  MONTHS_OLDEST_OPEN_REVOLVING_TRADE(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(Credit::monthsOldestOpenRevolvingTrade).orElse(null)
  ),
  ACCTS_DEFAULT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(Credit::defaultedTrades).orElse(null)
  ),
  IS_FROM_YARDI((alloyReport, property) ->
      property.integrationType() == BillingIntegrationTypeEnum.YARDI ? 1.0 : 0.0),
  IN_NETWORK_IND((alloyReport, property) ->
      Objects.equals(property.isOutOfNetwork(), Boolean.FALSE) ? 1.0 : 0.0),
  MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES(alloyReport ->
      alloyReport.getFormattedResponses().getTransunionCredit().getData().getMaxCreditLimitOpenCreditTrades()),
  EADS05_AGG504(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H86", "AGG504")
  ),
  EADS142_AT28B(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "AT28B")
  ),
  EADS52OB_BKC320(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WH1", "BKC320")
  ),
  EADS52EP_REV255(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WG9", "REV255")
  ),
  EADS142_AT24S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "AT24S")
  ),
  EADS142_G411S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "G411S")
  ),
  EADS10_PAYMNT07(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H91", "PAYMNT07")
  ),
  EADS142_G224C(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "G224C")
  ),

  // flexscore 6 additions

  INTEGRATION_IND((alloyReport, property) -> property.getFlexScoreSixIntegrationIndicator()),
  NETWORK_IND((alloyReport, property) -> property.getFlexScoreSixNetworkIndicator()),
  AADM86_LINKA006(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA006")
  ),
  AADM86_LINKF115(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF115")
  ),
  AADM86_LINKF181(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF181")
  ),
  AADM86_LINKF069(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF069")
  ),
  EADS142_S061S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "S061S")
  ),
  EADS15_INDEXQ1(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V53", "INDEXQ1")
  ),
  EADS15_INDEXQ2(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V53", "INDEXQ2")
  ),
  EADS15_INDEXQ3(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V53", "INDEXQ3")
  ),
  EADS15_INDEXQ4(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V53", "INDEXQ4")
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceOpenGoodInstallment()).orElse(null)
  ),
  UTILIZATION_AUTHORIZED_USER(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.utilizationAuthorizedUser()).orElse(null)
  ),
  EADS142_FI34S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "FI34S")
  ),
  AADM86_LINKF038(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF038")
  ),
  AADM86_LINKA022(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA022")
  ),
  EADS52PR_AUT225(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WG8", "AUT225")
  ),
  AADM86_LINKF167(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF167")
  ),
  AADM86_LINKA021(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA021")
  ),
  EADS142_BR02S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "BR02S")
  ),
  AADM86_LINKA002(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z17", "LINKA002")
  ),
  EADS142_ST24S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "ST24S")
  ),
  TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.totalMonthlyPaymentOpenInstallment()).orElse(null)
  ),
  MONTHS_SINCE_LAST_BANKRUPTCY_RECORD(alloyReport ->
      getMonthsSinceLastBankruptcyRecord(alloyReport).orElse(-1)),

  // flexscore 7 additions
  AADM86_LINKF185(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF185")
  ),
  AADM86_LINKF068(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF068")
  ),
  AADM86_LINKF107(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF107")
  ),
  AADM86_LINKF145(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF145")
  ),
  AADM86_LINKF176(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF176")
  ),
  AADM86_LINKF053(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF053")
  ),
  AADM86_LINKF090(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF090")
  ),
  AADM86_LINKF166(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00Z23", "LINKF166")
  ),
  EADS52OB_BKC326(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WH1", "BKC326")
  ),
  EADS142_S071A(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "S071A")
  ),
  EADS142_BC103S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "BC103S")
  ),
  EADS05_AGG205(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H86", "AGG205")
  ),
  EADS52EP_ALL252(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WG9", "ALL252")
  ),
  EADS142_BR34S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "BR34S")
  ),
  EADS11_CV28(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V26", "CV28")
  ),
  EAPR1P_PLATTR04(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WG4", "PLATTR04")
  ),
  EADM09_P02F(alloyReport ->
      alloyReport.getRawResponses().transunion().getAddOnProduct("00WBO")
          .map(AddOnProduct::getScoreModel)
          .map(scoreModel -> scoreModel.getLongCharacteristic("P02F"))
          .orElse(null)
  ),
  INSTALLMENT_ACCTS_30_DQ_L12M(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.installmentDqAccounts(2, 12)).orElse(null)
  ),
  REVOLVING_TRADES_60_UTIL(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(0.60)).orElse(null)
  ),
  VANTAGE40_SCORE(alloyReport ->
      alloyReport.getFormattedResponses().getTransunionCredit().getData()
          .getVantagescore40().getScore()
  ),
  EADS05_AGG801(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H86", "AGG801")
  ),
  EADS15_AGGS106(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00V53", "AGGS106")
  ),
  EADS52EP_STD255(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WG9", "STD255")
  ),
  EADS142_G242F(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "G242F")
  ),
  EADS142_RE28S(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WR3", "RE28S")
  ),
  EADS52OB_BKC322(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00WH1", "BKC322")
  ),
  EADS10_PAYMNT10(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H91", "PAYMNT10")
  ),
  EADS06_TRV07(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H87", "TRV07")
  ),
  EADS06_TRV08(alloyReport ->
      getDoubleCharacteristic(alloyReport, "00H87", "TRV08")
  ),
  MONTHS_OLDEST_TRADE(alloyReport ->
      alloyReport.getRawResponses().transunion().getCredit()
          .map(Credit::monthsOldestTrade).orElse(null)
  );


  private static Double getDoubleCharacteristic(AlloyReport alloyReport, String productName, String characteristicId) {
    return alloyReport.getRawResponses().transunion().getAddOnProduct(productName)
        .map(AddOnProduct::getScoreModel)
        .map(scoreModel -> scoreModel.getDoubleCharacteristic(characteristicId)).orElse(null);
  }

  private static Optional<Integer> getMonthsSinceLastBankruptcyRecord(AlloyReport alloyReport) {
    CreditVisionEnrichedAttributesV2 enrichedAttributesV2 =
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getCreditVisionEnrichedAttributesV2();
    if (enrichedAttributesV2 == null || enrichedAttributesV2.getS207S() == null) {
      return Optional.empty();
    }
    return Optional.of(enrichedAttributesV2.getS207S());
  }

  private final BiFunction<AlloyReport, PropertyInfo, Number> formula;
  /**
   * TRUE - feature is only based on Alloy report. FALSE - unknown (can't tell from {@link BiFunction} parameter).
   */
  private final boolean alloyReportOnly;

  AlloyExtractedFeatureNumber(BiFunction<AlloyReport, PropertyInfo, Number> formula) {
    alloyReportOnly = false;
    this.formula = formula;
  }

  AlloyExtractedFeatureNumber(Function<AlloyReport, Number> fromAlloyReport) {
    alloyReportOnly = true;
    this.formula = ((alloyReport, propertyInfo) -> fromAlloyReport.apply(alloyReport));
  }

  public Number apply(AlloyReport alloyReport) {
    if (!alloyReportOnly) {
      throw new UnsupportedOperationException("Feature can't be applied with alloy report alone");
    }
    return formula.apply(alloyReport, null);
  }
}
