package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import java.util.Set;

/**
 * Enum containing all available account types (according to the TUXML schema documentation)
 */
public enum AccountType {
  /**
   * Appliance/Furniture
   */
  AF,
  /**
   * Collection Agency/Attorney
   */
  AG,
  /**
   * Auto Lease
   */
  AL,
  /**
   * Automobile
   */
  AU,
  /**
   * Agricultural Loan
   */
  AX,
  /**
   * Business Credit Card
   */
  BC,
  /**
   * Revolving Business Lines
   */
  BL,
  /**
   * Business
   */
  BU,
  /**
   * Combined Credit Plan
   */
  CB,
  /**
   * Credit Card
   */
  CC,
  /**
   * Commercial Line of Credit
   */
  CE,
  /**
   * Charge Account
   */
  CH,
  /**
   * Commercial Installment Loan
   */
  CI,
  /**
   * Consolidation
   */
  CO,
  /**
   * Child Support
   */
  CP,
  /**
   * Cond. Sales Contract; Refinance
   */
  CR,
  /**
   * Telecommunications/C ellular
   */
  CU,
  /**
   * Conventional Real Estate Mortgage
   */
  CV,
  /**
   * Commercial Mortgage
   */
  CY,
  /**
   * Debit Card
   */
  DC,
  /**
   * Deposit Account with Overdraft Protection
   */
  DR,
  /**
   * Debt Counseling Service
   */
  DS,
  /**
   * Employment
   */
  EM,
  /**
   * Debt Buyer
   */
  FC,
  /**
   * Fraud Identify Check
   */
  FD,
  /**
   * Attorney Fees
   */
  FE,
  /**
   * FHA Home Improvement
   */
  FI,
  /**
   * FMHA Real Estate Mortgage
   */
  FL,
  /**
   * Family Support
   */
  FM,
  /**
   * FHA Real Estate Mortgage
   */
  FR,
  /**
   * Collection Credit Report Inquiry
   */
  FT,
  /**
   * Flexible Spending Credit Card
   */
  FX,
  /**
   * Government Employee Advance
   */
  GA,
  /**
   * Government Fee for Services
   */
  GE,
  /**
   * Government Fines
   */
  GF,
  /**
   * Government Grant
   */
  GG,
  /**
   * Government Overpayment
   */
  GO,
  /**
   * Government Secured
   */
  GS,
  /**
   * Govt. Unsecured Guar/Dir Ln
   */
  GU,
  /**
   * Government
   */
  GV,
  /**
   * Home Equity Loan
   */
  HE,
  /**
   * Household Goods
   */
  HG,
  /**
   * Home Improvement
   */
  HI,
  /**
   * ID Report for Employment
   */
  IE,
  /**
   * Installment Sales Contract
   */
  IS,
  /**
   * Line of Credit
   */
  LC,
  /**
   * Lease
   */
  LE,
  /**
   * Lender-placed Insurance
   */
  LI,
  /**
   * Construction Loan
   */
  LN,
  /**
   * Credit Line Secured
   */
  LS,
  /**
   * Manufactured Housing
   */
  MB,
  /**
   * Medical Debt
   */
  MD,
  /**
   * Medical/Health Care
   */
  MH,
  /**
   * Note Loan
   */
  NT,
  /**
   * Partly Secured
   */
  PS,
  /**
   * Rental Agreement
   */
  RA,
  /**
   * Returned Check
   */
  RC,
  /**
   * Recreational Merchandise
   */
  RD,
  /**
   * Real Estate
   */
  RE,
  /**
   * Real Estate — Junior Liens
   */
  RL,
  /**
   * Real Estate Mortgage
   */
  RM,
  /**
   * Summary of Accounts — Same Status
   */
  SA,
  /**
   * Secured Credit Card
   */
  SC,
  /**
   * Secured
   */
  SE,
  /**
   * Secondary Use of a Credit Report for Auto Financing
   */
  SF,
  /**
   * Secured by Household Goods
   */
  SH,
  /**
   * Secured Home Improvement
   */
  SI,
  /**
   * Second Mortgage
   */
  SM,
  /**
   * Secured by Household Goods & Collateral
   */
  SO,
  /**
   * Secondary Use of a Credit Report
   */
  SR,
  /**
   * Student Loan
   */
  ST,
  /**
   * Spouse Support
   */
  SU,
  /**
   * Secondary Use of a Credit Report for Other Financing
   */
  SX,
  /**
   * Time Shared Loan
   */
  TS,
  /**
   * Utility Company
   */
  UC,
  /**
   * Unknown
   */
  UK,
  /**
   * Unsecured
   */
  US,
  /**
   * V.A. Real Estate Mortgage
   */
  VM,
  /**
   * Individual Monitoring Report Inquiry
   */
  WT,
  /**
   * Unique missing code created by Flex team; It does, rarely, happen that the account type field is entirely missing
   * from the report.
   */
  MISSING;

  public static final Set<AccountType> INSTALLMENTS = Set.of(AF, AL, AU, AX, CO, CR, CV, FE, FI, FL, FR, HE, HG, HI,
      IS, LE, LI, LN, MB, MD, MH, NT, PS, RA, RD, RE, RL, RM, SE, SH, SI, SM, SO, ST, TS, US, VM);

  public static final Set<AccountType> REVOLVING_TYPES = Set.of(BC, BL, CB, CC, CH, FX, LC, LS, SC);

  public static final Set<AccountType> MEDICAL = Set.of(MD);

  public static final Set<AccountType> INVALID = Set.of(BU, CE, CI, CP, CY, DC, DR, EM, FD, FM, FT, GA, GE, GF, GG, GO,
      GS, GU, GV, IE, RC, SA, SF, SR, SU, SX, UK, WT, MISSING);
}
