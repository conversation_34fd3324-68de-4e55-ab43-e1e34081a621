package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.Getter;

@Data
public class Credit {

  /**
   * Two double values are "equal" if their difference is within this limit.
   */
  public static final double DOUBLE_PRECISION_DELTA = 0.000001;

  //
  // This section is for real properties (that actually exist in credit report)
  //

  @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  @JsonProperty("trade")
  private List<Trade> trades = List.of();

  @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  @JsonProperty("collection")
  private List<Trade> collections;

  //
  // This section is various cached view of trades/collections
  //

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> sanityCheckedTrades = trades.stream().filter(Trade::sanityCheck)
      .sorted(Trade.CHRONOLOGICAL_ORDER).toList();

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> validTrades = filter(getSanityCheckedTrades(), Trade::isValid);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> payPatternTrades = getValidTrades().stream().filter(t -> t.getPayPattern() != null)
      .toList();

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> revolvingTrades = filter(getValidTrades(), Trade::isRevolving);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> openRevolvingTrades = filter(getRevolvingTrades(), Trade::isOpen);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> installmentTrades = filter(getValidTrades(), Trade::isInstallment);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> defaultedTrades = filter(getValidTrades(), Trade::isDefaulted);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> openInstallmentTrades = filter(getInstallmentTrades(), Trade::isOpen);

  @Getter(lazy = true)
  @JsonIgnore
  private final List<Trade> openTrades = filter(getValidTrades(), Trade::isOpen);

  //
  // Credit features section
  //

  public Long dqAccounts(int dqLevel, int months) {
    return dqCalculation(dqLevel, months, getValidTrades(), t -> true);
  }

  public Long defaultedTrades() {
    return getValidTrades().stream().filter(Trade::isDefaulted).count();
  }

  public Integer monthsOldestOpenRevolvingTrade() {
    if (getOpenRevolvingTrades().isEmpty()) {
      return null;
    }
    return getOpenRevolvingTrades().get(0).ageInMonth();
  }

  public Integer monthsOldestTrade() {
    if (getValidTrades().isEmpty()) {
      return null;
    }
    return getValidTrades().get(0).ageInMonth();
  }

  public Long revolvingTradesAboveUtilizationThreshold(double threshold) {
    var utilizedRevolvingTrades = getOpenRevolvingTrades().stream().filter(t -> t.utilization() != null).toList();
    if (utilizedRevolvingTrades.isEmpty()) {
      return null;
    }
    return utilizedRevolvingTrades.stream().filter(t -> t.utilization() >= threshold - DOUBLE_PRECISION_DELTA).count();
  }

  public Long totalOutstandingBalanceFromAllOpenGoodRevolvingTrades() {
    return getOpenRevolvingTrades().stream().filter(Trade::isGoodStanding).map(Trade::getCurrentBalance)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Long totalOutstandingBalanceFromAllOpenRevolvingTrades() {
    return getOpenRevolvingTrades().stream().map(Trade::getCurrentBalance)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Double revolvingTradesUtilization() {
    return meanUtilization(getOpenRevolvingTrades().stream());
  }

  public Long revolvingDqAccounts(int dqLevel, int months) {
    return dqCalculation(dqLevel, months, getRevolvingTrades(), Trade::isRevolving);
  }

  public Long totalHighCreditOpenGoodRevolving() {
    return getOpenRevolvingTrades().stream().filter(Trade::isGoodStanding).map(Trade::getHighCredit)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Long installmentDqAccounts(int dqLevel, int months) {
    return dqCalculation(dqLevel, months, getInstallmentTrades(), Trade::isInstallment);
  }

  private Long dqCalculation(int dqLevel, int months, List<Trade> trades, Predicate<Trade> tradePredicate) {
    long defaultTrades = trades.stream().filter(Trade::isDefaulted)
        .filter(t -> t.monthsSinceClosed() < months).count();
    var payPatterns = getPayPatternTrades().stream().filter(Predicate.not(Trade::isDefaulted).and(tradePredicate))
        .map(t -> t.preprocessPayPatternForDqCalculation(months)).toList();

    if (defaultTrades == 0 && payPatterns.isEmpty()) {
      return null;
    }

    return payPatterns.stream().filter(Objects::nonNull).filter(pp -> pp.numDqs(dqLevel, months) > 0).count()
        + defaultTrades;
  }

  public Integer monthsSinceDefault() {
    if (getDefaultedTrades().isEmpty()) {
      return null;
    }

    return getDefaultedTrades().stream().map(Trade::monthsSinceClosed).min(Integer::compareTo).get();
  }

  public Integer numCollections() {
    if (collections == null) {
      return 0;
    }

    return collections.size();
  }

  public Long totalOriginalBalanceOpenGoodInstallment() {
    return getOpenInstallmentTrades().stream().filter(Trade::isGoodStanding).map(Trade::getHighCredit)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Long totalOriginalBalanceOpenInstallment() {
    return getOpenInstallmentTrades().stream().map(Trade::getHighCredit).filter(Objects::nonNull).reduce(Long::sum)
        .orElse(null);
  }

  public Long totalMonthlyPaymentOpenInstallment() {
    return getOpenInstallmentTrades().stream().map(Trade::getTerms).filter(Objects::nonNull)
        .map(Terms::getScheduledMonthlyPayment).filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Long numOpenRetailTrades() {
    return getOpenTrades().stream().filter(Trade::isRetail).count();
  }

  public Long numAccountsRevolvingGood() {
    return getOpenRevolvingTrades().stream().filter(Trade::isGoodStanding).count();
  }

  public Long collectionsBalanceNoMedical() {
    if (collections == null) {
      return null;
    }

    return collections.stream().filter(Predicate.not(Trade::isMedical)).map(Trade::getCurrentBalance)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  /**
   * Helper function to build various filtered view.
   *
   * @param originalList
   * @param predicate
   * @return
   */
  private static <T> List<T> filter(List<T> originalList, Predicate<T> predicate) {
    return originalList.stream().filter(predicate).toList();
  }

  public Long totalOutstandingBalanceOpenGoodInstallment() {
    return getOpenInstallmentTrades().stream().filter(Trade::isGoodStanding).map(Trade::getCurrentBalance)
        .filter(Objects::nonNull).reduce(Long::sum).orElse(null);
  }

  public Double utilizationAuthorizedUser() {
    return meanUtilization(getOpenRevolvingTrades().stream().filter(Trade::isAuthorizedUser));
  }

  /**
   * Total balance divided by total credit limit, after filtering out trades with invalid balance/creditLimit.
   *
   * @param trades
   * @return NULL - if there is no trade with valid balance/creditLimit
   */
  static Double meanUtilization(Stream<Trade> trades) {
    return trades.filter(t -> t.getCurrentBalance() != null)
        .filter(t -> t.getCreditLimit() != null && t.getCreditLimit() > 0)
        .collect(Collectors.teeing(Collectors.counting(),
            Collectors.teeing(Collectors.summingLong(Trade::getCurrentBalance),
                Collectors.summingLong(Trade::getCreditLimit), (balance, limit) -> balance * 1.0 / limit),
            (count, util) -> count > 0 ? util : null));
  }
}
