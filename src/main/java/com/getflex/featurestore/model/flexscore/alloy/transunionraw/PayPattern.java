package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import java.time.Period;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.Getter;

@Getter
public class PayPattern {

  /**
   * Representation of payment pattern of a trade and API for extracting information about payment patterns.
   * <p>
   * TransUnion represents a trade's payment history as a string of integer chars, such as '1112332', called a payment
   * pattern. Each char represents the currency of the trade in a given month, with '1' indicating that a trade was
   * current and every other char n indicating that the trade was (n-1) payments delinquent. Eg, '2' indicates that the
   * trade was behind schedule by 1 payment.
   */
  public static final Set<Character> VALID_PATTERN_CHARS = Set.of('1', '2', '3', '4', '5', 'X');

  private final String pattern;

  public PayPattern(String pattern) {
    for (int i = 0; i < pattern.length(); i++) {
      if (!VALID_PATTERN_CHARS.contains(pattern.charAt(i))) {
        throw new IllegalArgumentException("Pay pattern must only include these characters: " + VALID_PATTERN_CHARS);
      }
    }

    this.pattern = pattern;
  }

  /**
   * Get a new pattern object with only last specified period of data.
   *
   * @param retained Only year & month is supported and must be positive.
   * @return New {@link PayPattern} carrying only trailing subset of pattern.
   */
  public PayPattern trimOldRecord(Period retained) {
    if (retained.getDays() != 0 || retained.isNegative() || retained.isZero()
        || retained.toTotalMonths() > Integer.MAX_VALUE) {
      throw new IllegalArgumentException("Unsupported parameter");
    }
    return new PayPattern(safeSubString((int) retained.toTotalMonths(), pattern));
  }

  /**
   * Distinct periods of delinquencies of at least <code>dqLevel</code>.
   * <p>
   * Counts how many times in the past `months` periods the trade has entered a state of delinquency of at least
   * <code>dqLevel</code>. By "distinct periods" we mean distinct substrings in {@link this#getPattern()}. Note that
   * we count periods over which the trade was AT LEAST <code>dqLevel</code> delinquent.
   *
   * @param dqLevel
   * @param months  Optional, could be null
   * @return
   */
  public long numDqs(int dqLevel, Integer months) {
    if (dqLevel < 2 || dqLevel > 5) {
      throw new IllegalArgumentException("dqLevel must be in [2..5]");
    }

    String pattern = this.pattern;
    if (months != null) {
      pattern = safeSubString(months, pattern);
    }
    final char[] paymentPattern = pattern.toCharArray();

    // Characters of interest based on dqLevel
    Set<Character> delinquencyChars = VALID_PATTERN_CHARS.stream().filter(c -> c >= '0' + dqLevel)
        .collect(Collectors.toSet());

    // Indices into paymentPattern that should be checked
    List<Integer> delinquencyIndices = IntStream.range(0, paymentPattern.length)
        .filter(i -> delinquencyChars.contains(paymentPattern[i])).boxed().toList();

    LinkedList<LinkedList<Integer>> groupedIndices = new LinkedList<>();
    Integer previousDqKey = null;
    for (int i = 0; i < delinquencyIndices.size(); i++) {
      final int index = delinquencyIndices.get(i);

      final Integer currentDqKey = i - index; // Key of the whole algorithm, not sure exactly how it works though

      if (!Objects.equals(currentDqKey, previousDqKey)) {
        groupedIndices.add(new LinkedList<>());
        previousDqKey = currentDqKey;
      }
      groupedIndices.getLast().add(index);
    }

    return groupedIndices.stream()
        .filter(subStringIndices -> subStringIndices.stream().map(i -> paymentPattern[i]).anyMatch(c -> c != 'X'))
        .count();
  }

  private static String safeSubString(int months, String pattern) {
    return pattern.substring(0, Math.min(months, pattern.length()));
  }
}
