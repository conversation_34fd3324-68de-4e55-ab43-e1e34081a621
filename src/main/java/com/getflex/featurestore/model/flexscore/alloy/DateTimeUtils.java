package com.getflex.featurestore.model.flexscore.alloy;

import com.getflex.featurestore.model.flexscore.alloy.transunionraw.Tracking;
import java.time.LocalDate;
import java.time.OffsetDateTime;

public class DateTimeUtils {

  /**
   * Calculate natural month difference, disregarding order (date1 could be earlier or later than date2).
   *
   * @param date1
   * @param date2
   * @return Zero or positive integer.
   */
  public static int monthDiff(LocalDate date1, LocalDate date2) {
    return Math.abs(12 * (date1.getYear() - date2.getYear()) + (date1.getMonthValue() - date2.getMonthValue()));
  }

  /**
   * Convert credit report date time to local date for day/month gap calculation.
   * <br>
   * Note that this uses whatever timezone AlloyReport uses. For example {@link Tracking#getTransactionTimeStamp()}
   * seems to be US central time, and we want to calculate date/month difference based on that timezone.
   *
   * @param offsetDateTime
   * @return
   */
  public static LocalDate toLocalDate(OffsetDateTime offsetDateTime) {
    return offsetDateTime.toLocalDate();
  }
}
