package com.getflex.featurestore.model.flexscore.feature;

import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * Parameter wrapper for FlexScore feature formula functions
 */
@Getter
@AllArgsConstructor
@Builder
public class FlexScoreModelFeatureFormulaParams {
  private final AlloyReport alloyReport;
  private final PropertyInfo propertyInfo;
  private final EvalParams evalParams;
  private final FeatureFactory featureFactory;
}
