package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class SubjectRecord {
  private FileSummary fileSummary;
  private Custom custom;
  @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  @JsonProperty("addOnProduct")
  private List<AddOnProduct> addOnProducts;

  public AddOnProduct getAddOnProducts(String productName) {
    List<AddOnProduct> matchingProducts = getAddOnProducts().stream()
        .filter(p -> Objects.equals(productName, p.getCode())).toList();
    if (matchingProducts.size() > 1) {
      log.warn("Found {} matching addOnProduct", matchingProducts.size());
      return null;
    }
    return matchingProducts.stream().findFirst().orElse(null);
  }
}
