package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.featurestore.model.flexscore.alloy.DateTimeUtils;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.Comparator;
import java.util.Objects;
import lombok.Data;

@Data
public class Trade {

  /**
   * Order based on open time. Null value goes first (following credit-feature Python behavior).
   */
  public static final Comparator<? super Trade> CHRONOLOGICAL_ORDER = Comparator.comparing(Trade::getDateOpened,
      Comparator.nullsFirst(Comparator.naturalOrder()));

  private String closedIndicator;
  private PaymentHistory paymentHistory;
  private Date dateOpened;
  private Date dateClosed;
  private Account account;
  @JsonProperty("ECOADesignator")
  private String ecoaDesignator;

  private Long creditLimit;
  private Long currentBalance;
  private Long highCredit;

  private AccountRating accountRating;
  private Subscriber subscriber;
  private Remark remark;

  private Terms terms;

  /**
   * This is injected by {@link PropagateReportDateTime}, not deserialized from Alloy report.
   */
  @JsonIgnore
  private OffsetDateTime creditReportDateTime;

  public boolean sanityCheck() {
    if (account == null) {
      return false;
    }

    if (dateOpened == null) {
      return false;
    }

    return getPaymentHistory() == null || getPaymentHistory().getPaymentPattern() == null
        || getPaymentHistory().getPaymentPattern().getText() == null
        || getPaymentHistory().getPaymentPattern().getPattern() != null;
  }

  //
  // Categorizations
  //

  public boolean isOpen() {
    return getClosedIndicator() == null;
  }

  public boolean isRevolving() {
    if (getAccountType() == null) {
      return false;
    }
    return AccountType.REVOLVING_TYPES.contains(getAccountType());
  }

  public boolean isGoodStanding() {
    return Objects.equals(AccountRating.PAID_OR_PAYING, getAccountRating());
  }

  public boolean isInstallment() {
    if (getAccountType() == null) {
      return false;
    }
    return AccountType.INSTALLMENTS.contains(getAccountType());
  }

  public boolean isRetail() {
    return isRevolving() && IndustryCode.ALL_RETAIL.contains(getSubscriber().getIndustryCode());
  }

  public boolean isDefaulted() {
    if (AccountRating.DEFAULTED.contains(getAccountRating())) {
      if (getAccountRating() == AccountRating.UNRATED) {
        return getRemarkCode() != null && RemarkCode.BANKRUPTCY.contains(getRemarkCode());
      } else {
        return true;
      }
    }
    return false;
  }

  public boolean isValid() {
    if (getAccountType() == null) {
      return true;
    }
    return !AccountType.INVALID.contains(getAccountType());
  }

  public boolean isMedical() {
    if (getAccountType() == null) {
      return false;
    }
    return AccountType.MEDICAL.contains(getAccountType());
  }

  //
  // Time related attributes
  //

  public int ageInMonth() {
    return DateTimeUtils.monthDiff(dateOpened.getDate(), DateTimeUtils.toLocalDate(getCreditReportDateTime()));
  }

  public int monthsSinceClosed() {
    if (dateClosed == null) {
      return 0;
    }

    return DateTimeUtils.monthDiff(dateClosed.getDate(), DateTimeUtils.toLocalDate(getCreditReportDateTime()));
  }

  //
  // Credit utilization
  //

  public Double utilization() {
    if (creditLimit == null || creditLimit <= 0L || currentBalance == null) {
      return null;
    }
    return currentBalance * 1.0 / creditLimit;
  }

  //
  // Payment history
  //

  public PayPattern getPayPattern() {
    if (getPaymentHistory() != null && getPaymentHistory().getPaymentPattern() != null) {
      return getPaymentHistory().getPaymentPattern().getPattern();
    }
    return null;
  }

  /**
   * {@link PayPattern} of open trades can properly handle DQ calculation. But closed trades needs some preprocessing.
   *
   * @param months
   * @return
   */
  public PayPattern preprocessPayPatternForDqCalculation(int months) {
    if (isOpen()) {
      return getPaymentHistory().getPaymentPattern().getPattern();
    }
    int effectiveMonths = months - monthsSinceClosed();
    if (effectiveMonths <= 0) {
      return null;
    }
    return getPaymentHistory().getPaymentPattern().getPattern().trimOldRecord(Period.ofMonths(effectiveMonths));
  }

  //
  // Helper functions
  //
  AccountType getAccountType() {
    if (getAccount() == null) {
      return null;
    }
    if (getAccount().getType() == null) {
      return AccountType.MISSING;
    }
    return getAccount().getType();
  }

  RemarkCode getRemarkCode() {
    if (getRemark() == null) {
      return null;
    }
    if (getRemark().getCode() == null) {
      return RemarkCode.UNKNOWN;
    }
    return getRemark().getCode();
  }

  public boolean isAuthorizedUser() {
    return "authorizedUser".equals(getEcoaDesignator());
  }
}
