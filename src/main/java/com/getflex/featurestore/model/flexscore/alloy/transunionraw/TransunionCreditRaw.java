package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.getflex.featurestore.model.flexscore.alloy.DateTimeUtils;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.Optional;
import lombok.Data;

@Data
public class TransunionCreditRaw {

  private CreditBureau creditBureau;

  /**
   * {@link Period} from when the subject was recorded for first time to when the credit report was generated.
   *
   * @return Null if <code>inFileSinceDate</code> doesn't exist.
   */
  public Integer creditHistoryLength() {
    Date inFileSinceDate = getCreditBureau().getProduct().getSubject().getSubjectRecord().getFileSummary()
        .getInFileSinceDate();
    if (inFileSinceDate == null) {
      return null;
    }
    return DateTimeUtils.monthDiff(inFileSinceDate.getDate(), DateTimeUtils.toLocalDate(reportDateTime()));
  }

  /**
   * When the credit report is generated.
   *
   * @return
   */
  public OffsetDateTime reportDateTime() {
    return getCreditBureau().getTransactionControl().getTracking().getTransactionTimeStamp();
  }

  /**
   * A shortcut to <code>product.subject.subjectRecord.custom.credit</code>.
   *
   * @return {@link Credit} object if it exists
   */
  public Optional<Credit> getCredit() {
    // If any part is null, return empty
    return Optional.ofNullable(getCreditBureau())
        .map(CreditBureau::getProduct)
        .map(Product::getSubject)
        .map(Subject::getSubjectRecord)
        .map(SubjectRecord::getCustom)
        .map(Custom::getCredit);
  }

  /**
   * A shortcut to get a specific {@link AddOnProduct}.
   *
   * @param productName Matches {@link AddOnProduct#getCode()}
   * @return {@link AddOnProduct} if specified productName/code exists
   */
  public Optional<AddOnProduct> getAddOnProduct(String productName) {
    return Optional.ofNullable(
        getCreditBureau().getProduct().getSubject().getSubjectRecord().getAddOnProducts(productName));
  }

}
