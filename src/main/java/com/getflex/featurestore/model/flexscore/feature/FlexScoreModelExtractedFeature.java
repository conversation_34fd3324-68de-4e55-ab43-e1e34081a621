package com.getflex.featurestore.model.flexscore.feature;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.CustomerOfferHistoryFeature;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.alloy.CreditVisionEnrichedAttributesV2;
import com.getflex.featurestore.model.flexscore.alloy.transunionraw.AddOnProduct;
import com.getflex.featurestore.model.flexscore.alloy.transunionraw.Credit;
import java.util.Objects;
import java.util.Optional;
import lombok.Getter;


/**
 * This defines all features to be extracted from credit file & property info to run various versions of flexscore
 * model.
 * <br>
 * Formula function MUST stay true to the input data, no default value should be specified by the formula, null should
 * be returned instead. That is because different score model may use different default value and having default value
 * at this layer makes it impossible to monitor data availability.
 * <br>
 * Each feature accepts FlexScoreModelFormulaParams
 */
@Getter
public enum FlexScoreModelExtractedFeature {
  EADM09_P02E(param ->
      param.getAlloyReport().getRawResponses().transunion().getAddOnProduct("00WBO")
          .map(AddOnProduct::getScoreModel)
          .map(scoreModel -> scoreModel.getLongCharacteristic("P02E"))
          .orElse(null)
  ),
  EADS05_AGG602(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H86", "AGG602")
  ),
  EADS11_CV23(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V26", "CV23")
  ),
  VANTAGE30_SCORE(param ->
      param.getAlloyReport().getFormattedResponses().getTransunionCredit().getData()
          .getVantagescore30().getScore()
  ),
  EADS52OB_REV322(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WH1", "REV322")
  ),
  REVOLVING_TRADES_100_UTIL(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(1.0)).orElse(null)
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceFromAllOpenGoodRevolvingTrades()).orElse(null)
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceFromAllOpenRevolvingTrades()).orElse(null)
  ),
  REVOLVING_ACCTS_60_DQ_L24M(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingDqAccounts(3, 24)).orElse(null)
  ),
  REVOLVING_TRADES_UTIL(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesUtilization()).orElse(null)
  ),
  TOTAL_HIGH_CREDIT_OPEN_GOOD_REVOLVING(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalHighCreditOpenGoodRevolving()).orElse(null)
  ),
  AADM86_LINKF193(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF193")
  ),
  EADS05_AGG218(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H86", "AGG218")
  ),
  INSTALLMENT_ACCTS_30_DQ_L24M(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.installmentDqAccounts(2, 24)).orElse(null)
  ),
  MONTHS_SINCE_DEFAULT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.monthsSinceDefault()).orElse(null)
  ),
  NUM_COLLECTIONS(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.numCollections()).orElse(null)
  ),
  TOTAL_ORIGINAL_BALANCE_OPEN_GOOD_INSTALLMENT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalOriginalBalanceOpenGoodInstallment()).orElse(null)
  ),
  TOTAL_ORIGINAL_BALANCE_OPEN_INSTALLMENT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalOriginalBalanceOpenInstallment()).orElse(null)
  ),
  MONTHS_ON_FILE(param ->
      param.getAlloyReport().getRawResponses().transunion().creditHistoryLength()
  ),
  AADM86_LINKA027(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA027")
  ),
  EADS07_BALMAG01(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H88", "BALMAG01")
  ),
  EADS142_AT104S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "AT104S")
  ),
  EADS142_G208B(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "G208B")
  ),
  NUM_OPEN_RETAIL_TRADES(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.numOpenRetailTrades()).orElse(null)
  ),
  ACCTS_30_DQ_L24M(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.dqAccounts(2, 24)).orElse(null)
  ),
  NUM_ACCTS_REVOLVING_GOOD(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.numAccountsRevolvingGood()).orElse(null)
  ),
  COLLECTIONS_BALANCE_NO_MEDICAL(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(Credit::collectionsBalanceNoMedical).orElse(null)
  ),
  REVOLVING_TRADES_85_UTIL(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(0.85)).orElse(null)
  ),
  AADM86_LINKA029(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA029")
  ),
  MONTHS_OLDEST_OPEN_REVOLVING_TRADE(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(Credit::monthsOldestOpenRevolvingTrade).orElse(null)
  ),
  ACCTS_DEFAULT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(Credit::defaultedTrades).orElse(null)
  ),
  IS_FROM_YARDI(param ->
      param.getPropertyInfo().integrationType() == BillingIntegrationTypeEnum.YARDI ? 1.0 : 0.0),

  IN_NETWORK_IND(param ->
      Objects.equals(param.getPropertyInfo().isOutOfNetwork(), Boolean.FALSE) ? 1.0 : 0.0),
  MAX_CREDIT_LIMIT_OPEN_CREDIT_TRADES(param ->
      param.getAlloyReport().getFormattedResponses().getTransunionCredit().getData()
          .getMaxCreditLimitOpenCreditTrades()),
  EADS05_AGG504(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H86", "AGG504")
  ),
  EADS142_AT28B(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "AT28B")
  ),
  EADS52OB_BKC320(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WH1", "BKC320")
  ),
  EADS52EP_REV255(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WG9", "REV255")
  ),
  EADS142_AT24S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "AT24S")
  ),
  EADS142_G411S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "G411S")
  ),
  EADS10_PAYMNT07(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H91", "PAYMNT07")
  ),
  EADS142_G224C(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "G224C")
  ),

  // flexscore 6 additions

  INTEGRATION_IND(param -> param.getPropertyInfo().getFlexScoreSixIntegrationIndicator()),
  NETWORK_IND(param -> param.getPropertyInfo().getFlexScoreSixNetworkIndicator()),
  AADM86_LINKA006(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA006")
  ),
  AADM86_LINKF115(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF115")
  ),
  AADM86_LINKF181(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF181")
  ),
  AADM86_LINKF069(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF069")
  ),
  EADS142_S061S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "S061S")
  ),
  EADS15_INDEXQ1(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V53", "INDEXQ1")
  ),
  EADS15_INDEXQ2(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V53", "INDEXQ2")
  ),
  EADS15_INDEXQ3(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V53", "INDEXQ3")
  ),
  EADS15_INDEXQ4(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V53", "INDEXQ4")
  ),
  TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_INSTALLMENT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalOutstandingBalanceOpenGoodInstallment()).orElse(null)
  ),
  UTILIZATION_AUTHORIZED_USER(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.utilizationAuthorizedUser()).orElse(null)
  ),
  EADS142_FI34S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "FI34S")
  ),
  AADM86_LINKF038(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF038")
  ),
  AADM86_LINKA022(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA022")
  ),
  EADS52PR_AUT225(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WG8", "AUT225")
  ),
  AADM86_LINKF167(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF167")
  ),
  AADM86_LINKA021(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA021")
  ),
  EADS142_BR02S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "BR02S")
  ),
  AADM86_LINKA002(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z17", "LINKA002")
  ),
  EADS142_ST24S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "ST24S")
  ),
  TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.totalMonthlyPaymentOpenInstallment()).orElse(null)
  ),
  MONTHS_SINCE_LAST_BANKRUPTCY_RECORD(param ->
      getMonthsSinceLastBankruptcyRecord(param.getAlloyReport()).orElse(-1)),

  // flexscore 7 additions
  IS_FA_OR_P2P(param ->
      (param.getPropertyInfo().integrationType() == BillingIntegrationTypeEnum.FLEX_ANYWHERE
       || param.getPropertyInfo().integrationType() == BillingIntegrationTypeEnum.P2P) ? 1.0 : 0.0),
  IS_RETURNING_CUSTOMER(param ->
      getIsReturningCustomer(param.getEvalParams(), param.getFeatureFactory())),

  AADM86_LINKF185(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF185")
  ),
  AADM86_LINKF068(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF068")
  ),
  AADM86_LINKF107(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF107")
  ),
  AADM86_LINKF145(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF145")
  ),
  AADM86_LINKF176(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF176")
  ),
  AADM86_LINKF053(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF053")
  ),
  AADM86_LINKF090(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF090")
  ),
  AADM86_LINKF166(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00Z23", "LINKF166")
  ),
  EADS52OB_BKC326(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WH1", "BKC326")
  ),
  EADS142_S071A(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "S071A")
  ),
  EADS142_BC103S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "BC103S")
  ),
  EADS05_AGG205(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H86", "AGG205")
  ),
  EADS52EP_ALL252(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WG9", "ALL252")
  ),
  EADS142_BR34S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "BR34S")
  ),
  EADS11_CV28(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V26", "CV28")
  ),
  EAPR1P_PLATTR04(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WG4", "PLATTR04")
  ),
  EADM09_P02F(param ->
      param.getAlloyReport().getRawResponses().transunion().getAddOnProduct("00WBO")
          .map(AddOnProduct::getScoreModel)
          .map(scoreModel -> scoreModel.getLongCharacteristic("P02F"))
          .orElse(null)
  ),
  INSTALLMENT_ACCTS_30_DQ_L12M(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.installmentDqAccounts(2, 12)).orElse(null)
  ),
  REVOLVING_TRADES_60_UTIL(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(c -> c.revolvingTradesAboveUtilizationThreshold(0.60)).orElse(null)
  ),
  VANTAGE40_SCORE(param ->
      param.getAlloyReport().getFormattedResponses().getTransunionCredit().getData()
          .getVantagescore40().getScore()
  ),
  EADS05_AGG801(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H86", "AGG801")
  ),
  EADS15_AGGS106(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00V53", "AGGS106")
  ),
  EADS52EP_STD255(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WG9", "STD255")
  ),
  EADS142_G242F(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "G242F")
  ),
  EADS142_RE28S(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WR3", "RE28S")
  ),
  EADS52OB_BKC322(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00WH1", "BKC322")
  ),
  EADS10_PAYMNT10(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H91", "PAYMNT10")
  ),
  EADS06_TRV07(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H87", "TRV07")
  ),
  EADS06_TRV08(param ->
      getDoubleCharacteristic(param.getAlloyReport(), "00H87", "TRV08")
  ),
  MONTHS_OLDEST_TRADE(param ->
      param.getAlloyReport().getRawResponses().transunion().getCredit()
          .map(Credit::monthsOldestTrade).orElse(null)
  );

  private static Double getDoubleCharacteristic(AlloyReport alloyReport, String productName, String characteristicId) {
    return alloyReport.getRawResponses().transunion().getAddOnProduct(productName)
        .map(AddOnProduct::getScoreModel)
        .map(scoreModel -> scoreModel.getDoubleCharacteristic(characteristicId)).orElse(null);
  }

  private static Optional<Integer> getMonthsSinceLastBankruptcyRecord(AlloyReport alloyReport) {
    CreditVisionEnrichedAttributesV2 enrichedAttributesV2 =
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getCreditVisionEnrichedAttributesV2();
    if (enrichedAttributesV2 == null || enrichedAttributesV2.getS207S() == null) {
      return Optional.empty();
    }
    return Optional.of(enrichedAttributesV2.getS207S());
  }

  private static Integer getIsReturningCustomer(EvalParams evalParams, FeatureFactory featureFactory) {
    try {
      Object customerOfferHistory = fetchValueFromFeature(featureFactory, evalParams,
          CustomerOfferHistoryFeature.class.getSimpleName());

      JsonNode node = OBJECT_MAPPER.valueToTree(customerOfferHistory);
      long daysSinceAccepted = node.get("days_since_last_accepted_offer").asLong();
      long daysSinceCanceled = node.get("days_since_last_canceled_offer").asLong();
      boolean isReturningCustomer =
          (daysSinceAccepted >= 0) && (daysSinceCanceled >= 0 && daysSinceCanceled <= 720);
      return isReturningCustomer ? 1 : 0;
    } catch (Exception e) {
      throw new RuntimeException("Error getting is returning customer", e);
    }
  }

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
  private final FlexScoreModelFeatureFormula formula;

  FlexScoreModelExtractedFeature(FlexScoreModelFeatureFormula formula) {
    this.formula = formula;
  }

  public Number apply(FlexScoreModelFeatureFormulaParams formulaParams) {
    return formula.apply(formulaParams);
  }

  static Object fetchValueFromFeature(FeatureFactory featureFactory, EvalParams evalParams,
      String featureName) {
    BaseFeature feature = featureFactory.getFeature(featureName);
    FeatureValue featurevalue = feature.fetchFeatureValue(evalParams);
    return featurevalue.getValue();
  }
}
