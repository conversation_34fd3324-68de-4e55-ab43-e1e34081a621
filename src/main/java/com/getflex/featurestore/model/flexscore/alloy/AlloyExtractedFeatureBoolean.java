package com.getflex.featurestore.model.flexscore.alloy;

import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import lombok.Getter;

/**
 * This defines all features to be extracted from credit file & property info to run various versions of flexscore
 * model.
 * <br>
 * Formula function MUST stay true to the input data, no default value should be specified by the formula, null should
 * be returned instead. That is because different score model may use different default value and having default value
 * at this layer makes it impossible to monitor data availability.
 * <br>
 * If feature is solely based on alloy report, the enum value must use the constructor with {@link Function}
 * parameter.
 */
@Getter
public enum AlloyExtractedFeatureBoolean {
  IS_CREDIT_REPORT_FROZEN(alloyReport -> Boolean.TRUE.equals(
      alloyReport.getFormattedResponses().getTransunionCredit().getData().getFileFrozen())),
  IS_CREDIT_REPORT_SUPPRESSED(alloyReport -> Boolean.TRUE.equals(
      alloyReport.getFormattedResponses().getTransunionCredit().getData().getCreditDataSuppressed())),
  IS_FRAUD_ALERT_REPORT(alloyReport ->
      isCreditReportHit(alloyReport) && getFraudStatementType(alloyReport).isPresent()),
  IS_INITIAL_FRAUD_ALERT_REPORT(alloyReport ->
      isCreditReportHit(alloyReport) && getFraudStatementType(alloyReport).isPresent()
          && getFraudStatementType(alloyReport).get().equalsIgnoreCase(Constant.INITIAL_FRAUD_ALERT_LOWER)),
  IS_CREDIT_REPORT_SUBJECT_DECEASED(alloyReport -> {
    Vantagescore30 vantagescore30 = alloyReport.getFormattedResponses().getTransunionCredit().getData()
        .getVantagescore30();
    return Boolean.TRUE.equals(vantagescore30 != null && vantagescore30.getScore() == Constant.VANTAGE_SCORE_DECEASED);
  }),
  IS_THIN_CREDIT_FILE(alloyReport -> {
    Vantagescore30 vantagescore30 = alloyReport.getFormattedResponses().getTransunionCredit().getData()
        .getVantagescore30();
    return (vantagescore30 != null
        && Boolean.TRUE.equals(vantagescore30.getExists())
        && vantagescore30.getScore() != null
        && vantagescore30.getScore() == Constant.VANTAGE_SCORE_THIN_FILE);
  }),
  IS_MISSING_CREDIT_FILE(alloyReport -> {
    Vantagescore30 vantagescore30 = alloyReport.getFormattedResponses().getTransunionCredit().getData()
        .getVantagescore30();
    return (vantagescore30 == null
        || Boolean.FALSE.equals(vantagescore30.getExists())
        || vantagescore30.getScore() == null);
  });

  private static Optional<String> getFraudStatementType(AlloyReport alloyReport) {
    return Optional.ofNullable(
            alloyReport.getFormattedResponses().getTransunionCredit().getData().getFraudStatementType())
        .filter(fraudStatementType -> !fraudStatementType.isBlank());
  }

  private static Boolean isCreditReportHit(AlloyReport alloyReport) {
    return Boolean.TRUE.equals(alloyReport.getFormattedResponses().getTransunionCredit().getData().getFileHitFlag());
  }

  private final BiFunction<AlloyReport, PropertyInfo, Boolean> formula;
  /**
   * TRUE - feature is only based on Alloy report. FALSE - unknown (can't tell from {@link BiFunction} parameter).
   */
  private final boolean alloyReportOnly;

  AlloyExtractedFeatureBoolean(BiFunction<AlloyReport, PropertyInfo, Boolean> formula) {
    alloyReportOnly = false;
    this.formula = formula;
  }

  AlloyExtractedFeatureBoolean(Function<AlloyReport, Boolean> fromAlloyReport) {
    alloyReportOnly = true;
    this.formula = ((alloyReport, propertyInfo) -> fromAlloyReport.apply(alloyReport));
  }
}
