package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;

public enum IndustryCode {
  AUTOMOTIVE("A"),
  AUTO_AUCTION("AA"),
  AUTO_LEASING("AC"),
  FARM_IMPLEMENT_DEALERS("AF"),
  TRUCK_DEALERS("AL"),
  AUTO_PARTS("AP"),
  AUTO_REPAIRS("AR"),
  SERVICE_STATIONS("AS"),
  TIRE_DEALERS("AT"),
  NEW_AUTO_DEALERS("AN"),
  USED_AUTO_DEALERS("AU"),
  AUTO_MISCELLANEOUS("AZ"),

  <PERSON><PERSON><PERSON>("B"),
  AUTO_LOANS("BA"),
  COMMERCIAL_BANKS("BB"),
  CREDIT_CARDS("BC"),
  HOME_EQUITY_LOANS("BH"),
  INSTALLMENT_LOANS("BI"),
  BANKS_LINE_OF_CREDIT("BL"),
  MORTGAGE_LOANS("BM"),
  FULL_SERVICE_BANKS("BO"),
  STUDENT_LOANS("BS"),
  BANK_COLLECTION("BY"),
  BANK_MISCELLANEOUS("BZ"),

  CLOTHING("C"),
  MENS_APPAREL("CB"),
  UNISEX_APPAREL("CG"),
  OFF_PRICE_CLOTHING("CO"),
  SPECIALTY_CLOTHING("CS"),
  TEXTILE_MILLS("CT"),
  UPSCALE_CLOTHING("CU"),
  WOMENS_APPAREL("CW"),
  CLOTHING_STORE_COLLECTION("CY"),
  CLOTHING_MISCELLANEOUS("CZ"),

  DEPARTMENT_OR_VARIETY("D"),
  COMPLETE_DEPARTMENT_STORE("DC"),
  NATIONAL_CHAIN("DN"),
  OFF_PRICE_STORE("DO"),
  MAIL_ORDER_FIRMS("DP"),
  USED_MERCHANDISE("DU"),
  VARIETY_STORE("DV"),
  DEPARTMENT_STORE_COLLECTION("DY"),
  DEPARTMENT_STORE_MISCELLANEOUS("DZ"),

  EDUCATION_OR_EMPLOYMENT("E"),
  BUSINESS_EDUCATION("EB"),
  STUDENT_LOAN_SERVICING("EL"),
  EMPLOYMENT_SERVICES("ES"),
  TECHNICAL_EDUCATION("ET"),
  UNIVERSITIES("EU"),
  VOCATIONAL("EV"),
  EDUCATION_COLLECTION("EY"),
  EDUCATION_MISCELLANEOUS("EZ"),

  PERSONAL_FINANCE("F"),
  AUTO_FINANCING("FA"),
  CREDIT_CARDS_ISSUED_BY_FINANCE_COMPANIES("FC"),
  SALES_FINANCING("FF"),
  HOME_LOAN_FINANCING("FH"),
  INVESTMENT_FIRMS("FI"),
  MORTGAGE_LOAN_FINANCING("FM"),
  PERSONAL_LOAN_COMPANIES("FP"),
  SAVING_AND_LOANS_COMPANIES("FS"),
  FINANCE_COLLECTION("FY"),
  FINANCE_MISCELLANEOUS("FZ"),

  GROCERIES("G"),
  BAKERIES("GB"),
  DAIRIES("GD"),
  LIQUOR_STORES("GL"),
  MEAT_MARKETS("GM"),
  NEIGHBORHOOD_GROCERIES("GN"),
  SUPERMARKETS("GS"),
  GROCERIES_MIOSCELLANEOUS("GZ"),

  HOME_OFFICE_FURNISHINGS("H"),
  APPLIANCE_SALES_AND_SERVICE("HA"),
  CARPET_AND_FLOOR_COVERINGS("HC"),
  INTERIOR_DESIGN("HD"),
  HOME_ELECTRONICS("HE"),
  FURNITURE_STORES("HF"),
  MUSIC_STORES("HM"),
  FURNITURE_RENTALS("HR"),
  TV_AND_RADIO_SALES("HT"),
  HOME_OFFICE_COLLECTION("HY"),
  HOME_OFFICE_MISCELLANEOUS("HZ"),

  INSURANCE("I"),
  GENERAL_INSURANCE("IG"),
  HEALTH_AND_ACCIDENT_INSURANCE("IH"),
  LIFE_INSURANCE("IL"),
  PROPERTY_INSURANCE("IP"),
  RETIREMENT_PLAN("IR"),
  INSURANCE_MISCELLANEOUS("IZ"),

  JEWELRY_OR_CAMERAS("J"),
  JEWELERS("JA"),
  CAMERAS("JC"),
  COMPUTER_SALES_AND_SERVICE("JP"),
  JEWELRY_MISCELLANEOUS("JZ"),

  CONTRACTORS("K"),
  GENERAL_CONTRACTORS("KG"),
  HOME_IMPROVEMENT_CONTRACTORS("KI"),
  SUBCONTRACTORS("KS"),
  CONTRACTORS_MISCELLANEOUS("KZ"),

  LUMBER_AND_HARDWARE("L"),
  AIR_CONDITIONING_PLUMBING_ELECTRICAL("LA"),
  CONCRETE_PRODUCTS("LC"),
  DOORS_WINDOWS_SALES_AND_SERVICES("LD"),
  CABINET_SUPPLIES("LF"),
  HARDWARE_STORES("LH"),
  PAINT_GLASS_WALLPAPER_STORES("LP"),
  LUMBER_YARDS("LY"),
  HARDWARE_MISCELLANEOUS("LZ"),

  MEDICAL("M"),
  //DENTISTS("MD"), // ambiguous
  CHIROPRACTORS("MC"),
  DOCTORS_AND_CLINICS("MD"),
  MEDICAL_EQUIPMENT("ME"),
  FUNERAL_HOMES("MF"),
  HOSPITALS("MH"),
  CEMETERIES("MM"),
  OSTEOPATHS("MO"),
  PHARMACIES_AND_DRUG_STORES("MP"),
  VETERINARIANS("MV"),
  MEDICAL_MISCELLANEOUS("MZ"),

  TRAVEL_AND_ENTERTAINMENT("N"),
  AIRLINE_CARD("NA"),
  AFFINITY_CREDIT_CARD("NC"),
  NATIONAL_DRUG_CHAIN("ND"),
  AUTO_RENTAL_COMPANIES("NR"),
  TRAVEL_AND_ENTERTAINMENT_CARD("NT"),
  TRAVEL_MISCELLANEOUS("NZ"),

  OIL("O"),
  OIL_COMPANIES("OC"),
  OIL_MISCELLANEOUS("OZ"),

  PERSONAL_SERVICES("P"),
  ACCOUNTANTS("PA"),
  BARBER_SHOPS("PB"),
  EQUIPMENT_RENTALS("PC"),
  DRY_CLEANING_AND_LAUNDRY_SERVICES("PD"),
  ENGINEERING_OF_ALL_KINDS("PE"),
  FLORISTS("PF"),
  PHOTOGRAPHERS("PG"),
  HEALTH_CLUBS("PH"),
  INVESTIGATIVE_SERVICES("PI"),
  JANITORIAL_SERVICES("PJ"),
  LEGAL_SERVICES("PL"),
  MANAGEMENT_AND_INVESTMENT_SERVICES("PM"),
  ENTERTAINMENT("PN"),
  PEST_CONTROL("PP"),
  RESTAURANTS_BARS_COUNTRY_CLUBS("PR"),
  STORAGE_WAREHOUSE("PS"),
  TRANSPORTATION_OR_DELIVERY_SERVICES("PT"),
  ANIMAL_SPECIALTY_SERVICES("PW"),
  PERSONAL_MISCELLANEOUS("PZ"),

  CREDIT_UNIONS("Q"),
  AUTO_FINANCE("QA"),
  CREDIT_UNION_CREDIT_CARD("QC"),
  SALES_FINANCING_COMPANIES("QF"),
  MORTGAGE_COMPANIES("QM"),
  CREDIT_UNION("QU"),
  CREDIT_UNIONS_LINE_OF_CREDIT("QY"),
  CREDIT_UNIONS_MISCELLANEOUS("QZ"),

  REAL_ESTATE("R"),
  APARTMENTS("RA"),
  OFFICE_LEASING("RC"),
  MOBILE_HOME_MANUFACTURERS("RD"),
  REAL_ESTATE_SALES_AND_RENTALS("RE"),
  HOTELS("RH"),
  MOTELS("RM"),
  MOBILE_HOME_PARKS("RP"),
  PROPERTY_MANAGEMENT("RR"),
  REAL_ESTATE_MISCELLANEOUS("RZ"),

  SPORTING_GOODS("S"),
  AIRCRAFT_SALES_AND_SERVICES("SA"),
  BOATS_MARINAS_SALES_AND_SERVICES("SB"),
  SPORTING_GOOD_STORES("SG"),
  MOTORCYCLES_BICYCLES_SALES_AND_SERVICES("SM"),
  SPORTING_GOODS_MISCELLANEOUS("SZ"),

  FARM_AND_GARDEN("T"),
  FARM_CHEMICALS_AND_FERTILIZER_STORES("TC"),
  FEED_SEED_STORES("TF"),
  NURSERY_LANDSCAPING_SUPPLIES_AND_SERVICES("TN"),
  FARM_AND_GARDEN_MISCELLANEOUS("TZ"),

  UTILITIES_AND_FUEL("U"),
  CABLE_SATELLITE_COMPANIES("UB"),
  COAL_WOOD_DEALERS("UC"),
  GARBAGE_RUBBISH_DISPOSAL_COMPANIES("UD"),
  ELECTRIC_POWER_COMPANIES("UE"),
  FUEL_OIL_DISTRIBUTORS("UF"),
  GAS_COMPANIES_NATURAL_BOTTLED("UG"),
  CELLULAR_TELEPHONE_PAGING_COMPANIES("UR"),
  TELEPHONE_COMPANIES("UT"),
  WATER_SANITARY_SERVICE_COMPANIES("UW"),
  UTILITIES_MISCELLANEOUS("UZ"),

  GOVERNMENT("V"),
  CITY_COUNTY("VC"),
  FEDERAL_GOVERNMENT("VF"),
  GOVERNMENT_STUDENT_LOANS("VG"),
  GOVERNMENT_LAW_ENFORCEMENT("VL"),
  STATE_GOVERNMENT("VS"),
  GOVERNMENT_MISCELLANEOUS("VZ"),

  WHOLESALE("W"),
  WHOLESALE_AUTOMOTIVE_SUPPLIES("WA"),
  WHOLESALE_BUILDING_SUPPLIES("WB"),
  WHOLESALE_CLOTHING_AND_DRY_GOODS("WC"),
  WHOLESALE_DRUGS_CHEMICALS("WD"),
  WHOLESALE_GROCERIES("WG"),
  WHOLESALE_HOME_AND_OFFICE_SUPPLIES("WH"),
  WHOLESALE_MACHINERY_SUPPLIES("WM"),
  WHOLESALE_PETROLEUM_PRODUCTS("WP"),
  WHOLESALE_MISCELLANEOUS("WZ"),

  ADVERTISING("X"),
  ADVERTISING_AGENCIES("XA"),
  ADVERTISING_MEDIA("XM"),
  ADVERTISING_MISCELLANEOUS("XZ"),

  COLLECTION_SERVICES("Y"),
  COLLECTION_DEPARTMENTS_WITHIN_ACB_CREDIT_BUREAUS("YA"),
  OTHER_COLLECTION_AGENCIES("YC"),

  MISCELLANEOUS("Z"),
  CREDIT_REPORT_BROKERS("ZB"),
  CREDIT_BUREAU_INQUIRIES("ZC"),
  CREDIT_BUREAU_MORTGAGE_PROCESSING("ZM"),
  PUBLIC_RECORDS("ZP"),
  TENANT_SCREENERS("ZT"),

  UNKNOWN("UNKNOWN");

  @Getter
  @JsonValue
  private final String value;

  IndustryCode(String value) {
    this.value = value;
  }

  private static final Map<String, IndustryCode> VALUE_TO_ENUM = Arrays.stream(values())
      .collect(Collectors.collectingAndThen(Collectors.toMap(e -> e.value, e -> e), Collections::unmodifiableMap));

  @JsonCreator
  public static IndustryCode fromValue(String value) {
    IndustryCode result = VALUE_TO_ENUM.get(value);
    if (result == null) {
      return UNKNOWN;
    }
    return result;
  }

  public static final Set<IndustryCode> ALL_RETAIL = Arrays.stream(values())
      .filter(ic -> ic.value.startsWith(CLOTHING.value) || ic.value.startsWith(DEPARTMENT_OR_VARIETY.value))
      .collect(Collectors.toUnmodifiableSet());
}
