package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class PaymentPattern {
  private PayPattern pattern;

  private String text;

  @JsonSetter
  public void setText(String text) {
    this.text = text;
    try {
      pattern = new PayPattern(text);
    } catch (IllegalArgumentException e) {
      log.debug("Invalid MOP code detected: {}", text, e);
    }
  }
}
