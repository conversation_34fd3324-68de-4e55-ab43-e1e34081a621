package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Set;

public enum RemarkCode {
  /**
   * Included in bankruptcy
   */
  BKL,
  /**
   * Bankruptcy withdrawn
   */
  BKW,
  /**
   * Chapter 7 bankruptcy
   */
  CBL,
  /**
   * Chapter 7 BK (acct. closed)
   */
  CCD,
  /**
   * Likely Chapter 7 BK (may be disputed)
   */
  CDC,
  /**
   * Likely Chapter 7 BK (may be disputed)
   */
  CDL,
  /**
   * Chapter 11 bankruptcy
   */
  CBR,
  /**
   * Chapter 11 BK (acct. closed)
   */
  CDD,
  /**
   * Likely Chapter 11 BK (may be disputed)
   */
  CDR,
  /**
   * Chapter 12 bankruptcy
   */
  CBT,
  /**
   * Likely Chapter 12 BK (may be disputed)
   */
  CDT,
  /**
   * Bankruptcy dismissed
   */
  DM,
  /**
   * Lease assumption
   */
  LA,
  /**
   * Personal receivership
   */
  PRS,
  /**
   * Reaffirmation of debt
   */
  REA,
  /**
   * Chapter 13 bankruptcy
   */
  WEP,
  /**
   * Account information disputed by consumer
   */
  AID,
  /**
   * Dispute of account information/closed by consumer
   */
  CAD,
  /**
   * Account closed by consumer
   */
  CBC,
  /**
   * Dispute resolved; consumer disagrees/account closed by consumer
   */
  CBD,
  /**
   * Dispute resolved—subscriber disagrees
   */
  DRC,
  /**
   * Dispute resolved reported by grantor
   */
  DRG,
  /**
   * Loan assumed by another party
   */
  AAP,
  /**
   * Acquired from another lender
   */
  ACQ,
  /**
   * Account closed due to refinance
   */
  ACR,
  /**
   * Account closed due to transfer
   */
  ACT,
  /**
   * Account acquired by RTC/FDIC/NCUA
   */
  AFR,
  /**
   * Adjustment pending
   */
  AJP,
  /**
   * Active military duty
   */
  AMD,
  /**
   * Affected by natural/declared disaster
   */
  AND,
  /**
   * Balloon payment
   */
  BAL,
  /**
   * Account closed by credit grantor
   */
  CBG,
  /**
   * Contingent liability—corporate defaults
   */
  CLB,
  /**
   * Account closed
   */
  CLC,
  /**
   * Credit line reduced due to collateral depreciation
   */
  CLR,
  /**
   * Credit line suspended
   */
  CLS,
  /**
   * Subscriber pays balance in full each month
   */
  CPB,
  /**
   * Collateral released by creditor/balance owing
   */
  CRB,
  /**
   * Account closed—transfer or refinance
   */
  CTR,
  /**
   * Contact subscriber
   */
  CTS,
  /**
   * Election of remedy
   */
  ER,
  /**
   * Early termination/balance owing
   */
  ETB,
  /**
   * Early termination/insurance loss
   */
  ETI,
  /**
   * Early termination/obligation satisfied
   */
  ETO,
  /**
   * Early termination/status pending
   */
  ETS,
  /**
   * Foreclosure
   */
  FCL,
  /**
   * Account in forbearance
   */
  FOR,
  /**
   * Account paid, foreclosure started
   */
  FPD,
  /**
   * Foreclosure initiated
   */
  FPI,
  /**
   * Full termination/balance owing
   */
  FTB,
  /**
   * Full termination/obligation satisfied
   */
  FTO,
  /**
   * Full termination/status pending
   */
  FTS,
  /**
   * Inactive account
   */
  INA,
  /**
   * Debt being paid through insurance
   */
  INP,
  /**
   * Paid by insurance
   */
  INS,
  /**
   * Involuntary repossession/balance owing
   */
  IRB,
  /**
   * Involuntary repossession
   */
  IRE,
  /**
   * Involuntary repossession/obligation satisfied
   */
  IRO,
  /**
   * Judgment granted
   */
  JUG,
  /**
   * Loan modified under federal government plan
   */
  LMD,
  /**
   * Loan modified non-government
   */
  LMN,
  /**
   * Credit line no longer available – in repayment phase
   */
  LNA,
  /**
   * Managed by debt counseling service
   */
  MCC,
  /**
   * No forwarding address
   */
  MOV,
  /**
   * Student loan not in repayment
   */
  NIR,
  /**
   * Now paying
   */
  NPA,
  /**
   * Purchased by another lender
   */
  PAL,
  /**
   * Paid by dealer
   */
  PDD,
  /**
   * Payment deferred
   */
  PDE,
  /**
   * Principal deferred/interest payment only
   */
  PDI,
  /**
   * Account paid from collateral
   */
  PFC,
  /**
   * Prepaid lease
   */
  PLL,
  /**
   * First payment never received
   */
  PNR,
  /**
   * Paying under partial or modified payment agreement
   */
  PPA,
  /**
   * Paid by co-maker
   */
  PPD,
  /**
   * Payroll deduction
   */
  PRD,
  /**
   * Account payment, wage garnish
   */
  PWG,
  /**
   * Substitute/Replacement account
   */
  REP,
  /**
   * Rent paid before day six
   */
  RMM,
  /**
   * Rent paid on day six or before day 15
   */
  RNN,
  /**
   * Rent paid on or after day 15
   */
  ROO,
  /**
   * Rent paid, but required demand letter
   */
  RPP,
  /**
   * Eviction—non legal action
   */
  RQQ,
  /**
   * Repossession; redeemed
   */
  RRE,
  /**
   * Eviction
   */
  RRR,
  /**
   * Rent unpaid—renter skipped, and did not fulfill remaining lease term
   */
  RSS,
  /**
   * Voluntary surrender redeemed
   */
  RVR,
  /**
   * Credit line suspended due to collateral depreciation
   */
  SCD,
  /**
   * Settled—less than full balance
   */
  SET,
  /**
   * Simple interest loan
   */
  SIL,
  /**
   * Student loan perm assign government
   */
  SLP,
  /**
   * Single payment loan
   */
  SPL,
  /**
   * Credit card lost or stolen
   */
  STL,
  /**
   * Transferred to another lender
   */
  TRL,
  /**
   * Transferred to recovery
   */
  TTR,
  /**
   * Placed for collection
   */
  CLA,
  /**
   * Closed
   */
  CLO,
  /**
   * Deed in lieu
   */
  DLU,
  /**
   * Foreclosure, collateral sold
   */
  FRD,
  /**
   * Paid collection
   */
  PCL,
  /**
   * Profit and loss now paying
   */
  PLP,
  /**
   * Paid profit and loss
   */
  PPL,
  /**
   * Profit and loss write-off
   */
  PRL,
  /**
   * Refinanced
   */
  RFN,
  /**
   * Paid repossession
   */
  RPD,
  /**
   * Repossession
   */
  RPO,
  /**
   * Voluntary surrender
   */
  RVN,
  /**
   * Claim filed with government
   */
  SGL,
  /**
   * Transfer
   */
  TRF,
  /**
   * Non-defined code placeholder
   */
  UNKNOWN;

  @JsonCreator
  public static RemarkCode parse(String code) {
    try {
      return RemarkCode.valueOf(code);
    } catch (IllegalArgumentException e) {
      return RemarkCode.UNKNOWN;
    }
  }

  public static final Set<RemarkCode> BANKRUPTCY = Set.of(BKL, CBL, CBR, CBT, WEP);
}
