package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;

@Data
public class ScoreModel {
  @JsonFormat(with = JsonFormat.Feature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
  private List<Characteristic> characteristic;

  @Getter(lazy = true)
  @JsonIgnore
  private final Map<String, String> characteristicIdValueMap = Optional.ofNullable(getCharacteristic())
      .map(chars -> chars.stream()
          .filter(c -> c.getValue() != null)
          .collect(Collectors.toMap(Characteristic::getId, Characteristic::getValue)))
      .orElse(Map.of());

  public Long getLongCharacteristic(String id) {
    return extractCharacteristicValue(id, Long::parseLong);
  }

  public Double getDoubleCharacteristic(String id) {
    return extractCharacteristicValue(id, Double::parseDouble);
  }

  private <T> T extractCharacteristicValue(String id, Function<String, T> parser) {
    return Optional.ofNullable(getCharacteristicIdValueMap().get(id)).map(parser).orElse(null);
  }
}
