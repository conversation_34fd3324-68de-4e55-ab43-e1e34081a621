Troubleshoot FlexScoreInputFeature bug

## Compare with credit-features
1. Setup dev env with [offers repo](https://github.com/flexapp/offers)
2. Add alloy json file with name `data.json` to repo root directory
3. Run following script in Python REPL environment
   ```
   import json
   from credit_features import BureauReportParser
   
   # Open and read the JSON file
   with open('data.json', 'r') as file:
     data = json.load(file)
   
   parser = BureauReportParser(**data)
   ```
4. Then you can check the behavior/output of BureauReportParser, such as `parser.revolving_trades`
5. If necessary, you can also modify credit-features source code in VSCode to add debug output 
