package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.databind.util.StdConverter;
import java.time.OffsetDateTime;
import java.util.Optional;

public class PropagateReportDateTime extends StdConverter<CreditBureau, CreditBureau> {

  @Override
  public CreditBureau convert(CreditBureau value) {
    // If any part is null, reportDateTime will be null
    OffsetDateTime reportDateTime = Optional.ofNullable(value)
        .map(CreditBureau::getTransactionControl)
        .map(TransactionControl::getTracking)
        .map(Tracking::getTransactionTimeStamp)
        .orElse(null);

    // propagate it to all trades
    Optional.ofNullable(value)
        .map(CreditBureau::getProduct)
        .map(Product::getSubject)
        .map(Subject::getSubjectRecord)
        .map(SubjectRecord::getCustom)
        .map(Custom::getCredit)
        .map(Credit::getTrades)
        .ifPresent(trades -> trades.forEach(t -> t.setCreditReportDateTime(reportDateTime)));
    
    return value;
  }
}
