package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * Enum containing all available account ratings (according to the
 * TUXML schema documentation)
 */
public enum AccountRating {
  PAID_OR_PAYING("01"),
  PAST_DUE_30_DAYS("02"),
  PAST_DUE_60_DAYS("03"),
  PAST_DUE_90_DAYS("04"),
  PAST_DUE_120_DAYS("05"),
  WAGE_EARNER("07"),
  REPOSESSION("08"),
  VOLUNTARY_SURRENDER("8A"),
  PAYMENT_AFTER_REPOSESSION("8P"),
  BAD_DEBT("09"),
  COLLECTION_ACCOUNT("9B"),
  PAYMENT_AFTER_COLLECTION("9P"),
  UNRATED("UR");

  @Getter
  @JsonValue
  private final String value;

  AccountRating(String value) {
    this.value = value;
  }

  private static final Map<String, AccountRating> VALUE_TO_ENUM = Arrays.stream(values())
      .collect(Collectors.collectingAndThen(Collectors.toMap(e -> e.value, e -> e), Collections::unmodifiableMap));

  @JsonCreator
  public static AccountRating fromValue(String value) {
    AccountRating result = VALUE_TO_ENUM.get(value);
    if (result == null) {
      throw new IllegalArgumentException("Unknown value");
    }
    return result;
  }

  public static final Set<AccountRating> DEFAULTED = Set.of(
      WAGE_EARNER, REPOSESSION, VOLUNTARY_SURRENDER, PAYMENT_AFTER_REPOSESSION, BAD_DEBT, COLLECTION_ACCOUNT,
      PAYMENT_AFTER_COLLECTION, UNRATED
  );
}
