package com.getflex.featurestore.model.flexscore.alloy;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.Optional;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransunionData {
  private Long maxCreditLimitOpenCreditTrades;
  private Vantagescore30 vantagescore30;
  private Vantagescore40 vantagescore40;
  private Boolean fileFrozen;
  private Boolean creditDataSuppressed;
  private Boolean fileHitFlag;
  private String fraudStatementType;
  private CreditVisionEnrichedAttributesV2 creditVisionEnrichedAttributesV2;
}
