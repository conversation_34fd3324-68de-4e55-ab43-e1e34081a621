package com.getflex.featurestore.model.flexscore.alloy;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.featurestore.model.flexscore.alloy.transunionraw.TransunionCreditRaw;
import com.google.common.collect.Iterables;
import java.util.List;
import lombok.Data;

@Data
public class RawResponses {
  @JsonProperty("TransUnion Credit")
  private List<TransunionCreditRaw> transunionCredit;

  public TransunionCreditRaw transunion() {
    return Iterables.getOnlyElement(getTransunionCredit());
  }
}
