package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.ProductType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsCustomerSlcFeature extends BaseFeature {

  private final OfferService offerService;
  private static List<ProductType> PRODUCT_TYPES = List.of(ProductType.SLC, ProductType.CREDITBUILDER);

  public IsCustomerSlcFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offerByCustomerId = offerService.getOfferByCustomerId(evalParams.getCustomerId());
    if (offerByCustomerId != null) {
      // Check if the offer is SLC or CreditBuilder
      if (PRODUCT_TYPES.contains(offerByCustomerId.getProductType())) {
        return new FeatureOutput(true);
      }
    }

    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "Check to see if the customer is SLC or CreditBuilder";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
