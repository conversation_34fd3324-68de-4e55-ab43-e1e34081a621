package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.UserAccount;

@RegisterFeature
public class CustomerEmailVerifiedFeature extends BaseFeature {
  private final IdentityService identityService;
  private final UserAccountService userAccountService;

  public CustomerEmailVerifiedFeature(IdentityService identityService, UserAccountService userAccountService) {
    this.identityService = identityService;
    this.userAccountService = userAccountService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    UserAccount userAccount = userAccountService.getUserAccountByPublicId(customer.getCustomerPublicId());
    return new FeatureOutput(userAccount.getEmailVerified(), null);
  }

  @Override
  public String getDescription() {
    return "Check if customer's email has been verified";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
