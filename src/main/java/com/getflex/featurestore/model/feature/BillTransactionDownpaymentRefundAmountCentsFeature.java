package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;

@RegisterFeature
public class BillTransactionDownpaymentRefundAmountCentsFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public BillTransactionDownpaymentRefundAmountCentsFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> txs = ledgerService.getDownpaymentRecords(evalParams.getBillTransactionId(),
        MovementCategory.REFUND);
    if (txs == null || txs.isEmpty()) {
      return new FeatureOutput(-1L);
    }
    long total = txs.stream().mapToLong(t -> {
      try {
        return t.getAmount();
      } catch (Exception e) {
        return 0L;
      }
    }).sum();
    return new FeatureOutput(total);
  }

  @Override
  public String getDescription() {
    return "Downpayment Refund amount in cents during BP";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }

}
