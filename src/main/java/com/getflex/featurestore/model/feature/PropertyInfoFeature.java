package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class PropertyInfoFeature extends BaseFeature {

  private final IdentityService identityService;
  private final OfferService offerService;
  private final BillingService billingService;

  public PropertyInfoFeature(IdentityService identityService, OfferService offerService,
      BillingService billingService) {
    this.identityService = identityService;
    this.offerService = offerService;
    this.billingService = billingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(getPropertyInfo(evalParams));
  }

  protected PropertyInfo getPropertyInfo(EvalParams evalParams) {
    Long billerId = evalParams.getBillerId();
    if (billerId == null) {
      Long billerAccountId = evalParams.getBillerAccountId();
      if (billerAccountId == null) {
        billerAccountId = offerService.getOffer(evalParams.getOfferId(), null).getBillerAccountId();
      }
      ExtendedBillerAccountData billerAccount = identityService.getBillerAccount(billerAccountId);
      billerId = billerAccount.getBillerId();
    }
    return billingService.getPropertyByIdentityBillerId(billerId);
  }

  @Override
  public String getDescription() {
    return "Provide property info such as integration type";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.OFFER_ID, EvalParamKey.BILLER_ID);
  }
}
