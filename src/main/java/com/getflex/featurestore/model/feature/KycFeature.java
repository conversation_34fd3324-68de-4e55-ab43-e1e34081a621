package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class KycFeature extends BaseFeature {

  private final VerificationService verificationService;

  public KycFeature(VerificationService verificationService) {

    this.verificationService = verificationService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.KYC_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(verificationService.getKyc(evalParams.getKycId()));
  }

  @Override
  public String getDescription() {
    return "Get KYC record";
  }
}
