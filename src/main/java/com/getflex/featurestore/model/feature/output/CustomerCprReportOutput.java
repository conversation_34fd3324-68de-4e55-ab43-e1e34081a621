package com.getflex.featurestore.model.feature.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerCprReportOutput {

  @JsonProperty("vantage_score_40")
  private Integer vantageScore40;

  @JsonProperty("vantage_40_adverse_reason_code_1")
  private Integer vantage40AdverseReasonCode1;

  @JsonProperty("vantage_40_adverse_reason_code_2")
  private Integer vantage40AdverseReasonCode2;

  @JsonProperty("vantage_40_adverse_reason_code_3")
  private Integer vantage40AdverseReasonCode3;

  @JsonProperty("vantage_40_adverse_reason_code_4")
  private Integer vantage40AdverseReasonCode4;

  @JsonProperty("vantage_40_adverse_reason_code_5")
  private Integer vantage40AdverseReasonCode5;

  @JsonProperty("vantage_score_30")
  private Integer vantageScore30;

  @JsonProperty("eads142_s207s")
  private Integer eads142S207s;

  public CustomerCprReportOutput(Integer vantageScore40) {
    this.vantageScore40 = vantageScore40;
  }
}
