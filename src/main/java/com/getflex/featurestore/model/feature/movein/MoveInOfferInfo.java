package com.getflex.featurestore.model.feature.movein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.credit.client.model.Loan;
import com.getflex.offerv2.model.OverallState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents information about a single move-in offer.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MoveInOfferInfo {
  
  /**
   * The offer ID.
   */
  @JsonProperty("offer_id")
  private String offerId;
  
  /**
   * The offer version.
   */
  @JsonProperty("offer_version")
  private Long offerVersion;
  
  /**
   * The loan ID if this is a LOAN type offer, null for PAY_IN_FULL.
   */
  @JsonProperty("loan_id")
  private Long loanId;
  
  /**
   * The type of move-in offer: PAY_IN_FULL or LOAN.
   */
  @JsonProperty("type")
  private MoveInType type;
  
  /**
   * The overall state of the offer (e.g., ACTIVE, SUSPENDED).
   */
  @JsonProperty("offer_state")
  private OverallState offerState;
  
  /**
   * The full loan object if this is a LOAN type offer, null for PAY_IN_FULL.
   */
  @JsonProperty("loan")
  private Loan loan;
}
