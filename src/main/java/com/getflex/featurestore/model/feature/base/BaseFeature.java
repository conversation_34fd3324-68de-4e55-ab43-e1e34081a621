package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
//import com.getflex.featurestore.model.Feature;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public abstract class BaseFeature {

  private String providedName = null;

  public abstract FeatureTypeEnum getType();

  protected abstract FeatureOutput getValue(EvalParams evalParams);

  public abstract String getDescription();

  public abstract ParamRequired getRequiredEvalParamKeys();

  // feature value is set to default value when exception is thrown
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(null, null);
  }

  public void setName(String name) {
    this.providedName = name;
  }

  public String getName() {
    if (providedName != null) {
      return providedName;
    }
    return this.getClass().getSimpleName();
  }

  public FeatureValue fetchFeatureValue(EvalParams evalParams) {
    FeatureValue featureValue = new FeatureValue();
    try {
      featureValue.setName(getName());
      featureValue.setType(getType());
      validateEvaluationParams(evalParams);
      FeatureOutput output = getValue(evalParams);
      featureValue.setValue(output.value());
      featureValue.setMetadata(output.metadata());
    } catch (Exception e) {
      log.error("Error when evaluating featureName={}, message={}", getName(), e.getMessage());
      featureValue.setErrorMessage(e.getMessage());
      FeatureOutput output = getDefaultValue();
      featureValue.setValue(output.value());
      featureValue.setMetadata(output.metadata());
    }
    return featureValue;
  }

  protected void validateEvaluationParams(EvalParams evalParams) throws IllegalArgumentException {
    Set<EvalParamKey> availableParams = new HashSet<>();
    if (evalParams.getCustomerId() != null) {
      availableParams.add(EvalParamKey.CUSTOMER_ID);
    }
    if (evalParams.getBillerAccountId() != null) {
      availableParams.add(EvalParamKey.BILLER_ACCOUNT_ID);
    }
    if (evalParams.getBillerId() != null) {
      availableParams.add(EvalParamKey.BILLER_ID);
    }
    if (evalParams.getDeviceId() != null) {
      availableParams.add(EvalParamKey.DEVICE_ID);
    }
    if (evalParams.getIpAddress() != null) {
      availableParams.add(EvalParamKey.IP_ADDRESS);
    }
    if (!ObjectUtils.isEmpty(evalParams.getAlloyReportUrl())) {
      availableParams.add(EvalParamKey.ALLOY_REPORT_URL);
    }
    if (!ObjectUtils.isEmpty(evalParams.getStripePaymentFullName())) {
      availableParams.add(EvalParamKey.STRIPE_PAYMENT_FULL_NAME);
    }
    if (!ObjectUtils.isEmpty(evalParams.getStripeZipCode())) {
      availableParams.add(EvalParamKey.STRIPE_ZIP_CODE);
    }
    if (!ObjectUtils.isEmpty(evalParams.getStripeAddressLine1())) {
      availableParams.add(EvalParamKey.STRIPE_ADDRESS_LINE_1);
    }
    if (!ObjectUtils.isEmpty(evalParams.getStripeAvsStreetCheck())) {
      availableParams.add(EvalParamKey.STRIPE_AVS_STREET_CHECK);
    }
    if (!ObjectUtils.isEmpty(evalParams.getStripeAvsZipCheck())) {
      availableParams.add(EvalParamKey.STRIPE_AVS_ZIP_CHECK);
    }
    if (!ObjectUtils.isEmpty(evalParams.getCardFingerprint())) {
      availableParams.add(EvalParamKey.CARD_FINGERPRINT);
    }
    if (!ObjectUtils.isEmpty(evalParams.getCardType())) {
      availableParams.add(EvalParamKey.CARD_TYPE);
    }
    if (!ObjectUtils.isEmpty(evalParams.getCardIssuer())) {
      availableParams.add(EvalParamKey.CARD_ISSUER);
    }
    if (evalParams.getOfferVersion() != null) {
      availableParams.add(EvalParamKey.OFFER_VERSION);
    }
    if (!ObjectUtils.isEmpty(evalParams.getOfferId())) {
      availableParams.add(EvalParamKey.OFFER_ID);
    }
    if (!ObjectUtils.isEmpty(evalParams.getBillTransactionId())) {
      availableParams.add(EvalParamKey.BILL_TRANSACTION_ID);
    }
    if (!ObjectUtils.isEmpty(evalParams.getBatchMemo())) {
      availableParams.add(EvalParamKey.BATCH_MEMO);
    }
    if (evalParams.getTransactionAmountCents() != null) {
      availableParams.add(EvalParamKey.TRANSACTION_AMOUNT_CENTS);
    }
    if (!ObjectUtils.isEmpty(evalParams.getVerificationId())) {
      availableParams.add(EvalParamKey.VERIFICATION_ID);
    }
    if (!ObjectUtils.isEmpty(evalParams.getSsnCipher())) {
      availableParams.add(EvalParamKey.SSN_CIPHER);
    }
    if (!ObjectUtils.isEmpty(evalParams.getDobCipher())) {
      availableParams.add(EvalParamKey.DOB_CIPHER);
    }
    if (!ObjectUtils.isEmpty(evalParams.getDisputeId())) {
      availableParams.add(EvalParamKey.DISPUTE_ID);
    }
    if (evalParams.getEstimatedRentAmountCent() != null) {
      availableParams.add(EvalParamKey.ESTIMATED_RENT_AMOUNT_CENT);
    }
    if (!ObjectUtils.isEmpty(evalParams.getKycId())) {
      availableParams.add(EvalParamKey.KYC_ID);
    }
    if (!ObjectUtils.isEmpty(evalParams.getHubUserKycId())) {
      availableParams.add(EvalParamKey.HUB_USER_KYC_ID);
    }
    if (evalParams.getPaymentMethodId() != null) {
      availableParams.add(EvalParamKey.PAYMENT_METHOD_ID);
    }
    if (evalParams.getSsnHmac() != null) {
      availableParams.add(EvalParamKey.SSN_HMAC);
    }
    if (evalParams.getOnboardingDomain() != null) {
      availableParams.add(EvalParamKey.ONBOARDING_DOMAIN);
    }
    if (!ObjectUtils.isEmpty(evalParams.getCheckpointName())) {
      availableParams.add(EvalParamKey.CHECKPOINT_NAME);
    }
    if (!ObjectUtils.isEmpty(evalParams.getBusinessId())) {
      availableParams.add(EvalParamKey.BUSINESS_ID);
    }
    if (!ObjectUtils.isEmpty(evalParams.getEinHmac())) {
      availableParams.add(EvalParamKey.EIN_HMAC);
    }
    getRequiredEvalParamKeys().validateEvaluationParams(availableParams);
  }
}
