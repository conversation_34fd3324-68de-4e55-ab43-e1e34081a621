package com.getflex.featurestore.model.feature.base.param;

import com.getflex.featurestore.model.EvalParamKey;
import java.util.Set;

public record Single(EvalParamKey evalParamKey) implements ParamRequired {
  @Override
  public void validateEvaluationParams(Set<EvalParamKey> availableParams) {
    if (!availableParams.contains(this.evalParamKey())) {
      throw new IllegalArgumentException("Missing required " + this.evalParamKey().name());
    }
  }
}
