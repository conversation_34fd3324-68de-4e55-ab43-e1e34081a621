package com.getflex.featurestore.model.feature.base;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.integration.flex.flexscore.model.ModelOutput;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FlexScoreMetadata;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelFeatureFormulaParams;
import com.google.common.annotations.VisibleForTesting;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractFlexScoreFeature extends BaseFeature {

  @VisibleForTesting
  public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
      .enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
      .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE) // retain original timezone from AlloyReport
      .registerModule(new Jdk8Module())
      .registerModule(new JavaTimeModule());
  private static final PropertyInfo DEFAULT_PROPERTY_INFO = new PropertyInfo(null, null, null);
  protected final FlexScoreModel flexScoreModel;
  protected final BillingService billingService;
  protected final IdentityService identityService;
  protected final Function<String, String> alloyReportDownloader;
  private final Version flexScoreVersion;
  private final FeatureFactory featureFactory;

  public AbstractFlexScoreFeature(Version flexScoreVersion, FlexScoreModel flexScoreModel,
      BillingService billingService, IdentityService identityService, Function<String, String> alloyReportDownloader,
      FeatureFactory featureFactory) {
    this.flexScoreModel = flexScoreModel;
    this.billingService = billingService;
    this.identityService = identityService;
    this.alloyReportDownloader = alloyReportDownloader;
    this.flexScoreVersion = flexScoreVersion;
    this.featureFactory = featureFactory;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      AlloyReport alloyReport = OBJECT_MAPPER.readValue(alloyReportDownloader.apply(evalParams.getAlloyReportUrl()),
          AlloyReport.class);
      PropertyInfo property;
      try {
        property = billingService.getPropertyByIdentityBillerId(
            identityService.getBillerByBillerAccountId(evalParams.getBillerAccountId()).getBillerId());
      } catch (RuntimeException e) {
        log.warn("Failed loading property info, using default value");
        property = DEFAULT_PROPERTY_INFO;
      }
      FlexScoreModelFeatureFormulaParams formulaParams = FlexScoreModelFeatureFormulaParams.builder()
          .alloyReport(alloyReport)
          .propertyInfo(property)
          .featureFactory(featureFactory)
          .evalParams(evalParams)
          .build();

      ModelOutput output = flexScoreModel.execute(flexScoreVersion, formulaParams);
      return toFeatureFormat(output);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private FeatureOutput toFeatureFormat(ModelOutput output)
      throws JsonProcessingException {
    FlexScoreMetadata metadata = new FlexScoreMetadata();
    metadata.setInputFeatures(convertInputFeature(output.inputFeatures()));
    metadata.setVantageScore(Optional.ofNullable(output.vantageScore()).map(Number::intValue).orElse(null));
    metadata.setDecile(output.decile());
    metadata.setReasonCodes(List.copyOf(output.rejectionCodes()));
    return new FeatureOutput(output.flexScore(),
        OBJECT_MAPPER.writeValueAsString(metadata));
  }

  static Map<String, BigDecimal> convertInputFeature(Map<FlexScoreModelExtractedFeature, Number> numberMap) {
    Map<String, BigDecimal> result = new HashMap<>(numberMap.size());
    numberMap.forEach((feature, number) -> {
      BigDecimal bigDecimal = null;
      if (number != null) {
        if (number instanceof BigDecimal) {
          bigDecimal = (BigDecimal) number;
        } else if (number instanceof Integer || number instanceof Long) {
          // There are many other integer types under Number, but these are the only ones we use for model input
          bigDecimal = BigDecimal.valueOf(number.longValue());
        } else {
          bigDecimal = BigDecimal.valueOf(number.doubleValue());
        }
      }
      result.put(feature.name(), bigDecimal);
    });
    return result;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.ALLOY_REPORT_URL);
  }
}
