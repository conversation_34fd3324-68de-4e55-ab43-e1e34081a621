package com.getflex.featurestore.model.feature.docv;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;

@RegisterFeature
public class OfferHasOnboardingKycReferEventFeature extends BaseFeature {
  private final EventRepository eventRepository;

  public OfferHasOnboardingKycReferEventFeature(EventRepository eventRepository) {
    this.eventRepository = eventRepository;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String entityId = evalParams.getOfferId() + "-" + evalParams.getOfferVersion().toString();
    Optional<Event> referEvent = eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
        entityId, EventName.ONBOARDING_KYC_REFER);

    return new FeatureOutput(referEvent.isPresent());
  }

  @Override
  public String getDescription() {
    return "Offer has Refer event during KYC";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.OFFER_VERSION, EvalParamKey.OFFER_ID);
  }
}
