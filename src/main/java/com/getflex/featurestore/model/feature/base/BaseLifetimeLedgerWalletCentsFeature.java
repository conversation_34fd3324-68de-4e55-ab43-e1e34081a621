package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import java.util.function.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public abstract class BaseLifetimeLedgerWalletCentsFeature extends BaseFeature {

  private LedgerService ledgerService;

  @Autowired
  public BaseLifetimeLedgerWalletCentsFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    List<RecordLedgerWallet> ledgerRecords = ledgerService.getWalletRecordLedgerByCustomerId(customerId);
    if (ledgerRecords.isEmpty()) {
      return new FeatureOutput(0L);
    }
    Long amount = ledgerRecords.stream()
        .filter(rlw -> rlw.getPaymentCategoryId() != null)
        .filter(getFilterPredicate())
        .mapToLong(rlw -> rlw.getAmount())
        .sum();
    return new FeatureOutput(amount);
  }

  protected abstract Predicate<RecordLedgerWallet> getFilterPredicate();
}
