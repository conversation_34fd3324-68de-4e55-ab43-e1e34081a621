package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import com.getflex.fulfillment.model.GetStatusResponseV2.StatusEnum;
import java.util.List;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RegisterFeature
public class BillTransactionHasValidFulfillmentStatusFeature extends BaseFeature {

  private final FulfillmentService fulfillmentService;
  private final List<StatusEnum> invalidStatuses = List.of(StatusEnum.CANCELED);

  public BillTransactionHasValidFulfillmentStatusFeature(FulfillmentService fulfillmentService) {
    super();
    this.fulfillmentService = fulfillmentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetStatusResponseV2 response;
    try {
      response = fulfillmentService.getStatusCode(
          evalParams.getBillTransactionId());
    } catch (InternalDependencyFailureException e) {
      return new FeatureOutput(false);
    }

    if (response == null) {
      log.error("no fulfillment status found for btx id {}", evalParams.getBillTransactionId());
      return new FeatureOutput(false);
    }
    return new FeatureOutput(!this.invalidStatuses.contains(response.getStatus()));
  }

  @Override
  public String getDescription() {
    return "If the fulfillment is in valid status for settlement or not";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
