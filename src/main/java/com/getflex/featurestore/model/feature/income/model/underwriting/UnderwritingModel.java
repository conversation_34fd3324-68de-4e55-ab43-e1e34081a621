package com.getflex.featurestore.model.feature.income.model.underwriting;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnderwritingModel {
  Boolean isExpired;
  IsIneligible isIneligible;
  Long averageDailyBalanceCent;
  Long monthlyGrossIncomeCent;
  Long monthlyGrossSelfReportedIncomeCent;
  Long monthlyGrossVerifiedIncomeCent;
  Long annualTaxAmountCent;
  Long monthlyNetIncomeCent;
  Long estimatedRentAmountCent;
  Long totalMonthlyObligationRevolvingTradesCent;
  Long totalMonthlyObligationInstallmentTradesCent;
  Long nonMedicalCollectionBalanceCent;
  Double netIncomeToRentRatio;
  Double grossIncomeToRentRatio;
  Double verifiedGrossIncomeToRentRatio;
  OriginalTuFeatures originalTuFeatures;
}
