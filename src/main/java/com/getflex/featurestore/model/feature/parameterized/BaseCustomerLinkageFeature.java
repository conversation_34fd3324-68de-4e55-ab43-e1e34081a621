package com.getflex.featurestore.model.feature.parameterized;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.identity.model.v2.CustomerDataV2;
import com.getflex.identity.model.v2.SearchCustomerRequest;
import com.getflex.identity.model.v2.SearchCustomerRequestFilters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BaseCustomerLinkageFeature extends BaseFeature {

  private final IdentityService identityService;
  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

  // 1-active, 2-deleted, 3-canceled
  private static final List<Integer> CUSTOMER_STATUS_IDS = List.of(1, 2, 3);

  public BaseCustomerLinkageFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    CustomerDataV2 customer = identityService.getCustomerById(customerId);

    SearchCustomerRequest request =
        buildSearchRequest(customer)
            .filters(new SearchCustomerRequestFilters().status(CUSTOMER_STATUS_IDS));

    // Create metadata
    String metadataString = null;
    try {
      Map<String, Object> metadata = new HashMap<>();
      metadata.put("searchCustomerRequest", request);
      metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
    } catch (Exception e) {
      log.error("Failed to create metadata for customerId={}", customerId, e);
    }

    List<CustomerDataV2> customerList = identityService.searchCustomer(request);
    int result = (int) customerList.stream().map(CustomerDataV2::getId).distinct().count();

    return new FeatureOutput(result, metadataString);
  }

  protected abstract SearchCustomerRequest buildSearchRequest(CustomerDataV2 customer);
}
