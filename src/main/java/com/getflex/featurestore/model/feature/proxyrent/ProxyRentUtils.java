package com.getflex.featurestore.model.feature.proxyrent;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.utils.CustomSoundex;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.language.Soundex;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ProxyRentUtils {

  private static final String DEFAULT_EMPTY = "";

  private static final String PRE = "(?<=\\s|^)";
  private static final String POST = "(?=[\\s,.$]|$)";
  private static final List<Pair<String, String>> addressAbbr = List.of(
      Pair.of("st", "street,"),
      Pair.of("ave", "avenue,"),
      Pair.of("dr", "drive,"),
      Pair.of("rd", "road,"),
      Pair.of("ct", "court,"),
      Pair.of("pkwy", "parkway,"),
      Pair.of("pl", "place,"),
      Pair.of("ter", "terrace,"),
      Pair.of("cir", "circle,"),
      Pair.of("hwy", "highway,"),
      Pair.of("ln", "lane,"));

  private static final String CLEAN = "[^\\w]";
  private static final String EXTRACT_WORDS_AFTER_NUMBER = "\\s+([A-Za-z\\s\\d\\W]+?)(?=\\s*[,#]|\\s*$)";
  private static final Pattern PATTERN = Pattern.compile(EXTRACT_WORDS_AFTER_NUMBER);

  private static final Soundex SOUNDEX = new Soundex();
  private static final CustomSoundex CUSTOM_SOUNDEX = new CustomSoundex();

  private static final ObjectMapper OBJECT_MAPPER =
      new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
          .registerModule(new JavaTimeModule());

  private final IdentityService identityService;
  private final BillingService billingService;

  public ProxyRentUtils(IdentityService identityService, BillingService billingService) {
    this.identityService = identityService;
    this.billingService = billingService;
  }

  public static List<String> extractCreditReportFeature(Object creditReport, String... keys) {
    Map<String, Object> creditReportMap = new HashMap<>(
        OBJECT_MAPPER.convertValue(creditReport, new TypeReference<>() {}));
    // If feature is not present, default to 0
    return Arrays.stream(keys).map(key -> Optional.ofNullable(
        creditReportMap.getOrDefault(key, 0))
        .orElse(0).toString()).toList();
  }

  public String formatPrimaryKey(String featureName, Long billerId) {
    Long propertyId = Optional.ofNullable(billingService.getPropertyByBillerId(billerId))
        .orElseThrow(() -> new InternalDependencyFailureException(featureName)).getPropertyId();
    return String.valueOf(propertyId);
  }

  public String formatPrimaryKey(String featureName, EvalParams evalParams) {
    GetCustomerResponse customerResponse = identityService.getCustomer(evalParams.getCustomerId());
    String primaryKey = DEFAULT_EMPTY;

    switch (featureName) {
      case ZipCodeBpMeanFeature.OFFLINE_FEATURE_NAME, ZipCodeRentMeanFeature.OFFLINE_FEATURE_NAME:
        // Drop leading zeros
        primaryKey = getZip5(customerResponse.getZip()).replaceAll("^0+", DEFAULT_EMPTY);
        break;
      case CityNameBpMeanFeature.OFFLINE_FEATURE_NAME, CityNameRentMeanFeature.OFFLINE_FEATURE_NAME:
        String cityName = cleanString(customerResponse.getCity());
        String stateName = cleanString(customerResponse.getState());
        primaryKey = String.format("%s_%s", SOUNDEX.encode(cityName), stateName);
        break;
      case StreetNameBpMeanFeature.OFFLINE_FEATURE_NAME, StreetNameRentMeanFeature.OFFLINE_FEATURE_NAME:
        String addressLine = Optional.ofNullable(customerResponse.getAddressLine1())
            .orElse(DEFAULT_EMPTY).toLowerCase();
        addressLine = addressAbbr.stream().reduce(addressLine, (acc, abbr) ->
            acc.replaceAll(PRE + abbr.getLeft() + POST, abbr.getRight()), (s1, s2) -> s1);
        Matcher matcher = PATTERN.matcher(addressLine);
        if (!matcher.find()) {
          break;
        }
        String streetName = cleanString(matcher.group(1));
        String zip5 = getZip5(customerResponse.getZip());
        primaryKey = String.format("%s_%s", CUSTOM_SOUNDEX.encode(streetName), zip5);
        break;
      case StateNameBpMeanFeature.OFFLINE_FEATURE_NAME, StateNameRentMeanFeature.OFFLINE_FEATURE_NAME:
        primaryKey = cleanString(customerResponse.getState());
        break;
      default:
        primaryKey = DEFAULT_EMPTY;
    }

    log.info("feature={} primaryKey={}", featureName, primaryKey.toLowerCase());
    return primaryKey.toLowerCase();
  }

  private String getZip5(String zipcode) {
    return StringUtils.isEmpty(zipcode) ? DEFAULT_EMPTY
        : zipcode.substring(0, Math.min(5, zipcode.length()));
  }

  private String cleanString(String str) {
    return Optional.ofNullable(str).orElse(DEFAULT_EMPTY)
        .replaceAll(CLEAN, DEFAULT_EMPTY).toLowerCase();
  }

}
