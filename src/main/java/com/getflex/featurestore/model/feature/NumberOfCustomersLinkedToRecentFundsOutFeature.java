package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.wallet.model.Card;

@RegisterFeature
public class NumberOfCustomersLinkedToRecentFundsOutFeature extends BaseFeature {

  private final WalletService walletService;
  private final IdentityService identityService;

  public NumberOfCustomersLinkedToRecentFundsOutFeature(
      WalletService walletService,
      IdentityService identityService
  ) {
    this.walletService = walletService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    var customer = identityService.getCustomer(evalParams.getCustomerId());
    var getDefaultCardResponse = walletService.getDefaultCard(customer.getCustomerPublicId());
    if (getDefaultCardResponse == null || getDefaultCardResponse.getCard() == null) {
      return new FeatureOutput(0);
    }
    var cards = walletService.getCardsByFingerprint(getDefaultCardResponse.getCard().getFingerprint());
    var numberOfCustomersLinkedToRecentFundsOut = (int) cards.stream().map(Card::getStripeCustomerId)
        .distinct()
        .count();
    return new FeatureOutput(numberOfCustomersLinkedToRecentFundsOut);
  }

  @Override
  public String getDescription() {
    return "Number of customers linked to recent funds out";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
