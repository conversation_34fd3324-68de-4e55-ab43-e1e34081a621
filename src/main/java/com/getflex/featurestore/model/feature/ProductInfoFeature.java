package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class ProductInfoFeature extends BaseFeature {

  private final IdentityService identityService;
  private final OfferService offerService;

  public ProductInfoFeature(IdentityService identityService, OfferService offerService) {
    this.identityService = identityService;
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    if (billerAccountId == null) {
      billerAccountId = offerService.getOffer(evalParams.getOfferId(), null).getBillerAccountId();
    }
    ExtendedBillerAccountData billerAccount = identityService.getBillerAccount(billerAccountId);
    return new FeatureOutput(new ProductInfo(billerAccount.getProductId(), billerAccount.getCategoryId()));
  }

  @Override
  public String getDescription() {
    return "Product info including product_id and category_id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.OFFER_ID);
  }

  record ProductInfo(@JsonProperty("product_id") Long productId, @JsonProperty("category_id") Long categoryId) {
  }
}
