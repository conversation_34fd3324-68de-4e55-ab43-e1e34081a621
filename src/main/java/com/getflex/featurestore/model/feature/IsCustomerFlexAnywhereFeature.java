package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.BillerDto;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RegisterFeature
public class IsCustomerFlexAnywhereFeature extends BaseFeature {

  private final BillingService billingService;
  private final IdentityService identityService;

  public IsCustomerFlexAnywhereFeature(BillingService billingService, IdentityService identityService) {
    super();
    this.billingService = billingService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    BillerDto billerFromIdentity = identityService.getBillerByBillerAccountId(billerAccountId);

    ComGetflexBillingControllerV2PropertyControllerPropertyResponse billerFromBilling = billingService.getPropertyById(
        billerFromIdentity.getBillerId());

    return new FeatureOutput(billerFromBilling.getIntegrationType().equals(IntegrationTypeEnum.FLEX_ANYWHERE));
  }

  @Override
  public String getDescription() {
    return "This feature returns the value of whether the customer "
        + "is a FlexAnywhere customer or not from biller account id";
  }
}
