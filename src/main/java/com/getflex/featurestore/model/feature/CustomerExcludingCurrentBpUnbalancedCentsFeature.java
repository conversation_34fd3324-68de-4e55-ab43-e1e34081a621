package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.GetOutstandingBalanceResponse;
import com.getflex.ledger.model.GetOutstandingBalanceResponseTransactionIdListInner;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerExcludingCurrentBpUnbalancedCentsFeature extends BaseFeature {

  private final IdentityService identityService;
  private final LedgerService ledgerService;
  private final FulfillmentService fulfillmentService;
  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

  public CustomerExcludingCurrentBpUnbalancedCentsFeature(
      IdentityService identityService,
      LedgerService ledgerService,
      FulfillmentService fulfillmentService) {
    this.identityService = identityService;
    this.ledgerService = ledgerService;
    this.fulfillmentService = fulfillmentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    
    // Get current BP date in normalized format (YYYY-MM-01)
    ZonedDateTime currentBpDateTime = FeatureUtils.getCurrentBpDateTime();
    LocalDate currentBpDate = currentBpDateTime.toLocalDate();
    String currentBpDateStr = currentBpDate.toString();
    
    GetCustomerResponse customer = identityService.getCustomer(customerId);
    String customerPublicId = customer.getCustomerPublicId();
    
    GetOutstandingBalanceResponse outstandingBalanceResponse =
        ledgerService.getOutstandingBalanceRaw(customerPublicId, false);
    
    Long totalAmount = outstandingBalanceResponse.getTotalAmount();
    List<GetOutstandingBalanceResponseTransactionIdListInner> transactionIdList = 
        outstandingBalanceResponse.getTransactionIdList();
    
    if (transactionIdList == null || transactionIdList.isEmpty() || totalAmount == null) {
      return new FeatureOutput(totalAmount != null ? totalAmount : 0L);
    }
    
    // Process each transaction, excluding it if from this BP
    long excludedAmount = 0L;
    int excludedCount = 0;
    
    for (GetOutstandingBalanceResponseTransactionIdListInner transaction : transactionIdList) {
      try {
        String billTransactionId = transaction.getBillTransactionId();
        Long amount = transaction.getAmount();
        
        if (billTransactionId == null || amount == null) {
          continue;
        }
        
        // Get fulfillment status to retrieve the pay_date
        GetStatusResponseV2 statusResponse = fulfillmentService.getStatusCode(billTransactionId);
        
        if (statusResponse != null && statusResponse.getPayDate() != null) {
          // If pay_date matches current BP date, exclude this transaction
          if (statusResponse.getPayDate().equals(currentBpDateStr)) {
            excludedAmount += amount;
            excludedCount++;
            log.debug("Excluding transaction btxId={} amount={} payDate={} for customerId={}", 
                billTransactionId, amount, statusResponse.getPayDate(), customerId);
          }
        }
      } catch (Exception e) {
        log.warn("Failed to process transaction for customerId={} error={}", customerId, e.getMessage());
        // Continue processing other transactions
      }
    }
    
    // Calculate final amount
    Long finalAmount = totalAmount - excludedAmount;
    
    log.info("Excluded current BP transactions customerId={} currentBp={} excludedCount={} "
        + "excludedAmount={} finalAmount={}", customerId, currentBpDateStr, excludedCount, 
        excludedAmount, finalAmount);
    
    // Create metadata
    String metadataString = null;
    try {
      Map<String, Object> metadata = new HashMap<>();
      metadata.put("totalAmountBeforeExclusion", totalAmount);
      metadata.put("currentBpDate", currentBpDateStr);
      metadata.put("excludedTransactionCount", excludedCount);
      metadata.put("excludedAmount", excludedAmount);
      metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
    } catch (Exception e) {
      log.error("Failed to create metadata for customerId={}", customerId, e);
    }
    
    return new FeatureOutput(finalAmount, metadataString);
  }

  @Override
  public String getDescription() {
    return "Customer's outstanding balance excluding current billing period transactions";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
