package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureMethod;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureResult;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.utils.ThreeDomainSecureUtils;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.Model3dsAuthRecord;
import com.getflex.wallet.model.Card;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerSuccessfulChallenge3DsCountFeature extends BaseFeature {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

  private final ThreeDomainSecureUtils threeDomainSecureUtils = new ThreeDomainSecureUtils();
  private final EventRepository eventRepository;
  private final IdentityService identityService;
  private final WalletService walletService;
  private final PaymentService paymentService;

  public CustomerSuccessfulChallenge3DsCountFeature(
      EventRepository eventRepository,
      IdentityService identityService,
      WalletService walletService,
      PaymentService paymentService
  ) {
    this.eventRepository = eventRepository;
    this.identityService = identityService;
    this.walletService = walletService;
    this.paymentService = paymentService;
  }

  @Override
  public String getDescription() {
    return "Count the total number of successful challenge 3DS for the customer and card fingerprint.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.CARD_FINGERPRINT);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<Event> events = getEvents(evalParams);
    int addCard3dsCount = threeDomainSecureUtils.get3dsAddCardCount(
        evalParams,
        events,
        attempt ->
            attempt.result() == ThreeDomainSecureResult.SUCCEEDED
                && attempt.method() == ThreeDomainSecureMethod.CHALLENGE
    );
    int authCard3dsCount = get3dsAuthCount(evalParams);
    return new FeatureOutput(addCard3dsCount + authCard3dsCount);
  }

  private Integer get3dsAuthCount(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    List<Card> cards = walletService.getCardsByFingerprint(evalParams.getCardFingerprint());
    if (cards == null || cards.isEmpty()) {
      return 0;
    }
    List<Long> validPaymentMethodIds = cards.stream().map(Card::getPaymentMethodId).toList();
    List<Model3dsAuthRecord> authRecords = paymentService.get3dsAuthRecords(customer.getCustomerPublicId());
    return (int) authRecords.stream()
        .filter(authRecord -> validPaymentMethodIds.contains(authRecord.getPaymentMethodId()))
        .filter(authRecord ->
            authRecord.getStatus() != null
                && authRecord.getStatus().equalsIgnoreCase("succeeded")
        ).filter(authRecord ->
            authRecord.getOutcome() != null
                && (authRecord.getOutcome().equalsIgnoreCase("authenticated")
                || authRecord.getOutcome().equalsIgnoreCase("attempt_acknowledged"))
        ).map(Model3dsAuthRecord::getMetadata)
        .filter(Objects::nonNull)
        .filter(
            authRecordMetadata -> {
              try {
                Map<String, Object> metadataMap = OBJECT_MAPPER.convertValue(
                      authRecordMetadata,
                      new TypeReference<>() {}
                );
                if (metadataMap.containsKey("flowPreference")) {
                  Object flowPreference = metadataMap.get("flowPreference");
                  if (flowPreference instanceof String) {
                    return "challenge".equalsIgnoreCase(flowPreference.toString());
                  } else if (flowPreference instanceof Map) {
                    Map<String, Object> flowPreferenceMap = OBJECT_MAPPER.convertValue(
                        flowPreference,
                        new TypeReference<>() {}
                    );
                    if (flowPreferenceMap.containsKey("type")) {
                      Object type = flowPreferenceMap.get("type");
                      return "challenge".equalsIgnoreCase(type.toString());
                    }
                  }
                }
                return false;
              } catch (Exception e) {
                log.error("Failed to parse auth record metadata for customerId={}", evalParams.getCustomerId(), e);
                return false;
              }
            }
        ).count();
  }

  private List<Event> getEvents(EvalParams evalParams) {
    return eventRepository.findAllByNameAndCustomerIdAndEntityIdOrderByDtArrivedDesc(
        EventName.STRIPE_SETUP_ATTEMPT,
        evalParams.getCustomerId().toString(),
        evalParams.getCardFingerprint()
    );
  }
}
