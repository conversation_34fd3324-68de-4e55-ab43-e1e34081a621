package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.VantageScoreMetadata;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerEprVantageScoreFeature extends BaseOfflineFeature {

  private final OfflineFeatureRepo offlineFeatureRepo;
  // 0 is not a valid score, use it as a fallback to indicate a missing vantage score from EPR
  private static final int DEFAULT_VANTAGE_SCORE = 0;

  public CustomerEprVantageScoreFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerId = evalParams.getCustomerId().toString();

    Optional<VantageScoreMetadata> vantageScoreMetadata =
        offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            this.getOfflineFeatureName(),
            this.getPrimaryKey(evalParams)
        ).stream().map(feature -> {
          try {
            VantageScoreMetadata featureMetadata = EventMetadata.OBJECT_MAPPER.readValue(
                feature.getFeatureValue(), VantageScoreMetadata.class);
            featureMetadata.setUpdatedAt(feature.getDtCreated());
            return featureMetadata;
          } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
          }
        }).findFirst();

    if (vantageScoreMetadata.isEmpty()) {
      String msg = String.format("No vantage scores found for customer %s", customerId);
      log.warn(msg);
      return new FeatureOutput(DEFAULT_VANTAGE_SCORE);
    }

    return new FeatureOutput(vantageScoreMetadata.get().getVantageScore());
  }

  @Override
  public String getDescription() {
    return "Vantage score of customer from EPR";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_epr_vantage_score";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString() + "_";
  }
}
