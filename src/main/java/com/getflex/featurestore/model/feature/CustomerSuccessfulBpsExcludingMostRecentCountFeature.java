package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RegisterFeature
public class CustomerSuccessfulBpsExcludingMostRecentCountFeature extends BaseOfflineFeature {

  private static final int DEFAULT_VALUE = 0;


  public CustomerSuccessfulBpsExcludingMostRecentCountFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_successful_bps";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String featureValue = super.getRawFeatureValue(evalParams);
      if (StringUtils.isEmpty(featureValue)) {
        return new FeatureOutput(DEFAULT_VALUE);
      }
      return new FeatureOutput(Integer.parseInt(featureValue));
    } catch (Exception e) {
      log.warn("Failed to get number of successful BPs for customer={}", evalParams.getCustomerId(), e);
      return new FeatureOutput(DEFAULT_VALUE);
    }
  }

  @Override
  public String getDescription() {
    return "Number of successful fulfillments the customer has gone through since they joined Flex.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
