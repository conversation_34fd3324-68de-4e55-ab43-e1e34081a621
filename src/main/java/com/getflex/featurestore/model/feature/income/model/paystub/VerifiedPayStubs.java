package com.getflex.featurestore.model.feature.income.model.paystub;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VerifiedPayStubs {
  List<VerifiedPayStub> failGradePayStubs;
  List<VerifiedPayStub> passGradePayStubs;
  List<VerifiedPayStub> uncertainGradePayStubs;
}
