package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RegisterFeature
public class IsSuspendedBeforeCancelledFeature extends BaseFeature {

  private final OfferService offerService;

  private static final String SUSPEND_REASON = "SuspendCreditLine";
  private static final String CANCEL_REASON = "CancelCreditLine";

  public IsSuspendedBeforeCancelledFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    try {
      Long customerId = evalParams.getCustomerId();
      // Find all offers for the customer with all versions
      List<InternalOffer> rawOffers = new ArrayList<>(offerService.searchOffer(customerId, Boolean.TRUE));
      // Sort the offers by initiation time desc
      List<InternalOffer> offers = rawOffers.stream()
          .filter(o -> o.getInitiationTime() != null)
          .sorted((o1, o2) -> o2.getInitiationTime().compareTo(o1.getInitiationTime()))
          .toList();

      // Find the most recent cancelled offer
      Optional<InternalOffer> mostRecentCancelledOffer =
          offers.stream().filter(offer -> CANCEL_REASON.equals(offer.getDeactivationReason())).findFirst();
      // If there is a cancelled offer, check if the previous offer is a suspended offer
      if (mostRecentCancelledOffer.isPresent()) {
        InternalOffer cancelledOffer = mostRecentCancelledOffer.get();

        // If dt_created is equal to dt_terminated, it means the offer is cancelled right after suspended.
        // It is the only case we care about
        if (Objects.equals(cancelledOffer.getInitiationTime(), cancelledOffer.getTerminationTime())) {
          int cancelIndex = offers.indexOf(cancelledOffer);
          if (cancelIndex + 1 < offers.size()) {
            InternalOffer prevOffer = offers.get(cancelIndex + 1);
            return new FeatureOutput(SUSPEND_REASON.equals(prevOffer.getDeactivationReason())
                && Objects.equals(prevOffer.getOfferId(), cancelledOffer.getOfferId()));
          }
        }
      }
    } catch (Exception e) {
      throw new FeatureNotFoundException("Failed to get the value of IsSuspendedBeforeCancelled. customerId="
          + evalParams.getCustomerId());
    }
    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "Whether or not the customer is suspended before it gets cancelled";
  }
}
