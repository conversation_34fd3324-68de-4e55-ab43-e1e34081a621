package com.getflex.featurestore.model.feature.ledgerbalance;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;

@RegisterFeature
public class CustomerLifetimeDisputeCountFeature extends BaseFeature {

  /**
   * Per policy team request, exclude processing fee transactions when counting disputes,
   * but still include in cents total {@link CustomerLifetimeDisputeCentsFeature}.
   */
  private static final List<Long> DISPUTE_TRANSACTION_TYPES = List.of(
      MoneyMovementType.REPAYMENT.getValue(),
      MoneyMovementType.DOWNPAYMENT.getValue()
  );

  private LedgerService ledgerService;

  public CustomerLifetimeDisputeCountFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    List<RecordLedgerWallet> ledgerRecords = ledgerService.getWalletRecordLedgerByCustomerId(customerId);
    if (ledgerRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    List<RecordLedgerWallet> disputes = ledgerRecords.stream()
        .filter(rlw ->
            rlw.getPaymentCategoryId() != null
            && rlw.getPaymentCategoryId() == MovementCategory.DISPUTE.getValue()
            && rlw.getPaymentStatusId() == PaymentState.SETTLED.getValue()
            && DISPUTE_TRANSACTION_TYPES.contains(rlw.getMoneyMovementTypeId())
        ).toList();
    return new FeatureOutput(disputes.size());
  }

  @Override
  public String getDescription() {
    return "This feature returns the total count of lifetime disputes for a customer.";
  }
}
