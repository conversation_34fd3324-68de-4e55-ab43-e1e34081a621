package com.getflex.featurestore.model.feature.fraud;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;

@RegisterFeature
public class IsFraudTaggedFraudulentDisputeFeature extends BaseFraudTaggedFeature {

  public IsFraudTaggedFraudulentDisputeFeature(TaggingService taggingService) {
    super(taggingService);
  }

  @Override
  public FraudTagName getFraudTagName() {
    return FraudTagName.FRAUDULENT_DISPUTE;
  }

  @Override
  public String getDescription() {
    return "Account tagged as fraudulent dispute";
  }
}
