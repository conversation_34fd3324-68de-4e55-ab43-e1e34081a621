package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;

@RegisterFeature
public class BillTransactionBillAmountFromLedgerFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public BillTransactionBillAmountFromLedgerFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> transactions = ledgerService.getLedgersByBillTransactionId(
        evalParams.getBillTransactionId(), PaymentState.BILL_PAID, MoneyMovementType.PAY_BILLER);
    if (transactions == null || transactions.isEmpty()) {
      return new FeatureOutput(0);
    }
    return new FeatureOutput(transactions.get(0).getAmount().intValue());
  }

  @Override
  public String getDescription() {
    return "PAY BILLER AMOUNT from ledger";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
