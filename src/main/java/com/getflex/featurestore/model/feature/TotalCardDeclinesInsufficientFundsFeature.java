package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.payment.model.DeclineCodeEnum;

@RegisterFeature
public class TotalCardDeclinesInsufficientFundsFeature extends BaseFeature {

  private final PaymentService paymentService;

  public TotalCardDeclinesInsufficientFundsFeature(PaymentService paymentService) {
    super();
    this.paymentService = paymentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(paymentService.getTotalUserPaymentsDeclined(
      evalParams.getCustomerId(), DeclineCodeEnum.INSUFFICIENT_FUNDS));
  }

  @Override
  public String getDescription() {
    return "Total number of card declines due to insufficient funds";
  }

  @Override
  public FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }
}
