package com.getflex.featurestore.model.feature;

import com.getflex.autopay.model.EpisodicToggleStatus;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerIsAutopayOnFeature extends BaseFeature {

  private final AutopayService autopayService;

  public CustomerIsAutopayOnFeature(AutopayService autopayService) {
    this.autopayService = autopayService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      EpisodicToggleStatus status = autopayService.getCustomerEpisodicToggle(evalParams.getCustomerId())
          .getOptInStatus();
      return new FeatureOutput(status != null && status.equals(EpisodicToggleStatus.AUTOPAY_ON));
    } catch (InternalDependencyFailureException e) {
      log.warn("error getting autopay status for customer_id={}, default to False", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
  }

  @Override
  public String getDescription() {
    return "Customer's autopay status based on autopay episodic toggle";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
