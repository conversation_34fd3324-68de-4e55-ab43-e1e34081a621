package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiPredicate;

public abstract class BaseFundsOutFundsInCardCountFeature extends BaseFeature {

  private final WalletService walletService;
  private final IdentityService identityService;
  private final LedgerUtils ledgerUtils;
  protected final LookbackDurationFeatureParams parameters;

  public BaseFundsOutFundsInCardCountFeature(
      WalletService walletService,
      IdentityService identityService,
      LedgerUtils ledgerUtils,
      LookbackDurationFeatureParams parameters
  ) {
    this.walletService = walletService;
    this.identityService = identityService;
    this.ledgerUtils = ledgerUtils;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    Map<Long, String> paymentMethodIdFingerprintMap = paymentMethodIdFingerprintMap(customerId);
    OffsetDateTime dtCreatedStartRange = OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()));
    List<RecordLedger> fundsOutRecords = ledgerUtils.getFundsOutRecords(customerId, dtCreatedStartRange);
    if (fundsOutRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    List<RecordLedgerMetadata> fundsOutMetadataList = toRecordLedgerMetadataList(
        fundsOutRecords,
        paymentMethodIdFingerprintMap,
        Boolean.TRUE
    );
    List<RecordLedger> fundsInRecords = ledgerUtils.getFundsInRecords(customerId, null);
    if (fundsInRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    List<RecordLedgerMetadata> fundsInMetadataList = toRecordLedgerMetadataList(
        fundsInRecords,
        paymentMethodIdFingerprintMap,
        Boolean.FALSE
    );
    long count = fundsOutMetadataList.stream()
        .filter(fundsOutMetadata ->
            fundsInMetadataList.stream()
                .anyMatch(fundsInMetadata ->
                    getComparisonPredicate().test(fundsOutMetadata, fundsInMetadata)
                )
        ).count();
    return new FeatureOutput((int) count);
  }

  protected abstract BiPredicate<RecordLedgerMetadata, RecordLedgerMetadata> getComparisonPredicate();

  public record RecordLedgerMetadata(String fingerprint, OffsetDateTime timestamp) {

  }

  private Map<Long, String> paymentMethodIdFingerprintMap(Long customerId) {
    GetCustomerResponse customer = identityService.getCustomer(customerId);
    List<Card> cards = walletService.getCards(customer.getCustomerPublicId());
    if (cards == null || cards.isEmpty()) {
      return new HashMap<>();
    }
    return cards.stream().collect(
        HashMap::new,
        (map, card) -> map.put(card.getPaymentMethodId(), card.getFingerprint()),
        HashMap::putAll
    );
  }

  private List<RecordLedgerMetadata> toRecordLedgerMetadataList(
      List<RecordLedger> recordLedgers,
      Map<Long, String> paymentMethodIdFingerprintMap,
      Boolean fundsOut
  ) {
    return recordLedgers.stream()
        .map(recordLedger ->
            toRecordLedgerMetadata(
                recordLedger,
                paymentMethodIdFingerprintMap,
                fundsOut ? recordLedger.getToPaymentMethodId() : recordLedger.getFromPaymentMethodId()
            )
        ).filter(Objects::nonNull)
        .toList();
  }

  private RecordLedgerMetadata toRecordLedgerMetadata(
      RecordLedger recordLedger,
      Map<Long, String> paymentMethodIdFingerprintMap,
      Long paymentMethodId
  ) {
    if (paymentMethodIdFingerprintMap.containsKey(paymentMethodId)) {
      return new RecordLedgerMetadata(
          paymentMethodIdFingerprintMap.get(paymentMethodId),
          recordLedger.getDtCreated()
      );
    } else {
      Card card = walletService.getCard(paymentMethodId);
      if (card == null || card.getFingerprint() == null) {
        return null;
      }
      return new RecordLedgerMetadata(card.getFingerprint(), recordLedger.getDtCreated());
    }
  }
}