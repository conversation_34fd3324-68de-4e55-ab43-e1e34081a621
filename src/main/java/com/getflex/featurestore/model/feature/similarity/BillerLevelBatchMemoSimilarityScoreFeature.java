package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.SimilarityScoreBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;


@RegisterFeature
public class BillerLevelBatchMemoSimilarityScoreFeature extends SimilarityScoreBaseFeature {

  public BillerLevelBatchMemoSimilarityScoreFeature(OfflineFeatureRepo offlineFeatureRepo
  ) {
    super(offlineFeatureRepo);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "biller_level_batch_memo_history";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getBillerId().toString();
  }

  @Override
  public String getDescription() {
    return "Get the similarity score between batch memo and biller level batch memo history list";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ID);
  }
}

