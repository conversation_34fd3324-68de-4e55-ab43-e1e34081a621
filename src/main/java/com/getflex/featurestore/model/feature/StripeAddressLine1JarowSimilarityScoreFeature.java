package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RegisterFeature
public class StripeAddressLine1JarowSimilarityScoreFeature extends BaseJarowSimilarityScoreFeature {

  public StripeAddressLine1JarowSimilarityScoreFeature(IdentityService identityService) {
    super(identityService);
  }

  @Override
  protected String getLeftValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    String addressLine1Identity = customer.getAddressLine1();
    if (StringUtils.isEmpty(addressLine1Identity)) {
      log.warn("addressLine1 is null in identity service for customerId={}", evalParams.getCustomerId());
      return null;
    }
    return addressLine1Identity.trim().toLowerCase();
  }

  @Override
  protected String getRightValue(EvalParams evalParams) {
    return evalParams.getStripeAddressLine1().trim().toLowerCase();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_ADDRESS_LINE_1);
  }

  @Override
  public String getDescription() {
    return "Jaro-Winkler similarity score between Stripe payment address line 1 "
        + "and Flex identity customer address line 1";
  }
}
