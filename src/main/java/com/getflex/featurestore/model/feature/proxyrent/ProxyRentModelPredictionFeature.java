package com.getflex.featurestore.model.feature.proxyrent;

import static com.getflex.featurestore.utils.FlexConstant.CSV_UTF8_CONTENT_TYPE;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.ProxyRentModelInput;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseModelFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.JSONObject;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@Slf4j
@RegisterFeature
public class ProxyRentModelPredictionFeature extends BaseModelFeature<ProxyRentModelInput> {

  private static final List<ProxyRentModelInput> MODEL_INPUT_FEATURES_LIST =
      new ArrayList<>(EnumSet.allOf(ProxyRentModelInput.class));

  private final ServiceConfig serviceConfig;

  public ProxyRentModelPredictionFeature(SageMakerRuntimeClient sageMaker, ExecutorService executorService,
      FeatureFactory featureFactory, ServiceConfig serviceConfig) {
    super(sageMaker, executorService, featureFactory);
    this.serviceConfig = serviceConfig;
  }

  @Override
  protected Map<ProxyRentModelInput, Number> getModelFeatureValues(EvalParams evalParams) {
    Map<ProxyRentModelInput, Number> valuesMap = Collections.synchronizedMap(new HashMap<>());

    List<Callable<Pair<ProxyRentModelInput, Number>>> tasks = getModelFeaturesList().stream()
        .map(f -> (Callable<Pair<ProxyRentModelInput, Number>>) () -> {
          Number value = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          return Pair.of(f, value);
        })
        .toList();

    List<Future<Pair<ProxyRentModelInput, Number>>> futures;
    try {
      futures = executorService.invokeAll(tasks);
      for (Future<Pair<ProxyRentModelInput, Number>> future : futures) {
        Pair<ProxyRentModelInput, Number> entry = future.get();
        valuesMap.put(entry.getKey(), entry.getValue());
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return valuesMap;
  }

  @Override
  protected FeatureOutput toFeatureOutput(InvokeEndpointResponse response,
      Map<ProxyRentModelInput, Number> featureValuesMap) {
    if (CSV_UTF8_CONTENT_TYPE.equalsIgnoreCase(response.contentType())) {
      String value = response.body().asUtf8String();
      log.info("raw output of ProxyRentModelPredictionFeature is {}", value);

      // Convert from dollars to cents
      double rentCents = Double.parseDouble(value) * 100;

      // Enforce min of $100 max of $10000
      rentCents = Double.min(rentCents, 1000000);
      rentCents = Double.max(rentCents, 10000);

      // Round to the closest Long value
      return new FeatureOutput(Math.round(rentCents), new JSONObject(featureValuesMap).toJSONString());
    }

    throw new RuntimeException("Unsupported Sagemaker output content type");
  }

  @Override
  protected String toCsv(List<ProxyRentModelInput> featuresList, Map<ProxyRentModelInput, Number> featureValuesMap) {
    String featureString = featuresList.stream().map(f -> {
      Number featureValue = featureValuesMap.get(f);
      // Python to interpret NaN as null
      return featureValue != null ? featureValue.toString() : String.valueOf(Double.NaN);
    }).collect(Collectors.joining(","));
    
    // TODO remove post UAT
    log.info("ProxyRentModelInput feature string is {}", featureString);
    return featureString;
  }

  @Override
  public List<ProxyRentModelInput> getModelFeaturesList() {
    return MODEL_INPUT_FEATURES_LIST;
  }

  @Override
  protected String getEndpointName() {
    return serviceConfig.getProxyRentModelEndpoint();
  }

  @Override
  public String getDescription() {
    return "Proxy Rent amount live model evaluation";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.ALLOY_REPORT_URL, EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ID);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }
}
