package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.model.feature.base.BaseDeviceMetricFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.service.EventService;

@RegisterFeature(value = "NumberOfDeviceIdChangesFeature")
@RegisterFeature(
    value = "NumberOfDeviceIdChangesFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfDeviceIdChangesFeature extends BaseDeviceMetricFeature {

  public NumberOfDeviceIdChangesFeature(EventService eventService) {
    super(eventService, new LookbackDurationFeatureParams(null));
  }

  public NumberOfDeviceIdChangesFeature(EventService eventService,
      LookbackDurationFeatureParams parameters) {
    super(eventService, parameters);
  }

  @Override
  protected String extractProperty(DeviceMetadata deviceMetadata) {
    return deviceMetadata.getDeviceId();
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the amount of times a customer has changed their device id."
        + " Unless specified, time window defaults to lifetime of customer's account."
        + " Time window should follow the convention as"
        + " NumberOfDeviceIdChangesFeature_TimeWindowIsoDurationFormat";
  }
}
