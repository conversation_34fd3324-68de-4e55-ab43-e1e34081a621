package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.SettlementService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.settlement.model.DdaDecision;
import java.util.List;

@RegisterFeature
public class BillTransactionHasDuplicatedDdaTransactionFeature extends BaseFeature {

  private final SettlementService settlementService;

  public BillTransactionHasDuplicatedDdaTransactionFeature(SettlementService settlementService) {
    super();
    this.settlementService = settlementService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<DdaDecision> txs = settlementService.getDdaDecisionsByBillTransactionId(
        evalParams.getBillTransactionId());
    if (txs == null || txs.isEmpty()) {
      return new FeatureOutput(false);
    }
    List<DdaDecision> filteredTxs = txs.stream().filter(t ->
        (t.getTransactionTypeId() != null) && (t.getTransactionTypeId() == 1L)
            && (t.getAmount() > 300L))
        .toList();
    return new FeatureOutput(!filteredTxs.isEmpty());
  }

  @Override
  public String getDescription() {
    return "If the PAY_BILLER record already settled in ledger with given bill transaction id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
