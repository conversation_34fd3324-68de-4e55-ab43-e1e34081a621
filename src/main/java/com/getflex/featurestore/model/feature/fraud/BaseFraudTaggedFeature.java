package com.getflex.featurestore.model.feature.fraud;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;
import com.getflex.tagging.model.CustomerTagDataItem;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;

@Slf4j
public abstract class BaseFraudTaggedFeature extends BaseFeature {

  private static final String KEY_TAG_NAME = "tag_name";
  private static final String KEY_DT_TAGGED = "dt_tagged";
  private static final String KEY_TAGGED_BY = "tagged_by";

  private final TaggingService taggingService;

  public BaseFraudTaggedFeature(TaggingService taggingService) {
    this.taggingService = taggingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<CustomerTagDataItem> tags = new ArrayList<>(taggingService.getFraudTags(
        evalParams.getCustomerId()));
    if (tags.isEmpty()) {
      return new FeatureOutput(false);
    }

    CustomerTagDataItem fraudTag = getFraudTag(tags);
    if (fraudTag != null) {
      return new FeatureOutput(true, getMetadata(fraudTag));
    }

    return new FeatureOutput(false);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private static void sortByBeginDate(List<CustomerTagDataItem> tags) {
    tags.sort(Comparator.comparing(CustomerTagDataItem::getCustomerTagEffectiveBeginDate).reversed());
  }

  private CustomerTagDataItem getFraudTag(List<CustomerTagDataItem> tags) {
    if (tags.size() > 1) {
      sortByBeginDate(tags);
    }
    return tags.stream()
        .filter(tag -> Objects.equals(tag.getTagName(), getFraudTagName().name()))
        .findFirst()
        .orElse(null);
  }

  private String getMetadata(CustomerTagDataItem fraudTag) {
    JSONObject metadata = new JSONObject();
    metadata.put(KEY_TAG_NAME, fraudTag.getTagName());

    //TODO @andy: Temporary until tagging service exposes dt_created (OffsetDateTime).
    LocalDateTime effectiveBeginDateTime = Objects.requireNonNull(fraudTag.getCustomerTagEffectiveBeginDate())
        .atStartOfDay();
    OffsetDateTime dtTagged = effectiveBeginDateTime.atOffset(ZoneOffset.UTC);
    metadata.put(KEY_DT_TAGGED, dtTagged.toString());
    metadata.put(KEY_TAGGED_BY, fraudTag.getCustomerTagCreatedBy());
    return metadata.toString();
  }

  public abstract FraudTagName getFraudTagName();
}
