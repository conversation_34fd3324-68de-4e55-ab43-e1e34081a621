package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;

@RegisterFeature
public class StripeZipCodeMatchV2Feature extends BaseFeature {
  private final IdentityService identityService;

  public StripeZipCodeMatchV2Feature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    if (customer.getZip() == null) {
      throw new InternalDependencyFailureException(String.format("Customer zip code is null for customerId=%s",
          evalParams.getCustomerId()));
    }
    String customerZip = customer.getZip().replaceAll("\\s", "");
    String stripeZipCode = evalParams.getStripeZipCode().replaceAll("\\s", "").split("-")[0];
    if (stripeZipCode.equals(customerZip)) {
      return new FeatureOutput(true, null);
    }

    return new FeatureOutput(false, null);
  }

  @Override
  public String getDescription() {
    return "Check Stripe payment zip code and Flex customer profile zip code match";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_ZIP_CODE);
  }
}
