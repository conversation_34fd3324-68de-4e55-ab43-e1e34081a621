package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.utils.AlloyUtils.ALLOY_ONLY_BOOLEAN_FEATURES;
import static com.getflex.featurestore.utils.AlloyUtils.ALLOY_ONLY_NUMBER_FEATURES;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.AlloyUtils;
import java.util.HashMap;
import java.util.Map;

/**
 * This feature extracts applicable FlexScoreInputFeature from Alloy report and return an object carrying the feature
 * name to value map.
 * <br>
 * Note: this feature downloads alloy report using S3 client after parsing http based S3 URI. So the signature on the
 * URI doesn't matter.
 */
@RegisterFeature
public class CreditReportFeature extends BaseFeature {

  private AlloyUtils alloyUtils;

  public CreditReportFeature(AlloyUtils alloyUtils) {
    this.alloyUtils = alloyUtils;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    AlloyReport alloyReport = alloyUtils.downloadAlloyReport(evalParams.getAlloyReportUrl());

    Map<String, Object> result = new HashMap<>(
        ALLOY_ONLY_NUMBER_FEATURES.size() + ALLOY_ONLY_BOOLEAN_FEATURES.size() + 1);
    ALLOY_ONLY_NUMBER_FEATURES.forEach(f -> {
      Object value = f.getFormula().apply(alloyReport, null);
      result.put(f.name(), value);
    });
    ALLOY_ONLY_BOOLEAN_FEATURES.forEach(f -> {
      Object value = f.getFormula().apply(alloyReport, null);
      result.put(f.name(), value);
    });

    return new FeatureOutput(result);
  }

  @Override
  public String getDescription() {
    return "Get FlexScoreInputFeature extracted from Alloy report, same as what is fed into flexscore model";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.ALLOY_REPORT_URL);
  }
}
