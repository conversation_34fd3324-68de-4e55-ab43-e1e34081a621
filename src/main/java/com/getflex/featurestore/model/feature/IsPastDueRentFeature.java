package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.client.ApiException;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseBillPayRestrictionResult;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsPastDueRentFeature extends BaseFeature {

  private final BillingService billingService;
  private final IdentityService identityService;

  public IsPastDueRentFeature(BillingService billingService, IdentityService identityService) {
    this.billingService = billingService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    Long billerId;
    if (evalParams.getBillerId() != null) {
      billerId = evalParams.getBillerId();
    } else {
      try {
        billerId = identityService.getBillerByBillerAccountId(billerAccountId).getBillerId();
      } catch (Exception e) {
        log.error("Error while fetching biller for biller account id: {}", billerAccountId);
        return new FeatureOutput(false);
      }
    }
    try {
      ComGetflexBillingDtoResponseBillPayRestrictionResult resp =
          billingService.getBillPayRestrictionStatusWithBillerId(billerId, billerAccountId);
      return new FeatureOutput(resp.getPastDueRent() == Boolean.TRUE);
    } catch (InternalDependencyFailureException e) {
      Throwable cause = e.getCause();
      // 501 NOT_IMPLEMENTED means that we do not support getting BP restriction status for this customer
      if (cause instanceof ApiException && ((ApiException) cause).getCode() == 501) {
        log.warn("Exception calling BP restriction API: {}", e.getMessage(), e);
      } else {
        log.error("Exception calling BP restriction API: {}", e.getMessage(), e);
      }
      return new FeatureOutput(false);
    }
  }

  @Override
  public String getDescription() {
    return "Is the customer past due rent, determined by billing service.";
  }
}
