package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.feature.base.BaseCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.payment.model.DeclineCodeEnum;
import java.util.List;

@RegisterFeature(
    value = "CardVelocityExceededCardDeclinesCountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class CardVelocityExceededCardDeclinesCountFeature extends BaseCardDeclinesCountFeature {

  public CardVelocityExceededCardDeclinesCountFeature(PaymentService paymentService,
      LookbackDurationFeatureParams parameters) {
    super(paymentService, parameters);
  }

  @Override
  protected List<DeclineCodeEnum> getDeclineCodes() {
    return List.of(DeclineCodeEnum.CARD_VELOCITY_EXCEEDED);
  }

  @Override
  public String getDescription() {
    return "Card velocity exceeded card declines count in past specified time window";
  }
}
