package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.similarity.JaroWinklerDistance;

public abstract class BaseJarowSimilarityScoreFeature extends BaseFeature {

  private final JaroWinklerDistance jaroWinklerDistance = new JaroWinklerDistance();

  protected final IdentityService identityService;

  public BaseJarowSimilarityScoreFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  protected abstract String getLeftValue(EvalParams evalParams);

  protected abstract String getRightValue(EvalParams evalParams);

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String left = getLeftValue(evalParams);
    if (StringUtils.isEmpty(left)) {
      return getDefaultValue();
    }

    String right = getRightValue(evalParams);
    if (StringUtils.isEmpty(right)) {
      return getDefaultValue();
    }

    return new FeatureOutput(jaroWinklerDistance.apply(left, right) * 100);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(-1.0, null);
  }
}
