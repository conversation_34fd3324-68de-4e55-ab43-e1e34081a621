package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.GeoLocation;
import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.dao.repo.GeoIpLocationRepo;
import com.getflex.featurestore.model.feature.base.BaseDeviceMetricFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.service.EventService;

@RegisterFeature(
    value = "NumberOfIpStateChangesFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class NumberOfIpStateChangesFeature extends BaseDeviceMetricFeature {

  private final GeoIpLocationRepo geoIpLocationRepo;

  public NumberOfIpStateChangesFeature(
      EventService eventService,
      GeoIpLocationRepo geoIpLocationRepo,
      LookbackDurationFeatureParams parameters) {
    super(eventService, parameters);
    this.geoIpLocationRepo = geoIpLocationRepo;
  }

  @Override
  protected String extractProperty(DeviceMetadata deviceMetadata) {
    return deviceMetadata.getIpAddress();
  }

  @Override
  protected String mapFunction(DdbEvent event) {
    String ipAddress = getMetric(event.getMetadata());
    GeoLocation geoLocation = geoIpLocationRepo.findGeoLocationDataByIpAddress(ipAddress);
    if (geoLocation != null) {
      return geoLocation.getSubdivision1Name();
    }
    return null;
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the number of times a customer's IP address state has changed."
        + " Time window should follow the convention as"
        + " NumberOfIpStateChangesFeature_TimeWindowIsoDurationFormat";
  }
}
