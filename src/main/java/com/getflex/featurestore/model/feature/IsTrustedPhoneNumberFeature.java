package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.IsTrustedEntityBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;

@RegisterFeature
public class IsTrustedPhoneNumberFeature extends IsTrustedEntityBaseFeature {

  public IsTrustedPhoneNumberFeature(OfflineFeatureRepo offlineFeatureRepo,
                                        IdentityService identityService) {
    super(offlineFeatureRepo, identityService);
  }

  @Override
  public String getDescription() {
    return "Check if given phone number is trusted, matching to the trusted_phone_number_to_customer_id offline "
        + "feature. primary key is phone number and feature value is the customer_public_id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "trusted_phone_number_to_customer_id";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    GetCustomerResponse customer = this.identityService.getCustomer(customerId);

    return customer.getPhone();
  }
}
