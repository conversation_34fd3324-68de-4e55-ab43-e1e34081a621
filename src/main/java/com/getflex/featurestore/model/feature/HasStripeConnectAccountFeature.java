package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class HasStripeConnectAccountFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;

  public HasStripeConnectAccountFeature(IdentityService identityService, WalletService walletService) {
    this.identityService = identityService;
    this.walletService = walletService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public String getDescription() {
    return "Indicates whether a customer has a Stripe Connect account";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerPublicId = identityService.getCustomer(evalParams.getCustomerId()).getCustomerPublicId();
    Boolean hasStripeConnectAccount = walletService.hasStripeConnectAccount(customerPublicId);
    return new FeatureOutput(hasStripeConnectAccount);
  }
}