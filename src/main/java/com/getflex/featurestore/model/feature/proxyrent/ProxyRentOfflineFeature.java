package com.getflex.featurestore.model.feature.proxyrent;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;

public abstract class ProxyRentOfflineFeature extends BaseOfflineFeature {

  private final ProxyRentUtils proxyRentUtils;

  protected ProxyRentOfflineFeature(ProxyRentUtils proxyRentUtils, OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.proxyRentUtils = proxyRentUtils;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      return new FeatureOutput(Double.valueOf(rawOfflineString));
    } catch (Exception e) {
      return new FeatureOutput(null);
    }
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ID);
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return proxyRentUtils.formatPrimaryKey(getOfflineFeatureName(), evalParams);
  }
}
