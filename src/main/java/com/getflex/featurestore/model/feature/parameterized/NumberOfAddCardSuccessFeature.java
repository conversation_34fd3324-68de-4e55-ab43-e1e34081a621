
package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature(
    value = "NumberOfAddCardSuccessFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfAddCardSuccessFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;
  private final LookbackDurationFeatureParams parameters;

  public NumberOfAddCardSuccessFeature(
      IdentityService identityService,
      WalletService walletService,
      LookbackDurationFeatureParams parameters
  ) {
    this.identityService = identityService;
    this.walletService = walletService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerPublicId = identityService.getCustomer(evalParams.getCustomerId()).getCustomerPublicId();
    OffsetDateTime lookbackWindow = OffsetDateTime.now().minus(Duration.parse(this.parameters.lookbackWindow()));
    List<Card> cards = walletService.getCards(customerPublicId);
    int addCardSuccess = (int) cards.stream()
        .filter(card -> card.getDtCreated() != null && card.getDtCreated().isAfter(lookbackWindow))
        .count();
    return new FeatureOutput(addCardSuccess);
  }

  @Override
  public String getDescription() {
    return "Number of successful add card of a customer for a specified lookback window";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
