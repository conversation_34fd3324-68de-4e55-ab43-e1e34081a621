package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.wallet.model.Card;
import java.util.List;

@RegisterFeature
public class RealtimeCardLinkageCountFeature extends BaseFeature {

  private final WalletService walletService;

  public RealtimeCardLinkageCountFeature(WalletService walletService) {
    this.walletService = walletService;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<Card> cards = walletService.getCardsByFingerprint(evalParams.getCardFingerprint());
    if (cards == null || cards.isEmpty()) {
      return new FeatureOutput(0);
    }
    int result = (int) cards.stream().map(Card::getStripeCustomerId).distinct().count();
    return new FeatureOutput(result);
  }

  @Override
  public String getDescription() {
    return "Given a card fingerprint, how many distinct customers are linked with the same card fingerprint";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CARD_FINGERPRINT);
  }
}
