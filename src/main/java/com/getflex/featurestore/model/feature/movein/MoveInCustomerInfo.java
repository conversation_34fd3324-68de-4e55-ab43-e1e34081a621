package com.getflex.featurestore.model.feature.movein;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents move-in customer information, including corresponding offer and loan information.
 * Supports both PAY_IN_FULL and LOAN type move-in offers.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MoveInCustomerInfo {
  
  /**
   * Whether is using move-in or not..
   */
  @JsonProperty("is_move_in")
  private boolean isMoveIn;
  
  /**
   * The type of move-in for the customer based on their recent move-in offer.
   * PAY_IN_FULL if no loan is associated, LOAN if a loan exists.
   * Null if customer is not a move-in customer.
   */
  @JsonProperty("move_in_type")
  private MoveInType moveInType;
  
  /**
   * List of all  move-in offers for the customer.
   * Empty list if customer is not a move-in customer.
   */
  @JsonProperty("move_in_offers")
  private List<MoveInOfferInfo> moveInOffers;
}
