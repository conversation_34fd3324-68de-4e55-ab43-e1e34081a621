package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class TotalLostDisputesFeature extends BaseFeature {

  private final DisputeService disputeService;

  public TotalLostDisputesFeature(DisputeService disputeService) {
    super();
    this.disputeService = disputeService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    return new FeatureOutput(disputeService.getLostDisputes(customerId).size());
  }

  @Override
  public String getDescription() {
    return "This feature returns the total number of lost disputes for a customer.";
  }
}
