package com.getflex.featurestore.model.feature;

import static java.util.Comparator.comparing;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RegisterFeature
public class CustomerLatestAcceptedCreditLineFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerLatestAcceptedCreditLineFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    try {
      Long customerId = evalParams.getCustomerId();
      // Find all offers for the customer with all versions
      List<InternalOffer> rawOffers = new ArrayList<>(offerService.searchOffer(customerId, Boolean.TRUE));

      // Find the most recent accepted offer
      Optional<InternalOffer> mostRecentAcceptedOffer = rawOffers.stream()
          .filter(o -> o.getInitiationTime() != null && OfferStateEnum.ACCEPTED.equals(o.getOfferState()))
          .sorted(comparing(InternalOffer::getInitiationTime).reversed())
          .findFirst();

      if (mostRecentAcceptedOffer.isPresent()) {
        InternalOffer acceptedOffer = mostRecentAcceptedOffer.get();
        return new FeatureOutput(acceptedOffer.getCreditLimitAmountCent());
      }
    } catch (Exception e) {
      throw new RuntimeException("Exception getting CustomerLatestAcceptedCreditLineFeature. customerId="
          + evalParams.getCustomerId());
    }
    throw new FeatureNotFoundException("CustomerLatestAcceptedCreditLineFeature value not found. customerId="
        + evalParams.getCustomerId());
  }

  @Override
  public String getDescription() {
    return "The credit line of the customer's most recent accepted offer.";
  }
}
