package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.getflex.cipher.util.CipherUtil;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.socure.SocureService;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Deprecated: Verification Service is now the owner of KYC process. Use KycFeature instead.
 */
@Deprecated
@RegisterFeature
public class SocureFeature extends BaseFeature {

  /**
   * Key is PII property name in <code>bestMatchedEntity</code> object.
   * Value is regex pattern that validate data format (can be refactored to a string predicate if necessary).
   */
  private static final Map<String, Pattern> PII_TO_ENCRYPT = Map.of(
      SocureService.SOCURE_SSN_FIELD, Pattern.compile("\\d{3}-?\\d{3}-?\\d{4}"),
      SocureService.SOCURE_DOB_FIELD, Pattern.compile("\\d{4}[-/]?\\d{2}[-/]?\\d{2}")
  );

  private final IdentityService identityService;
  private final SocureService socureService;
  private final CipherUtil cipherUtil;

  public SocureFeature(IdentityService identityService, SocureService socureService, CipherUtil cipherUtil) {
    this.identityService = identityService;
    this.socureService = socureService;
    this.cipherUtil = cipherUtil;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    CustomerInfo customer = identityService.getCompleteCustomerInfo(evalParams);

    JsonNode rawSocureOutput = socureService.runKyc(SocureService.buildSocureRequest(customer,
        evalParams.getIpAddress()));

    sanitize(rawSocureOutput);

    return new FeatureOutput(rawSocureOutput);
  }

  /**
   * Modify <code>bestMatchedEntity</code> within <code>rawSocureOutput</code> so that it ONLY contains PII that is
   * both allowlisted and encrypted.
   *
   * @param rawSocureOutput
   */
  private void sanitize(JsonNode rawSocureOutput) {
    ObjectNode bestMatchedEntity = (ObjectNode) rawSocureOutput.at(SocureService.SOCURE_BEST_MATCHED_ENTITY_PATH);
    if (!bestMatchedEntity.isMissingNode() & bestMatchedEntity.isObject()) {
      // save PII of interest into a map
      final Map<String, String> piiMap = PII_TO_ENCRYPT.keySet().stream()
          .collect(Collectors.toMap(Function.identity(), piiKey -> bestMatchedEntity.get(piiKey).textValue()));

      // remove all
      bestMatchedEntity.removeAll();

      // put back cipher
      PII_TO_ENCRYPT.forEach((key, pattern) -> {
        String pii = piiMap.get(key);
        if (pii != null && pattern.matcher(pii).matches()) {
          bestMatchedEntity.put(key, cipherUtil.encryptWithCache(pii));
        }
      });
    }
  }

  @Override
  public String getDescription() {
    return "Get Socure KYC response";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.IP_ADDRESS, EvalParamKey.SSN_CIPHER, EvalParamKey.DOB_CIPHER);
  }
}
