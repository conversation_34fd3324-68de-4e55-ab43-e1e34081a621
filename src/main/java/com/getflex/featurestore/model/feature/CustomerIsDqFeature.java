package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerIsDqFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerIsDqFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Boolean customerDq = offerService.getDqStatus(evalParams.getCustomerId()).getIsDelinquent();
    if (customerDq != null) {
      return new FeatureOutput(customerDq);
    }
    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "Check to see if the customer is DQ";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
