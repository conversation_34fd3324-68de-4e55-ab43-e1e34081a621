package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.decisionengine.async.model.CheckpointDecisionLog;
import com.getflex.decisionengine.async.model.GetCheckpointDecisionLogsResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.OnboardingOfferSocureFeatureDto;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.StringToJsonCustomParser;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class OnboardingOfferSocureFeature extends BaseFeature {
  private final OfferService offerService;
  private final DecisionEngineService decisionEngineService;
  private static final String CHECKPOINT_NAME = "EvaluateKycCheckpoint";
  private static final String FEATURE_KEY = "SocureFeature";
  private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

  public OnboardingOfferSocureFeature(
      OfferService offerService, DecisionEngineService decisionEngineService) {
    super();
    this.offerService = offerService;
    this.decisionEngineService = decisionEngineService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    Long customerId = evalParams.getCustomerId();

    // First try to get the response from DynamoDB via decision engine API
    CompletableFuture<GetCheckpointDecisionLogsResponse> decisionLogsResp =
        decisionEngineService.getCheckpointDecisionLogs(
        customerId, CHECKPOINT_NAME, null);
    try {
      List<CheckpointDecisionLog> kycCheckpointLogs = decisionLogsResp.join().getCheckpointDecisionLogs();
      if (!kycCheckpointLogs.isEmpty()) {
        CheckpointDecisionLog kycCheckpointLog = kycCheckpointLogs.stream()
            .max(Comparator.comparing(CheckpointDecisionLog::getDtCreated))
            .orElse(null);
        Object rawSocureFeature = kycCheckpointLog.getFeatureValues().get(FEATURE_KEY);

        // First, try to directly parse it
        try {
          JsonNode socureNode;
          if (rawSocureFeature instanceof CharSequence) {
            socureNode = objectMapper.readTree(rawSocureFeature.toString());
          } else {
            socureNode = objectMapper.valueToTree(rawSocureFeature);
          }
          return new FeatureOutput(objectMapper.valueToTree(OnboardingOfferSocureFeatureDto.fromRaw(socureNode)));
        } catch (JsonProcessingException e) {
          log.warn("Failed to parse SocureFeature Json using ObjectMapper for customerId={}", customerId, e);
        }

        // Fallback to customized parsing
        try {
          JsonNode socureNode = new StringToJsonCustomParser().parseAsJsonNode(rawSocureFeature.toString());
          return new FeatureOutput(objectMapper.valueToTree(OnboardingOfferSocureFeatureDto.fromRaw(socureNode)));
        } catch (Exception e) {
          log.warn("Failed to parse SocureFeature Json using CustomParser for customerId={}", customerId, e);
        }
      }
    } catch (Exception e) {
      log.warn("Failed to get kyc checkpoint logs from DDB. Fetch from offer instead. customerId={}", customerId, e);
    }

    // If not, try to get it from offer evaluation context
    InternalOffer offer;
    if (billerAccountId != null) {
      try {
        offer = offerService.getOfferByBillerAccountId(billerAccountId);
      } catch (InternalDependencyFailureException e) {
        log.warn("Could not fetch offer from billerAccountId={}. Looking for offer using customerId={}",
            billerAccountId, customerId);
        offer = offerService.getOfferByCustomerId(customerId);
      }
    } else {
      offer = offerService.getOfferByCustomerId(customerId);
    }

    if (offer.getEvaluationContext() == null) {
      log.warn("Evaluation context is null for offerId={}, offerVersion={}, offerState={}",
          offer.getOfferId(), offer.getOfferVersion(), offer.getOfferState());
      return null;
    }
    Boolean isRoot = offer.getEvaluationContext().getIsRootEvaluation();
    String rootOfferId = offer.getEvaluationContext().getRootOfferId();

    if (Boolean.FALSE.equals(isRoot) && rootOfferId != null) {
      offer = offerService.getRootOffer(rootOfferId, offer.getEvaluationContext().getRootOfferVersion());
      if (offer.getEvaluationContext() == null) {
        log.warn("Evaluation context is null for root offer offerId={}, offerVersion={}, offerState={}",
            offer.getOfferId(), offer.getOfferVersion(), offer.getOfferState());
        return null;
      }
    }
    try {
      InternalOfferAllOfEvaluationContext ctx = offer.getEvaluationContext();
      return new FeatureOutput(
          objectMapper.valueToTree(
              OnboardingOfferSocureFeatureDto.fromEvaluationContext(ctx)
          ));
    } catch (Exception e) {
      log.error("Failed to convert offer evaluation context into JsonNode for offerId={}",
          offer.getOfferId());
      return null;
    }
  }

  @Override
  public String getDescription() {
    return "This feature returns the kyc Socure response of the given customer in JsonNode format.";
  }
}
