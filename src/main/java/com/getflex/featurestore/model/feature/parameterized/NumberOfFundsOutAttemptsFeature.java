
package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseWalletFundsCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.function.Predicate;

@RegisterFeature(
    value = "NumberOfFundsOutAttemptsFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfFundsOutAttemptsFeature extends BaseWalletFundsCountFeature {

  public NumberOfFundsOutAttemptsFeature(LedgerService ledgerService, LookbackDurationFeatureParams parameters) {
    super(ledgerService, parameters);
  }

  @Override
  public String getDescription() {
    return "Number of funds out attempts in past specified time window";
  }

  @Override
  protected APIretrieveLedgerByCustomerIdRequest getRetrieveLedgerRequest(
      Long customerId,
      OffsetDateTime dtCreatedStartRange
  ) {
    return APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRange)
        .includeRefund(IncludeRefundEnum.INCLUDE)
        .build();
  }

  @Override
  protected Predicate<RecordLedger> getFilterPredicate() {
    return recordLedger -> validFundsOutRecord(recordLedger)
        && validPaymentStatuses(recordLedger, PaymentState.SETTLED, PaymentState.DECLINED);
  }

  private Boolean validFundsOutRecord(RecordLedger recordLedger) {
    if (recordLedger.getMoneyMovementTypeId() == null || recordLedger.getPaymentCategoryId() == null) {
      return false;
    }
    return (recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_OUT.getValue()
        && recordLedger.getPaymentCategoryId() == MovementCategory.CHARGE.getValue())
        || (recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_IN.getValue()
        && recordLedger.getPaymentCategoryId() == MovementCategory.REFUND.getValue());
  }
}
