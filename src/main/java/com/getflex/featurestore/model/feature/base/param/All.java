package com.getflex.featurestore.model.feature.base.param;

import com.getflex.featurestore.model.EvalParamKey;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record All(Set<? extends ParamRequired> criteriaList) implements ParamRequired {
  public All {
    criteriaList = Set.copyOf(criteriaList);
  }

  public static All of(EvalParamKey... paramKeys) {
    return new All(Arrays.stream(paramKeys).map(Single::new).collect(Collectors.toSet()));
  }

  /**
   * Helper function to build {@link ParamRequired} for a child feature that inherits behavior from parent.
   *
   * @param parent    {@link ParamRequired} specified by parent feature
   * @param paramKeys Additional params required by child feature
   * @return
   */
  public static All of(ParamRequired parent, EvalParamKey... paramKeys) {
    return new All(Stream.concat(Arrays.stream(paramKeys).map(Single::new), Stream.of(parent))
        .collect(Collectors.toSet()));
  }

  @Override
  public void validateEvaluationParams(Set<EvalParamKey> availableParams) {
    for (ParamRequired criteria : criteriaList) {
      criteria.validateEvaluationParams(availableParams);
    }
  }
}
