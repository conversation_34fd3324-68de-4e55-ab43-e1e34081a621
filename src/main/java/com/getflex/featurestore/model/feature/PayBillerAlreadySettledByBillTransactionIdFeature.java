package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;


@RegisterFeature
public class PayBillerAlreadySettledByBillTransactionIdFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public PayBillerAlreadySettledByBillTransactionIdFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> txs = ledgerService.getLedgersByBillTransactionId(evalParams.getBillTransactionId(),
        PaymentState.SETTLED,
        MoneyMovementType.PAY_BILLER);
    return new FeatureOutput(txs != null && !txs.isEmpty());
  }

  @Override
  public String getDescription() {
    return "If the PAY_BILLER record already settled in ledger with given bill transaction id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
