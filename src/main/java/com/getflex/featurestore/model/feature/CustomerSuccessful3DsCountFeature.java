package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureResult;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseThreeDomainSecureFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.utils.ThreeDomainSecureUtils.ThreeDomainSecureAttempt;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerSuccessful3DsCountFeature extends BaseThreeDomainSecureFeature {
  public CustomerSuccessful3DsCountFeature(EventRepository eventRepository) {
    super(eventRepository);
  }

  @Override
  public String getDescription() {
    return "Count the total number of successful 3DS for the customer and card fingerprint.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.CARD_FINGERPRINT);
  }

  @Override
  protected Predicate<ThreeDomainSecureAttempt> getFilterPredicate() {
    return attempt -> attempt.result() == ThreeDomainSecureResult.SUCCEEDED;
  }

  @Override
  protected List<Event> getEvents(EvalParams evalParams) {
    return eventRepository.findAllByNameAndCustomerIdAndEntityIdOrderByDtArrivedDesc(
        EventName.STRIPE_SETUP_ATTEMPT,
        evalParams.getCustomerId().toString(),
        evalParams.getCardFingerprint()
    );
  }
}
