package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;

@RegisterFeature
public class SelfReportedAnnualGrossIncomeCentFeature extends BaseFeature  {

  private final ObjectMapper objectMapper = new ObjectMapper();

  private final EventRepository eventRepository;

  public SelfReportedAnnualGrossIncomeCentFeature(EventRepository eventRepository) {
    this.eventRepository = eventRepository;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String verificationId = evalParams.getVerificationId();
    Optional<Event> optionalEvent = eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
        verificationId, EventName.SELF_REPORTED_INCOME_VERIFICATION);
    if (optionalEvent.isEmpty()) {
      String msg = String.format("No self reported income found for verification %s", verificationId);
      throw new FeatureNotFoundException(msg);
    }

    try {
      IncomeMetadata incomeMetadata = objectMapper.readValue(optionalEvent.get().getMetadata(), IncomeMetadata.class);
      return new FeatureOutput(Integer.valueOf(incomeMetadata.getEstimatedGrossAnnualIncomeCents()), null);
    } catch (JsonProcessingException e) {
      String msg = String.format("Failed to parse self reported income metadata for verification %s", verificationId);
      throw new EventMetadataParsingException(msg);
    }
  }

  @Override
  public String getDescription() {
    return "Self reported income used for evaluating income eligibility.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.VERIFICATION_ID);
  }
}
