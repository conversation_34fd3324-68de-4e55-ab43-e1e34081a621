package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CurrentCardSameAsDownpaymentCardFeature extends BaseFeature {

  private final WalletService walletService;
  private final LedgerService ledgerService;
  private final IdentityService identityService;

  public CurrentCardSameAsDownpaymentCardFeature(WalletService walletService,
                                                 LedgerService ledgerService, IdentityService identityService
  ) {
    super();
    this.walletService = walletService;
    this.ledgerService = ledgerService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    // first get all funds in transactions in past x timeRange
    List<RecordLedger> dpRecords = ledgerService.getLedgersByBillTransactionId(evalParams.getBillTransactionId(),
        LedgerService.PaymentState.SETTLED, LedgerService.MoneyMovementType.DOWNPAYMENT);
    if (dpRecords == null) {
      log.warn("No settled down payment records for customer id {}", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
    dpRecords = dpRecords.stream()
        .filter(recordLedger -> recordLedger.getPaymentCategoryId() != null)
        .filter(recordLedger -> recordLedger.getPaymentCategoryId().equals(
            MovementCategory.CHARGE.getValue()
        ))
        .toList();
    if (dpRecords.isEmpty()) {
      log.warn("No settled down payment records for customer id {}", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
    String customerPublicId = identityService.getCustomer(evalParams.getCustomerId()).getCustomerPublicId();
    GetDefaultCardResponse c = walletService.getDefaultCard(customerPublicId);
    if (c == null || c.getCard() == null) {
      log.warn("No card found for customer id {}", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
    if (c.getCard().getPaymentMethodId() == null) {
      log.warn("No active payment method id found for customer id {}", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
    if (dpRecords.stream().filter(r -> r.getFromPaymentMethodId() == null).count() > 0) {
      log.warn("No payment method id found in down payment records for customer id {}, btx id {}",
          evalParams.getCustomerId(), evalParams.getBillerAccountId());
      return new FeatureOutput(false);
    }
    return dpRecords.stream().allMatch(r -> r.getFromPaymentMethodId().equals(
        c.getCard().getPaymentMethodId()))
        ? new FeatureOutput(true) : new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "whether customers current card is the same as the down payment card retrieved by bill transaction id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID, EvalParamKey.CUSTOMER_ID);
  }
}
