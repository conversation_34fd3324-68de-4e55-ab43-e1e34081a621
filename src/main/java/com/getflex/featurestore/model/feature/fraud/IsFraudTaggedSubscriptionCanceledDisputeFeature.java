package com.getflex.featurestore.model.feature.fraud;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;

@RegisterFeature
public class IsFraudTaggedSubscriptionCanceledDisputeFeature extends BaseFraudTaggedFeature {

  public IsFraudTaggedSubscriptionCanceledDisputeFeature(TaggingService taggingService) {
    super(taggingService);
  }

  @Override
  public FraudTagName getFraudTagName() {
    return FraudTagName.SUBSCRIPTION_CANCELED_DISPUTE;
  }

  @Override
  public String getDescription() {
    return "Account tagged as fraud subscription canceled";
  }
}
