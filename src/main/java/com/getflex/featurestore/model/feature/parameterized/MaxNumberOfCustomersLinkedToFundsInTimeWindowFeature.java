package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;

@RegisterFeature(
    value = "MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature extends BaseFeature {
  private final LedgerService ledgerService;
  private final WalletService walletService;
  private final LookbackDurationFeatureParams parameters;

  public MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature(
      LedgerService ledgerService, WalletService walletService, LookbackDurationFeatureParams parameters) {
    this.ledgerService = ledgerService;
    this.walletService = walletService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    // Step 1: Given customer id, find all the funds-in transactions within the time window
    Long customerId = evalParams.getCustomerId();
    OffsetDateTime dtCreatedStartRangePastInDays =
        OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()));
    APIretrieveLedgerByCustomerIdRequest request =
        LedgerApi.APIretrieveLedgerByCustomerIdRequest.newBuilder()
            .customerId(customerId)
            .dtCreatedStartRange(dtCreatedStartRangePastInDays)
            .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
            .includeRefund(IncludeRefundEnum.EXCLUDE)
            .build();
    List<RecordLedger> fundsInRecords = ledgerService.retrieveLedgerByCustomerId(request);

    if (fundsInRecords == null || fundsInRecords.isEmpty()) {
      return new FeatureOutput(0);
    }

    // Filter records for movement category of CHARGE (3L)
    List<Long> fundsInPaymentMethodIds =
        fundsInRecords.stream()
            .filter(
                record ->
                    Objects.equals(
                        MovementCategory.CHARGE.getValue(), record.getPaymentCategoryId()))
            .map(RecordLedger::getFromPaymentMethodId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();

    // Step 2: For each unique fingerprint, check the number of customer linked and get the maximum
    int maxNumberOfCustomersLinked = 0;
    for (Long fundsInId : fundsInPaymentMethodIds) {
      Card fundsInCard = walletService.getCard(fundsInId);
      if (fundsInCard != null) {
        String fingerprint = fundsInCard.getFingerprint();
        List<Card> cards = walletService.getCardsByFingerprint(fingerprint);
        int numberOfCustomersLinked =
            (int)
                cards.stream()
                    .map(Card::getStripeCustomerId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();
        if (numberOfCustomersLinked > maxNumberOfCustomersLinked) {
          maxNumberOfCustomersLinked = numberOfCustomersLinked;
        }
      }
    }

    return new FeatureOutput(maxNumberOfCustomersLinked);
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the maximum number of customers linked to any of the"
               + " funds-in payment fingerprints that the given customer has used within the past x"
               + " days Feature should follow the convention as "
               + "MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature_TimeWindowIsoDurationFormat";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
