package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class MonthsSinceLastSignupFeature extends BillPayOfflineFeature {

  public MonthsSinceLastSignupFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's number of months since last sign up";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "months_since_last_signup";
  }
}
