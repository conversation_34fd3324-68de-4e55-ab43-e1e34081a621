package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsOutOfNetworkFeature extends BaseFeature {

  private final IdentityService identityService;
  private final BillingService billingService;

  public IsOutOfNetworkFeature(IdentityService identityService, BillingService billingService) {
    this.identityService = identityService;
    this.billingService = billingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      Long billerId = identityService.getBillerByBillerAccountId(evalParams.getBillerAccountId()).getBillerId();
      Boolean isOutOfNetwork = billingService.getPropertyById(billerId).getIsOutOfNetwork();
      return new FeatureOutput(isOutOfNetwork);
    } catch (Exception e) {
      log.error("Error while fetching is_out_of_network for biller account id: {}", evalParams.getBillerAccountId());
      return new FeatureOutput(false);
    }
  }

  @Override
  public String getDescription() {
    return "If the customer is out of network";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
