package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.IsTrustedEntityBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class IsTrustedDeviceIdFeature extends IsTrustedEntityBaseFeature {


  public IsTrustedDeviceIdFeature(OfflineFeatureRepo offlineFeatureRepo,
                                     IdentityService identityService) {
    super(offlineFeatureRepo, identityService);
  }

  @Override
  public String getDescription() {
    return "Check if given device id is trusted, matching to the trusted_device_id_to_customer_id offline feature. "
        + "primary key is trusted device id and feature value is the customer_public_id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.DEVICE_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "trusted_device_id_to_customer_id";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getDeviceId();
  }
}
