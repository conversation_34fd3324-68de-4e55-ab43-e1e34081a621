package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Slf4j
@RegisterFeature
public class CustomerSentilinkThirdPartySyntheticScoreFeature extends BaseFeature {

  private static final String OFFLINE_FEATURE_NAME = "sentilink_third_party_synthetic_score";

  private final OfferService offerService;
  private final OfflineFeatureRepo offlineFeatureRepo;

  public CustomerSentilinkThirdPartySyntheticScoreFeature(OfferService offerService,
      OfflineFeatureRepo offlineFeatureRepo) {
    super();
    this.offerService = offerService;
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Integer valueFromOffer = this.getValueFromRootOffer(evalParams);

    if (valueFromOffer != null) {
      return new FeatureOutput(valueFromOffer);
    }

    return new FeatureOutput(this.getValueFromOfflineFeature(evalParams));
  }

  public Integer getValueFromOfflineFeature(EvalParams evalParams) {
    Optional<OfflineFeature> feature = this.offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        this.OFFLINE_FEATURE_NAME, evalParams.getCustomerId().toString());
    if (feature.isEmpty() || StringUtils.isEmpty(feature.get().getFeatureValue())) {
      return null;
    }
    return Integer.parseInt(feature.get().getFeatureValue());
  }

  public Integer getValueFromRootOffer(EvalParams evalParams) {
    InternalOffer offer = offerService.getFirstEverAcceptedOffer(evalParams.getCustomerId());
    if (offer == null) {
      offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
      if (offer == null) {
        log.warn("Could not fetch offer for customerId={}", evalParams.getCustomerId());
        return null;
      }
    }

    if (offer.getEvaluationContext() == null) {
      log.warn("Evaluation context is null for offerId={}, offerVersion={}, offerState={}", offer.getOfferId(),
          offer.getOfferVersion(), offer.getOfferState());
      return null;
    }

    if (offer.getEvaluationContext().getSentilinkThirdPartySyntheticScore() == null) {
      log.warn("Sentilink third party synthetic score is null for offerId={}, offerVersion={}, offerState={}",
          offer.getOfferId(), offer.getOfferVersion(), offer.getOfferState());
      return null;
    }

    return offer.getEvaluationContext().getSentilinkThirdPartySyntheticScore();
  }

  @Override
  public String getDescription() {
    return "This feature returns the Sentilink third party synthetic score for the given customer ID.";
  }
}
