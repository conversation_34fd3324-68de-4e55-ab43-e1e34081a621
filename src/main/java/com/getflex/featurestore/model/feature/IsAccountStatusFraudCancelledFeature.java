package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.IdentityService.BillerAccountCategoryStatus;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.BillerAccountStatusRecord;
import com.getflex.identity.model.BillerAccountStatusRecord.StatusEnum;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsAccountStatusFraudCancelledFeature extends BaseFeature {

  private final IdentityService identityService;

  private static final String FRAUD = "fraud";

  public IsAccountStatusFraudCancelledFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    boolean isAccountFraudCancelled = false;
    Long customerId = evalParams.getCustomerId();

    List<ExtendedBillerAccountData> billerAccounts =
        identityService.getBillerAccountsByCustomerId(customerId);
    Optional<ExtendedBillerAccountData> latestBillerAccount =
        billerAccounts.stream()
            .filter(
                record ->
                    !StatusEnum.PENDING.name().equalsIgnoreCase(record.getStatus()))
            .findFirst();

    if (latestBillerAccount.isPresent()) {
      if (StatusEnum.CLOSED
              .name()
              .equalsIgnoreCase(latestBillerAccount.get().getStatus())
          || BillerAccountCategoryStatus.CANCELED
              .name()
              .equalsIgnoreCase(latestBillerAccount.get().getCategoryStatus())) {
        Long billerAcctId = latestBillerAccount.get().getBillerAccountId();
        List<BillerAccountStatusRecord> billerAcctHistory =
            identityService.getBillerAccountStatusHistory(billerAcctId);

        isAccountFraudCancelled =
            billerAcctHistory.stream()
                .anyMatch(
                    record ->
                        StatusEnum.CLOSED
                                .name()
                                .equalsIgnoreCase(String.valueOf(record.getStatus()))
                            && FRAUD.equalsIgnoreCase(record.getReason()));
      }
    }
    return new FeatureOutput(isAccountFraudCancelled);
  }

  @Override
  public String getDescription() {
    return "Returns whether a customer's account has been cancelled with reason being fraud.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
