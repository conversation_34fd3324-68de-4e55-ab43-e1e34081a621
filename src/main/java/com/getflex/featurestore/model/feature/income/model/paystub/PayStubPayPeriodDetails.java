package com.getflex.featurestore.model.feature.income.model.paystub;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class PayStubPayPeriodDetails implements Serializable {
  @JsonFormat(pattern = "yyyy-MM-dd")
  LocalDate startDate;
  @JsonFormat(pattern = "yyyy-MM-dd")
  LocalDate endDate;
  @JsonFormat(pattern = "yyyy-MM-dd")
  LocalDate payDate;
  Double payAmount;
  Double grossEarnings;
  String payFrequency;
}
