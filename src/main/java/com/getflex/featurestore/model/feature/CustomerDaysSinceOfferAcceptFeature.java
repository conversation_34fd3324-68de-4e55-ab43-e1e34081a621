package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;

@RegisterFeature
public class CustomerDaysSinceOfferAcceptFeature extends BaseFeature {

  private final OfferService offerService;

  // It is expected that we may have null value for this feature. Set it to -1 for rule evaluation
  private static final int DEAFULT_VALUE = -1;

  public CustomerDaysSinceOfferAcceptFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Object value;
    Long customerId = evalParams.getCustomerId();
    InternalOffer offer = null;
    OffsetDateTime acceptanceTime = null;
    try {
      offer = offerService.getFirstEverAcceptedOffer(customerId);
    } catch (InternalDependencyFailureException e) {
      return new FeatureOutput(DEAFULT_VALUE);
    }
    if (offer == null) {
      return new FeatureOutput(DEAFULT_VALUE);
    }
    acceptanceTime = offer.getAcceptanceTime();
    if (acceptanceTime == null) {
      throw new FeatureNotFoundException("No acceptance time found. customerId=%s, offerId=%s"
          .formatted(customerId, offer.getOfferId()));
    }
    // The number of days between acceptanceTime and now cannot exceed Integer.MAX_VALUE.
    // If it does, it means that the acceptanceTime is incorrect, likely set to an unrealistically far past date.
    try {
      value = Math.toIntExact(ChronoUnit.DAYS.between(acceptanceTime, OffsetDateTime.now()));
    } catch (ArithmeticException e) {
      throw new InternalServiceBadDataException("Bad acceptanceTime received. customerId=%s, acceptanceTime=%s"
          .formatted(customerId, acceptanceTime));
    }
    return new FeatureOutput(value);
  }

  @Override
  public String getDescription() {
    return "Number of days since the customer accepted the first/earliest offer";
  }
}
