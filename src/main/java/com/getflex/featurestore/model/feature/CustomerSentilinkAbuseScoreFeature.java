package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerSentilinkAbuseScoreFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerSentilinkAbuseScoreFeature(OfferService offerService) {
    super();
    this.offerService = offerService;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }

  @Override
  public String getDescription() {
    return "Sentilink Abuse Score from customer's first ever offer";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offer = offerService.getFirstEverAcceptedOffer(evalParams.getCustomerId());
    if (offer == null) {
      offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
      if (offer == null) {
        log.warn("Could not fetch offer for customerId={}", evalParams.getCustomerId());
        return getDefaultValue();
      }
    }

    InternalOfferAllOfEvaluationContext evalContext = offer.getEvaluationContext();
    if (evalContext == null) {
      log.warn("Evaluation Context is null for offerId={}", offer.getOfferId());
      return getDefaultValue();
    }
    Integer sentiLinkAbuseScore = evalContext.getSentilinkAbuseScore();
    if (sentiLinkAbuseScore == null) {
      log.warn("SentiLink Abuse Score is null for offerId={}", offer.getOfferId());
      return getDefaultValue();
    }
    return new FeatureOutput(sentiLinkAbuseScore);
  }

}
