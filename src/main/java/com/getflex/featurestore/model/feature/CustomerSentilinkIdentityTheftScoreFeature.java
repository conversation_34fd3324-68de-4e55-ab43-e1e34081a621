package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerSentilinkIdentityTheftScoreFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerSentilinkIdentityTheftScoreFeature(OfferService offerService) {
    super();
    this.offerService = offerService;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offer = offerService.getFirstEverAcceptedOffer(evalParams.getCustomerId());
    if (offer == null) {
      offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
      if (offer == null) {
        log.warn("Could not fetch offer for customerId={}", evalParams.getCustomerId());
        return getDefaultValue();
      }
    }

    InternalOfferAllOfEvaluationContext evalContext = offer.getEvaluationContext();
    if (evalContext == null) {
      log.warn("Evaluation Context is null for offerId={}", offer.getOfferId());
      return getDefaultValue();
    }
    Integer sentiLinkIdentityTheftScore = evalContext.getSentilinkIdTheftScore();
    if (sentiLinkIdentityTheftScore == null) {
      log.warn("SentiLink Identity Theft Score is null for offerId={}", offer.getOfferId());
      return getDefaultValue();
    }
    return new FeatureOutput(sentiLinkIdentityTheftScore);
  }

  @Override
  public String getDescription() {
    return "SentiLink Identity Theft Score from customer's first ever accepted offer";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
