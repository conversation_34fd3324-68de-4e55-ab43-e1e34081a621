package com.getflex.featurestore.model.feature.utils;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureMethod;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureResult;
import com.getflex.featurestore.model.EvalParams;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ThreeDomainSecureUtils {

  public Integer get3dsAddCardCount(
      EvalParams evalParams,
      List<Event> events,
      Predicate<ThreeDomainSecureAttempt> predicate
  ) {
    try {
      return events.stream()
          .map(event -> {
            try {
              return EventMetadata.OBJECT_MAPPER.readValue(
                  event.getMetadata(),
                  StripeSetupAttemptMetadata.class
              );
            } catch (Exception e) {
              log.error("Failed to parse event metadata for customerId={}", evalParams.getCustomerId(), e);
              return null;
            }
          })
          .filter(Objects::nonNull)
          .filter(metadata -> {
            ThreeDomainSecureMethod method = metadata.getPaymentMethodDetails() != null
                && metadata.getPaymentMethodDetails().getCard() != null
                && metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure() != null
                && metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure().getAuthenticationFlow() != null
                ? ThreeDomainSecureMethod.fromString(
                metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure().getAuthenticationFlow())
                : null;

            ThreeDomainSecureResult result = metadata.getPaymentMethodDetails() != null
                && metadata.getPaymentMethodDetails().getCard() != null
                && metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure() != null
                && metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure().getResult() != null
                ? ThreeDomainSecureResult.fromString(
                metadata.getPaymentMethodDetails().getCard().getThreeDomainSecure().getResult())
                : null;

            if (metadata.getSetupError() != null || result == ThreeDomainSecureResult.PROCESSING_ERROR) {
              result = ThreeDomainSecureResult.FAILED;
            }

            if (method == null && result == ThreeDomainSecureResult.FAILED) {
              method = ThreeDomainSecureMethod.FRICTIONLESS;
            }

            return predicate.test(new ThreeDomainSecureAttempt(method, result));
          })
          .collect(
              Collectors.toMap(
                  StripeSetupAttemptMetadata::getId,
                  Function.identity(),
                  (existing, replacement) -> existing
              )
          ).size();
    } catch (Exception e) {
      log.error("Failed to calculate 3DS count for customerId={}: {}", evalParams.getCustomerId(), e.getMessage());
      return 0;
    }
  }

  public record ThreeDomainSecureAttempt(ThreeDomainSecureMethod method, ThreeDomainSecureResult result) {
  }

}
