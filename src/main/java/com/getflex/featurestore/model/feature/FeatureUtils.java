package com.getflex.featurestore.model.feature;

import com.getflex.identity.model.GetCustomerResponse;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.OptionalInt;
import org.apache.commons.text.similarity.LevenshteinDistance;

public class FeatureUtils {

  public static ZonedDateTime getCurrentBpDateTime() {
    // Eastern Time zone (ET)
    ZoneId easternTimeZone = ZoneId.of("America/New_York");
    ZonedDateTime easternTimeNow = ZonedDateTime.now(easternTimeZone);
    // if the current day is less than 25, return the first day of the current month,
    // otherwise return the first day of the next month
    if (easternTimeNow.getDayOfMonth() < 25) {
      return easternTimeNow.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
    }
    return easternTimeNow.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0)
        .plusMonths(1);
  }

  public static String cleanBatchMemo(String batchMemo) {
    String cleanedBatchMemo = batchMemo.replaceAll("[^a-zA-Z]", "").toLowerCase();
    if (cleanedBatchMemo.startsWith("pl")) {
      return cleanedBatchMemo.substring(2);
    }
    return cleanedBatchMemo;
  }

  public static String extractDomainFromUrl(String url) {
    url = url.toLowerCase();
    //remove the http://, https:// and www.
    if (url.startsWith("https://")) {
      url = url.substring("https://".length());
    } else if (url.startsWith("http://")) {
      url = url.substring("http://".length());
    }

    if (url.startsWith("www.")) {
      url = url.substring("www.".length());
    }
    // truncate the url at the first dot
    String[] cleanUrl = url.split("\\.", 2);
    return cleanUrl[0];
  }

  public static int getSimilarityScore(String source, List<String> targets) {
    // use the levinsten distance to calculate similarity, range between 0 to 100
    if (targets == null || targets.isEmpty()) {
      return 0;
    }

    OptionalInt maxDistance = targets.stream()
        .mapToInt(t -> {
          LevenshteinDistance levenshtein = new LevenshteinDistance();
          int distance = levenshtein.apply(source, t);
          double similarity = (1 - (double) distance / Math.max(source.length(), t.length())) * 100;
          return (int) similarity;
        }).max();
    return maxDistance.getAsInt();
  }

  public static String sanitizeCustomerAddress(GetCustomerResponse customer) {
    String address = String.join("&", customer.getCity(),
        customer.getState(), customer.getZip());
    return address.replaceAll("[^a-zA-Z0-9&]", "").toLowerCase();
  }
}
