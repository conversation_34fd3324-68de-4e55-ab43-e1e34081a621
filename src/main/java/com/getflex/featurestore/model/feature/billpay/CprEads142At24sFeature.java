package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class CprEads142At24sFeature extends BillPayOfflineFeature {

  public CprEads142At24sFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's cpr eads142_at24s";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "cpr_eads142_at24s";
  }
}
