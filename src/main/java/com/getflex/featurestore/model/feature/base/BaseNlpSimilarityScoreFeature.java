package com.getflex.featurestore.model.feature.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@Slf4j
public abstract class BaseNlpSimilarityScoreFeature extends BaseFeature {

  private static final String ENDPOINT_NAME = "SimilarityScoreModelEndpoint";
  private static final Double DEFAULT_SIMILARITY_SCORE = 0.0;

  private final SageMakerRuntimeClient sagemaker;

  public BaseNlpSimilarityScoreFeature(SageMakerRuntimeClient sagemaker) {
    this.sagemaker = sagemaker;
  }

  public abstract List<String> getTargetList(EvalParams evalParams);

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    if (evalParams.getBatchMemo() == null || evalParams.getBatchMemo().trim().isEmpty()) {
      return new FeatureOutput(DEFAULT_SIMILARITY_SCORE);
    }
    List<String> targetList = this.getTargetList(evalParams).stream()
        .filter(s -> s != null && !s.trim().isEmpty()).toList();
    if (targetList.isEmpty()) {
      return new FeatureOutput(DEFAULT_SIMILARITY_SCORE);
    }
    try {
      String batchMemo = evalParams.getBatchMemo();

      Map<String, Object> inputData = Map.of(
          "SOURCE", batchMemo,
          "TARGET_LIST", targetList
      );
      ObjectMapper objectMapper = new ObjectMapper();
      String jsonString = objectMapper.writeValueAsString(inputData);
      InvokeEndpointRequest invokeEndpointRequest = InvokeEndpointRequest.builder()
          .endpointName(ENDPOINT_NAME)
          .contentType("application/json")
          .body(SdkBytes.fromString(jsonString, StandardCharsets.UTF_8))
          .build();
      InvokeEndpointResponse response = sagemaker.invokeEndpoint(invokeEndpointRequest);
      String res = response.body().asString(StandardCharsets.UTF_8);
      List<Double> scoreList = Arrays.stream(
              res.replace("[", "").replace("]", "").split(",")
          )
          .map(String::trim)  // Remove any extra spaces
          .map(Double::parseDouble)
          .toList();
      return new FeatureOutput(Collections.max(scoreList));
    } catch (Exception e) {
      log.error("Exception getting similarity score feature, ", e);
    }
    return new FeatureOutput(DEFAULT_SIMILARITY_SCORE);
  }
}
