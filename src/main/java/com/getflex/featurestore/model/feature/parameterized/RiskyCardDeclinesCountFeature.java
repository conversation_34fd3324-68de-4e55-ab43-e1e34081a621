package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.feature.base.BaseCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.payment.model.DeclineCodeEnum;
import java.util.List;

@RegisterFeature(
    value = "RiskyCardDeclinesCountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class RiskyCardDeclinesCountFeature extends BaseCardDeclinesCountFeature {

  public RiskyCardDeclinesCountFeature(PaymentService paymentService, LookbackDurationFeatureParams parameters) {
    super(paymentService, parameters);
  }

  @Override
  protected List<DeclineCodeEnum> getDeclineCodes() {
    return List.of(
        DeclineCodeEnum.CARD_VELOCITY_EXCEEDED,
        DeclineCodeEnum.DO_NOT_HONOR,
        DeclineCodeEnum.REVOCATION,
        DeclineCodeEnum.INVALID_ACCOUNT,
        DeclineCodeEnum.LOST_CARD,
        DeclineCodeEnum.RESTRICTED_CARD,
        DeclineCodeEnum.FRAUDULENT,
        DeclineCodeEnum.STOLEN_CARD
    );
  }

  @Override
  public String getDescription() {
    return "Risky (includes various decline codes) card declines count in past specified time window";
  }
}
