package com.getflex.featurestore.model.feature.movein;

import static com.getflex.featurestore.constant.ProductConstants.MOVE_IN_CATEGORY_ID;
import static com.getflex.featurestore.constant.ProductConstants.RENTAL_PRODUCT_ID;

import com.getflex.credit.client.model.Loan;
import com.getflex.credit.client.model.Loan.StateEnum;
import com.getflex.featurestore.exception.UnexpectedFeatureStateException;
import com.getflex.featurestore.integration.flex.CreditManagementService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class MoveInCustomerFeature extends BaseFeature {

  private final OfferService offerService;
  private final CreditManagementService creditManagementService;

  public MoveInCustomerFeature(OfferService offerService, CreditManagementService creditManagementService) {
    this.offerService = offerService;
    this.creditManagementService = creditManagementService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();

    List<InternalOffer> offers = offerService.searchOfferByProduct(
        customerId,
        Set.of(RENTAL_PRODUCT_ID),
        Set.of(MOVE_IN_CATEGORY_ID),
        false);

    List<InternalOffer> moveinOffers = offers.stream()
        .filter(this::overallStateFilter)
        .toList();

    if (moveinOffers.isEmpty()) {
      return new FeatureOutput(MoveInCustomerInfo.builder()
          .isMoveIn(false)
          .moveInType(null)
          .moveInOffers(Collections.emptyList())
          .build());
    }

    List<MoveInOfferInfo> offerInfos = moveinOffers.stream()
        .map(this::buildMoveInOfferInfo).toList();

    MoveInType overallType = determineOverallMoveInType(moveinOffers, customerId);

    MoveInCustomerInfo result = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(overallType)
        .moveInOffers(offerInfos)
        .build();

    return new FeatureOutput(result);
  }

  private boolean overallStateFilter(InternalOffer offer) {
    if (offer == null) {
      return false;
    }
    return List.of(OverallState.ACTIVE, OverallState.SUSPENDED, OverallState.CANCELED)
        .contains(offer.getOverallState());
  }

  private MoveInType determineOverallMoveInType(List<InternalOffer> offers, Long customerId) {
    InternalOffer activeOffer = offers.stream()
        .filter(this::isActiveOrSuspendedOffer)
        .findFirst()
        .orElse(null);

    if (activeOffer != null) {
      return activeOffer.getLoanId() != null ? MoveInType.LOAN : MoveInType.PAY_IN_FULL;
    }

    InternalOffer mostRecentClosedOffer = offers.stream()
        .filter(offer -> offer.getTerminationTime() != null)
        .sorted((o1, o2) -> o2.getTerminationTime().compareTo(o1.getTerminationTime()))
        .findFirst()
        .orElse(null);

    if (mostRecentClosedOffer != null) {
      return mostRecentClosedOffer.getLoanId() != null ? MoveInType.LOAN : MoveInType.PAY_IN_FULL;
    }

    throw new UnexpectedFeatureStateException(
        String.format("Invalid state: Unable to determine move in type for customerId %d",
            customerId));
  }

  private MoveInOfferInfo buildMoveInOfferInfo(InternalOffer offer) {
    MoveInOfferInfo.MoveInOfferInfoBuilder builder = MoveInOfferInfo.builder()
        .offerId(offer.getOfferId())
        .offerVersion(offer.getOfferVersion())
        .loanId(offer.getLoanId())
        .offerState(offer.getOverallState());

    if (offer.getLoanId() != null) {
      builder.type(MoveInType.LOAN);
      Loan loan = creditManagementService.getLoanById(offer.getLoanId());
      builder.loan(loan);

      if (isOfferCancelled(offer) && isLoanOpen(loan)) {
        throw new UnexpectedFeatureStateException(
            String.format("Invalid state: Offer %s is closed but loan %d is still open",
                offer.getOfferId(), offer.getLoanId()));
      }
    } else {
      builder.type(MoveInType.PAY_IN_FULL);
    }

    return builder.build();
  }

  private boolean isOfferCancelled(InternalOffer offer) {
    return offer != null && offer.getOverallState() != null && offer.getOverallState().equals(OverallState.CANCELED);
  }

  private boolean isActiveOrSuspendedOffer(InternalOffer offer) {
    return offer != null && (OverallState.ACTIVE.equals(offer.getOverallState())
        || OverallState.SUSPENDED.equals(offer.getOverallState()));
  }

  private boolean isLoanOpen(Loan loan) {
    return loan != null && loan.getState() != null && !loan.getState().equals(StateEnum.CLOSED);
  }

  @Override
  public String getDescription() {
    return "Returns move-in customer information including all active/suspended/canceled move-in offers "
        + "with loan details when applicable (rental product ID = 1, move-in category ID = 2)";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
