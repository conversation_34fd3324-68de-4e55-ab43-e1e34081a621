package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.feature.base.BaseFundsOutFundsInCardCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Objects;
import java.util.function.BiPredicate;

@RegisterFeature(
    value = "FundsOutFundsInDifferentCardCountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class FundsOutFundsInDifferentCardCountFeature extends BaseFundsOutFundsInCardCountFeature {

  public FundsOutFundsInDifferentCardCountFeature(
      WalletService walletService,
      IdentityService identityService,
      LedgerUtils ledgerUtils,
      LookbackDurationFeatureParams parameters
  ) {
    super(walletService, identityService, ledgerUtils, parameters);
  }

  @Override
  public String getDescription() {
    return "Number of funds out and funds in on the same card in past specified time window";
  }

  @Override
  protected BiPredicate<RecordLedgerMetadata, RecordLedgerMetadata> getComparisonPredicate() {
    return (fundsOutMetadata, fundsInRecord) ->
        !Objects.equals(fundsOutMetadata.fingerprint(), fundsInRecord.fingerprint())
            && fundsOutMetadata.timestamp().isAfter(fundsInRecord.timestamp());
  }

}
