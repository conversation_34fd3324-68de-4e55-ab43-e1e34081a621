package com.getflex.featurestore.model.feature.base;

import static com.getflex.featurestore.utils.FlexConstant.CSV_UTF8_CONTENT_TYPE;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import org.json.simple.JSONObject;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

/***
 * BaseModelFeature class
 *
 * @param <T> should be an enum mapping the sagemaker model features to the feature store features
 */

public abstract class BaseModelFeature<T extends BaseModelInput> extends BaseFeature {

  protected static final Number DEFAULT_MODEL_INPUT_VALUE = -99999999;
  private static final String REQUEST_CSV_CONTENT_TYPE = "text/csv";

  public abstract List<T> getModelFeaturesList();

  protected abstract String getEndpointName();

  protected final SageMakerRuntimeClient sageMaker;
  protected final ExecutorService executorService;
  protected final FeatureFactory featureFactory;

  public BaseModelFeature(SageMakerRuntimeClient sageMaker, ExecutorService executorService,
      FeatureFactory featureFactory) {
    this.sageMaker = sageMaker;
    this.executorService = executorService;
    this.featureFactory = featureFactory;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(-1.0, null);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<T> featuresList = getModelFeaturesList();
    Map<T, Number> featureValuesMap = getModelFeatureValues(evalParams);
    InvokeEndpointResponse response = sageMaker.invokeEndpoint(buildRequest(featuresList, featureValuesMap));
    return toFeatureOutput(response, featureValuesMap);
  }

  protected Map<T, Number> getModelFeatureValues(EvalParams evalParams) {
    Map<T, Number> valuesMap = new ConcurrentHashMap<>();

    List<Callable<Entry<T, Number>>> tasks = getModelFeaturesList().stream()
        .map(f -> (Callable<Entry<T, Number>>) () -> {
          Number value = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          value = value != null ? value : getModelDefaultValue();
          return Map.entry(f, value);
        })
        .toList();

    List<Future<Entry<T, Number>>> futures;
    try {
      futures = executorService.invokeAll(tasks);
      for (Future<Map.Entry<T, Number>> future : futures) {
        Map.Entry<T, Number> entry = future.get();
        valuesMap.put(entry.getKey(), entry.getValue());
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return valuesMap;
  }

  protected InvokeEndpointRequest buildRequest(List<T> featuresList, Map<T, Number> featureValuesMap) {
    return InvokeEndpointRequest.builder()
        .endpointName(getEndpointName())
        .contentType(REQUEST_CSV_CONTENT_TYPE)
        .body(SdkBytes.fromUtf8String(toCsv(featuresList, featureValuesMap)))
        .build();
  }

  protected FeatureOutput toFeatureOutput(InvokeEndpointResponse response,
      Map<T, Number> featureValuesMap) {
    if (CSV_UTF8_CONTENT_TYPE.equalsIgnoreCase(response.contentType())) {
      String value = response.body().asUtf8String();
      return new FeatureOutput(Double.valueOf(value), new JSONObject(featureValuesMap).toJSONString());
    }

    throw new RuntimeException("Unsupported Sagemaker output content type");
  }

  protected Number getModelDefaultValue() {
    return DEFAULT_MODEL_INPUT_VALUE;
  }

  protected String toCsv(List<T> featuresList, Map<T, Number> featureValuesMap) {
    return featuresList.stream().map(f -> {
      Number featureValue = featureValuesMap.get(f);
      return featureValue != null ? featureValue.toString() : getModelDefaultValue().toString();
    }).collect(Collectors.joining(","));
  }
}
