package com.getflex.featurestore.model.feature.income.model.paystub;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PayStubModel {
  Boolean isSubmissionFraudulent;
  Double maxRiskScore;
  // min of self reported income and verified income
  Double grossMonthlyIncomeCent;
  // verified income directly from IV data
  Double verifiedGrossMonthlyIncomeCent;
  Double grossAnnualSelfReportedIncomeCent;
  VerifiedPayStubs verifiedPayStubs;
}
