package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class DistinctPaymentMethodsFeature extends BaseFeature {

  private final WalletService walletService;

  public DistinctPaymentMethodsFeature(WalletService walletService) {
    super();
    this.walletService = walletService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Integer distinctPaymentMethodsCount = walletService.getDistinctPaymentMethodByCustomerId(
        evalParams.getCustomerId()).size();
    return new FeatureOutput(distinctPaymentMethodsCount);
  }

  @Override
  public String getDescription() {
    return "This feature returns the total number of distinct payment methods for a customer.";
  }
}
