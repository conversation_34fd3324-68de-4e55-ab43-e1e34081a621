package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;


@RegisterFeature
public class BillTransactionHasDownpaymentRefundFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public BillTransactionHasDownpaymentRefundFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> txs = ledgerService.getDownpaymentRecords(evalParams.getBillTransactionId(),
        MovementCategory.REFUND);
    return new FeatureOutput(txs != null && !txs.isEmpty());
  }

  @Override
  public String getDescription() {
    return "Whether bill transaction has a downpayment refund transaction";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
