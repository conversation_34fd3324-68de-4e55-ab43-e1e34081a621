package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;

public abstract class BillPayOfflineFeature extends BaseOfflineFeature {

  private final IdentityService identityService;

  protected BillPayOfflineFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String rawOfflineString = super.getRawFeatureValue(evalParams);
    if (rawOfflineString == null) {
      return new FeatureOutput(null);
    }
    return new FeatureOutput(Double.valueOf(rawOfflineString));
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return identityService.getCustomer(evalParams.getCustomerId()).getCustomerPublicId();
  }
}
