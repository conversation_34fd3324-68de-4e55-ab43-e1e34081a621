
package com.getflex.featurestore.model.feature.output;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class IpToGeoLocationOutput {
  private String network;
  private Double latitude;
  private Double longitude;
  private String cityName;
  private String countryName;
  private String countryIsoCode;
  private String continentCode;
  private String subdivision1IsoCode;
  private String subdivision1Name;
  private String subdivision2IsoCode;
  private String subdivision2Name;
}