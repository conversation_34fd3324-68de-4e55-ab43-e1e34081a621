package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.GetWalletBalanceResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerSlcWithdrawableAmountCentsFeature extends BaseFeature {

  private final LedgerService ledgerService;
  private final IdentityService identityService;

  public CustomerSlcWithdrawableAmountCentsFeature(LedgerService ledgerService,
                                                   IdentityService identityService) {
    this.ledgerService = ledgerService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetWalletBalanceResponse getWalletBalanceResponse =
        ledgerService.getWalletBalance(evalParams.getCustomerId());
    if (getWalletBalanceResponse == null) {
      log.error("Failed to fetch wallet balance for customerId={}", evalParams.getCustomerId());
      return new FeatureOutput(0L);
    }
    Long walletBalance = getWalletBalanceResponse.getWalletAvailableBalance();

    GetCustomerResponse getCustomerResponse = identityService.getCustomer(evalParams.getCustomerId());
    String customerPublicId = getCustomerResponse.getCustomerPublicId();
    Long unpaidBalance =
        ledgerService.getCustomerOutstandingBalance(customerPublicId);
    long max = Math.max(0, walletBalance - Math.max(0, unpaidBalance));
    return new FeatureOutput(max);
  }

  @Override
  public String getDescription() {
    return "Check to see if the customer has a transaction with a biller";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
