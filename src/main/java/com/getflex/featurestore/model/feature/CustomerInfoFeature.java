package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

/**
 * Provide current customer's unencrypted PII recorded in Identity (except SSN/DOB which is encrypted).
 */
@RegisterFeature
public class CustomerInfoFeature extends BaseFeature {

  private final IdentityService identityService;

  public CustomerInfoFeature(IdentityService identityService) {

    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(identityService.getCustomerInfo(evalParams));
  }

  @Override
  public String getDescription() {
    return "Get customer PII that is not encrypted";
  }
}
