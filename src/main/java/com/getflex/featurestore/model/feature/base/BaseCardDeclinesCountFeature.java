package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.payment.model.DeclineCodeEnum;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;

public abstract class BaseCardDeclinesCountFeature extends BaseFeature {

  private final PaymentService paymentService;
  private final LookbackDurationFeatureParams parameters;

  public BaseCardDeclinesCountFeature(PaymentService paymentService, LookbackDurationFeatureParams parameters) {
    this.paymentService = paymentService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(
        paymentService.getTotalUserPaymentsDeclinedV2(
            evalParams.getCustomerId(),
            getDeclineCodes(),
            OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()))
        )
    );
  }

  protected abstract List<DeclineCodeEnum> getDeclineCodes();

  @Override
  public FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }
}
