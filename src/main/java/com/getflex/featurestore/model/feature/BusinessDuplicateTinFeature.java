package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.verification.model.RiskProfileDto;
import java.util.List;
import org.springframework.stereotype.Component;

@RegisterFeature
public class BusinessDuplicateTinFeature extends BaseFeature {

  private final VerificationService verificationService;

  public BusinessDuplicateTinFeature(VerificationService verificationService) {
    super();
    this.verificationService = verificationService;
  }


  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RiskProfileDto> riskProfiles = verificationService.findDuplicateRiskProfilesByTin(evalParams.getEinHmac());
    return new FeatureOutput(!riskProfiles.isEmpty());
  }

  @Override
  public String getDescription() {
    return "Checks if the provided EIN HMAC is associated with existing active businesses.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.EIN_HMAC);
  }
}
