package com.getflex.featurestore.model.feature;


import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.fulfillment.model.FulfillmentInfo;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerHasActiveFulfillmentFeature extends BaseFeature {

  private final FulfillmentService fulfillmentService;

  public CustomerHasActiveFulfillmentFeature(FulfillmentService fulfillmentService) {
    this.fulfillmentService = fulfillmentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      FulfillmentInfo fulfillmentInfo =
          fulfillmentService.getFulfillmentByCustomerInBp(evalParams.getCustomerId());
      if (fulfillmentInfo != null) {
        String billTransactionId = fulfillmentInfo.getBillTransactionId();
        GetStatusResponseV2 getStatusResponseV2 =
            fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
                billTransactionId, fulfillmentInfo.getFulfillmentId());
        if (getStatusResponseV2 != null) {
          GetStatusResponseV2.StatusEnum status = getStatusResponseV2.getStatus();
          if (status == GetStatusResponseV2.StatusEnum.PAUSED || status == GetStatusResponseV2.StatusEnum.WAITING) {
            return new FeatureOutput(true);
          }
        }
      }
    } catch (Exception e) {
      log.error("Failed to fetch fulfillment status for customerId={}", evalParams.getCustomerId(), e);
    }
    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "Check to see if the customer has an active fulfillment";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}