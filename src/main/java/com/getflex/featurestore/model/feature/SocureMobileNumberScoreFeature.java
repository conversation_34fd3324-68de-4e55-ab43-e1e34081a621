package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class SocureMobileNumberScoreFeature extends BaseFeature {

  private static final Double DEFAULT_SOCURE_MOBILE_NUMBER_SCORE = 1D;

  private final OfferService offerService;

  public SocureMobileNumberScoreFeature(OfferService offerService) {
    super();
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(DEFAULT_SOCURE_MOBILE_NUMBER_SCORE);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    InternalOffer offer;
    if (evalParams.getBillerAccountId() != null) {
      try {
        offer = offerService.getOfferByBillerAccountId(billerAccountId);
      } catch (InternalDependencyFailureException e) {
        log.warn("Could not fetch offer from billerAccountId={}. Looking for offer using customerId={}",
            evalParams.getBillerAccountId(), evalParams.getCustomerId());
        offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
      }
    } else {
      offer = offerService.getFirstEverAcceptedOffer(evalParams.getCustomerId());
    }

    if (offer.getEvaluationContext() == null) {
      log.warn("Evaluation context is null for offerId={}, offerVersion={}, offerState={}",
          offer.getOfferId(), offer.getOfferVersion(), offer.getOfferState());
      return new FeatureOutput(DEFAULT_SOCURE_MOBILE_NUMBER_SCORE);
    }
    Boolean isRoot = offer.getEvaluationContext().getIsRootEvaluation();
    String rootOfferId = offer.getEvaluationContext().getRootOfferId();

    if (Boolean.FALSE.equals(isRoot) && rootOfferId != null) {
      offer = offerService.getRootOffer(rootOfferId, offer.getEvaluationContext().getRootOfferVersion());
    }

    if (offer.getEvaluationContext().getSocureMobileNumberScore() == null) {
      log.warn("Socure mobile number score is null for biller account ID: {}", billerAccountId);
      return new FeatureOutput(DEFAULT_SOCURE_MOBILE_NUMBER_SCORE);
    }

    return new FeatureOutput(
        Double.valueOf(String.format("%.2f", offer.getEvaluationContext().getSocureMobileNumberScore())));
  }

  @Override
  public String getDescription() {
    return "This feature returns the Socure mobile number score for the given biller account ID or customer ID.";
  }
}
