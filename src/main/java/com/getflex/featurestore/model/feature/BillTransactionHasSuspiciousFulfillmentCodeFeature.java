package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.fulfillment.model.FulfillmentCode;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import java.util.List;

@RegisterFeature
public class BillTransactionHasSuspiciousFulfillmentCodeFeature extends BaseFeature {

  private final FulfillmentService fulfillmentService;
  private final List<String> suspiciousCodes = List.of(FulfillmentCode.SELFPAY_VC_OFFERED.toString(),
      FulfillmentCode.SELFPAY_VC_STARTED.toString());

  public BillTransactionHasSuspiciousFulfillmentCodeFeature(FulfillmentService fulfillmentService) {
    super();
    this.fulfillmentService = fulfillmentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetStatusResponseV2 response;
    try {
      response = fulfillmentService.getStatusCode(
          evalParams.getBillTransactionId());
    } catch (
    InternalDependencyFailureException e) {
      return new FeatureOutput(true);
    }

    if (response == null || response.getCodes() == null) {
      return new FeatureOutput(true);
    }
    List<String> list = response.getCodes().stream().filter(suspiciousCodes::contains)
        .toList();
    return new FeatureOutput(!list.isEmpty());

  }

  @Override
  public String getDescription() {
    return "If customer's fufillment is in valid state";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
