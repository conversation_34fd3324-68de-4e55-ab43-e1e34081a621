package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class IsInNetworkFeature extends BaseFeature {

  private final BillingService billingService;

  public IsInNetworkFeature(BillingService billingService) {
    this.billingService = billingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      PropertyInfo propertyInfo = billingService.getPropertyByIdentityBillerId(evalParams.getBillerId());
      return new FeatureOutput(propertyInfo.getInNetworkIndicator());
    } catch (Exception e) {
      return new FeatureOutput(0);
    }
  }

  @Override
  public String getDescription() {
    return "If the customer is in network";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
