package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature(
    value = "NumberOfAddCardAttemptsFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfAddCardAttemptsFeature extends BaseFeature {

  private final EventRepository eventRepository;
  private final LookbackDurationFeatureParams parameters;

  public NumberOfAddCardAttemptsFeature(
      EventRepository eventRepository,
      LookbackDurationFeatureParams parameters
  ) {
    this.eventRepository = eventRepository;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerId = evalParams.getCustomerId().toString();
    var lookbackWindow = OffsetDateTime.now().minus(Duration.parse(this.parameters.lookbackWindow()));
    var numberOfAddCardAttempts = eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.STRIPE_SETUP_ATTEMPT,
            customerId
        ).stream()
        .filter(event -> event.getDtArrived().isAfter(lookbackWindow))
        .map(event -> {
              try {
                return EventMetadata.OBJECT_MAPPER.readValue(
                    event.getMetadata(),
                    StripeSetupAttemptMetadata.class
                );
              } catch (Exception e) {
                log.error("Failed to parse event metadata for customerId={}", customerId, e);
                return null;
              }
        }
        ).filter(Objects::nonNull)
        .collect(
            Collectors.toMap(
                StripeSetupAttemptMetadata::getId,
                Function.identity(),
                (existing, replacement) -> existing
            )
        ).size();
    return new FeatureOutput(numberOfAddCardAttempts);
  }

  @Override
  public String getDescription() {
    return "Number of card attempts for a specified lookback window";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
