package com.getflex.featurestore.model.feature.base.param;

import com.getflex.featurestore.model.EvalParamKey;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

public record Any(Set<? extends ParamRequired> criteriaList) implements ParamRequired {
  public Any {
    criteriaList = Set.copyOf(criteriaList);
  }

  /**
   * @param paramKeys At least one param key must be specified. If no param is required, use
   *                  {@link All#of(EvalParamKey...)} without any argument.
   * @return
   */
  public static ParamRequired of(EvalParamKey... paramKeys) {
    if (paramKeys.length == 0) {
      throw new IllegalArgumentException("Must specify at least one param key");
    }
    return new Any(Arrays.stream(paramKeys).map(Single::new).collect(Collectors.toSet()));
  }

  @Override
  public void validateEvaluationParams(Set<EvalParamKey> availableParams) {
    RuntimeException lastException = null;
    for (ParamRequired criteria : criteriaList) {
      try {
        criteria.validateEvaluationParams(availableParams);
        return;
      } catch (RuntimeException e) {
        lastException = e;
      }
    }
    throw lastException;
  }
}
