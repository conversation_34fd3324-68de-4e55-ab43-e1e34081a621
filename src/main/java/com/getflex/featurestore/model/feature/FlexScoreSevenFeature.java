package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

@Slf4j
@RegisterFeature
public class FlexScoreSevenFeature extends AbstractFlexScoreFeature {

  public FlexScoreSevenFeature(FlexScoreModel flexScoreModel, BillingService billingService,
      IdentityService identityService,
      @Qualifier("alloyReportDownloader") Function<String, String> alloyReportDownloader,
      FeatureFactory featureFactory) {
    super(Version.V7, flexScoreModel, billingService, identityService, alloyReportDownloader, featureFactory);
  }

  @Override
  public String getDescription() {
    return "flexscore-7";
  }
}
