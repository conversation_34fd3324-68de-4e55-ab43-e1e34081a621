package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.model.feature.base.BaseDeviceMetricFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.service.EventService;

@RegisterFeature
@RegisterFeature(
    value = "NumberOfIpChangesFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfIpChangesFeature extends BaseDeviceMetricFeature {

  public NumberOfIpChangesFeature(EventService eventService) {
    super(eventService, new LookbackDurationFeatureParams(null));
  }

  public NumberOfIpChangesFeature(EventService eventService,
      LookbackDurationFeatureParams parameters) {
    super(eventService, parameters);
  }

  @Override
  protected String extractProperty(DeviceMetadata deviceMetadata) {
    return deviceMetadata.getIpAddress();
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the amount of times a customer has changed their ip address."
        + " Unless specified, time window defaults to lifetime of customer's account."
        + " Time window should follow the convention as"
        + " NumberOfIpChangesFeature_TimeWindowIsoDurationFormat";
  }
}
