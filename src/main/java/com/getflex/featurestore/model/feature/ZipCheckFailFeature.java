package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsZipCheckEnum;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class ZipCheckFailFeature extends BaseFeature {

  public ZipCheckFailFeature() {
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    StripeAvsZipCheckEnum result = evalParams.getStripeAvsZipCheck();
    return new FeatureOutput(result.equals(StripeAvsZipCheckEnum.FAIL) ? 1 : 0);
  }

  @Override
  public String getDescription() {
    return "Stripe avs zip check fail - 1 indicates fail, 0 otherwise";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.STRIPE_AVS_ZIP_CHECK);
  }
}
