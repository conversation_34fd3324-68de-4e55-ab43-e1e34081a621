package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerFlexAnywhereHasDuplicatedAddressFeature extends BaseFeature {

  protected IdentityService identityService;

  public CustomerFlexAnywhereHasDuplicatedAddressFeature(IdentityService identityService) {
    super();
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Integer duplicateAddress = identityService.getCustomerFlexAnywhereDuplicatedAddress(
        evalParams.getCustomerId());
    if (duplicateAddress == 0) {
      // None flexAnywhere customer will return 0, so default to false
      log.warn("None-flexanywhere customer, customerId={}", evalParams.getCustomerId());
      return new FeatureOutput(false);
    }
    return new FeatureOutput(duplicateAddress > 1);
  }

  @Override
  public String getDescription() {
    return "number of duplicated address for customer, only works for flexanywhere";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
