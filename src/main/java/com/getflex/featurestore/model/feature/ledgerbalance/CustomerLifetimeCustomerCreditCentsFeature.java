package com.getflex.featurestore.model.feature.ledgerbalance;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.feature.base.BaseLifetimeLedgerWalletCentsFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.function.Predicate;

@RegisterFeature
public class CustomerLifetimeCustomerCreditCentsFeature extends BaseLifetimeLedgerWalletCentsFeature {

  public CustomerLifetimeCustomerCreditCentsFeature(LedgerService ledgerService) {
    super(ledgerService);
  }

  @Override
  protected Predicate<RecordLedgerWallet> getFilterPredicate() {
    return rlw ->
        rlw.getMoneyMovementTypeId() == LedgerService.MoneyMovementType.CUSTOMER_CREDIT.getValue()
            && rlw.getPaymentStatusId() == LedgerService.PaymentState.SETTLED.getValue();
  }

  @Override
  public String getDescription() {
    return "Customer's life time canceled pay_biller amount in cents.";
  }
}
