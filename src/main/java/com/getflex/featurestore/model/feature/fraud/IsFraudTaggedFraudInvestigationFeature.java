package com.getflex.featurestore.model.feature.fraud;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;

@RegisterFeature
public class IsFraudTaggedFraudInvestigationFeature extends BaseFraudTaggedFeature {

  public IsFraudTaggedFraudInvestigationFeature(TaggingService taggingService) {
    super(taggingService);
  }

  @Override
  public FraudTagName getFraudTagName() {
    return FraudTagName.FRAUD_INVESTIGATION;
  }

  @Override
  public String getDescription() {
    return "Account tagged as fraud subscription canceled";
  }
}
