package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;


@RegisterFeature
public class CardLinkageCountFeature extends BaseOfflineFeature {

  public CardLinkageCountFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String featureValue = super.getRawFeatureValue(evalParams);

    if (featureValue == null) {
      return new FeatureOutput(0);
    }

    return new FeatureOutput(Integer.parseInt(featureValue));
  }

  @Override
  public String getDescription() {
    return "Given one card fingerprint, how many customers are linked with the same card fingerprint";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CARD_FINGERPRINT);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "card_fingerprint_linkage";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCardFingerprint();
  }
}
