package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.client.ApiException;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.List;
import org.springframework.http.HttpStatus;

@RegisterFeature
public class CustomerIsActiveStatusFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerIsActiveStatusFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offer;
    try {
      offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
    } catch (InternalDependencyFailureException e) {
      if (e.getCause() instanceof ApiException cause && cause.getCode() == HttpStatus.NOT_FOUND.value()) {
        offer = null;
      } else {
        throw e;
      }
    }

    return new FeatureOutput(
        offer != null
            && offer.getOverallState() != null
            && List.of(OverallState.ACTIVE, OverallState.SUSPENDED).contains(offer.getOverallState())
    );
  }

  @Override
  public String getDescription() {
    return "If customer holds an active split rent offer.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
