package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsDeviceIdBlockedFeature extends BaseOfflineFeature {

  public IsDeviceIdBlockedFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Check if the device id is in the denylist.";
  }

  @Override
  public String getOfflineFeatureName() {
    return "denylist_device_id";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getDeviceId();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.DEVICE_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      boolean isDenylisted = "1".equals(rawOfflineString);
      return new FeatureOutput(isDenylisted);
    } catch (Exception e) {
      log.warn("Failed to get the feature value of denylist device id. deviceId={}", evalParams.getDeviceId(), e);
      return new FeatureOutput(false);
    }
  }
}
