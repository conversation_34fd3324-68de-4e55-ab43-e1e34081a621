package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.GeoLocation;
import com.getflex.featurestore.dao.repo.GeoIpLocationRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.None;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.output.IpToGeoLocationOutput;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
@RegisterFeature
public class IpToGeoLocationFeature extends BaseFeature {

  private final GeoIpLocationRepo geoIpLocationRepo;

  public IpToGeoLocationFeature(GeoIpLocationRepo geoIpLocationRepo) {
    this.geoIpLocationRepo = geoIpLocationRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    if (!StringUtils.hasLength(evalParams.getIpAddress())) {
      log.warn("IP address is empty or null");
      return new FeatureOutput(createEmptyGeoLocationOutput(), "IP address is null or empty, returning empty object.");
    }

    GeoLocation geoData = geoIpLocationRepo.findGeoLocationDataByIpAddress(evalParams.getIpAddress());

    if (geoData == null) {
      log.warn("No geo location data found for the IP address: {}", evalParams.getIpAddress());
      return new FeatureOutput(createEmptyGeoLocationOutput(),
          "No geo location data found for the IP address: " + evalParams.getIpAddress());
    }

    IpToGeoLocationOutput output = createGeoLocationOutput(geoData);
    return new FeatureOutput(output);
  }

  @Override
  public String getDescription() {
    return "Geolocation data based on IP address";
  }

  /*
   * This requires IP Address to work, but we don't require it because it will throw an exception.
   * When IP Address is missing we just return a default value instead.
   */
  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return None.INSTANCE;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(createEmptyGeoLocationOutput(),
        "Error processing IP geolocation, returning empty object.");
  }

  private IpToGeoLocationOutput createGeoLocationOutput(GeoLocation geoData) {
    return IpToGeoLocationOutput.builder()
        .network(geoData.getNetwork())
        .latitude(geoData.getLatitude())
        .longitude(geoData.getLongitude())
        .cityName(geoData.getCityName())
        .countryName(geoData.getCountryName())
        .countryIsoCode(geoData.getCountryIsoCode())
        .continentCode(geoData.getContinentCode())
        .subdivision1IsoCode(geoData.getSubdivision1IsoCode())
        .subdivision1Name(geoData.getSubdivision1Name())
        .subdivision2IsoCode(geoData.getSubdivision2IsoCode())
        .subdivision2Name(geoData.getSubdivision2Name())
        .build();
  }

  /**
   * Creates an empty IpToGeoLocationOutput with all string fields set to empty values instead of null values. Lat and
   * Long are set to 999.9999 to indicate that the Lat and Long are not available.
   *
   * @return An IpToGeoLocationOutput with empty values
   */
  private IpToGeoLocationOutput createEmptyGeoLocationOutput() {
    return IpToGeoLocationOutput.builder()
        .network("")
        .latitude(999.9999)
        .longitude(999.9999)
        .cityName("")
        .countryName("")
        .countryIsoCode("")
        .continentCode("")
        .subdivision1IsoCode("")
        .subdivision1Name("")
        .subdivision2IsoCode("")
        .subdivision2Name("")
        .build();
  }
}