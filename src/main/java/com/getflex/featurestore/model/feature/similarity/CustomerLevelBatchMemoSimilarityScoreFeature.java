package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.SimilarityScoreBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;


@RegisterFeature
public class CustomerLevelBatchMemoSimilarityScoreFeature extends SimilarityScoreBaseFeature {

  public CustomerLevelBatchMemoSimilarityScoreFeature(OfflineFeatureRepo offlineFeatureRepo
  ) {
    super(offlineFeatureRepo);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_level_batch_memo_history";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public String getDescription() {
    return "Get the similarity score between batch memo and customer level batch memo history list";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.CUSTOMER_ID);
  }
}