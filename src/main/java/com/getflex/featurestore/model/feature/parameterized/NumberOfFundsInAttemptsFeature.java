
package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseWalletFundsCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.function.Predicate;

@RegisterFeature(
    value = "NumberOfFundsInAttemptsFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfFundsInAttemptsFeature extends BaseWalletFundsCountFeature {

  public NumberOfFundsInAttemptsFeature(LedgerService ledgerService, LookbackDurationFeatureParams parameters) {
    super(ledgerService, parameters);
  }

  @Override
  public String getDescription() {
    return "Number of funds in attempts in past specified time window";
  }

  @Override
  protected APIretrieveLedgerByCustomerIdRequest getRetrieveLedgerRequest(
      Long customerId,
      OffsetDateTime dtCreatedStartRange
  ) {
    return APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRange)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .includeRefund(IncludeRefundEnum.EXCLUDE)
        .build();
  }

  @Override
  protected Predicate<RecordLedger> getFilterPredicate() {
    return recordLedger -> validChargePaymentCategory(recordLedger)
        && validPaymentStatuses(recordLedger, PaymentState.SETTLED, PaymentState.DECLINED);
  }
}
