package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class AvgCardDeclinedInvalidAccountLast6MonthsFeature extends BillPayOfflineFeature {

  public AvgCardDeclinedInvalidAccountLast6MonthsFeature(
      IdentityService identityService,
      OfflineFeatureRepo offlineFeatureRepo
  ) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Average number of card declined due to invalid account over the last 6 months";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "avg_card_declined_invalid_account_last_6_months";
  }
}
