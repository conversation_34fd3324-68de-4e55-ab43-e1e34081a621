package com.getflex.featurestore.model.feature.income.model.paystub;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VerifiedPayStubMetadata {
  Boolean employeeNamePresent;
  Boolean doesEmployeeNameMatch;
  Boolean payDatePresent;
  Boolean isRecent;
  Double riskScore;
}
