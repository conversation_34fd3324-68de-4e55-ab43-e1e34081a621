package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.partnerhub.model.User;
import java.util.Optional;

@RegisterFeature
public class HubUserForPmcFeature extends BaseFeature {

  private final IdentityService identityService;
  private final BillingService billingService;
  private final PartnerHubService partnerHubService;

  public HubUserForPmcFeature(IdentityService identityService, BillingService billingService,
      PartnerHubService partnerHubService) {
    this.identityService = identityService;
    this.billingService = billingService;
    this.partnerHubService = partnerHubService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    ExtendedBillerAccountData billerAccount = identityService.getBillerAccount(evalParams.getBillerAccountId());
    ComGetflexBillingControllerV2PropertyControllerPropertyResponse property = billingService.getPropertyByBillerId(
        billerAccount.getBillerId());
    if (property.getIntegrationType() != IntegrationTypeEnum.P2P) {
      return new FeatureOutput("Not a P2P property");
    }
    Optional<User> user = partnerHubService.getHubUserByPmcId(property.getPmcId());
    return user.map(FeatureOutput::new)
        .orElseGet(() -> new FeatureOutput("No qualified hub user associated with property"));
  }

  @Override
  public String getDescription() {
    return "Returns the hub user associated with the PMC from the renter's biller account ID.";
  }
}
