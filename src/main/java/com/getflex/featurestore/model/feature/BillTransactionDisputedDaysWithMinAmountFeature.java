package com.getflex.featurestore.model.feature;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.dao.model.event.eventmetadata.BillTransactionDisputedDaysMetadata;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class BillTransactionDisputedDaysWithMinAmountFeature extends BaseFeature {

  private final DisputeService disputeService;
  private final AllowDenyListRepo allowDenyListRepo;
  private static final String BYPASS_DISPUTE_DAYS_FEATURE = "bypass_dispute_days_feature";
  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
      .registerModule(new JavaTimeModule());
  private static final Integer MIN_DISPUTE_CENT = 3000;

  public BillTransactionDisputedDaysWithMinAmountFeature(
      AllowDenyListRepo allowDenyListRepo, DisputeService disputeService) {
    this.allowDenyListRepo = allowDenyListRepo;
    this.disputeService = disputeService;
  }


  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    if (isCustomerAllowedToByPass(customerId)) {
      log.info("allow list bypassing dispute days feature for customerId={}", customerId);
      return new FeatureOutput(-1L);
    }
    List<Dispute> disputes = disputeService.getAllDisputesByCustomerId(evalParams.getCustomerId());
    Optional<Dispute> optionalDispute = Optional.ofNullable(disputes)
        .orElse(Collections.emptyList()).stream()
        .filter(d -> d.getDisputeAmount() != null && d.getDisputeAmount() > MIN_DISPUTE_CENT)
        .filter(d -> d.getDtCreated() != null)
        .max(Comparator.comparing(Dispute::getDtCreated));
    if (optionalDispute.isEmpty()) {
      log.warn("no dispute for customerId={}", customerId);
      return new FeatureOutput(-1L);
    }
    Dispute latestDispute = optionalDispute.get();
    OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
    String metadataString = null;
    try {
      BillTransactionDisputedDaysMetadata metadata = BillTransactionDisputedDaysMetadata.builder()
          .customerId(customerId)
          .latestDisputedDate(Objects.requireNonNull(latestDispute.getDtCreated()).toString())
          .build();
      metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
    } catch (Exception e) {
      throw new InternalServiceBadDataException(
          "Error while parsing metadata of BillTransactionDisputedDaysFeature for customer: %s"
              .formatted(customerId));
    }
    return new FeatureOutput(ChronoUnit.DAYS.between(latestDispute.getDtCreated(), now), metadataString);
  }

  @Override
  public String getDescription() {
    return "Number of days since the last dispute event for a customer";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private boolean isCustomerAllowedToByPass(Long customerId) {
    return
        allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(BYPASS_DISPUTE_DAYS_FEATURE, String.valueOf(customerId),
            true)
            != null;
  }
}
