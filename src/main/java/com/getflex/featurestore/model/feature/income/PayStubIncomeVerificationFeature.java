package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.EvalParamKey.CUSTOMER_ID;
import static com.getflex.featurestore.model.EvalParamKey.VERIFICATION_ID;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.MAXIMUM_VIABLE_RISK_SCORE;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.MINIMUM_VIABLE_RISK_SCORE;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.PAY_STUB_VALID_FOR_DAYS;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.PROCESSING_COMPLETE;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.ZERO_DEFAULT;
import static com.getflex.featurestore.model.feature.income.IncomeUtils.OBJECT_MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStub;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubEligibility;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubEligibilityGrade;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubModel;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubReadabilityGrade;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStub;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStubMetadata;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStubs;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import com.plaid.client.model.CreditPayStub;
import com.plaid.client.model.CreditPayStubEmployee;
import com.plaid.client.model.CreditPayrollIncomeGetResponse;
import com.plaid.client.model.CreditPayrollIncomeRiskSignalsGetResponse;
import com.plaid.client.model.PayStubPayPeriodDetails;
import com.plaid.client.model.PayrollItem;
import com.plaid.client.model.PayrollRiskSignalsItem;
import java.net.URISyntaxException;
import java.time.Clock;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

@Slf4j
@RegisterFeature
public class PayStubIncomeVerificationFeature extends BaseFeature {
  private final IncomeUtils incomeUtils;
  private final Clock etClock;
  private final VerificationService verificationService;

  public PayStubIncomeVerificationFeature(IncomeUtils incomeUtils, Clock etClock,
      VerificationService verificationService) {
    this.incomeUtils = incomeUtils;
    this.etClock = etClock;
    this.verificationService = verificationService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      PayStubModel featureData = getPayStubFeatures(evalParams);
      return new FeatureOutput(featureData);
    } catch (JsonProcessingException e) {
      log.error("unable to deserialize plaid payload {}", e.getMessage());
      throw new EventMetadataParsingException(e.getMessage());
    }
  }

  @Override
  public String getDescription() {
    return "Deserializes and calculates features from income verification results returned from Plaid";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(VERIFICATION_ID, CUSTOMER_ID);
  }

  private PayStubModel getPayStubFeatures(EvalParams evalParams) throws JsonProcessingException {
    Verification verification = verificationService.getVerification(evalParams.getVerificationId());
    Pair<CreditPayrollIncomeGetResponse, CreditPayrollIncomeRiskSignalsGetResponse> payStubIncomeMetadata =
        getPayStubIncomeMetadata(verification);

    Double maxRiskScore = getMaxRiskScore(payStubIncomeMetadata.getRight());
    Boolean isSubmissionFraudulent = isSubmissionFraudulent(maxRiskScore, verification);

    VerifiedPayStubs verifiedPayStubs = verifyPayStubs(evalParams.getCustomerId(),
        payStubIncomeMetadata.getLeft().getItems(), payStubIncomeMetadata.getRight().getItems());

    Double grossAnnualSelfReportedIncomeCent = incomeUtils.getSelfReportedAnnualGrossIncomeCents(
        evalParams.getVerificationId());

    // If verification has a COMPLETED status use VerificationContext numbers
    boolean isCompleted = isCompleted(verification);
    Double verifiedGrossMonthlyIncomeCent = isCompleted
        ? getCompletedVerifiedGrossMonthlyIncomeCent(verification)
        // Use pay stubs that have ALL required fields for automated review
        : calculateVerifiedMonthlyGrossIncomeCents(verifiedPayStubs.getPassGradePayStubs());
    Double grossMonthlyIncomeCent = Math.min(verifiedGrossMonthlyIncomeCent,  grossAnnualSelfReportedIncomeCent / 12);

    return PayStubModel.builder()
        .isSubmissionFraudulent(isSubmissionFraudulent)
        .maxRiskScore(maxRiskScore)
        .grossMonthlyIncomeCent(grossMonthlyIncomeCent)
        .verifiedGrossMonthlyIncomeCent(verifiedGrossMonthlyIncomeCent)
        .grossAnnualSelfReportedIncomeCent(grossAnnualSelfReportedIncomeCent)
        .verifiedPayStubs(verifiedPayStubs)
        .build();
  }

  private VerifiedPayStubs verifyPayStubs(Long customerId, List<PayrollItem> payrollItems,
      List<PayrollRiskSignalsItem> riskSignalsItems) {
    String customerName = incomeUtils.getCustomerFullName(customerId);
    LocalDate dateCutoff = LocalDate.now(etClock).minusDays(PAY_STUB_VALID_FOR_DAYS);

    List<VerifiedPayStub> passGradePayStubs = new ArrayList<>();
    List<VerifiedPayStub> failGradePayStubs = new ArrayList<>();
    List<VerifiedPayStub> uncertainGradePayStubs = new ArrayList<>();

    List<CreditPayStub> allPayStubs = payrollItems.stream()
        .filter(item -> Objects.nonNull(item.getStatus())
            && Objects.equals(item.getStatus().getProcessingStatus(), PROCESSING_COMPLETE))
        .flatMap(item -> item.getPayrollIncome().stream())
        .flatMap(income -> income.getPayStubs().stream())
        .toList();

    List<CreditPayStub> dedupedPayStubs = getDedupedPayStubs(allPayStubs);

    for (CreditPayStub payStub : dedupedPayStubs) {
      PayStubEligibility eligibility = getPayStubEligibility(customerName, dateCutoff, payStub, riskSignalsItems);
      VerifiedPayStub verifiedPayStub = VerifiedPayStub.builder()
          .payStub(OBJECT_MAPPER.convertValue(payStub, PayStub.class)).metadata(eligibility.getMetadata()).build();

      switch (eligibility.getGrade()) {
        case PASS -> passGradePayStubs.add(verifiedPayStub);
        case FAIL -> failGradePayStubs.add(verifiedPayStub);
        case UNCERTAIN -> uncertainGradePayStubs.add(verifiedPayStub);
        default -> throw new RuntimeException("There should not be an unhandled case for eligibility grade.");
      }
    }

    return VerifiedPayStubs.builder().passGradePayStubs(passGradePayStubs).failGradePayStubs(failGradePayStubs)
        .uncertainGradePayStubs(uncertainGradePayStubs).build();
  }

  private List<CreditPayStub> getDedupedPayStubs(List<CreditPayStub> payStubs) {
    return payStubs.stream()
        .filter(distinctByKey((payStub) -> payStub.getPayPeriodDetails().getStartDate()
              + ":" + payStub.getPayPeriodDetails().getEndDate()
              + ":" + payStub.getPayPeriodDetails().getPayDate()
              + ":" + payStub.getPayPeriodDetails().getPayAmount()
              + ":" + payStub.getPayPeriodDetails().getGrossEarnings()
              + ":" + payStub.getPayPeriodDetails().getPayFrequency()
              + ":" + payStub.getEmployee().getName()
              + ":" + payStub.getEmployer().getName()
        )).toList();
  }

  private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = ConcurrentHashMap.newKeySet();
    return t -> seen.add(keyExtractor.apply(t));
  }

  private PayStubEligibility getPayStubEligibility(String customerName, LocalDate dateCutoff,
      CreditPayStub payStub, List<PayrollRiskSignalsItem> riskSignalsItems) {

    Double riskScore = Optional.ofNullable(getRiskScore(payStub.getDocumentId(), riskSignalsItems))
        .orElse(ZERO_DEFAULT);
    PayStubReadabilityGrade readabilityGrade = getReadabilityGrade(riskScore, payStub);
    boolean employeeNamePresent = Optional.ofNullable(payStub.getEmployee())
        .map(CreditPayStubEmployee::getName).orElse(null) != null;
    boolean doesEmployeeNameMatch = Objects.nonNull(payStub.getEmployee().getName())
        && incomeUtils.namesFuzzyMatch(customerName, payStub.getEmployee().getName());
    boolean payDatePresent = Optional.ofNullable(payStub.getPayPeriodDetails())
        .map(PayStubPayPeriodDetails::getPayDate).orElse(null) != null;
    boolean isRecent = Objects.nonNull(payStub.getPayPeriodDetails().getPayDate())
        && payStub.getPayPeriodDetails().getPayDate().isAfter(dateCutoff);

    VerifiedPayStubMetadata metadata = VerifiedPayStubMetadata.builder().riskScore(riskScore)
        .employeeNamePresent(employeeNamePresent).doesEmployeeNameMatch(doesEmployeeNameMatch)
        .payDatePresent(payDatePresent).isRecent(isRecent).build();

    PayStubEligibility.PayStubEligibilityBuilder eligibilityBuilder = PayStubEligibility.builder().metadata(metadata);

    if (readabilityGrade == PayStubReadabilityGrade.FULLY_READABLE && riskScore <= MINIMUM_VIABLE_RISK_SCORE
        && doesEmployeeNameMatch && isRecent) {
      return eligibilityBuilder.grade(PayStubEligibilityGrade.PASS).build();
    }

    if (readabilityGrade == PayStubReadabilityGrade.NOT_READABLE || riskScore > MAXIMUM_VIABLE_RISK_SCORE
        || (employeeNamePresent && !doesEmployeeNameMatch) || (payDatePresent && !isRecent)) {
      return eligibilityBuilder.grade(PayStubEligibilityGrade.FAIL).build();
    }

    return eligibilityBuilder.grade(PayStubEligibilityGrade.UNCERTAIN).build();
  }

  private PayStubReadabilityGrade getReadabilityGrade(Double riskScore, CreditPayStub payStub) {
    if (Objects.nonNull(riskScore)
        && Objects.nonNull(payStub.getEmployer().getName())
        && Objects.nonNull(payStub.getEmployee().getName())
        && hasAllDateFields(payStub)
        && hasAtLeastOneIncomeField(payStub.getPayPeriodDetails())) {
      return PayStubReadabilityGrade.FULLY_READABLE;
    } else if (Objects.isNull(riskScore)
        && Objects.isNull(payStub.getEmployer().getName())
        && Objects.isNull(payStub.getEmployee().getName())
        && !hasAllDateFields(payStub)
        && !hasAtLeastOneIncomeField(payStub.getPayPeriodDetails())) {
      return PayStubReadabilityGrade.NOT_READABLE;
    }

    return PayStubReadabilityGrade.PARTIALLY_READABLE;
  }

  private Double getRiskScore(String documentId, List<PayrollRiskSignalsItem> riskSignalsItems) {
    return riskSignalsItems.stream()
        .flatMap(item -> item.getVerificationRiskSignals().stream())
        .filter(signal -> Objects.nonNull(signal.getSingleDocumentRiskSignals()))
        .flatMap(signal -> signal.getSingleDocumentRiskSignals().stream())
        .filter(single -> Objects.nonNull(single.getDocumentReference())
            && Objects.equals(single.getDocumentReference().getDocumentId(), documentId))
        .map(single -> single.getRiskSummary().getRiskScore())
        .filter(Objects::nonNull).findFirst().orElse(null);
  }

  private Boolean isSubmissionFraudulent(Double maxRiskScore, Verification verification) {
    boolean isSubmissionFraudulentByRiskScore = maxRiskScore > MAXIMUM_VIABLE_RISK_SCORE;
    if (isCompleted(verification)) {
      Optional<Boolean> overriddenIsSubmissionFraudulent = Optional.ofNullable(verification.getContext())
          .map(VerificationContext::getValidationOverride)
          .map(Validation::getIsSubmissionFraudulent);

      Optional<Boolean> isSubmissionFraudulent = Optional.ofNullable(verification.getContext())
          .map(VerificationContext::getValidation)
          .map(Validation::getIsSubmissionFraudulent);

      return overriddenIsSubmissionFraudulent.orElse(isSubmissionFraudulent.orElse(isSubmissionFraudulentByRiskScore));
    }

    return isSubmissionFraudulentByRiskScore;
  }

  private Double getMaxRiskScore(CreditPayrollIncomeRiskSignalsGetResponse riskSignals) {
    return riskSignals.getItems().stream()
        .flatMap(item -> item.getVerificationRiskSignals().stream())
        .filter(signal -> Objects.nonNull(signal.getSingleDocumentRiskSignals()))
        .flatMap(signal -> signal.getSingleDocumentRiskSignals().stream())
        .map(single -> single.getRiskSummary().getRiskScore()).filter(Objects::nonNull)
        .max(Double::compare)
        .orElse(ZERO_DEFAULT);
  }

  private Double getCompletedVerifiedGrossMonthlyIncomeCent(Verification verification) {
    Optional<Long> overriddenGrossMonthlyIncome = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getValidationOverride)
        .map(Validation::getGrossMonthlyIncomeCent);

    Optional<Long> grossMonthlyIncome = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getValidation)
        .map(Validation::getGrossMonthlyIncomeCent);

    return Double.valueOf(overriddenGrossMonthlyIncome.orElse(grossMonthlyIncome.orElse(ZERO_DEFAULT.longValue())));
  }

  private Double calculateVerifiedMonthlyGrossIncomeCents(List<VerifiedPayStub> eligiblePayStubs) {
    if (eligiblePayStubs.isEmpty()) {
      return ZERO_DEFAULT;
    }

    LocalDate earliestDate = null;
    LocalDate latestDate = null;

    // Determining earliest and latest date on eligible pay stubs
    for (VerifiedPayStub verifiedPayStub : eligiblePayStubs) {
      if (earliestDate == null || verifiedPayStub.getPayStub().getPayPeriodDetails().getStartDate()
          .isBefore(earliestDate)) {
        earliestDate = verifiedPayStub.getPayStub().getPayPeriodDetails().getStartDate();
      }
      if (latestDate == null || verifiedPayStub.getPayStub().getPayPeriodDetails().getEndDate().isAfter(latestDate)) {
        latestDate = verifiedPayStub.getPayStub().getPayPeriodDetails().getEndDate();
      }
    }

    Double sumGrossEarningsCents =
        eligiblePayStubs.stream()
            .map(verifiedPayStub -> verifiedPayStub.getPayStub().getPayPeriodDetails().getGrossEarnings())
            .filter(Objects::nonNull)
            .reduce(Double::sum).orElse(ZERO_DEFAULT) * 100;

    return (sumGrossEarningsCents / ChronoUnit.DAYS.between(earliestDate, latestDate)) * 30;
  }

  private boolean hasAllDateFields(CreditPayStub payStub) {
    return Objects.nonNull(payStub.getPayPeriodDetails().getStartDate())
        && Objects.nonNull(payStub.getPayPeriodDetails().getEndDate())
        && Objects.nonNull(payStub.getPayPeriodDetails().getPayDate());
  }

  private boolean hasAtLeastOneIncomeField(PayStubPayPeriodDetails payPeriodDetails) {
    return Objects.nonNull(payPeriodDetails.getGrossEarnings()) || Objects.nonNull(payPeriodDetails.getPayAmount());
  }

  private Pair<CreditPayrollIncomeGetResponse, CreditPayrollIncomeRiskSignalsGetResponse> getPayStubIncomeMetadata(
      Verification verification) {

    String plaidReportS3Uri = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getPlaidReportS3Uri).orElseThrow(() ->
            new FeatureNotFoundException("Could not find S3 uri to fetch Plaid report from"));

    PayStubIncomeVerificationReports reports = downloadPayStubReports(plaidReportS3Uri);

    return Pair.of(reports.getPayStubIncomeOutput(), reports.getDocumentRiskOutput());
  }

  private PayStubIncomeVerificationReports downloadPayStubReports(String s3Uri) {
    try {
      return OBJECT_MAPPER.readValue(incomeUtils.downloadReports(s3Uri), PayStubIncomeVerificationReports.class);
    } catch (URISyntaxException | JsonProcessingException e) {
      throw new EventMetadataParsingException(e);
    }
  }

  private boolean isCompleted(Verification verification) {
    return verification.getStatus() != null && "COMPLETED".equals(verification.getStatus());
  }

}
