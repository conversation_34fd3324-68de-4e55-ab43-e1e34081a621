package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;


@RegisterFeature
public class HasSentilinkCarrierIndicatorFeature extends BaseOfflineFeature {

  public HasSentilinkCarrierIndicatorFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Checks if the customer's carrier indicator is in the sentilink earliest alloy report";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "sentilink_carrier_indicator";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String featureValue = super.getRawFeatureValue(evalParams);

    if (featureValue == null) {
      return new FeatureOutput(false, null);
    }

    return new FeatureOutput(true, null);
  }

}
