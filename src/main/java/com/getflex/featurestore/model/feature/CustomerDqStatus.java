package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.DelinquencyStatusResponse;
import java.time.Clock;
import java.time.LocalDate;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerDqStatus extends BaseFeature {

  private final OfferService offerService;
  private final Clock etClock;

  public CustomerDqStatus(OfferService offerService, Clock etClock) {
    this.offerService = offerService;
    this.etClock = etClock;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    DelinquencyStatusResponse resp = offerService.getDqStatus(evalParams.getCustomerId());
    return new FeatureOutput(new DqStatus(resp.getIsDelinquent(), calculateBpDate(resp), resp.getDelinquentForDays()));
  }

  /**
   * Get normalized BP date related to current DQ status.
   *
   * @param resp
   * @return
   */
  private LocalDate calculateBpDate(DelinquencyStatusResponse resp) {
    if (!resp.getIsDelinquent()) {
      return null;
    }
    long dqDays = Optional.ofNullable(resp.getDelinquentForDays()).orElse(0L);
    return LocalDate.now(etClock).minusDays(dqDays + 1L).withDayOfMonth(1);
  }

  @Override
  public String getDescription() {
    return "Get customer current DQ status";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  record DqStatus(boolean isDq, LocalDate dqBpDate, Long dqDays) {
  }
}
