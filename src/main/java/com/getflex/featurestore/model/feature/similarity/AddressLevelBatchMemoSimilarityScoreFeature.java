package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.FeatureUtils;
import com.getflex.featurestore.model.feature.base.SimilarityScoreBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;

@RegisterFeature
public class AddressLevelBatchMemoSimilarityScoreFeature extends SimilarityScoreBaseFeature {

  private final IdentityService identityService;

  public AddressLevelBatchMemoSimilarityScoreFeature(OfflineFeatureRepo offlineFeatureRepo,
      IdentityService identityService) {
    super(offlineFeatureRepo);
    this.identityService = identityService;
  }

  @Override
  protected String getOfflineFeatureName() {
    return "address_level_batch_memo_history";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    GetCustomerResponse customer = this.identityService.getCustomer(evalParams.getCustomerId());
    return FeatureUtils.sanitizeCustomerAddress(customer);
  }

  @Override
  public String getDescription() {
    return "Get the similarity score between batch memo and address level batch memo history list";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.CUSTOMER_ID);
  }
}
