package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RegisterFeature
public class CustomerDaysSinceLastSuspensionFeature extends BaseFeature {
  private final OfferService offerService;

  @Autowired
  public CustomerDaysSinceLastSuspensionFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Object value;
    Long customerId = evalParams.getCustomerId();
    InternalOffer offer = offerService.getLastSuspension(customerId);
    // if there's no suspension, return the maximum value, rule can evaluate as suspension happened in the far past
    if (offer == null) {
      return new FeatureOutput(Integer.MAX_VALUE);
    }
    OffsetDateTime terminateTime = offer.getTerminationTime();
    if (terminateTime == null) {
      throw new FeatureNotFoundException("No terminate time found. customerId=%s, offerId=%s"
          .formatted(customerId, offer.getOfferId()));
    }
    // The number of days between terminate time and now cannot exceed Integer.MAX_VALUE.
    // If it does, it means that terminate time is incorrect, likely set to an unrealistically far past date.
    try {
      value = Math.toIntExact(ChronoUnit.DAYS.between(terminateTime, OffsetDateTime.now()));
    } catch (ArithmeticException e) {
      throw new InternalServiceBadDataException("Bad terminate received. customerId=%s, acceptanceTime=%s"
          .formatted(customerId, terminateTime));
    }
    return new FeatureOutput(value);
  }

  @Override
  public String getDescription() {
    return "Number of days since the customer last suspension";
  }
}
