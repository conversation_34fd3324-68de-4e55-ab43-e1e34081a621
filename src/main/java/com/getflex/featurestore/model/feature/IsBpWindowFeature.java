package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.Clock;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

@RegisterFeature
public class IsBpWindowFeature extends BaseFeature {
  private final Clock etClock;

  public IsBpWindowFeature(Clock etClock) {
    super();
    this.etClock = etClock;
  }


  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }


  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(isDateInBpRange(ZonedDateTime.now(etClock)));
  }

  @Override
  public String getDescription() {
    return "Check if the current date is in the BP window (T-4 to T+4)";
  }

  public boolean isDateInBpRange(ZonedDateTime date) {
    date = date.truncatedTo(ChronoUnit.DAYS);

    ZonedDateTime firstDayOfMonth = date.withDayOfMonth(1);
    ZonedDateTime lastDayOfMonth = date.withDayOfMonth(date.toLocalDate().lengthOfMonth());
    ZonedDateTime fourthDayAfterFirst = firstDayOfMonth.plusDays(4);
    ZonedDateTime fourthDayBeforeFirst = lastDayOfMonth.minusDays(3);

    Boolean isInTminus4Range = ((date.isEqual(fourthDayBeforeFirst) || date.isAfter(fourthDayBeforeFirst))
        && (date.isBefore(lastDayOfMonth) || date.isEqual(lastDayOfMonth)));
    Boolean isInTplus4Range = ((date.isEqual(firstDayOfMonth) || date.isAfter(firstDayOfMonth))
        && (date.isBefore(fourthDayAfterFirst) || date.isEqual(fourthDayAfterFirst)));

    return isInTminus4Range || isInTplus4Range;
  }

}
