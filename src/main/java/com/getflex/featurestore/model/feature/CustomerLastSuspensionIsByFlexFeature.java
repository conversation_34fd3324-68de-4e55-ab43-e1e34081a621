package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.Objects;

@RegisterFeature
public class CustomerLastSuspensionIsByFlexFeature extends BaseFeature {

  private final OfferService offerService;

  public CustomerLastSuspensionIsByFlexFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    InternalOffer offer = offerService.getLastSuspension(customerId);
    if (offer == null) {
      return new FeatureOutput(false);
    }
    return new FeatureOutput(!Objects.equals(offer.getDeactivationReasonDetail(), "UserInitiated"));
  }

  @Override
  public String getDescription() {
    return "Customer has last suspended offer by Flex";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
