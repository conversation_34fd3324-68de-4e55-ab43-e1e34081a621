package com.getflex.featurestore.model.feature.cmmv3;

public enum CmmV3Inputs {
  NUMBER_OF_BILL_PAID_LAST_6MONTHS,
  BP_RATE_LAST_12MONTHS,
  NUMBER_OF_BILL_PAID_LAST_12MONTHS,
  BP_RATE_LAST_6MONTHS,
  NUMBER_OF_BILL_PAID_LAST_24MONTHS,
  NUMBER_OF_BP_INITIATION_LAST_12MONTHS,
  NUMBER_OF_BP_INITIATION_LAST_6MONTHS,
  MONTHS_INACTIVE_L24,
  NSF_RATE_LAST_24MONTHS,
  NSF_RATE_LAST_6MONTHS,
  MONTHS_IN_SUSPENSION_LAST_24MONTHS,
  MONTHS_ACTIVE_LAST_6MONTHS,
  MONTHS_SINCE_LAST_SIGNUP,
  AVERAGE_CREDIT_UTILIZATION_RATIO_LAST_6MONTHS,
  CPR_VANTAGE40_SCORE,
  CPR_EADS142_G224C,
  CPR_CFFE02_COLTOT,
  CPR_EADS142_S061S,
  CPR_CFFE02_COLNMED,
  CPR_CFFE02_BALGRE,
  CPR_CFFE02_ACCTDE,
  CPR_CFFE02_MONDE,
  CPR_CFFE02_ACT24M,
  CPR_CFFE02_HCLGRE,
  CPR_CFFE02_IACCT24,
  CPR_EADS142_BR02S,
  CPR_EADS142_G411S,
  CPR_CFFE02_MONOREV,
  CPR_CFFE02_REV24M,
  CPR_CFFE02_ACCTREG,
  CPR_EADS142_AT24S,
  CPR_CFFE02_REV100UT,
  CPR_CFFE02_REVUT,
  CPR_CFFE02_RETOP,
  CDQ0_FREQ_1MON,
  CDQ0_FREQ_6MON,
  DQ0_FREQ_LAST_12MONTHS,
  CDQ0_FREQ_24MON,
  CDQ_UP_TO_30_FREQ_1MON,
  MONTHS_DI_LAST_12MONTHS,
  PAY_SUCCESS_RATE_24MON,
  PAY_SUCCESS_RATE_6MON,
  PAY_SUCCESS_RATE_3MON,
  PAY_SUCCESS_RATE_1MON,
  PAY_SUCCESS_RATE_6MON_GREATERTHAN50,
  PAY_SUCCESS_RATE_1MON_GREATERTHAN50,
  PAY_SUCCESS_RATE_DOWN_0MON,
  AVG_DAYS_REPAYMENT_LEFT_1MON,
  AVG_DAYS_REPAYMENT_LEFT_3MON,
  AVG_DAYS_REPAYMENT_LEFT_24MON,
  AVG_DAYS_REPAYMENT_LEFT_6MON,
  AVG_TRANS_24MON,
  AVG_TRANS_3MON,
  AVG_DECLINE_INSUFF_FUNDS_3MON,
  AVG_TRANS_0MON,
  AVG_DECLINE_INSUFF_FUNDS_1MON_GREATERTHAN50,
  AVG_DECLINE_INSUFF_FUNDS_0MON,
  AVG_TRANS_1MON_GREATERTHAN50,
  MAX_DAYS_DOWN_PAID_1MON,
  AVG_DECLINE_INSUFF_FUNDS_6MON_GREATERTHAN50,
  MAX_DAYS_DOWN_PAID_0MON,
  PAYMENT_RESCHULED_1MON,
  ACCT_STATS_1MON,
  BP_SUCCESS_0MON,
  BP_OUTCOME_1MON,
  BP_ATTEMPS_0MON,
  ACCOUNT_STATE_0MON;

  public static CmmV3Inputs fromValue(String value) {
    for (CmmV3Inputs type : CmmV3Inputs.values()) {
      if (type.name().equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid value for this enum: " + value);
  }
}
