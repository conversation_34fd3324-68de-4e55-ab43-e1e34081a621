package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerEprVantageScoreLastPullDaysFeature extends BaseOfflineFeature {

  private final OfflineFeatureRepo offlineFeatureRepo;

  public CustomerEprVantageScoreLastPullDaysFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Optional<OfflineFeature> optionalOfflineFeature = offlineFeatureRepo
            .findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
                    this.getOfflineFeatureName(),
                    this.getPrimaryKey(evalParams)
            ).stream().findFirst();
    if (optionalOfflineFeature.isEmpty()) {
      return new FeatureOutput(-1L);
    }
    OffsetDateTime dtCreated = optionalOfflineFeature.get().getDtCreated();
    LocalDate now = LocalDate.now();
    LocalDate createdDate = dtCreated.toLocalDate();
    long diffDays = ChronoUnit.DAYS.between(createdDate, now);
    return new FeatureOutput(diffDays);
  }

  @Override
  public String getDescription() {
    return "Number of days since the last Vantage Score pull for the customer";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_epr_vantage_score";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }
}
