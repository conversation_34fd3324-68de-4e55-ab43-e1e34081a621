package com.getflex.featurestore.model.feature.proxyrent;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class CityNameRentMeanFeature extends ProxyRentOfflineFeature {

  public static final String OFFLINE_FEATURE_NAME = "city_name_rent_mean_feature";

  public CityNameRentMeanFeature(ProxyRentUtils proxyRentUtils, OfflineFeatureRepo offlineFeatureRepo) {
    super(proxyRentUtils, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Get the avg_bill_payment_amount value for a soundex representation of a provided city name.";
  }

  @Override
  protected String getOfflineFeatureName() {
    return OFFLINE_FEATURE_NAME;
  }
}
