package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.event.eventmetadata.LedgerBalanceCentsMetadata;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RegisterFeature
public class CustomerLedgerBalanceCentsFeature extends BaseFeature {

  private final IdentityService identityService;
  private final LedgerService ledgerService;
  private final AllowDenyListRepo allowDenyListRepo;
  // use case for bypass DQ check
  private static final String ALLOW_DENY_USE_CASE = "2023_jan_dq_override";
  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

  public CustomerLedgerBalanceCentsFeature(IdentityService identityService, LedgerService ledgerService,
      AllowDenyListRepo allowDenyListRepo) {
    this.identityService = identityService;
    this.ledgerService = ledgerService;
    this.allowDenyListRepo = allowDenyListRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    GetCustomerResponse customer = identityService.getCustomer(customerId);
    Long balance = ledgerService.getCustomerOutstandingBalance(customer.getCustomerPublicId()) * -1;
    String metadataString = null;
    try {
      if (balance < 0) {
        log.info("Customer {} is unbalanced. credit={} cents", customerId, balance);
        LedgerBalanceCentsMetadata metadata = LedgerBalanceCentsMetadata.builder().customerId(customerId)
            .unbalancedAmountCents(balance).build();
        if (this.isCustomerAllowlisted(customerId)) {
          log.info("Customer {} is allowlisted to pass the check", customerId);
          metadata.setAllowlisted(true);
          balance = 0L;
        }
        metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
      }
    } catch (Exception e) {
      log.error("Failed to create metadata for customer {} with balance {}", customerId, balance, e);
    }

    return new FeatureOutput(balance, metadataString);
  }

  @Override
  public String getDescription() {
    return "customer's ledger balance in cents";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private boolean isCustomerAllowlisted(Long customerId) {
    return
        allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(ALLOW_DENY_USE_CASE, String.valueOf(customerId), true)
            != null;
  }
}
