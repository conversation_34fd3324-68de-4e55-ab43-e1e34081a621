package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class CustomerBalanceFeature extends BaseFeature {

  private LedgerService ledgerService;

  public CustomerBalanceFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    return new FeatureOutput(ledgerService.getCustomerCreditBalance(customerId));
  }

  @Override
  public String getDescription() {
    return "This feature returns current credit balance of given customer.";
  }
}
