package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelV2Input;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.feature.base.BaseModelFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.concurrent.ExecutorService;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@RegisterFeature
public class FraudTransactionModelV2ScoreFeature extends BaseModelFeature<FraudTransactionModelV2Input> {

  private static final List<FraudTransactionModelV2Input> MODEL_INPUT_FEATURES_LIST =
      new ArrayList<>(EnumSet.allOf(FraudTransactionModelV2Input.class));

  private final ServiceConfig serviceConfig;

  public FraudTransactionModelV2ScoreFeature(SageMakerRuntimeClient sageMaker, FeatureFactory featureFactory,
       ExecutorService executorService, ServiceConfig serviceConfig) {
    super(sageMaker, executorService, featureFactory);
    this.serviceConfig = serviceConfig;
  }

  @Override
  public String getDescription() {
    return "Fraud Transaction Model V2 Score";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public List<FraudTransactionModelV2Input> getModelFeaturesList() {
    return MODEL_INPUT_FEATURES_LIST;
  }

  @Override
  protected String getEndpointName() {
    return serviceConfig.getFtmV2Endpoint();
  }
}
