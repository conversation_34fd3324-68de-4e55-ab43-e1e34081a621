package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.utils.FlexConstant.CSV_UTF8_CONTENT_TYPE;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelV3Input;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.feature.base.BaseModelFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import org.json.simple.JSONObject;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RegisterFeature
public class FraudTransactionModelV3ScoreFeature extends BaseModelFeature<FraudTransactionModelV3Input> {

  private static final List<FraudTransactionModelV3Input> MODEL_INPUT_FEATURES_LIST =
      new ArrayList<>(EnumSet.allOf(FraudTransactionModelV3Input.class));

  private final ServiceConfig serviceConfig;

  public FraudTransactionModelV3ScoreFeature(
      SageMakerRuntimeClient sageMaker,
      FeatureFactory featureFactory,
      ExecutorService executorService,
      ServiceConfig serviceConfig
  ) {
    super(sageMaker, executorService, featureFactory);
    this.serviceConfig = serviceConfig;
  }

  @Override
  public String getDescription() {
    return "Fraud Transaction Model V3 Score";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public List<FraudTransactionModelV3Input> getModelFeaturesList() {
    return MODEL_INPUT_FEATURES_LIST;
  }

  @Override
  protected String getEndpointName() {
    return serviceConfig.getFtmV3Endpoint();
  }

  @Override
  protected FeatureOutput toFeatureOutput(
      InvokeEndpointResponse response,
      Map<FraudTransactionModelV3Input, Number> featureValuesMap
  ) {
    if (CSV_UTF8_CONTENT_TYPE.equalsIgnoreCase(response.contentType())) {
      String value = response.body().asUtf8String();
      Map<FraudTransactionModelV3Input, String> updatedFeatureValuesMap =
          featureValuesMap.entrySet().stream()
              .collect(
                  Collectors.toMap(
                      Map.Entry::getKey,
                      entry -> resolveValue(entry.getValue())
                  )
              );
      return new FeatureOutput(Double.valueOf(value), new JSONObject(updatedFeatureValuesMap).toJSONString());
    }

    throw new RuntimeException("Unsupported Sagemaker output content type");
  }

  private String resolveValue(Number entryValue) {
    return !entryValue.equals(DEFAULT_MODEL_INPUT_VALUE)
        ? entryValue.toString()
        : String.format("%s (actual), %s (imputed)", null, entryValue);
  }
}
