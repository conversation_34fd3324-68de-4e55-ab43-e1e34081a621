package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@Slf4j
@RegisterFeature
public class BillerLevelBatchMemoNlpSimilarityScoreFeature extends BaseNlpSimilarityScoreFeature {

  private static final String FEATURE_NAME = "biller_level_batch_memo_history";

  private final OfflineFeatureRepo offlineFeatureRepo;

  public BillerLevelBatchMemoNlpSimilarityScoreFeature(OfflineFeatureRepo offlineFeatureRepo,
      SageMakerRuntimeClient sagemaker) {
    super(sagemaker);
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public String getDescription() {
    return "Biller level similarity score using NLP model";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ID);
  }

  @Override
  public List<String> getTargetList(EvalParams evalParams) {
    String key = evalParams.getBillerId().toString();
    Optional<OfflineFeature> history = this.offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        FEATURE_NAME, key);
    return history.map(
        offlineFeature -> Arrays.stream(offlineFeature.getFeatureValue().split("\\|"))
            .map(
                String::trim).toList()).orElseGet(List::of);
  }
}
