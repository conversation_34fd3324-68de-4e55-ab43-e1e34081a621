package com.getflex.featurestore.model.feature;

import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.billPaymentMethod.model.MethodType;
import com.getflex.billPaymentMethod.openapi.BillPaymentMethodApi;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.BillPaymentService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;

@RegisterFeature
public class Downpayment3DsSelfPayTypeDdaFeature extends BaseFeature {

  private final AutopayService autopayService;
  private final IdentityService identityService;
  private final BillPaymentService billPaymentService;

  public Downpayment3DsSelfPayTypeDdaFeature(
      AutopayService autopayService,
      IdentityService identityService,
      BillPaymentService billPaymentService) {
    super();
    this.autopayService = autopayService;
    this.identityService = identityService;
    this.billPaymentService = billPaymentService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.STRING;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    SearchAutopayTasksResponse autopay = autopayService.getCurrentAutoPay(
        customer.getCustomerPublicId(),
        FeatureUtils.getCurrentBpDateTime().toLocalDate()
    );
    if (
        autopay != null && autopay.getAutopayTasks() != null
            && !autopay.getAutopayTasks().isEmpty()
            && autopay.getAutopayTasks().get(0).getBillTransactionId() != null) {
      return new FeatureOutput(this.billPaymentService.getFlexAnywhereBillPaymentType(
          customer.getCustomerPublicId(),
          autopay.getAutopayTasks().get(0).getBillTransactionId()
      ).toString());
    }
    return new FeatureOutput(MethodType.DDA.toString());
  }

  @Override
  public String getDescription() {
    return "Get Customer Selfpay DDA type, either 'DDA' or 'VC'";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
