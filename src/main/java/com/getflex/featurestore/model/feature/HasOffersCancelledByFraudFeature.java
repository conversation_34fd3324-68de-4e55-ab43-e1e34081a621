package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class HasOffersCancelledByFraudFeature extends BaseFeature {

  private static final String CANCEL_CREDIT_LINE_DEACTIVATION_REASON = "CancelCreditLine";
  private static final String FRAUD_DEACTIVATION_REASON_DETAIL = "Fraud";

  private final OfferService offerService;

  public HasOffersCancelledByFraudFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    boolean hasFraudHistory = false;
    try {
      List<InternalOffer> offers = offerService.searchOffer(evalParams.getCustomerId(), true);

      hasFraudHistory = offers.stream()
          .anyMatch(o -> CANCEL_CREDIT_LINE_DEACTIVATION_REASON.equals(o.getDeactivationReason())
              && FRAUD_DEACTIVATION_REASON_DETAIL.equals(o.getDeactivationReasonDetail()));
    } catch (InternalDependencyFailureException e) {
      log.error("Error searching offers for customer {}, {}", evalParams.getCustomerId(), e.getMessage());
    }

    return new FeatureOutput(hasFraudHistory);
  }

  @Override
  public String getDescription() {
    return "Returns whether or not customer has an offer history of fraud";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
