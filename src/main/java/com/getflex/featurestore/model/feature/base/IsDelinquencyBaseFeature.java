package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.service.delinquency.DelinquencyInfo;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import java.time.Clock;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class IsDelinquencyBaseFeature extends BaseFeature {

  protected LedgerService ledgerService;
  protected Clock clock;
  protected DelinquencyService delinquencyService;

  public IsDelinquencyBaseFeature(LedgerService ledgerService, Clock clock, DelinquencyService delinquencyService) {
    super();
    this.ledgerService = ledgerService;
    this.clock = clock;
    this.delinquencyService = delinquencyService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  protected Boolean isDelinquency(Long customerId, Integer dqMonths, Integer lookBackMonths, Boolean excludeCb) {
    DelinquencyInfo delinquencyInfo = delinquencyService.getDelinquencyInfo(
        customerId, dqMonths, lookBackMonths, excludeCb);
    return delinquencyInfo.isDq();
  }
}
