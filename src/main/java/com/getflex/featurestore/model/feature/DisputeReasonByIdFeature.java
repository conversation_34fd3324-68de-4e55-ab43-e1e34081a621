package com.getflex.featurestore.model.feature;

import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import java.util.Optional;


@RegisterFeature
public class DisputeReasonByIdFeature extends BaseFeature {

  private DisputeService disputeService;

  public DisputeReasonByIdFeature(DisputeService disputeService) {
    super();
    this.disputeService = disputeService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.STRING;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.DISPUTE_ID);
  }

  /**
   * This feature filters for the expected dispute in memory since dispute service currently does not expose an API
   * for searching by dispute ID. While we certainly could add it, this approach is performant since it's used together
   * with other features that already cache the customer's lifetime disputes.
   *
   * @param evalParams
   * @return
   */
  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    List<Dispute> lifetimeDisputes = disputeService.getAllDisputesByCustomerId(customerId);

    Long latestDisputeId = evalParams.getDisputeId();
    if (!lifetimeDisputes.stream().anyMatch(d -> d.getId().equals(latestDisputeId))) {
      throw new InternalDependencyFailureException(
          "Expected dispute with id " + latestDisputeId + " does not exist in customer's disputes");
    }

    Optional<Dispute> latestDispute = lifetimeDisputes.stream()
        .filter(d -> d.getId().equals(latestDisputeId))
        .findFirst();

    return new FeatureOutput(latestDispute.get().getStripeDisputeReason());
  }

  @Override
  public String getDescription() {
    return "This feature returns the number of disputes for a customer in the past 6 months.";
  }
}
