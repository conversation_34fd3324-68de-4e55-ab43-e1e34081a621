package com.getflex.featurestore.model.feature.output;

import com.getflex.featurestore.model.dto.OfferSocureFeatureDto;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerUnderwritingScoresOutput {

  private static final Integer DEFAULT_FRAUD_REASON_CODE_SCORE = 0;
  private static final Integer MAX_FRAUD_REASON_CODE_SCORE = 1;

  private Double sigmaScore;
  private Float addressRiskScore;
  private Float emailRiskScore;
  private Float nameAddressCorrelationScore;
  private Float nameEmailCorrelationScore;
  private Float namePhoneCorrelationScore;
  private Float phoneRiskScore;
  private Float syntheticScore;
  private Integer flexScoreDecile;
  private SentilinkScores sentilinkScores;
  private Integer fraudReasonCodeR617;
  private Integer fraudReasonCodeR665;
  private Integer fraudReasonCodeI161;
  private Integer fraudReasonCodeR021;

  public static CustomerUnderwritingScoresOutput fromSocureFeature(OfferSocureFeatureDto offerSocureFeatureDto) {
    CustomerUnderwritingScoresOutput output = new CustomerUnderwritingScoresOutput();
    if (offerSocureFeatureDto.getSigmaScore() != null) {
      output.setSigmaScore(offerSocureFeatureDto.getSigmaScore());
    }
    if (offerSocureFeatureDto.getAddressRiskScore() != null) {
      output.setAddressRiskScore(offerSocureFeatureDto.getAddressRiskScore());
    }
    if (offerSocureFeatureDto.getEmailRiskScore() != null) {
      output.setEmailRiskScore(offerSocureFeatureDto.getEmailRiskScore());
    }
    if (offerSocureFeatureDto.getNameAddressCorrelationScore() != null) {
      output.setNameAddressCorrelationScore(offerSocureFeatureDto.getNameAddressCorrelationScore());
    }
    if (offerSocureFeatureDto.getNameEmailCorrelationScore() != null) {
      output.setNameEmailCorrelationScore(offerSocureFeatureDto.getNameEmailCorrelationScore());
    }
    if (offerSocureFeatureDto.getNamePhoneCorrelationScore() != null) {
      output.setNamePhoneCorrelationScore(offerSocureFeatureDto.getNamePhoneCorrelationScore());
    }
    if (offerSocureFeatureDto.getPhoneRiskScore() != null) {
      output.setPhoneRiskScore(offerSocureFeatureDto.getPhoneRiskScore());
    }
    if (offerSocureFeatureDto.getSyntheticScore() != null) {
      output.setSyntheticScore(offerSocureFeatureDto.getSyntheticScore());
    }
    return output;
  }

  public void upsertMaxScore(InternalOfferAllOfEvaluationContext evalContext) {
    Double sigmaScore = evalContext.getSocureSigmaScore();
    if (sigmaScore != null) {
      if (this.sigmaScore == null || sigmaScore > this.sigmaScore) {
        this.sigmaScore = sigmaScore;
      }
    }

    Float addressRiskCore = evalContext.getSocureAddressRiskScore();
    if (addressRiskCore != null) {
      if (this.addressRiskScore == null || addressRiskCore > this.addressRiskScore) {
        this.addressRiskScore = addressRiskCore;
      }
    }

    Float emailRiskScore = evalContext.getSocureEmailRiskScore();
    if (emailRiskScore != null) {
      if (this.emailRiskScore == null || emailRiskScore > this.emailRiskScore) {
        this.emailRiskScore = emailRiskScore;
      }
    }

    Float nameAddressCorrelationScore = evalContext.getSocureNameAddressCorrelationScore();
    if (nameAddressCorrelationScore != null) {
      if (this.nameAddressCorrelationScore == null || nameAddressCorrelationScore > this.nameAddressCorrelationScore) {
        this.nameAddressCorrelationScore = nameAddressCorrelationScore;
      }
    }

    Float nameEmailCorrelationScore = evalContext.getSocureNameEmailCorrelationScore();
    if (nameEmailCorrelationScore != null) {
      if (this.nameEmailCorrelationScore == null || nameEmailCorrelationScore > this.nameEmailCorrelationScore) {
        this.nameEmailCorrelationScore = nameEmailCorrelationScore;
      }
    }

    Float namePhoneCorrelationScore = evalContext.getSocureNamePhoneCorrelationScore();
    if (namePhoneCorrelationScore != null) {
      if (this.namePhoneCorrelationScore == null || namePhoneCorrelationScore > this.namePhoneCorrelationScore) {
        this.namePhoneCorrelationScore = namePhoneCorrelationScore;
      }
    }

    Float phoneRiskScore = evalContext.getSocurePhoneRiskScore();
    if (phoneRiskScore != null) {
      if (this.phoneRiskScore == null || phoneRiskScore > this.phoneRiskScore) {
        this.phoneRiskScore = phoneRiskScore;
      }
    }

    Float syntheticScore = evalContext.getSocureSyntheticScore();
    if (syntheticScore != null) {
      if (this.syntheticScore == null || syntheticScore > this.syntheticScore) {
        this.syntheticScore = syntheticScore;
      }
    }

    Integer flexScoreDecile = evalContext.getFlexScoreDecile();
    if (flexScoreDecile != null) {
      if (this.flexScoreDecile == null || flexScoreDecile > this.flexScoreDecile) {
        this.flexScoreDecile = flexScoreDecile;
      }
    }

    Set<String> fraudReasonCodes = evalContext.getSocureFraudReasonCodes();
    Boolean validFraudReasonCodes = fraudReasonCodes != null && !fraudReasonCodes.isEmpty();
    this.fraudReasonCodeR617 = setFraudReasonScore(fraudReasonCodes, validFraudReasonCodes, "R617");
    this.fraudReasonCodeR665 = setFraudReasonScore(fraudReasonCodes, validFraudReasonCodes, "R665");
    this.fraudReasonCodeI161 = setFraudReasonScore(fraudReasonCodes, validFraudReasonCodes, "I161");
    this.fraudReasonCodeR021 = setFraudReasonScore(fraudReasonCodes, validFraudReasonCodes, "R021");

    if (this.sentilinkScores == null) {
      this.sentilinkScores = new SentilinkScores();
    }

    Integer firstPartySyntheticScore = evalContext.getSentilinkFirstPartySyntheticScore();
    if (firstPartySyntheticScore != null) {
      if (this.sentilinkScores.getFirstPartySyntheticScore() == null
          || firstPartySyntheticScore > this.sentilinkScores.getFirstPartySyntheticScore()
      ) {
        this.sentilinkScores.setFirstPartySyntheticScore(firstPartySyntheticScore);
      }
    }

    Integer thirdPartySyntheticScore = evalContext.getSentilinkThirdPartySyntheticScore();
    if (thirdPartySyntheticScore != null) {
      if (this.sentilinkScores.getThirdPartySyntheticScore() == null
          || thirdPartySyntheticScore > this.sentilinkScores.getThirdPartySyntheticScore()
      ) {
        this.sentilinkScores.setThirdPartySyntheticScore(thirdPartySyntheticScore);
      }
    }

    Integer abuseScore = evalContext.getSentilinkAbuseScore();
    if (abuseScore != null) {
      if (this.sentilinkScores.getAbuseScore() == null || abuseScore > this.sentilinkScores.getAbuseScore()) {
        this.sentilinkScores.setAbuseScore(abuseScore);
      }
    }

    Integer idTheftScore = evalContext.getSentilinkIdTheftScore();
    if (idTheftScore != null) {
      if (this.sentilinkScores.getIdTheftScore() == null || idTheftScore > this.sentilinkScores.getIdTheftScore()) {
        this.sentilinkScores.setIdTheftScore(idTheftScore);
      }
    }
  }

  public void updateSocureMaxScoreFromOtherOutput(CustomerUnderwritingScoresOutput other) {
    if (other.getSigmaScore() != null) {
      if (this.sigmaScore == null || other.getSigmaScore() > this.sigmaScore) {
        this.sigmaScore = other.getSigmaScore();
      }
    }

    if (other.getAddressRiskScore() != null) {
      if (this.addressRiskScore == null || other.getAddressRiskScore() > this.addressRiskScore) {
        this.addressRiskScore = other.getAddressRiskScore();
      }
    }

    if (other.getEmailRiskScore() != null) {
      if (this.emailRiskScore == null || other.getEmailRiskScore() > this.emailRiskScore) {
        this.emailRiskScore = other.getEmailRiskScore();
      }
    }

    if (other.getNameAddressCorrelationScore() != null) {
      if (this.nameAddressCorrelationScore == null
          || other.getNameAddressCorrelationScore() > this.nameAddressCorrelationScore) {
        this.nameAddressCorrelationScore = other.getNameAddressCorrelationScore();
      }
    }


    if (other.getNameEmailCorrelationScore() != null) {
      if (this.nameEmailCorrelationScore == null
          || other.getNameEmailCorrelationScore() > this.nameEmailCorrelationScore) {
        this.nameEmailCorrelationScore = other.getNameEmailCorrelationScore();
      }
    }


    if (other.getNamePhoneCorrelationScore() != null) {
      if (this.namePhoneCorrelationScore == null
          || other.getNamePhoneCorrelationScore() > this.namePhoneCorrelationScore) {
        this.namePhoneCorrelationScore = other.getNamePhoneCorrelationScore();
      }
    }

    if (other.getPhoneRiskScore() != null) {
      if (this.phoneRiskScore == null || other.getPhoneRiskScore() > this.phoneRiskScore) {
        this.phoneRiskScore = other.getPhoneRiskScore();
      }
    }

    if (other.getSyntheticScore() != null) {
      if (this.syntheticScore == null || other.getSyntheticScore() > this.syntheticScore) {
        this.syntheticScore = other.getSyntheticScore();
      }
    }

    if (other.getFlexScoreDecile() != null) {
      if (this.flexScoreDecile == null || other.getFlexScoreDecile() > this.flexScoreDecile) {
        this.flexScoreDecile = other.getFlexScoreDecile();
      }
    }

    SentilinkScores otherSentilinkScores = other.getSentilinkScores();
    if (otherSentilinkScores != null) {
      if (this.sentilinkScores == null) {
        this.sentilinkScores = new SentilinkScores();
      }

      if (otherSentilinkScores.getFirstPartySyntheticScore() != null) {
        if (this.sentilinkScores.getFirstPartySyntheticScore() == null
            || otherSentilinkScores.getFirstPartySyntheticScore()
            > this.sentilinkScores.getFirstPartySyntheticScore()) {
          this.sentilinkScores.setFirstPartySyntheticScore(otherSentilinkScores.getFirstPartySyntheticScore());
        }
      }

      if (otherSentilinkScores.getThirdPartySyntheticScore() != null) {
        if (this.sentilinkScores.getThirdPartySyntheticScore() == null
            || otherSentilinkScores.getThirdPartySyntheticScore()
            > this.sentilinkScores.getThirdPartySyntheticScore()) {
          this.sentilinkScores.setThirdPartySyntheticScore(otherSentilinkScores.getThirdPartySyntheticScore());
        }
      }

      if (otherSentilinkScores.getAbuseScore() != null) {
        if (this.sentilinkScores.getAbuseScore() == null
            || otherSentilinkScores.getAbuseScore() > this.sentilinkScores.getAbuseScore()) {
          this.sentilinkScores.setAbuseScore(otherSentilinkScores.getAbuseScore());
        }
      }

      if (otherSentilinkScores.getIdTheftScore() != null) {
        if (this.sentilinkScores.getIdTheftScore() == null
            || otherSentilinkScores.getIdTheftScore() > this.sentilinkScores.getIdTheftScore()) {
          this.sentilinkScores.setIdTheftScore(otherSentilinkScores.getIdTheftScore());
        }
      }
    }

    if (other.getFraudReasonCodeR617() != null) {
      if (this.fraudReasonCodeR617 == null || other.getFraudReasonCodeR617() > this.fraudReasonCodeR617) {
        this.fraudReasonCodeR617 = other.getFraudReasonCodeR617();
      }
    }

    if (other.getFraudReasonCodeR665() != null) {
      if (this.fraudReasonCodeR665 == null || other.getFraudReasonCodeR665() > this.fraudReasonCodeR665) {
        this.fraudReasonCodeR665 = other.getFraudReasonCodeR665();
      }
    }

    if (other.getFraudReasonCodeI161() != null) {
      if (this.fraudReasonCodeI161 == null || other.getFraudReasonCodeI161() > this.fraudReasonCodeI161) {
        this.fraudReasonCodeI161 = other.getFraudReasonCodeI161();
      }
    }

    if (other.getFraudReasonCodeR021() != null) {
      if (this.fraudReasonCodeR021 == null || other.getFraudReasonCodeR021() > this.fraudReasonCodeR021) {
        this.fraudReasonCodeR021 = other.getFraudReasonCodeR021();
      }
    }
  }

  private Integer setFraudReasonScore(Set<String> fraudReasonCodes, Boolean validFraudReasonCodes, String prefix) {
    return validFraudReasonCodes && fraudReasonCodes.stream().anyMatch(code -> code.equals(prefix))
        ? MAX_FRAUD_REASON_CODE_SCORE
        : DEFAULT_FRAUD_REASON_CODE_SCORE;
  }

}
