package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static java.util.Map.entry;

import com.fasterxml.jackson.databind.JsonNode;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.CmmMetadata;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.cmmv3.CmmV3Inputs;
import com.getflex.featurestore.model.feature.cmmv3.CmmV3Mrc;
import com.getflex.featurestore.model.feature.cmmv3.CmmV3OfflineFeatureValue;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@RegisterFeature
public class CmmV3ScoreFeature extends BaseOfflineFeature {

  private static final double VALUE_FOR_MISSING_CMM_SCORE = -1.0;
  private static final int REJECTION_CODE_COUNT = 4;
  private static final Map<CmmV3Inputs, CmmV3Mrc> MODEL_REASON_CODE_MAPPING = Map.<CmmV3Inputs, CmmV3Mrc>ofEntries(
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_1MON, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_24MON, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_6MON, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.DQ0_FREQ_LAST_12MONTHS, CmmV3Mrc.MF03),
      entry(CmmV3Inputs.PAYMENT_RESCHULED_1MON, CmmV3Mrc.MF11),
      entry(CmmV3Inputs.BP_RATE_LAST_6MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_3MON, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.CPR_VANTAGE40_SCORE, CmmV3Mrc.MM04),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_1MON_GREATERTHAN50, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.CDQ0_FREQ_1MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.AVG_DAYS_REPAYMENT_LEFT_1MON, CmmV3Mrc.MF11),
      entry(CmmV3Inputs.BP_RATE_LAST_12MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.CDQ_UP_TO_30_FREQ_1MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.NSF_RATE_LAST_6MONTHS, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.NUMBER_OF_BILL_PAID_LAST_6MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_DOWN_0MON, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.NUMBER_OF_BILL_PAID_LAST_12MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.NSF_RATE_LAST_24MONTHS, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.AVG_TRANS_24MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.NUMBER_OF_BILL_PAID_LAST_24MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.CDQ0_FREQ_6MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_CFFE02_COLTOT, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.AVG_DAYS_REPAYMENT_LEFT_24MON, CmmV3Mrc.MF11),
      entry(CmmV3Inputs.MAX_DAYS_DOWN_PAID_0MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.AVG_DAYS_REPAYMENT_LEFT_3MON, CmmV3Mrc.MF11),
      entry(CmmV3Inputs.CPR_CFFE02_BALGRE, CmmV3Mrc.MM07),
      entry(CmmV3Inputs.CPR_EADS142_G411S, CmmV3Mrc.MM09),
      entry(CmmV3Inputs.MONTHS_IN_SUSPENSION_LAST_24MONTHS, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.MONTHS_ACTIVE_LAST_6MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.AVG_DAYS_REPAYMENT_LEFT_6MON, CmmV3Mrc.MF11),
      entry(CmmV3Inputs.AVERAGE_CREDIT_UTILIZATION_RATIO_LAST_6MONTHS, CmmV3Mrc.MM06),
      entry(CmmV3Inputs.AVG_TRANS_3MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.NUMBER_OF_BP_INITIATION_LAST_12MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.CPR_EADS142_G224C, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.CDQ0_FREQ_24MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_CFFE02_REVUT, CmmV3Mrc.MM06),
      entry(CmmV3Inputs.PAY_SUCCESS_RATE_6MON_GREATERTHAN50, CmmV3Mrc.MF01),
      entry(CmmV3Inputs.CPR_CFFE02_MONDE, CmmV3Mrc.MM12),
      entry(CmmV3Inputs.CPR_CFFE02_COLNMED, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.CPR_CFFE02_IACCT24, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.AVG_TRANS_0MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.AVG_DECLINE_INSUFF_FUNDS_0MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_EADS142_BR02S, CmmV3Mrc.MM07),
      entry(CmmV3Inputs.CPR_CFFE02_ACCTDE, CmmV3Mrc.MF05),
      entry(CmmV3Inputs.CPR_CFFE02_RETOP, CmmV3Mrc.MF07),
      entry(CmmV3Inputs.CPR_CFFE02_MONOREV, CmmV3Mrc.MM08),
      entry(CmmV3Inputs.MONTHS_SINCE_LAST_SIGNUP, CmmV3Mrc.MM10),
      entry(CmmV3Inputs.CPR_EADS142_S061S, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.CPR_CFFE02_ACCTREG, CmmV3Mrc.MM07),
      entry(CmmV3Inputs.AVG_DECLINE_INSUFF_FUNDS_3MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.AVG_DECLINE_INSUFF_FUNDS_6MON_GREATERTHAN50, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_CFFE02_REV24M, CmmV3Mrc.MM05),
      entry(CmmV3Inputs.NUMBER_OF_BP_INITIATION_LAST_6MONTHS, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.AVG_DECLINE_INSUFF_FUNDS_1MON_GREATERTHAN50, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_CFFE02_HCLGRE, CmmV3Mrc.MM07),
      entry(CmmV3Inputs.CPR_EADS142_AT24S, CmmV3Mrc.MM07),
      entry(CmmV3Inputs.CPR_CFFE02_REV100UT, CmmV3Mrc.MM06),
      entry(CmmV3Inputs.MONTHS_INACTIVE_L24, CmmV3Mrc.MF10),
      entry(CmmV3Inputs.AVG_TRANS_1MON_GREATERTHAN50, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.MAX_DAYS_DOWN_PAID_1MON, CmmV3Mrc.MF02),
      entry(CmmV3Inputs.CPR_CFFE02_ACT24M, CmmV3Mrc.MM05)
  );

  public CmmV3ScoreFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Get the CMM V3 score of the customer";
  }

  @Override
  public String getOfflineFeatureName() {
    return "cmm_v3_score";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      JsonNode jsonNode = OBJECT_MAPPER.readTree(rawOfflineString);
      CmmV3OfflineFeatureValue featureValue = OBJECT_MAPPER.convertValue(jsonNode, CmmV3OfflineFeatureValue.class);
      CmmMetadata metadata = new CmmMetadata();
      metadata.setModelReasonCodes(getRejectionCodes(featureValue).stream()
          .map(CmmV3Mrc::name)
          .collect(Collectors.toList()));
      return new FeatureOutput(featureValue.getScore(), OBJECT_MAPPER.writeValueAsString(metadata));
    } catch (Exception e) {
      log.warn("Failed to parse CMM v3 score. customerId={}", evalParams.getCustomerId(), e);
      return new FeatureOutput(VALUE_FOR_MISSING_CMM_SCORE);
    }
  }

  Set<CmmV3Mrc> getRejectionCodes(CmmV3OfflineFeatureValue featureValue) {
    if (featureValue == null || CollectionUtils.isEmpty(featureValue.getShapValues())) {
      return Collections.emptySet();
    }
    Map<String, Double> featureToShapValues = featureValue.getShapValues();
    return featureToShapValues.keySet().stream()
        .filter(k -> MODEL_REASON_CODE_MAPPING.containsKey(CmmV3Inputs.fromValue(k)))
        .sorted(Comparator.comparing(k -> featureToShapValues.getOrDefault(k, Double.NEGATIVE_INFINITY)).reversed())
        .limit(REJECTION_CODE_COUNT)
        .map(k -> MODEL_REASON_CODE_MAPPING.get(CmmV3Inputs.fromValue(k)))
        .collect(Collectors.toSet());
  }
}
