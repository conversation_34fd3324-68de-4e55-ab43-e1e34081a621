package com.getflex.featurestore.model.feature.base;

import com.getflex.ledger.model.LedgerMetadata;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.time.OffsetDateTime;
import java.util.List;

public class WalletLedgerRecordAdapter implements LedgerRecordInterface {

  private final RecordLedgerWallet record;

  public static final String CREDIT_BUILDER = "CreditBuilder";

  public WalletLedgerRecordAdapter(RecordLedgerWallet record) {
    this.record = record;
  }

  @Override
  public Long getMoneyMovementTypeId() {
    return record.getMoneyMovementTypeId();
  }

  @Override
  public Long getPaymentCategoryId() {
    return record.getPaymentCategoryId();
  }

  @Override
  public Long getAmount() {
    return record.getAmount();
  }

  @Override
  public Long getToParentIdentityId() {
    return record.getToParentIdentityId();
  }

  @Override
  public String getBillTransactionId() {
    return record.getBillTransactionId();
  }

  @Override
  public OffsetDateTime getDtCreated() {
    return record.getDtCreated();
  }

  @Override
  public Long getPaymentStatusId() {
    return record.getPaymentStatusId();
  }

  @Override
  public Boolean hasCreditBuilderTransactions() {
    List<LedgerMetadata> metadataList = record.getMetadataEntries();
    if (metadataList == null) {
      return false;
    }
    return metadataList.stream().anyMatch(
        meta -> meta.getMetadataValue().contains(CREDIT_BUILDER)
    );
  }

  public Long getProductCategoryId() {
    return record.getProductCategoryId();
  }
}
