package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.List;

@RegisterFeature
public class NumberOfSsnLinkFeature extends BaseFeature {

  private final OfferService offerService;

  public NumberOfSsnLinkFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    InternalOffer offer = offerService.getOfferByCustomerId(customerId);
    String ssnHmac = offer.getSsnHmac512();

    List<InternalOffer> offers = offerService.getOfferBySsnHmac(customerId, ssnHmac);
    int result = (int) offers.stream()
        .map(InternalOffer::getCustomerId)
        .distinct()
        .count();

    return new FeatureOutput(result);
  }

  @Override
  public String getDescription() {
    return "Feature to get the number of unique customers linked to a given customer's ssn.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}