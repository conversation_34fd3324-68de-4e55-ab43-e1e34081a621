package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseWalletFundsCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.function.Predicate;

@RegisterFeature(
    value = "NumberOfDeclinedFundsInFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class NumberOfDeclinedFundsInFeature extends BaseWalletFundsCountFeature {

  public NumberOfDeclinedFundsInFeature(LedgerService ledgerService, LookbackDurationFeatureParams parameters) {
    super(ledgerService, parameters);
  }

  @Override
  protected APIretrieveLedgerByCustomerIdRequest getRetrieveLedgerRequest(
      Long customerId,
      OffsetDateTime dtCreatedStartRange
  ) {
    return APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRange)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .paymentStatusId(PaymentState.DECLINED.getValue())
        .includeRefund(IncludeRefundEnum.EXCLUDE)
        .build();
  }

  @Override
  protected Predicate<RecordLedger> getFilterPredicate() {
    return this::validChargePaymentCategory;
  }

  @Override
  public String getDescription() {
    return "Number of declined funds attempts in past specified time window";
  }
}
