package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.EvalParamKey.CUSTOMER_ID;
import static com.getflex.featurestore.model.EvalParamKey.VERIFICATION_ID;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class PayStubIncomeVerificationRawFeature extends BaseFeature {

  private final IncomeUtils incomeUtils;

  public PayStubIncomeVerificationRawFeature(IncomeUtils incomeUtils) {
    this.incomeUtils = incomeUtils;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String rawPayload = getPayStubRawPayload(evalParams);
    return new FeatureOutput(rawPayload);
  }

  @Override
  public String getDescription() {
    return "Deserializes and calculates features from income verification results returned from Plaid";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(VERIFICATION_ID, CUSTOMER_ID);
  }

  private String getPayStubRawPayload(EvalParams evalParams) {
    Event event = incomeUtils.getEvent(evalParams.getVerificationId(), EventName.PAY_STUB_INCOME_VERIFICATION);
    return event.getMetadata();
  }
}
