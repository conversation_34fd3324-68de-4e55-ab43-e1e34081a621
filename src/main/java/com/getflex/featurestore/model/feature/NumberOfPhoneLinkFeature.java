package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.feature.parameterized.BaseCustomerLinkageFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.v2.CustomerDataV2;
import com.getflex.identity.model.v2.SearchCustomerRequest;

@RegisterFeature
public class NumberOfPhoneLinkFeature extends BaseCustomerLinkageFeature {

  public NumberOfPhoneLinkFeature(IdentityService identityService) {
    super(identityService);
  }

  @Override
  protected SearchCustomerRequest buildSearchRequest(CustomerDataV2 customer) {
    return new SearchCustomerRequest().phone(customer.getPhone());
  }

  @Override
  public String getDescription() {
    return "Feature to get the number of accounts linked to a given customer's phone number.";
  }
}
