package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.AgType;
import com.getflex.identity.model.CustomerAgreementRecord;
import com.getflex.identity.model.FinancialPartnerType;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class HasAcceptedDdaAgreementsFeature extends BaseFeature {

  private static final List<FinancialPartnerType> VALID_FINANCIAL_PARTNERS = List.of(
      FinancialPartnerType.BLUE_RIDGE_BANK,
      FinancialPartnerType.LEAD_BANK
  );
  private static final Map<FinancialPartnerType, AgType> FINANCIAL_PARTNER_TYPE_TO_AG_TYPE = Map.of(
      FinancialPartnerType.BLUE_RIDGE_BANK, AgType.BRB_DDA_AGREEMENT,
      FinancialPartnerType.LEAD_BANK, AgType.LEAD_DDA_AGREEMENT
  );

  private final IdentityService identityService;

  public HasAcceptedDdaAgreementsFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    FinancialPartnerType financialPartner = identityService.getFinancialPartner(customerId);
    if (financialPartner == FinancialPartnerType.NULL) {
      return new FeatureOutput(false);
    }
    if (!VALID_FINANCIAL_PARTNERS.contains(financialPartner)) {
      log.error("Financial partner not supported, financialPartner={}, customerId={}",
          financialPartner,
          customerId
      );
      return new FeatureOutput(false);
    }
    AgType ddaAgType = FINANCIAL_PARTNER_TYPE_TO_AG_TYPE.get(financialPartner);
    List<AgType> agTypes = identityService.getCustomerAgreements(
        evalParams.getCustomerId(),
        List.of(ddaAgType, AgType.STRIPE_CONNECT_AGREEMENT)
    ).stream().map(CustomerAgreementRecord::getAgreementType).toList();
    Boolean acceptedAgreements = agTypes.contains(ddaAgType) && agTypes.contains(AgType.STRIPE_CONNECT_AGREEMENT);
    return new FeatureOutput(acceptedAgreements);
  }

  @Override
  public String getDescription() {
    return "Customer has accepted DDA agreements";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
