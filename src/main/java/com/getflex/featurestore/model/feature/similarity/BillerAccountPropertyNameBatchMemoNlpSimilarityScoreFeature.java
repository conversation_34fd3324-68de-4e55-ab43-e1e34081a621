package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@Slf4j
@RegisterFeature
public class BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature extends
    BaseNlpSimilarityScoreFeature {

  private final IdentityService identityService;

  public BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature(
      IdentityService identityService,
      SageMakerRuntimeClient sagemaker) {
    super(sagemaker);
    this.identityService = identityService;
  }

  @Override
  public List<String> getTargetList(EvalParams evalParams) {
    String propertyName = "";
    try {
      propertyName = identityService.getPropertyNameByBillerAccountId(
          evalParams.getBillerAccountId()
      );
      if (propertyName == null || propertyName.isEmpty()) {
        return List.of();
      }
    } catch (Exception e) {
      log.error("exception fetching property name for baid={}", evalParams.getBillerAccountId(), e);
    }
    return List.of(propertyName);
  }

  @Override
  public String getDescription() {
    return "Property Name to Batch Memo similarity score using nlp model";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
