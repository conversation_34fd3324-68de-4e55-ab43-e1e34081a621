package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * Root offer provides important information around how the product was evaluated and approved.
 */
@Slf4j
@RegisterFeature
public class RootOfferFeature extends BaseFeature {

  private static final Set<OverallState> ELIGIBLE_OFFER_STATES = Set.of(OverallState.ACTIVE, OverallState.APPROVED,
      OverallState.SUSPENDED);

  private final OfferService offerService;

  public RootOfferFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.OFFER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String offerId = evalParams.getOfferId();
    Long offerVersion = evalParams.getOfferVersion();

    InternalOffer offer =
        offerId == null ? offerService.getOfferByBillerAccountIdAndOfferVersion(evalParams.getBillerAccountId(),
            evalParams.getOfferVersion()) : offerService.getOffer(offerId, offerVersion);
    if (offer == null) {
      return new FeatureOutput("Specified offer not found");
    }
    if (!ELIGIBLE_OFFER_STATES.contains(offer.getOverallState())) {
      return new FeatureOutput("Root offer not available");
    }

    offerId = offer.getOfferId();
    offerVersion = offer.getOfferVersion();

    return new FeatureOutput(offerService.getRootOffer(offerId, offerVersion));
  }

  @Override
  public String getDescription() {
    return "This feature returns the root offer for the given biller account ID or offer ID (offer version will always"
        + "be considered if it's specified)";
  }
}
