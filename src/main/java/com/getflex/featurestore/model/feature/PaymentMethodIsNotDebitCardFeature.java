package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.util.Objects;

@RegisterFeature
public class PaymentMethodIsNotDebitCardFeature extends BaseFeature {

  private final WalletService walletService;
  private final IdentityService identityService;

  public PaymentMethodIsNotDebitCardFeature(
      WalletService walletService,
      IdentityService identityService
  ) {
    this.walletService = walletService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    if (evalParams.getPaymentMethodId() == null) {
      Long customerId = evalParams.getCustomerId();
      if (customerId == null) {
        customerId = identityService.getBillerAccount(evalParams.getBillerAccountId()).getCustomerId();
      }

      GetCustomerResponse customer = identityService.getCustomer(customerId);
      GetDefaultCardResponse getDefaultCardResponse = walletService.getDefaultCard(customer.getCustomerPublicId());
      if (getDefaultCardResponse == null || getDefaultCardResponse.getCard() == null) {
        return new FeatureOutput(true);
      }
      return new FeatureOutput(!Objects.equals(getDefaultCardResponse.getCard().getCardType(), "debit"));
    }
    Card card = walletService.getCard(evalParams.getPaymentMethodId());
    if (card == null || card.getCardType() == null) {
      return new FeatureOutput(true);
    }
    return new FeatureOutput(!Objects.equals(card.getCardType(), "debit"));
  }

  @Override
  public String getDescription() {
    return "Payment method is not debit card";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.PAYMENT_METHOD_ID, EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
