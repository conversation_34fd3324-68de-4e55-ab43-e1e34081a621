package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.EvalParamKey.ESTIMATED_RENT_AMOUNT_CENT;
import static com.getflex.featurestore.model.EvalParamKey.VERIFICATION_ID;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.COLLECTIONS_BALANCE_NO_MEDICAL;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.EADS11_CV23;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT;

import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.income.model.underwriting.IsIneligible;
import com.getflex.featurestore.model.feature.income.model.underwriting.OriginalTuFeatures;
import com.getflex.featurestore.model.feature.income.model.underwriting.UnderwritingModel;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.AlloyUtils;
import com.getflex.featurestore.utils.AnnualTaxAmountHelper;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.ValidationVerifiedBankAccounts;
import com.getflex.verification.model.ValidationVerifiedPayStubs;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import io.micrometer.common.util.StringUtils;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * Main feature to use for making decisions in Offers undergoing Income Verification.
 * <p>
 * This feature takes uses an already-validated verification of types BANK_INCOME or PAY_STUB_INCOME,
 * extracts information and builds a {@link UnderwritingModel}.
 * </p>
 * <p>
 * We request Income Verification from a few places within Flex. The feature is used for decisioning both offers
 * at onboarding and about to be suspended.
 * </p>
 * When retrieving this feature from a verification at onboarding, the {@link EvalParams#getAlloyReportUrl()}
 * parameter is mandatory, as the policy requires to calculate {@link UnderwritingModel#getNetIncomeToRentRatio()}.
 * <p>
 * When retrieving this feature from a verification during the suspension process,
 * the {@link EvalParams#getAlloyReportUrl()} parameter is expected to be null, and as a result all of the
 * data points related to Alloy will be null. The policy for suspensions only uses gross income for now,
 * so the data coming from Alloy is not needed.
 * </p>
 */
@Slf4j
@RegisterFeature
public class IncomeVerificationUnderwritingFeature extends BaseFeature {

  private static final List<String> VALID_STATUS = List.of("COMPLETED", "EXPIRED");

  private final VerificationService verificationService;
  private final AlloyUtils alloyUtils;

  public IncomeVerificationUnderwritingFeature(VerificationService verificationService, AlloyUtils alloyUtils) {
    this.verificationService = verificationService;
    this.alloyUtils = alloyUtils;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(getUnderwritingFeatures(evalParams));
  }

  @Override
  public String getDescription() {
    return "Evaluation of income verification results to determine eligibility";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(VERIFICATION_ID, ESTIMATED_RENT_AMOUNT_CENT);
  }

  private UnderwritingModel getUnderwritingFeatures(EvalParams evalParams) {

    String verificationId = evalParams.getVerificationId();

    Verification verification = getCompletedVerificationRecord(verificationId);
    UnderwritingModel underwritingModel = new UnderwritingModel();

    Boolean isExpired = Optional.ofNullable(verification.getStatus()).orElseThrow(() ->
        throwBadDataException("status", evalParams.getVerificationId())).equals("EXPIRED");
    underwritingModel.setIsExpired(isExpired);

    if (isExpired) {
      return underwritingModel;
    }

    VerificationContext verificationContext = Optional.ofNullable(verification.getContext()).orElseThrow(() ->
        throwBadDataException("verification_context", verificationId));

    Validation context = verificationContext.getValidation();
    Validation overrideContext = verificationContext.getValidationOverride();

    if (context == null && overrideContext == null) {
      throwBadDataException("context", verificationId);
    }

    Long monthlyGrossIncome = getContextField(
        overrideContext, context, "monthlyGrossIncome", Long.class
    );
    Long verifiedGrossMonthlyIncomeCent = getContextField(
        overrideContext, context, "verifiedGrossMonthlyIncomeCent", Long.class
    );
    Long grossAnnualSelfReportedIncomeCent = getContextField(
        overrideContext, context, "grossAnnualSelfReportedIncomeCent", Long.class
    );
    long monthlyGrossSelfReportedIncomeCent = grossAnnualSelfReportedIncomeCent / 12;
    underwritingModel.setMonthlyGrossSelfReportedIncomeCent(monthlyGrossSelfReportedIncomeCent);
    underwritingModel.setMonthlyGrossVerifiedIncomeCent(verifiedGrossMonthlyIncomeCent);
    underwritingModel.setMonthlyGrossIncomeCent(monthlyGrossIncome);
    underwritingModel.setAnnualTaxAmountCent(AnnualTaxAmountHelper.estimateAnnualTax(monthlyGrossIncome));

    IsIneligible isIneligible = new IsIneligible();

    String verificationType = Optional.ofNullable(verification.getVerificationType()).orElseThrow(() ->
        throwBadDataException("verification_type", verificationId));

    switch (verificationType) {
      case "BANK_INCOME":
        underwritingModel.setAverageDailyBalanceCent(
            getContextField(overrideContext, context, "totalAverageDailyBalanceCent", Long.class)
        );
        isIneligible.setIncomeProofFailedValidation(
            getContextField(overrideContext, context, "eligibleBankAccounts", Boolean.class)
        );
        break;
      case "PAY_STUB_INCOME":
        Boolean noPassPayStubs =
            getContextField(overrideContext, context, "passGradePayStubs", Boolean.class);
        Boolean noUncertainPayStubs =
            getContextField(overrideContext, context, "uncertainGradePayStubs", Boolean.class);
        isIneligible.setIncomeProofFailedValidation(noPassPayStubs && noUncertainPayStubs);
        isIneligible.setFraudDetected(
            getContextField(overrideContext, context, "submissionFraudulent", Boolean.class)
        );
        break;
      default:
        throwBadDataException("verification_type", verificationId);
    }

    underwritingModel.setIsIneligible(isIneligible);
    underwritingModel.setEstimatedRentAmountCent(evalParams.getEstimatedRentAmountCent());
    setIncomeToRentRatioFields(underwritingModel, evalParams);

    return underwritingModel;
  }

  private Verification getCompletedVerificationRecord(String verificationId) {
    Verification verification = verificationService.getVerification(verificationId);

    if (!VALID_STATUS.contains(verification.getStatus())) {
      throwBadDataException("status", verificationId);
    }

    return verification;
  }

  private <T> T getContextField(Validation overrideContext, Validation context, String fieldName, Class<T> type) {
    return switch (fieldName) {
      case "totalAverageDailyBalanceCent" -> type.cast(
          Optional.ofNullable(
              Optional.ofNullable(overrideContext)
                  .map(Validation::getTotalAverageDailyBalanceCent)
                  .orElseGet(() -> Optional.ofNullable(context)
                      .map(Validation::getTotalAverageDailyBalanceCent)
                      .orElse(null))
          ).orElse(0L)
      );
      case "eligibleBankAccounts" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getVerifiedBankAccounts)
              .map(ValidationVerifiedBankAccounts::getEligibleBankAccounts)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getVerifiedBankAccounts)
                  .map(ValidationVerifiedBankAccounts::getEligibleBankAccounts))
              .orElse(List.of())
              .isEmpty()
      );
      case "monthlyGrossIncome" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getGrossMonthlyIncomeCent)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getGrossMonthlyIncomeCent))
              .orElse(0L)
      );
      case "verifiedGrossMonthlyIncomeCent" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getVerifiedGrossMonthlyIncomeCent)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getVerifiedGrossMonthlyIncomeCent))
              .orElse(0L)
      );
      case "grossAnnualSelfReportedIncomeCent" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getGrossAnnualSelfReportedIncomeCent)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getGrossAnnualSelfReportedIncomeCent))
              .orElse(0L)
      );
      case "passGradePayStubs" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getVerifiedPayStubs)
              .map(ValidationVerifiedPayStubs::getPassGradePayStubs)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getVerifiedPayStubs)
                  .map(ValidationVerifiedPayStubs::getPassGradePayStubs))
              .orElse(List.of())
              .isEmpty()
      );
      case "uncertainGradePayStubs" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getVerifiedPayStubs)
              .map(ValidationVerifiedPayStubs::getUncertainGradePayStubs)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getVerifiedPayStubs)
                  .map(ValidationVerifiedPayStubs::getUncertainGradePayStubs))
              .orElse(List.of())
              .isEmpty()
      );
      case "submissionFraudulent" -> type.cast(
          Optional.ofNullable(overrideContext)
              .map(Validation::getIsSubmissionFraudulent)
              .or(() -> Optional.ofNullable(context)
                  .map(Validation::getIsSubmissionFraudulent))
              .orElse(false)
      );
      default -> null;
    };
  }

  private void setIncomeToRentRatioFields(UnderwritingModel underwritingModel, EvalParams evalParams) {
    underwritingModel.setGrossIncomeToRentRatio(
        (double) underwritingModel.getMonthlyGrossIncomeCent() / evalParams.getEstimatedRentAmountCent());
    underwritingModel.setVerifiedGrossIncomeToRentRatio(
        (double) underwritingModel.getMonthlyGrossVerifiedIncomeCent() / evalParams.getEstimatedRentAmountCent());

    if (!StringUtils.isEmpty(evalParams.getAlloyReportUrl())) {
      fetchAlloyReport(underwritingModel, evalParams);

      Long monthlyNetIncome = Math.max(0, (
              (underwritingModel.getMonthlyGrossIncomeCent() * 12)
                  - underwritingModel.getAnnualTaxAmountCent()
                  - (underwritingModel.getTotalMonthlyObligationRevolvingTradesCent() * 12)
                  - (underwritingModel.getTotalMonthlyObligationInstallmentTradesCent() * 12)
                  - underwritingModel.getNonMedicalCollectionBalanceCent()
          )
      ) / 12;

      underwritingModel.setMonthlyNetIncomeCent(monthlyNetIncome);
      if (evalParams.getEstimatedRentAmountCent() != null && evalParams.getEstimatedRentAmountCent() != 0) {
        underwritingModel.setNetIncomeToRentRatio((double) monthlyNetIncome / evalParams.getEstimatedRentAmountCent());
      }
    }
  }

  private void fetchAlloyReport(UnderwritingModel underwritingModel, EvalParams evalParams) {
    AlloyReport alloyReport = alloyUtils.downloadAlloyReport(evalParams.getAlloyReportUrl());

    OriginalTuFeatures tuFeatures = new OriginalTuFeatures(
        EADS11_CV23.apply(alloyReport),
        TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT.apply(alloyReport),
        COLLECTIONS_BALANCE_NO_MEDICAL.apply(alloyReport)
    );
    underwritingModel.setOriginalTuFeatures(tuFeatures);
    underwritingModel.setTotalMonthlyObligationRevolvingTradesCent(
        castNumberToLong(tuFeatures.getEads11Cv23()) * 100);
    underwritingModel.setTotalMonthlyObligationInstallmentTradesCent(
        castNumberToLong(tuFeatures.getTotalMonthlyPmtOpenInstallment()) * 100);
    underwritingModel.setNonMedicalCollectionBalanceCent(
        castNumberToLong(tuFeatures.getCollectionsBalanceNoMedical()) * 100);
  }

  private InternalServiceBadDataException throwBadDataException(String field, String id) {
    throw new InternalServiceBadDataException(String.format("Missing or invalid %s on verification %s", field, id));
  }

  public static Long castNumberToLong(Number number) {
    return Optional.ofNullable(number).map(Number::longValue).map(v -> v < 0 ? 0 : v).orElse(0L);
  }

}
