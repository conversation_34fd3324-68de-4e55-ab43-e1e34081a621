package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.feature.base.BaseCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.payment.model.DeclineCodeEnum;
import java.util.List;

@RegisterFeature(
    value = "CardDeclinesCountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class CardDeclinesCountFeature extends BaseCardDeclinesCountFeature {

  public CardDeclinesCountFeature(PaymentService paymentService, LookbackDurationFeatureParams parameters) {
    super(paymentService, parameters);
  }

  @Override
  protected List<DeclineCodeEnum> getDeclineCodes() {
    return null;
  }

  @Override
  public String getDescription() {
    return "Card declines count in past specified time window";
  }
}
