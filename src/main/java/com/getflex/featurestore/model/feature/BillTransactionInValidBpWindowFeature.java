package com.getflex.featurestore.model.feature;

import com.getflex.autopay.model.GetBpWindowResponse;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.TimeService;
import java.time.Clock;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;

@RegisterFeature
public class BillTransactionInValidBpWindowFeature extends BaseFeature {

  private final AutopayService autopayService;
  public Clock clock;

  public BillTransactionInValidBpWindowFeature(AutopayService autopayService) {
    super();
    this.autopayService = autopayService;
    this.clock = Clock.systemDefaultZone();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetBpWindowResponse response = autopayService.getBpWindowByBillTransactionId(
        evalParams.getBillTransactionId());
    ZoneId easternZone = ZoneId.of("America/New_York");

    ZonedDateTime start = LocalDate.parse(response.getBpStartDate()).atStartOfDay()
        .atZone(easternZone);

    TimeService timeService = new TimeService(this.clock);
    ZonedDateTime now = timeService.getCurrentOffsetDateTime().toZonedDateTime();
    // set to 7th of every month + 2 business days
    ZonedDateTime end = LocalDate.parse(response.getBpEndDate()).atTime(23, 59, 59)
        .withDayOfMonth(7).atZone(easternZone);
    int businessDaysToAdd = 2;
    while (businessDaysToAdd > 0) {
      end = end.plusDays(1);
      if (isWeekday(end)) {
        businessDaysToAdd--;
      }
    }
    return new FeatureOutput(now.isAfter(start) && now.isBefore(end));
  }

  private static boolean isWeekday(ZonedDateTime dateTime) {
    // Check if the day is a weekend
    DayOfWeek dayOfWeek = dateTime.getDayOfWeek();
    return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
  }

  @Override
  public String getDescription() {
    return "If we are in valid bp window or not";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
