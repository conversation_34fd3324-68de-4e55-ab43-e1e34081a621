package com.getflex.featurestore.model.feature;


import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

/**
 * This feature retrieves a customer's biller integration type based on biller account ID.
 * It follows the `SCREAMING_SNAKE_CASE` convention from biller.properties.
 */
@Slf4j
@RegisterFeature
public class CustomerBillerIntegrationTypeFeature extends PropertyInfoFeature {


  public CustomerBillerIntegrationTypeFeature(BillingService billingService, IdentityService identityService,
      OfferService offerService) {
    super(identityService, offerService, billingService);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.STRING;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    PropertyInfo propertyInfo = getPropertyInfo(evalParams);
    BillingIntegrationTypeEnum integrationType = propertyInfo.integrationType();
    String integrationTypeStr = integrationType != null ? integrationType.toString() : null;
    return new FeatureOutput(integrationTypeStr);
  }

  @Override
  public String getDescription() {
    return "Get Customer Biller Integration Type";
  }
}
