package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.service.EventService;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BaseDeviceMetricFeature extends BaseFeature {

  private final EventService eventService;
  private final LookbackDurationFeatureParams parameters;

  public BaseDeviceMetricFeature(EventService eventService, LookbackDurationFeatureParams parameters) {
    this.eventService = eventService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    String category = EventCategory.DEVICE_DATA.toString();
    List<DdbEvent> events;

    if (parameters.lookbackWindow() != null && !parameters.lookbackWindow().isEmpty()) {
      OffsetDateTime dtCreatedStartRange =
          OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()));
      events =
          eventService.getCustomerEventsByCategoryWithTimestamp(
              customerId, category, dtCreatedStartRange);
    } else {
      events = eventService.getCustomerEventsByCategory(customerId, category);
    }

    int result =
        (int)
            events.stream()
                .map(this::mapFunction)
                .filter(Objects::nonNull)
                .distinct()
                .count();
    return new FeatureOutput(result);
  }

  protected abstract String extractProperty(DeviceMetadata deviceMetadata);

  protected String mapFunction(DdbEvent event) {
    return getMetric(event.getMetadata());
  }

  protected String getMetric(String metadataJson) {
    try {
      DeviceMetadata deviceMetadata =
          EventMetadata.OBJECT_MAPPER.readValue(metadataJson, DeviceMetadata.class);
      return extractProperty(deviceMetadata);
    } catch (Exception e) {
      log.error("Unable to parse EventMetadata={}", metadataJson, e);
      throw new RuntimeException("Unable to parse EventMetadata=%s".formatted(metadataJson), e);
    }
  }
}
