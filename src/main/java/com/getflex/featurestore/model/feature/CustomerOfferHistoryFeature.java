package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offer.model.OfferState;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.ProductType;
import java.time.Clock;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RegisterFeature
public class CustomerOfferHistoryFeature extends BaseFeature {

  private static final Long NO_VALUE = -1L;
  private final OfferService offerService;
  private final Clock etClock;

  public CustomerOfferHistoryFeature(OfferService offerService, Clock etClock) {
    this.offerService = offerService;
    this.etClock = etClock;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    OffsetDateTime now = OffsetDateTime.now(etClock).truncatedTo(ChronoUnit.DAYS);

    try {
      List<InternalOffer> offers = offerService.getOfferBySsnHmac(
          evalParams.getCustomerId(),
          evalParams.getSsnHmac()
      ).stream()
          .filter(offer ->
              Optional.ofNullable(offer.getProductType())
              // Exclude credit builder offers from evaluation
              .map(type -> !type.equals(ProductType.CREDITBUILDER))
              .orElse(true))
          .toList();

      Optional<InternalOffer> mostRecentlyAccepted = offers.stream()
          .filter(offer -> offer.getAcceptanceTime() != null)
          .min(Comparator.comparingLong((InternalOffer offer) ->
              ChronoUnit.DAYS.between(offer.getAcceptanceTime(), now)));

      Optional<InternalOffer> mostRecentlyCanceled = offers.stream()
          .filter(offer -> OfferState.CLOSED.getValue().equals(offer.getState())
              && "CancelCreditLine".equals(offer.getDeactivationReason())
              && offer.getAcceptanceTime() != null
              && offer.getTerminationTime() != null
          ).min(Comparator.comparingLong((InternalOffer offer) ->
              ChronoUnit.DAYS.between(offer.getTerminationTime(), now)));

      long daysSinceLastAccept = mostRecentlyAccepted.map(internalOffer -> ChronoUnit.DAYS.between(
          Objects.requireNonNull(internalOffer.getAcceptanceTime())
              .truncatedTo(ChronoUnit.DAYS), now)).orElse(NO_VALUE);

      long daysSinceLastCancel = mostRecentlyCanceled.map(internalOffer -> ChronoUnit.DAYS.between(
          Objects.requireNonNull(internalOffer.getTerminationTime())
              .truncatedTo(ChronoUnit.DAYS), now)).orElse(NO_VALUE);

      return new FeatureOutput(new CustomerOfferHistory(
          daysSinceLastAccept,
          daysSinceLastCancel
      ));
    } catch (InternalDependencyFailureException e) {
      return new FeatureOutput(new CustomerOfferHistory(NO_VALUE, NO_VALUE));
    }
  }

  @Override
  public String getDescription() {
    return "Provides details about customers history of most recently accepted and canceled offers";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.SSN_HMAC);
  }

  public record CustomerOfferHistory(
      @JsonProperty("days_since_last_accepted_offer") Long daysSinceAcceptedOffer,
      @JsonProperty("days_since_last_canceled_offer") Long daysSinceCanceledOffer) {
  }
}
