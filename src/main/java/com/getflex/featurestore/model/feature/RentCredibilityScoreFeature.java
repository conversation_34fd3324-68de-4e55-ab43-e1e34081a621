package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.DsScoreService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class RentCredibilityScoreFeature extends BaseFeature {
  private final DsScoreService service;

  public RentCredibilityScoreFeature(DsScoreService service) {
    this.service = service;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(service.getBatchMemoRentCredibilityScore(evalParams.getBatchMemo()));
  }

  @Override
  public String getDescription() {
    return "get RentCredibilityScore for given batchMemo";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO);
  }
}
