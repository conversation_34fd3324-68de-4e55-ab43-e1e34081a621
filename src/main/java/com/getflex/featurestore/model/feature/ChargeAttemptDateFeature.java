package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class ChargeAttemptDateFeature extends BaseOfflineFeature {

  private static final String DEFAULT_VALUE_FOR_INVALID_CUSTOMER = null;

  public ChargeAttemptDateFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "charge_attempt_dates";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.STRING;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      if (rawOfflineString == null) {
        return new FeatureOutput(DEFAULT_VALUE_FOR_INVALID_CUSTOMER);
      }
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      LocalDate now = LocalDate.now();
      Optional<LocalDate> closestFutureDate = Arrays.stream(rawOfflineString.split(","))
          .map(date -> LocalDate.parse(date.strip(), formatter))
          .filter(date -> date.isAfter(now))
          .min(Comparator.naturalOrder());
      return new FeatureOutput(closestFutureDate.map(LocalDate::toString).orElse(DEFAULT_VALUE_FOR_INVALID_CUSTOMER));
    } catch (Exception e) {
      log.error("Failed to parse charge attempt dates. customerId={}", evalParams.getCustomerId(), e);
      return new FeatureOutput(DEFAULT_VALUE_FOR_INVALID_CUSTOMER);
    }
  }

  @Override
  public String getDescription() {
    return "Get the charge attempt date of the customer";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
