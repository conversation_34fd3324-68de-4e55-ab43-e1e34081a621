package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.GetCustomerBillResponse;

@RegisterFeature
public class BillTransactionBillAmountCentsFromPaymentFeature extends BaseFeature {

  private final PaymentService paymentService;
  private final IdentityService identityService;

  public BillTransactionBillAmountCentsFromPaymentFeature(PaymentService paymentService,
      IdentityService identityService) {
    super();
    this.paymentService = paymentService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse c = this.identityService.getCustomer(evalParams.getCustomerId());
    String customerPublicId = c.getCustomerPublicId();
    GetCustomerBillResponse bill = this.paymentService.getCustomerBill(customerPublicId,
        evalParams.getBillTransactionId());
    if (bill == null) {
      return new FeatureOutput(0);
    }
    if (bill.getAmountOverwrite() != null) {
      return new FeatureOutput(bill.getAmountOverwrite().intValue());
    }
    if (bill.getAmount() == null) {
      return new FeatureOutput(0);
    }
    return new FeatureOutput(bill.getAmount().intValue());
  }

  @Override
  public String getDescription() {
    return "Customer's bill amount in cents from payment service";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILL_TRANSACTION_ID);
  }
}
