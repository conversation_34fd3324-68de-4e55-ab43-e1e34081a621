package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RegisterFeature
public class ProxyRentAmountCentFeature extends BaseOfflineFeature {

  private static final int VALUE_FOR_MISSING_PROXY_RENT_AMOUNT = -1;

  public ProxyRentAmountCentFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Get the proxy rent amount of the customer";
  }

  @Override
  public String getOfflineFeatureName() {
    return "proxy_rent_amount_feature";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      double rawValue = Double.parseDouble(rawOfflineString);
      int centValue = (int) Math.round(rawValue * 100);
      return new FeatureOutput(centValue);
    } catch (Exception e) {
      log.warn("Failed to parse proxy rent amount. customerId={}", evalParams.getCustomerId(), e);
      return new FeatureOutput(VALUE_FOR_MISSING_PROXY_RENT_AMOUNT);
    }
  }
}
