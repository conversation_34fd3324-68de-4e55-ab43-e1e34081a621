package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class PaySuccessRateLast6MonthsFeature extends BillPayOfflineFeature {

  public PaySuccessRateLast6MonthsFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Ratio of successful bill payments to total bill payment attempts over the last 6 months";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "pay_success_rate_last_6_months";
  }
}
