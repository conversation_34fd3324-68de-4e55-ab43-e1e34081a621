package com.getflex.featurestore.model.feature.income;

import java.text.DecimalFormat;
import java.util.List;
import org.apache.commons.text.similarity.LevenshteinDistance;

public class IncomeConstants {

  public static final LevenshteinDistance LEVENSHTEIN_DISTANCE = new LevenshteinDistance();
  public static DecimalFormat DF = new DecimalFormat("#.##");
  public static final List<String> FORBIDDEN_ACCOUNT_OWNER_PARTIAL_NAMES = List.of(
      "llc", "ltd.", "l.l.c.", "corp.", "inc.", "limited", "non profit", "business");
  public static final String PROCESSING_COMPLETE = "PROCESSING_COMPLETE";
  public static final Double ZERO_DEFAULT = 0.00;
  public static final int MINIMUM_VIABLE_RISK_SCORE = 30;
  public static final int MAXIMUM_VIABLE_RISK_SCORE = 79;
  public static final int PAY_STUB_VALID_FOR_DAYS = 40;
}
