package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.output.CustomerCprReportOutput;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerCprReportFeature extends BaseOfflineFeature {

  public static final int DEFAULT_VANTAGE_SCORE_40 = 0;
  private final OfflineFeatureRepo offlineFeatureRepo;

  public CustomerCprReportFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public String getDescription() {
    return "Features from customer's latest CPR Report";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_cpr_report";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString() + "_";
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerId = evalParams.getCustomerId().toString();

    Optional<CustomerCprReportOutput> optionalCustomerCprReportOutput =
        offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            this.getOfflineFeatureName(),
            this.getPrimaryKey(evalParams)
        ).stream().map(feature -> {
          try {
            return OBJECT_MAPPER.readValue(
                feature.getFeatureValue(), CustomerCprReportOutput.class);
          } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
          }
        }).findFirst();

    if (optionalCustomerCprReportOutput.isEmpty()) {
      String msg = String.format("No customer CPR report offline feature found for customer %s", customerId);
      log.warn(msg);
      return new FeatureOutput(new CustomerCprReportOutput(DEFAULT_VANTAGE_SCORE_40));
    }

    return new FeatureOutput(optionalCustomerCprReportOutput.get());
  }

}
