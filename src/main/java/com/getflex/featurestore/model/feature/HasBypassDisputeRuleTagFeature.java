package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class HasBypassDisputeRuleTagFeature extends BaseFeature {

  private final TaggingService taggingService;
  public static final String TAG_NAME = "bypass_dispute_rule";

  public HasBypassDisputeRuleTagFeature(TaggingService taggingService) {
    this.taggingService = taggingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    try {
      Boolean hasTag = taggingService.customerHasTag(customerId, TAG_NAME);
      return new FeatureOutput(hasTag);
    } catch (InternalDependencyFailureException e) {
      return new FeatureOutput(false);
    } catch (Exception e) {
      log.error("Error while fetching the bypass_dispute_rule tag for customer id {}", customerId, e);
      return new FeatureOutput(false);
    }
  }

  @Override
  public String getDescription() {
    return "Indicates whether the customer has the bypass_dispute_rule tag";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
