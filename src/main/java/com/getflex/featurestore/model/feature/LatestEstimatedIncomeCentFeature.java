package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;

@RegisterFeature
public class LatestEstimatedIncomeCentFeature extends BaseFeature {

  private final ObjectMapper objectMapper = new ObjectMapper();

  private final EventRepository eventRepository;

  public LatestEstimatedIncomeCentFeature(EventRepository eventRepository) {
    super();
    this.eventRepository = eventRepository;
  }


  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }


  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String customerId = evalParams.getCustomerId().toString();
    Optional<Event> optionalEvent = eventRepository.findFirstByNameAndCustomerIdOrderByDtArrivedDesc(
        EventName.EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME, customerId);
    if (optionalEvent.isEmpty()) {
      String msg = String.format("No income found for customer %s", customerId);
      throw new FeatureNotFoundException(msg);
    }

    IncomeMetadata incomeMetadata;
    try {
      incomeMetadata = objectMapper.readValue(optionalEvent.get().getMetadata(), IncomeMetadata.class);
      return new FeatureOutput(Integer.valueOf(incomeMetadata.getEstimatedGrossAnnualIncomeCents()), null);
    } catch (JsonProcessingException e) {
      String msg = String.format("Failed to parse income metadata for customer %s", customerId);
      throw new EventMetadataParsingException(msg);
    }
  }

  @Override
  public String getDescription() {
    return "This is the prototype feature for feature store";
  }
}
