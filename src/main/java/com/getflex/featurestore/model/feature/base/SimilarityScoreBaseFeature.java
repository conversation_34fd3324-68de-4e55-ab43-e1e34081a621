package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.FeatureUtils;
import java.util.Arrays;
import java.util.List;

public abstract class SimilarityScoreBaseFeature extends BaseOfflineFeature {

  public SimilarityScoreBaseFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String featureValue = this.getRawFeatureValue(evalParams);
    if (featureValue == null || featureValue.isEmpty()) {
      return new FeatureOutput(0L);
    }
    List<String> batchMemoHistory = Arrays.stream(this.getRawFeatureValue(evalParams).split("\\|"))
        .map(
            String::trim).toList();
    String batchMemo = evalParams.getBatchMemo();
    return new FeatureOutput(FeatureUtils.getSimilarityScore(batchMemo, batchMemoHistory));
  }
}
