package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureMethod;
import com.getflex.featurestore.dao.model.stripeevent.threedomainsecure.ThreeDomainSecureResult;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.feature.base.BaseThreeDomainSecureFeature;
import com.getflex.featurestore.model.feature.utils.ThreeDomainSecureUtils.ThreeDomainSecureAttempt;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerFailedStepUp3DsCountFeature extends BaseThreeDomainSecureFeature {
  public CustomerFailedStepUp3DsCountFeature(EventRepository eventRepository) {
    super(eventRepository);
  }

  @Override
  public String getDescription() {
    return "Count the total number of failed step-up (challenge) 3DS for the customer.";
  }

  @Override
  protected Predicate<ThreeDomainSecureAttempt> getFilterPredicate() {
    return attempt -> attempt.method() == ThreeDomainSecureMethod.CHALLENGE
        && attempt.result() == ThreeDomainSecureResult.FAILED;
  }
}
