package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class HasWalletCxEnabledTagFeature extends BaseFeature {

  public static final String TAG_NAME = "Wallet CX Enabled";

  private final TaggingService taggingService;

  public HasWalletCxEnabledTagFeature(TaggingService taggingService) {
    this.taggingService = taggingService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public String getDescription() {
    return "Indicates whether the customer has the %s tag".formatted(TAG_NAME);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Boolean hasTag = taggingService.customerHasTag(evalParams.getCustomerId(), TAG_NAME);
    return new FeatureOutput(hasTag);
  }
}
