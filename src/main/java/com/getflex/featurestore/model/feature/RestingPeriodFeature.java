package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.dao.model.event.EventName.ONBOARDING_CREDIT_RESTING;
import static com.getflex.featurestore.dao.model.event.EventName.ONBOARDING_FRAUD_RESTING;
import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static com.getflex.featurestore.model.EvalParams.OnboardingDomainEnum.CREDIT;
import static com.getflex.featurestore.utils.FlexConstant.FLEX_TIMEZONE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.RestingPeriodMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class RestingPeriodFeature extends BaseFeature {

  private final EventRepository eventRepository;

  public RestingPeriodFeature(EventRepository eventRepository) {
    this.eventRepository = eventRepository;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    long restingCount = 0;
    try {
      EventName eventName = CREDIT.equals(evalParams.getOnboardingDomain())
          ? ONBOARDING_CREDIT_RESTING : ONBOARDING_FRAUD_RESTING;
      List<Event> restingEvents = eventRepository.findAllByNameAndEntityIdOrderByDtArrivedDesc(
          eventName, evalParams.getSsnHmac());

      OffsetDateTime restingCutOff = OffsetDateTime.now(FLEX_TIMEZONE).truncatedTo(ChronoUnit.DAYS).minusDays(61);
      restingCount = restingEvents.stream().filter(e -> {
        try {
          RestingPeriodMetadata metadata = OBJECT_MAPPER.readValue(e.getMetadata(), RestingPeriodMetadata.class);
          return metadata.getFailureTimestamp().truncatedTo(ChronoUnit.DAYS).isAfter(restingCutOff);
        } catch (JsonProcessingException ex) {
          return false;
        }
      }).count();
    } catch (RuntimeException e) {
      log.error("Error getting resting period events {}", e.getMessage());
    }
    return new FeatureOutput(restingCount);
  }

  @Override
  public String getDescription() {
    return "Returns the count of external credit failures from the last 60 days";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.ONBOARDING_DOMAIN, EvalParamKey.SSN_HMAC);
  }
}
