package com.getflex.featurestore.model.feature.dispute;

import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class DisputeAmountInCentsByIdFeature extends BaseFeature {

  private final DisputeService disputeService;

  public DisputeAmountInCentsByIdFeature(DisputeService disputeService) {
    super();
    this.disputeService = disputeService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    return new FeatureOutput(disputeService.getDisputeById(evalParams.getDisputeId()).getDisputeAmount());
  }

  /**
   * @return Amount in cents for given dispute Id
   */
  @Override
  public String getDescription() {
    return "Amount in cents for given dispute Id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.DISPUTE_ID);
  }
}
