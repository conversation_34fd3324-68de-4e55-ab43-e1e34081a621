package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RegisterFeature
public class CustomerHasSettledPayBillerTransactionFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public CustomerHasSettledPayBillerTransactionFeature(LedgerService ledgerService) {
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> customerTransactions =
            ledgerService.getLedgersByCustomerId(evalParams.getCustomerId(), false);
    if (customerTransactions != null) {
      return customerTransactions.stream()
          .filter(transaction -> transaction.getMoneyMovementTypeId() != null
              && LedgerService.MoneyMovementType.PAY_BILLER.getValue()
              == transaction.getMoneyMovementTypeId())
          .findFirst()
          .map(transaction -> new FeatureOutput(true))
          .orElse(new FeatureOutput(false));
    }
    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "Check to see if the customer has settled a Pay Biller transaction";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}