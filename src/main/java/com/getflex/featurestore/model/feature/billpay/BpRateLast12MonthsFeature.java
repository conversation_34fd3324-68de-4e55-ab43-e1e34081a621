package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class BpRateLast12MonthsFeature extends BillPayOfflineFeature {

  public BpRateLast12MonthsFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's bill pay rate (number of bill paid / number of months active) for the last 12 months";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "bp_rate_last_12_months";
  }
}
