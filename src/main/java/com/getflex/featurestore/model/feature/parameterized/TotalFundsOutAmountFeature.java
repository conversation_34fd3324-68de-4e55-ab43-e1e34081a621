package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;

@RegisterFeature(
    value = "TotalFundsOutAmountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class TotalFundsOutAmountFeature extends BaseFeature {

  private final LedgerUtils ledgerUtils;
  private final LookbackDurationFeatureParams parameters;

  public TotalFundsOutAmountFeature(LedgerUtils ledgerUtils, LookbackDurationFeatureParams parameters) {
    this.ledgerUtils = ledgerUtils;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    OffsetDateTime dtCreatedStartRange = OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()));
    List<RecordLedger> fundsOutRecords = ledgerUtils.getFundsOutRecords(
        evalParams.getCustomerId(),
        dtCreatedStartRange
    );
    if (fundsOutRecords == null || fundsOutRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    var totalFundsOutAmount = (int) fundsOutRecords.stream()
        .mapToLong(RecordLedger::getAmount)
        .sum();
    return new FeatureOutput(totalFundsOutAmount);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public String getDescription() {
    return "Total funds out amount in past specified time window";
  }

  private Boolean validTransaction(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() != null
        && recordLedger.getMoneyMovementTypeId() != null;
  }

  private Boolean validFundsOut(RecordLedger recordLedger) {
    return isRegularFundsOut(recordLedger) || isOctFundsOut(recordLedger);
  }

  private Boolean isOctFundsOut(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() == MovementCategory.CHARGE.getValue()
        && recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_OUT.getValue();
  }

  private Boolean isRegularFundsOut(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() == MovementCategory.REFUND.getValue()
        && recordLedger.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_IN.getValue();
  }
}
