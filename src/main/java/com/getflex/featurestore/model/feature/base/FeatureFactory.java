package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.register.AnnotatedFeatureResolver;
import com.getflex.featurestore.register.FeatureMatcher;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FeatureFactory implements ApplicationListener<ContextRefreshedEvent> {

  public static final String FEATURE_PACKAGE_NAME = "com.getflex.featurestore.model.feature";
  private List<BaseFeature> features;
  private final AnnotatedFeatureResolver annotatedFeatureResolver;

  public FeatureFactory(
      AutowireCapableBeanFactory beanFactory
  ) {
    this.annotatedFeatureResolver = new AnnotatedFeatureResolver(beanFactory);
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent event) {
    // lets only scan once, we dont do any classpath hot loading
    if (features != null) {
      return;
    }

    try {
      // once all beans are initialized, we can initialize the features, we cant do it during
      // construction because the services are not necessarily fully initialized yet and there
      // can exist circular dependencies with some features needing the FeatureFactory itself
      features = annotatedFeatureResolver.getFeatureMatchers().stream()
          .map(FeatureMatcher::createBlankInstance)
          .collect(Collectors.toList());
    } catch (Exception e) {
      // errors here can be swallowed and not prevent application startup, ensure we log and
      // shutdown the application if we cant create a feature. This is a critical failure
      log.error("Fatal error initializing features, shutting down application", e);
      SpringApplication.exit(event.getApplicationContext());
    }
  }

  public List<BaseFeature> getFeatures(Set<EvalParamKey> evalParamKeys) {
    if (features == null) {
      throw new IllegalStateException("features has not been initialized");
    }
    return features.stream()
        .filter(feature -> {
          try {
            feature.getRequiredEvalParamKeys().validateEvaluationParams(evalParamKeys);
            return true;
          } catch (RuntimeException e) {
            return false;
          }
        })
        .collect(Collectors.toList());
  }

  public BaseFeature getFeature(String featureName) {
    // special processing for parameterized feature
    BaseFeature feature = annotatedFeatureResolver.resolveFeature(featureName);
    if (feature != null) {
      return feature;
    }

    throw new FeatureNotFoundException(
        String.format("Cannot find feature in feature registry. featureName=%s", featureName)
    );
  }
}
