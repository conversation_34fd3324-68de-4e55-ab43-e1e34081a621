package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.identity.model.GetCustomerResponse;

public abstract class IsTrustedEntityBaseFeature extends BaseOfflineFeature {

  protected IdentityService identityService;

  protected IsTrustedEntityBaseFeature(OfflineFeatureRepo offlineFeatureRepo,
                                       IdentityService identityService) {
    super(offlineFeatureRepo);
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String rawOfflineDbValue = super.getRawFeatureValue(evalParams);
    GetCustomerResponse customer = this.identityService.getCustomer(evalParams.getCustomerId());
    if (rawOfflineDbValue != null && rawOfflineDbValue.equals(customer.getCustomerPublicId())) {
      return new FeatureOutput(Boolean.TRUE, null);
    }
    return new FeatureOutput(Boolean.FALSE, null);
  }
}
