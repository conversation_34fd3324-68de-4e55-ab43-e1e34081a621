package com.getflex.featurestore.model.feature.ledgerbalance;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseLifetimeLedgerWalletCentsFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.function.Predicate;

@RegisterFeature
public class CustomerLifetimeDisputeCentsFeature extends BaseLifetimeLedgerWalletCentsFeature {


  public CustomerLifetimeDisputeCentsFeature(LedgerService ledgerService) {
    super(ledgerService);
  }

  @Override
  protected Predicate<RecordLedgerWallet> getFilterPredicate() {
    return rlw ->
        rlw.getPaymentCategoryId() == MovementCategory.DISPUTE.getValue()
        && rlw.getPaymentStatusId() == PaymentState.SETTLED.getValue();
  }

  @Override
  public String getDescription() {
    return "This feature returns the lifetime disputes amount in cents.";
  }
}
