package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class CprVantage40ScoreFeature extends BillPayOfflineFeature {

  public CprVantage40ScoreFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's cpr vantage 40 score";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "cpr_vantage40_score";
  }
}
