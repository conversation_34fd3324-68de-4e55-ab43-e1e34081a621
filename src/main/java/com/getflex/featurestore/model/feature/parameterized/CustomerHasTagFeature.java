package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@RegisterFeature(
    value = "CustomerHasTagFeature_{tag}",
    parameterType = CustomerHasTagFeature.Parameters.class)
public class CustomerHasTagFeature extends BaseFeature {
  private final TaggingService taggingService;
  public final String tagName;

  public record Parameters(String tag) {
  }

  public CustomerHasTagFeature(TaggingService taggingService, String tagName) {
    super();
    this.taggingService = taggingService;
    this.tagName = tagName;
  }

  public CustomerHasTagFeature(TaggingService taggingService, Parameters params) {
    super();
    this.taggingService = taggingService;
    this.tagName = params.tag();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      Boolean hasTag = taggingService.customerHasTag(evalParams.getCustomerId(), tagName);
      return new FeatureOutput(hasTag);
    } catch (Exception e) {
      log.error("Error while fetching tag for customer id {}, tagName {}",
          evalParams.getCustomerId(), this.tagName, e);
      return new FeatureOutput(false);
    }
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check if the customer has a specific tag, feature should "
        + "follow the convention as CustomHasTagFeature_TagName";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
