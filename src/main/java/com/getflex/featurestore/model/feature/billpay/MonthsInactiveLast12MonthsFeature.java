package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class MonthsInactiveLast12MonthsFeature extends BillPayOfflineFeature {

  public MonthsInactiveLast12MonthsFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's number of months inactive for the last 12 months";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "months_inactive_last_12_months";
  }
}
