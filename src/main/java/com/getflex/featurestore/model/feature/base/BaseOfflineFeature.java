package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public abstract class BaseOfflineFeature extends BaseFeature {

  protected OfflineFeatureRepo offlineFeatureRepo;

  protected BaseOfflineFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super();
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  protected abstract String getOfflineFeatureName();

  protected abstract String getPrimaryKey(EvalParams evalParams);

  // get raw string value of the feature
  protected String getRawFeatureValue(EvalParams evalParams) {
    Optional<OfflineFeature> feature =
        this.offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(this.getOfflineFeatureName(),
            this.getPrimaryKey(evalParams));
    if (feature.isEmpty() || StringUtils.isEmpty(feature.get().getFeatureValue())) {
      return null;
    }
    return feature.get().getFeatureValue();
  }
}
