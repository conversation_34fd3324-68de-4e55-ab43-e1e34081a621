package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.VantageScoreMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerVantageScoreFeature extends BaseOfflineFeature {

  private final EventRepository eventRepository;

  private final OfflineFeatureRepo offlineFeatureRepo;

  public CustomerVantageScoreFeature(EventRepository eventRepository, OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.eventRepository = eventRepository;
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.ARRAY;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String customerId = evalParams.getCustomerId().toString();

    List<VantageScoreMetadata> vantageScoreMetadata =
        offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            this.getOfflineFeatureName(),
            this.getPrimaryKey(evalParams)
        ).stream().map(feature -> {
          try {
            VantageScoreMetadata featureMetadata = EventMetadata.OBJECT_MAPPER.readValue(
                feature.getFeatureValue(), VantageScoreMetadata.class);
            featureMetadata.setUpdatedAt(feature.getDtCreated());
            return featureMetadata;
          } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
          }
        }).collect(Collectors.toList());

    vantageScoreMetadata.addAll(
        eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
                EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId)
            .stream().map(event -> {
              try {
                VantageScoreMetadata eventMetadata = EventMetadata.OBJECT_MAPPER.readValue(
                    event.getMetadata(), VantageScoreMetadata.class);
                eventMetadata.setUpdatedAt(event.getDtArrived());
                return eventMetadata;
              } catch (JsonProcessingException e) {
                String msg = String.format("Failed to parse vantage scores metadata for customer %s", customerId);
                log.error(msg, e);
                throw new EventMetadataParsingException(msg);
              }
            }).toList());

    if (vantageScoreMetadata.isEmpty()) {
      String msg = String.format("No vantage scores found for customer %s", customerId);
      log.error(msg);
      throw new FeatureNotFoundException(msg);
    }

    return new FeatureOutput(vantageScoreMetadata);
  }

  @Override
  public String getDescription() {
    return "Vantage score of customer pulled during onboarding by Alloy and monthly by TransUnion";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "customer_epr_vantage_score";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }
}
