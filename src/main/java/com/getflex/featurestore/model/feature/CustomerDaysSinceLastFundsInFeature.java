package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.utils.FlexConstant.UTC_TIMEZONE;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerDaysSinceLastFundsInFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public CustomerDaysSinceLastFundsInFeature(LedgerService ledgerService) {
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> customerTransactions =
        ledgerService.getLedgersByCustomerId(evalParams.getCustomerId(), false);

    if (customerTransactions == null || customerTransactions.isEmpty()) {
      throw new InternalDependencyFailureException(
          String.format("No ledger transactions found for customerId=%s", evalParams.getCustomerId())
      );
    }

    Optional<RecordLedger> lastFundsInRecord = customerTransactions.stream()
        .filter(t -> t != null
            && t.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_IN.getValue()
            && t.getPaymentCategoryId() == MovementCategory.CHARGE.getValue())
        .max(Comparator.comparing(RecordLedger::getDtCreated));

    if (lastFundsInRecord.isEmpty()) {
      throw new InternalDependencyFailureException(
          String.format("Unable to find last funds in date for customerId={}", evalParams.getCustomerId())
      );
    }

    OffsetDateTime now = OffsetDateTime.now(UTC_TIMEZONE);
    return new FeatureOutput(ChronoUnit.DAYS.between(lastFundsInRecord.get().getDtCreated(), now));
  }

  @Override
  public String getDescription() {
    return "Days since the customer successfully added funds into their wallet.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
