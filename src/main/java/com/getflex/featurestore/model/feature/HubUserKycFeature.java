package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.partnerhub.model.User;
import java.util.Optional;

/**
 * This feature can be used to look up KYC information related to a partner-hub user.
 * <br>
 * When {@link EvalParams#getHubUserKycId()} is not specified, {@link EvalParams#getBillerAccountId()} will be used to
 * find related hub user through various API calls. If for some reason KYC ID can't be located, an error message string
 * will be returned (while in successful case an object of KYC info is returned). Client can process return value
 * accordingly.
 */
@RegisterFeature
public class HubUserKycFeature extends BaseFeature {
  private final VerificationService verificationService;
  private final IdentityService identityService;
  private final BillingService billingService;
  private final PartnerHubService partnerHubService;

  public HubUserKycFeature(VerificationService verificationService, IdentityService identityService,
      BillingService billingService, PartnerHubService partnerHubService) {

    this.verificationService = verificationService;
    this.identityService = identityService;
    this.billingService = billingService;
    this.partnerHubService = partnerHubService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.HUB_USER_KYC_ID, EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String kycId = evalParams.getHubUserKycId();
    if (kycId == null) {
      ExtendedBillerAccountData billerAccount = identityService.getBillerAccount(
          evalParams.getBillerAccountId());
      ComGetflexBillingControllerV2PropertyControllerPropertyResponse property = billingService.getPropertyByBillerId(
          billerAccount.getBillerId());
      if (property.getIntegrationType() != IntegrationTypeEnum.P2P) {
        return new FeatureOutput("Not a P2P property");
      }
      Optional<User> user = partnerHubService.getHubUserByPmcId(property.getPmcId());
      if (user.isEmpty()) {
        return new FeatureOutput("No qualified hub user associated with property");
      }
      if (user.get().getKycId() == null) {
        return new FeatureOutput("No KYC associated with hub user");
      }
      kycId = user.get().getKycId();
    }
    return new FeatureOutput(verificationService.getKyc(kycId));
  }

  @Override
  public String getDescription() {
    return "Get KYC record";
  }
}
