package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;

@RegisterFeature(
    value = "NumberOfUniqueCardFingerprintFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfUniqueCardFingerprintFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;
  private final LookbackDurationFeatureParams parameters;

  public NumberOfUniqueCardFingerprintFeature(
      IdentityService identityService,
      WalletService walletService,
      LookbackDurationFeatureParams parameters
  ) {
    this.identityService = identityService;
    this.walletService = walletService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    var customer = identityService.getCustomer(evalParams.getCustomerId());
    var cards = walletService.getCards(customer.getCustomerPublicId());
    if (cards == null || cards.isEmpty()) {
      return new FeatureOutput(0);
    }
    var result = (int) cards.stream()
        .filter(card -> card.getDtCreated().isAfter(getDtCreatedLookbackWindow()))
        .map(Card::getFingerprint).distinct().count();
    return new FeatureOutput(result);
  }

  @Override
  public String getDescription() {
    return "Number of unique card fingerprints of a customer for a specified lookback window";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private OffsetDateTime getDtCreatedLookbackWindow() {
    try {
      var lookbackWindow = Duration.parse(parameters.lookbackWindow());
      return OffsetDateTime.now().minus(lookbackWindow);
    } catch (Exception e) {
      throw new InternalServiceBadDataException(
          String.format(
              "Invalid lookback window format: '%s'. Expected ISO-8601 format e.g. P30D",
              this.parameters.lookbackWindow()
          )
      );
    }
  }
}
