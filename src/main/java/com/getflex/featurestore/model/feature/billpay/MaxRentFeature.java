package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class MaxRentFeature extends BillPayOfflineFeature {

  public MaxRentFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Customer's max rent";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "max_rent";
  }
}
