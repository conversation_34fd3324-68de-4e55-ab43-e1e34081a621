package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@RegisterFeature
public class HasPendingWithdrawalLast24HoursFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public HasPendingWithdrawalLast24HoursFeature(LedgerService ledgerService) {
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public String getDescription() {
    return "Indicates whether the customer has any pending withdrawal transactions in the last 24 hours";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    OffsetDateTime dtCreatedStartRangePast30Days = OffsetDateTime.now().minusDays(30);
    APIretrieveLedgerByCustomerIdRequest request = LedgerApi.APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRangePast30Days)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
        .includeRefund(IncludeRefundEnum.EXCLUDE)
        .build();
    List<RecordLedger> ledgerRecords = ledgerService.retrieveLedgerByCustomerId(request);
    if (ledgerRecords != null && !ledgerRecords.isEmpty()) {
      RecordLedger latestFundsOutRecord = ledgerRecords.stream()
          .filter(recordLedger -> recordLedger.getDtCreated() != null)
          .max(Comparator.comparing(RecordLedger::getDtCreated))
          .orElse(null);
      if (latestFundsOutRecord != null
          && Objects.equals(latestFundsOutRecord.getPaymentStatusId(), PaymentState.INITIATED.getValue())) {
        OffsetDateTime dateCreated = latestFundsOutRecord.getDtCreated();
        if (OffsetDateTime.now().isBefore(dateCreated.plusHours(24))) {
          return new FeatureOutput(true);
        }
      }
    }
    return new FeatureOutput(false);
  }
}