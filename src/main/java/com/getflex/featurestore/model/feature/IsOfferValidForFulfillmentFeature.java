package com.getflex.featurestore.model.feature;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.event.eventmetadata.EligibilityVerificationMetadata;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.exception.fulfillment.FulfillmentOfferNotFoundException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsOfferValidForFulfillmentFeature extends BaseFeature {

  private final OfferService offerService;
  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().disable(
      DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

  public IsOfferValidForFulfillmentFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String metadataString;
    String exceptionType = null;
    String exceptionMessage = null;
    boolean isOfferValid = true;
    try {
      InternalOffer offer = offerService.getOfferByBillerAccountIdAndOfferVersion(evalParams.getBillerAccountId(),
          evalParams.getOfferVersion());
      if (offer == null) {
        throw new FulfillmentOfferNotFoundException(
            "Offer not found for biller account id " + evalParams.getBillerAccountId());
      }
      Set<OfferStateEnum> legitimateOfferState = new HashSet<>(Set.of(OfferStateEnum.ACCEPTED));
      if (evalParams.getOfferVersion() != null) {
        legitimateOfferState.add(OfferStateEnum.CLOSED);
      }
      if (!legitimateOfferState.contains(offer.getOfferState())) {
        InternalOffer customerOffer = null;
        isOfferValid = false;
        try {
          customerOffer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
        } catch (InternalDependencyFailureException e) {
          // no customerOffer found, continue
          log.info("No customer offer found for customerId={}", evalParams.getCustomerId());
        }
        if (customerOffer != null && (customerOffer.getOfferState() == OfferStateEnum.ACCEPTED || (
            !Objects.equals(customerOffer.getBillerAccountId(), offer.getBillerAccountId())
                && customerOffer.getOfferState() == OfferStateEnum.CLOSED && Objects.equals(
                customerOffer.getDeactivationReason(), "SuspendCreditLine")))) {
          exceptionType = "FulfillmentOutdatedBillAccountException";
          exceptionMessage = ("Outdated biller account provided " + "(biller_account_id=%s)").formatted(
              evalParams.getBillerAccountId());
        } else {
          exceptionType = "FulfillmentOfferStateNotEligibleException";
          exceptionMessage = "Offer state is not eligible for fulfillment (offerId=%s, state=%s)".formatted(
              offer.getOfferId(), offer.getOfferState());
        }
      } else {
        if (offer.getCreditUtilizationCent() == null) {
          isOfferValid = false;
          exceptionType = "FulfillmentNonVclOfferException";
          exceptionMessage = "NonVCL offer not allowed for biller_account_id=%s and version=%s".formatted(
              offer.getBillerAccountId(), offer.getOfferVersion());
        }
      }
      try {
        EligibilityVerificationMetadata metadata = EligibilityVerificationMetadata.builder()
            .offerId(offer.getOfferId())
            .estimatedBillAmountCents(offer.getEstimatedBillAmountCent())
            .offerVersion(offer.getOfferVersion())
            .exceptionType(exceptionType)
            .exceptionMessage(exceptionMessage)
            .build();
        metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
        return new FeatureOutput(isOfferValid, metadataString);
      } catch (JsonProcessingException e) {
        throw new InternalServiceBadDataException(
            "Error while parsing metadata of IsOfferValidForFulfillmentFeature for offer (offer_id=%s)".formatted(
                offer.getOfferId()));
      }
    } catch (InternalDependencyFailureException | FulfillmentOfferNotFoundException e) {
      try {
        exceptionType = "FulfillmentOfferNotFoundException";
        exceptionMessage = ("Offer or specified version not found (biller_account_id=%s, " + "version=%s").formatted(
            evalParams.getBillerAccountId(), evalParams.getOfferVersion());
        isOfferValid = false;
        EligibilityVerificationMetadata metadata = EligibilityVerificationMetadata.builder()
            .exceptionType(exceptionType)
            .exceptionMessage(exceptionMessage)
            .build();
        metadataString = OBJECT_MAPPER.writeValueAsString(metadata);
        return new FeatureOutput(isOfferValid, metadataString);
      } catch (JsonProcessingException ex) {
        throw new InternalServiceBadDataException(
            "Error while parsing metadata of IsOfferValidForFulfillmentFeature (biller_account_id=%s, version=%s)"
                .formatted(evalParams.getBillerAccountId(), evalParams.getOfferVersion()));
      }
    }
  }

  @Override
  public String getDescription() {
    return "Check if the offer is valid for fulfillment";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
