package com.getflex.featurestore.model.feature.similarity;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.FeatureUtils;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class BillerAccountPropertyNameBatchMemoSimilarityScoreFeature extends BaseFeature {

  private final IdentityService identityService;

  public BillerAccountPropertyNameBatchMemoSimilarityScoreFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String propertyName = identityService.getPropertyNameByBillerAccountId(
          evalParams.getBillerAccountId());
      if (propertyName.isEmpty()) {
        return new FeatureOutput(0);
      }
      return new FeatureOutput(
          FeatureUtils.getSimilarityScore(evalParams.getBatchMemo(), List.of(propertyName)));
    } catch (Exception e) {
      log.error("exception calculating similarity score for ba_id={}",
          evalParams.getBillerAccountId(), e);
    }
    return new FeatureOutput(0);
  }

  @Override
  public String getDescription() {
    return "Property name to batch memo similarity score";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
