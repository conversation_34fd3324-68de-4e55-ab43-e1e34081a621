package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.feature.base.CustomerIsReappyIneligibleBaseFeature;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerIsReapplyIneligibleBankruptcyFeature extends CustomerIsReappyIneligibleBaseFeature {

  public CustomerIsReapplyIneligibleBankruptcyFeature(OfferService offerService) {
    super(offerService);
  }

  @Override
  public String getDeactivationReasonDetail() {
    return "Bankruptcy";
  }

  @Override
  public String getDescription() {
    return "Check if the offer is closed due to bankruptcy";
  }
}
