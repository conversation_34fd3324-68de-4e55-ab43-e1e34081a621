package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.feature.base.CustomerDaysSinceLastContactInfoChangeBaseFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.useraccount.model.UpdateHistoryRecord;

@RegisterFeature
public class CustomerDaysSinceLastPhoneNumberChangeFeature extends CustomerDaysSinceLastContactInfoChangeBaseFeature {

  public CustomerDaysSinceLastPhoneNumberChangeFeature(IdentityService identityService,
      UserAccountService userAccountService) {
    super(identityService, userAccountService);
  }

  @Override
  public String getContactInfoFieldValue(UpdateHistoryRecord updateHistoryRecord) {
    return updateHistoryRecord.getPhone();
  }

  @Override
  public String getDescription() {
    return "number of days since customer's phone number was last updated";
  }

}
