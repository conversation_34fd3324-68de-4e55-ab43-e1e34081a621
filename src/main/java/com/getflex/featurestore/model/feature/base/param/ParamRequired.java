package com.getflex.featurestore.model.feature.base.param;

import com.getflex.featurestore.model.EvalParamKey;
import java.util.Set;

/**
 * Represent parameters required by feature.
 * It's a tree structure with {@link All}/{@link Any} as internal node and {@link Single} as leaf node.
 */
public interface ParamRequired {

  /**
   * @param availableParams
   * @throws IllegalArgumentException Thrown when availableParams doesn't contain mandatory param
   */
  void validateEvaluationParams(Set<EvalParamKey> availableParams);
}
