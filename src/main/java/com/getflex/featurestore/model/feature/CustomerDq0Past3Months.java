package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.IsDelinquencyBaseFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import java.time.Clock;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerDq0Past3Months extends IsDelinquencyBaseFeature {

  public CustomerDq0Past3Months(LedgerService ledgerService, Clock clock, DelinquencyService delinquencyService) {
    super(ledgerService, clock, delinquencyService);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    boolean isDq = super.isDelinquency(evalParams.getCustomerId(), 0, 3, false);
    log.info("CustomerDq0Past3Months: customerId={}, isDq={}", evalParams.getCustomerId(), isDq);
    return new FeatureOutput(isDq);
  }

  @Override
  public String getDescription() {
    return "Whether customer has dq0 in the past 3 months";
  }
}
