package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

@Slf4j
@RegisterFeature
public class CmmV2ScoreFeature extends BaseOfflineFeature {

  private static final double VALUE_FOR_MISSING_CMM_SCORE = -1.0;

  public CmmV2ScoreFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Get the CMM V2 score of the customer";
  }

  @Override
  public String getOfflineFeatureName() {
    return "cmm_v2_score";
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      String rawOfflineString = super.getRawFeatureValue(evalParams);
      JSONObject object = (JSONObject) new JSONParser().parse(rawOfflineString);
      return new FeatureOutput(Double.parseDouble(object.get("score").toString()));
    } catch (Exception e) {
      log.warn("Failed to parse CMM V2 score. customerId={}", evalParams.getCustomerId(), e);
      return new FeatureOutput(VALUE_FOR_MISSING_CMM_SCORE);
    }
  }
}
