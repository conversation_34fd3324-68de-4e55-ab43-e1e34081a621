package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.List;

@RegisterFeature
public class BillTransactionDeclinedDownpaymentCountFromLedgerFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public BillTransactionDeclinedDownpaymentCountFromLedgerFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  @JsonInclude(JsonInclude.Include.NON_EMPTY)
  @JsonPropertyOrder({"decline_count", "latest_dp_decline_dt"})
  public record DownpaymentDeclineInfo(int declineCount, OffsetDateTime latestDpDeclineDt) {}

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> transactions = ledgerService.getLedgersByBillTransactionId(
        evalParams.getBillTransactionId(), PaymentState.DECLINED, MoneyMovementType.DOWNPAYMENT)
        .stream()
        .filter(t -> t.getPaymentCategoryId() == LedgerService.MovementCategory.CHARGE.getValue()
            && t.getMoneyMovementTypeId() == MoneyMovementType.DOWNPAYMENT.getValue()
            && t.getPaymentStatusId() == PaymentState.DECLINED.getValue())
        .toList();

    if (transactions == null || transactions.isEmpty()) {
      return new FeatureOutput(new DownpaymentDeclineInfo(0, null));
    }

    OffsetDateTime latestDeclineDt = transactions.stream()
        .map(RecordLedger::getDtCreated)
        .max(Comparator.naturalOrder())
        .orElse(null);

    return new FeatureOutput(new DownpaymentDeclineInfo(transactions.size(), latestDeclineDt));
  }

  @Override
  public String getDescription() {
    return "Downpayment declined count and latest decline date for bill transaction";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
