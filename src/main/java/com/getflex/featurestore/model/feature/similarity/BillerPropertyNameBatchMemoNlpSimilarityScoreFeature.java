package com.getflex.featurestore.model.feature.similarity;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.None;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@RegisterFeature
public class BillerPropertyNameBatchMemoNlpSimilarityScoreFeature extends
    BaseNlpSimilarityScoreFeature {

  private final BillingService billingService;
  private static final List<String> OON_PMC_NAMES = List.of(
      "Generic OON Properties",
      "Flex Generic Portal Properties",
      "Flex Non PMC Properties",
      "Generic OON Flex Anywhere"
  );

  public BillerPropertyNameBatchMemoNlpSimilarityScoreFeature(
      SageMakerRuntimeClient sagemaker, BillingService billingService) {
    super(sagemaker);
    this.billingService = billingService;
  }

  @Override
  public List<String> getTargetList(EvalParams evalParams) {
    ComGetflexBillingControllerV2PropertyControllerPropertyResponse response
        = billingService.getPropertyByBillerId(evalParams.getBillerId());
    if (response == null || response.getName() == null || OON_PMC_NAMES.contains(
        response.getName())) {
      return List.of("");
    }
    return List.of(response.getName());
  }

  @Override
  public String getDescription() {
    return "Biller's PMC name similarity score with batch memo";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return None.INSTANCE;
  }
}
