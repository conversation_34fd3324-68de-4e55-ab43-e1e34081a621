package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.GeoIpNetworkRepo;
import com.getflex.featurestore.dao.repo.UsAddressesRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
@RegisterFeature
public class CustomerZipToIpDistanceFeature extends BaseFeature {

  /**
   * Default value used when distance calculation is not possible due to missing or invalid data. This value (99999)
   * indicates an error condition rather than an actual distance.
   */
  private static final double INVALID_DISTANCE_VALUE = 99999.0;

  private final IdentityService identityService;
  private final GeoIpNetworkRepo geoipNetworkRepo;
  private final UsAddressesRepo usAddressesRepo;

  public CustomerZipToIpDistanceFeature(
      IdentityService identityService,
      GeoIpNetworkRepo geoipNetworkRepo,
      UsAddressesRepo usAddressesRepo
  ) {
    this.identityService = identityService;
    this.geoipNetworkRepo = geoipNetworkRepo;
    this.usAddressesRepo = usAddressesRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    if (!StringUtils.hasLength(evalParams.getIpAddress())) {
      log.warn("IP address is empty or null");
      return new FeatureOutput(INVALID_DISTANCE_VALUE, "IP address is null or empty, cannot calculate distance.");
    }

    var customer = identityService.getCustomer(evalParams.getCustomerId());
    var ipAddressCoordinates = geoipNetworkRepo.findByIpAddressInRange(evalParams.getIpAddress());

    // Check if IP address coordinates were found
    if (ipAddressCoordinates == null) {
      log.warn("No coordinates found for the IP address: {}", evalParams.getIpAddress());
      return new FeatureOutput(INVALID_DISTANCE_VALUE,
          "No coordinates found for the IP address: " + evalParams.getIpAddress());
    }

    var zipCodeCoordinates = usAddressesRepo.findFirstByZipOrderByCityAscLongitudeDescLatitudeDesc(customer.getZip());

    // Check if zip code coordinates were found
    if (zipCodeCoordinates == null) {
      log.warn("No coordinates found for the zip code: {}", customer.getZip());
      return new FeatureOutput(INVALID_DISTANCE_VALUE, "No coordinates found for the zip code: " + customer.getZip());
    }

    try {
      return new FeatureOutput(ipAddressCoordinates.haversine(zipCodeCoordinates));
    } catch (IllegalArgumentException e) {
      log.warn("Error calculating distance: {}", e.getMessage());
      return new FeatureOutput(INVALID_DISTANCE_VALUE, "Error calculating distance: " + e.getMessage());
    }
  }

  @Override
  public String getDescription() {
    return "Distance from customer IP address to customer zip code";
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(INVALID_DISTANCE_VALUE,
        "Error processing Customer Zip to Ip Distance, returning default value.");
  }

  /*
   * This requires IP Address to work, but we don't require it because it will throw an exception.
   * When IP Address is missing we just return a default value instead.
   */
  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}