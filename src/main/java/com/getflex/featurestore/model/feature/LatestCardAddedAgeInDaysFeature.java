package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Comparator;

@RegisterFeature
public class LatestCardAddedAgeInDaysFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;

  public LatestCardAddedAgeInDaysFeature(
      IdentityService identityService,
      WalletService walletService
  ) {
    this.identityService = identityService;
    this.walletService = walletService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    var customer = identityService.getCustomer(evalParams.getCustomerId());
    var cards = walletService.getCards(customer.getCustomerPublicId());
    if (cards == null || cards.isEmpty()) {
      throw new FeatureNotFoundException("No cards found for customerId: " + customer.getCustomerId());
    }
    var latestCardAddRecord = cards.stream().max(Comparator.comparing(Card::getDtCreated)).get();
    var now = OffsetDateTime.now();
    var firstTimeLatestCardAddedDtCreated = cards.stream()
        .filter(card -> card.getFingerprint().equals(latestCardAddRecord.getFingerprint()))
        .min(Comparator.comparing(Card::getDtCreated))
        .map(Card::getDtCreated)
        .orElse(now);
    var result = (int) Duration.between(firstTimeLatestCardAddedDtCreated, now).toDays();
    return new FeatureOutput(result);
  }

  @Override
  public String getDescription() {
    return "Latest card added age in days";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
