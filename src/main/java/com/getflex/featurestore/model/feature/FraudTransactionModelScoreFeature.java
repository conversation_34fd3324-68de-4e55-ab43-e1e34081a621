package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelInput;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.feature.base.BaseModelFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.concurrent.ExecutorService;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@RegisterFeature
public class FraudTransactionModelScoreFeature extends BaseModelFeature<FraudTransactionModelInput> {

  public static final List<FraudTransactionModelInput> MODEL_INPUT_FEATURES_LIST =
      new ArrayList<>(EnumSet.allOf(FraudTransactionModelInput.class));

  private final ServiceConfig serviceConfig;

  public FraudTransactionModelScoreFeature(SageMakerRuntimeClient sageMaker, ServiceConfig serviceConfig,
      FeatureFactory featureFactory, ExecutorService executorService) {
    super(sageMaker, executorService, featureFactory);
    this.serviceConfig = serviceConfig;
  }

  @Override
  public String getDescription() {
    return "Fraud Transaction Model Score";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public List<FraudTransactionModelInput> getModelFeaturesList() {
    return MODEL_INPUT_FEATURES_LIST;
  }

  @Override
  protected String getEndpointName() {
    return serviceConfig.getFraudTransModelEndpoint();
  }
}
