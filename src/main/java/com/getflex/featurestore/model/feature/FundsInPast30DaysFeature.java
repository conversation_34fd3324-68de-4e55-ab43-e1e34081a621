package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;

@RegisterFeature
public class FundsInPast30DaysFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public FundsInPast30DaysFeature(LedgerService ledgerService) {
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    OffsetDateTime dtCreatedStartRangePast30Days = OffsetDateTime.now().minusDays(30);
    APIretrieveLedgerByCustomerIdRequest request = LedgerApi.APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRangePast30Days)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .includeRefund(IncludeRefundEnum.EXCLUDE)
        .build();
    List<RecordLedger> fundsInRecords = ledgerService.retrieveLedgerByCustomerId(request);
    if (fundsInRecords == null || fundsInRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    Integer fundsInCount = fundsInRecords.stream().filter(fundsIn -> fundsIn.getPaymentCategoryId() != null)
        .filter(fundsIn -> fundsIn.getPaymentCategoryId() == MovementCategory.CHARGE.getValue())
        .toList()
        .size();
    return new FeatureOutput(fundsInCount);
  }

  @Override
  public String getDescription() {
    return "Number of funds in made in the last 30 days";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
