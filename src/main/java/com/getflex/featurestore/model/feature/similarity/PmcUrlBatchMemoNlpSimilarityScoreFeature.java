package com.getflex.featurestore.model.feature.similarity;

import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaContainerPortalPortalContainer;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseGetBillerAccountResponse;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.FeatureUtils;
import com.getflex.featurestore.model.feature.base.BaseNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@Slf4j
@RegisterFeature
public class PmcUrlBatchMemoNlpSimilarityScoreFeature extends BaseNlpSimilarityScoreFeature {

  BillingService billingService;

  public PmcUrlBatchMemoNlpSimilarityScoreFeature(BillingService billingService, SageMakerRuntimeClient sagemaker) {
    super(sagemaker);
    this.billingService = billingService;
  }

  @Override
  public List<String> getTargetList(EvalParams evalParams) {
    List<String> result = List.of("", "");
    try {
      ComGetflexBillingControllerV2PropertyControllerPropertyResponse biller = this.billingService
          .getPropertyByBillerId(evalParams.getBillerId());
      IntegrationTypeEnum integrationType = biller.getIntegrationType();
      if (integrationType == null || integrationType == IntegrationTypeEnum.FLEX_ANYWHERE) {
        log.info("Skip flexanywhere biller for billerId {}", evalParams.getBillerId());
        return result;
      }

      Long portalId = biller.getPortalId();
      if (portalId == null) {
        log.info("No portal id for billerId {}", evalParams.getBillerId());
        return result;
      }

      List<ComGetflexBillingAuroraJpaContainerPortalPortalContainer> portals = billingService.getPortals();
      Optional<ComGetflexBillingAuroraJpaContainerPortalPortalContainer> matchingPortal = portals.stream()
          .filter(p -> Objects.equals(p.getId(), portalId)).findFirst();

      if (matchingPortal.isEmpty() || matchingPortal.get().getPortalDisplayConfig() == null
          || matchingPortal.get().getPortalDisplayConfig().getLoginUrl() == null) {
        log.info("No portal url for billerId {}", evalParams.getBillerId());
        return result;
      }

      String cleanUrl = FeatureUtils.extractDomainFromUrl(
          matchingPortal.get().getPortalDisplayConfig().getLoginUrl());
      log.info("portal login url for billerId {} is {}", evalParams.getBillerId(), cleanUrl);

      String pmcUrl = "";
      try {
        if (evalParams.getBillerAccountId() != null) {
          ComGetflexBillingDtoResponseGetBillerAccountResponse billerAccount
              = billingService.getBillerAccountByBillerIdAndBillerAccountId(
              evalParams.getBillerId(), evalParams.getBillerAccountId());
          List<Map<String, String>> fields = billerAccount.getIntegrationFields();
          if (fields != null) {
            Optional<Map<String, String>> dict = fields.stream()
                .filter(m -> m.get("name").equals("portal_url")).findFirst();
            if (dict.isPresent() && !dict.get().isEmpty()) {
              pmcUrl = dict.get().get("value");
            }
          }
        }
      } catch (Exception e) {
        log.warn("Exception fetching PMC url for billerId: {}, batchMemo: {}, billerAccountId: {}",
            evalParams.getBillerId(), evalParams.getBatchMemo(), evalParams.getBillerAccountId(), e);
      }
      pmcUrl = FeatureUtils.extractDomainFromUrl(pmcUrl);
      result = List.of(pmcUrl, cleanUrl);
    } catch (Exception e) {
      log.warn("Exception fetching protal url for biller id, exception", e);
      return result;
    }
    return result;
  }


  @Override
  public String getDescription() {
    return "Similarity score between batch memo and associated pmc";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ID, EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
