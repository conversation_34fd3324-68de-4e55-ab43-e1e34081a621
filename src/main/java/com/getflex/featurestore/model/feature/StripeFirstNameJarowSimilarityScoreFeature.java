package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RegisterFeature
public class StripeFirstNameJarowSimilarityScoreFeature extends BaseJarowSimilarityScoreFeature {

  public StripeFirstNameJarowSimilarityScoreFeature(IdentityService identityService) {
    super(identityService);
  }

  @Override
  protected String getLeftValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    String firstNameIdentity = customer.getFirstName();
    if (StringUtils.isEmpty(firstNameIdentity)) {
      log.warn("firstName is null in identity service for customerId={}", evalParams.getCustomerId());
      return null;
    }
    return firstNameIdentity.replaceAll("\\s", "").toLowerCase();
  }

  @Override
  protected String getRightValue(EvalParams evalParams) {
    String[] namePartition = evalParams.getStripePaymentFullName().split(" ");
    return namePartition[0].replaceAll("\\s", "").toLowerCase();
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_PAYMENT_FULL_NAME);
  }

  @Override
  public String getDescription() {
    return "Jaro-Winkler similarity score between Stripe payment first name and Flex identity customer first name";
  }
}
