package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

@RegisterFeature
public class CustomerDuplicatedDeviceIdFeature extends BaseOfflineFeature {

  private final OfflineFeatureRepo offlineFeatureRepo;

  public CustomerDuplicatedDeviceIdFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
    this.offlineFeatureRepo = offlineFeatureRepo;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(false);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "device_id_to_customer_id_list";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getDeviceId();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Optional<OfflineFeature> feature = offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        this.getOfflineFeatureName(), evalParams.getDeviceId().toLowerCase());
    if (feature.isEmpty() || StringUtils.isEmpty(feature.get().getFeatureValue())) {
      return getDefaultValue();
    }
    Set<String> customerSet = new HashSet<>(
        Arrays.asList(feature.get().getFeatureValue().split(",")));
    if (customerSet.isEmpty()) {
      return getDefaultValue();
    }
    if (customerSet.size() == 1) {
      return new FeatureOutput(!(customerSet.contains(Long.toString(evalParams.getCustomerId()))));
    }
    return new FeatureOutput(true);
  }

  @Override
  public String getDescription() {
    return "Whether device id is matched to another customer id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.DEVICE_ID, EvalParamKey.CUSTOMER_ID);
  }
}
