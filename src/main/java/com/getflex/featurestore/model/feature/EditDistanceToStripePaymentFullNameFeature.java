package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.util.StringUtils;

@Slf4j
@RegisterFeature
public class EditDistanceToStripePaymentFullNameFeature extends BaseFeature {

  private final IdentityService identityService;

  public EditDistanceToStripePaymentFullNameFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    if (!StringUtils.hasLength(evalParams.getStripePaymentFullName())) {
      log.warn("stripePaymentFullName is empty, returning defaultValue={}", getDefaultValue().value());
      return getDefaultValue();
    }

    GetCustomerResponse customer = this.identityService.getCustomer(evalParams.getCustomerId());
    String fullnameIdentity = (customer.getFirstName() + customer.getLastName()).replaceAll("\\s", "").toLowerCase();
    String fullnameStripe = (evalParams.getStripePaymentFullName()).replaceAll("\\s", "").toLowerCase();
    Integer distance = LevenshteinDistance.getDefaultInstance().apply(fullnameStripe, fullnameIdentity);
    return new FeatureOutput(distance, null);
  }

  @Override
  public String getDescription() {
    return "Levenshtein edit distance between the full name of the Flex customer account and the full name on the "
        + "payment method";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(-1, null);
  }
}
