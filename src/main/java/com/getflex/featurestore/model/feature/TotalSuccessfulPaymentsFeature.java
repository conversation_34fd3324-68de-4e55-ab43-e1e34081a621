package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseOfflineFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class TotalSuccessfulPaymentsFeature extends BaseOfflineFeature {

  public TotalSuccessfulPaymentsFeature(OfflineFeatureRepo offlineFeatureRepo) {
    super(offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Returns the total number of successful payments made by the customer";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "total_successful_payments";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getCustomerId().toString();
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String featureValue = super.getRawFeatureValue(evalParams);

    if (featureValue == null) {
      return new FeatureOutput(0);
    }

    return new FeatureOutput(Integer.parseInt(featureValue));
  }

}
