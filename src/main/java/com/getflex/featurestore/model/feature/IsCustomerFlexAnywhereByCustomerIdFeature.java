package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.BillerAccountRecord;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsCustomerFlexAnywhereByCustomerIdFeature extends BaseFeature {

  private final BillingService billingService;
  private final IdentityService identityService;

  public IsCustomerFlexAnywhereByCustomerIdFeature(BillingService billingService, IdentityService identityService) {
    super();
    this.billingService = billingService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    BillerAccountRecord billerAccountRecord = null;
    try {
      billerAccountRecord = identityService.getActiveBillerAccountByCustomerId(evalParams.getCustomerId());
    } catch (InternalDependencyFailureException e) {
      log.warn("Error while fetching biller account record for customerId={}, default to true",
          evalParams.getCustomerId(), e);
    }

    if ((billerAccountRecord == null) || (billerAccountRecord.getBillerId() == null)) {
      return new FeatureOutput(true);
    }
    ComGetflexBillingControllerV2PropertyControllerPropertyResponse
        comGetflexBillingControllerV2PropertyControllerPropertyResponse =
        billingService.getBillerInfoByBillerId(billerAccountRecord.getBillerId());
    if (comGetflexBillingControllerV2PropertyControllerPropertyResponse == null) {
      return new FeatureOutput(true);
    }
    return new FeatureOutput(
        comGetflexBillingControllerV2PropertyControllerPropertyResponse.getIntegrationType()
            == IntegrationTypeEnum.FLEX_ANYWHERE);
  }

  @Override
  public String getDescription() {
    return "This feature returns the value of whether the customer "
        + "is a FlexAnywhere customer or not from customer id";
  }
}
