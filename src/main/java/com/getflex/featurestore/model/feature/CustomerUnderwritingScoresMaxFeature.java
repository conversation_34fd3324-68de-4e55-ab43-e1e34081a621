package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.decisionengine.async.model.CheckpointDecisionLog;
import com.getflex.decisionengine.async.model.GetCheckpointDecisionLogsResponse;
import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.dto.OfferSocureFeatureDto;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.output.CustomerUnderwritingScoresOutput;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerUnderwritingScoresMaxFeature extends BaseFeature {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().registerModule(
      new JavaTimeModule());
  private static final List<String> SOCURE_FEATURES_CHECKPOINT_NAMES = List.of(
      "EvaluateKycCheckpointV2", "PreBureauReportFraudExternalSignalsCheckpoint"
  );
  private static final String FEATURE_KEY = "SocureFeature";

  private final OfferService offerService;
  private final DecisionEngineService decisionEngineService;

  public CustomerUnderwritingScoresMaxFeature(
      OfferService offerService,
      DecisionEngineService decisionEngineService
  ) {
    this.offerService = offerService;
    this.decisionEngineService = decisionEngineService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    List<InternalOffer> offers = offerService.searchOffer(customerId, true);
    List<CheckpointDecisionLog> socureLogs = new ArrayList<>();
    try {
      List<CompletableFuture<GetCheckpointDecisionLogsResponse>> futures =
          SOCURE_FEATURES_CHECKPOINT_NAMES.stream()
              .map(checkpointName ->
                  decisionEngineService.getCheckpointDecisionLogs(
                      customerId,
                      checkpointName,
                      null
                  )
              ).toList();

      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

      socureLogs = futures.stream()
          .map(CompletableFuture::join)
          .map(GetCheckpointDecisionLogsResponse::getCheckpointDecisionLogs)
          .filter(Objects::nonNull)
          .flatMap(List::stream)
          .toList();
    } catch (Exception ex) {
      log.warn("Failed to get socure checkpoint logs from DDB, customerId={}", customerId, ex);
    }

    Stream<CustomerUnderwritingScoresOutput> offerScores = offers.stream()
        .map(InternalOffer::getEvaluationContext)
        .filter(Objects::nonNull)
        .filter(evalContext -> evalContext.getIsRootEvaluation() != null && evalContext.getIsRootEvaluation())
        .map(evalContext -> {
          CustomerUnderwritingScoresOutput score = new CustomerUnderwritingScoresOutput();
          score.upsertMaxScore(evalContext);
          return score;
        });

    Stream<CustomerUnderwritingScoresOutput> socureScores =
        !socureLogs.isEmpty()
            ? socureLogs.stream()
            .map(checkpointDecisionLog -> checkpointDecisionLog.getFeatureValues().get(FEATURE_KEY))
            .map(rawSocureFeature -> getSocureFeature(rawSocureFeature, customerId))
            .filter(Objects::nonNull)
            .map(CustomerUnderwritingScoresOutput::fromSocureFeature)
            : Stream.empty();

    CustomerUnderwritingScoresOutput output = Stream.concat(offerScores, socureScores)
        .reduce(
            new CustomerUnderwritingScoresOutput(),
            (acc, score) -> {
              acc.updateSocureMaxScoreFromOtherOutput(score);
              return acc;
            });


    return new FeatureOutput(output);
  }

  @Override
  public String getDescription() {
    return "Maximum underwriting scores from all of the customer's offers. These include Socure scores (sigma, address"
        + "risk, email risk, name address correlation, "
        + "name email correlation phone risk, synthetic scores, and fraud reason codes), "
        + "Flex score decile, and Sentilink scores "
        + "(first party synthetic, third party synthetic, abuse, identity theft).";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private OfferSocureFeatureDto getSocureFeature(Object rawSocureFeature, Long customerId) {
    try {
      JsonNode socureNode;
      if (rawSocureFeature instanceof CharSequence) {
        socureNode = OBJECT_MAPPER.readTree(rawSocureFeature.toString());
      } else {
        socureNode = OBJECT_MAPPER.valueToTree(rawSocureFeature);
      }
      return OfferSocureFeatureDto.fromRaw(socureNode);
    } catch (JsonProcessingException ex) {
      log.warn("Failed to parse SocureFeature Json using ObjectMapper for customerId={}",
          customerId, ex);
      return null;
    }
  }
}
