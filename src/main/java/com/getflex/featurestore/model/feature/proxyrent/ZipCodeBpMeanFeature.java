package com.getflex.featurestore.model.feature.proxyrent;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class ZipCodeBpMeanFeature extends ProxyRentOfflineFeature {

  public static final String OFFLINE_FEATURE_NAME = "zip_code_rent_bp_feature";

  public ZipCodeBpMeanFeature(ProxyRentUtils proxyRentUtils, OfflineFeatureRepo offlineFeatureRepo) {
    super(proxyRentUtils, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Get the avg_bill_payment_amount value for a provided zip code";
  }

  @Override
  protected String getOfflineFeatureName() {
    return OFFLINE_FEATURE_NAME;
  }
}
