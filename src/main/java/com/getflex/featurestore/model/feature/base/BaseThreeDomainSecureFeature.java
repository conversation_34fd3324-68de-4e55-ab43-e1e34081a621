package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.utils.ThreeDomainSecureUtils;
import com.getflex.featurestore.model.feature.utils.ThreeDomainSecureUtils.ThreeDomainSecureAttempt;
import java.util.List;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public abstract class BaseThreeDomainSecureFeature extends BaseFeature {

  protected final EventRepository eventRepository;
  private final ThreeDomainSecureUtils threeDomainSecureUtils = new ThreeDomainSecureUtils();

  @Autowired
  public BaseThreeDomainSecureFeature(EventRepository eventRepository) {
    super();
    this.eventRepository = eventRepository;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    List<Event> events = getEvents(evalParams);
    int count = threeDomainSecureUtils.get3dsAddCardCount(evalParams, events, getFilterPredicate());
    return new FeatureOutput(count);
  }

  protected abstract Predicate<ThreeDomainSecureAttempt> getFilterPredicate();

  protected List<Event> getEvents(EvalParams evalParams) {
    return eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
        EventName.STRIPE_SETUP_ATTEMPT,
        evalParams.getCustomerId().toString()
    );
  }
}
