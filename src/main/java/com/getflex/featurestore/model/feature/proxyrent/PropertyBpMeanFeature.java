package com.getflex.featurestore.model.feature.proxyrent;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class PropertyBpMeanFeature extends ProxyRentOfflineFeature {

  public static final String OFFLINE_FEATURE_NAME = "property_rent_bp_feature";

  private final ProxyRentUtils proxyRentUtils;

  public PropertyBpMeanFeature(ProxyRentUtils proxyRentUtils, OfflineFeatureRepo offlineFeatureRepo) {
    super(proxyRentUtils, offlineFeatureRepo);
    this.proxyRentUtils = proxyRentUtils;
  }

  @Override
  public String getDescription() {
    return "Get the avg_bill_payment_amount value for a provided property id";
  }

  @Override
  protected String getOfflineFeatureName() {
    return OFFLINE_FEATURE_NAME;
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    try {
      return proxyRentUtils.formatPrimaryKey(getOfflineFeatureName(), evalParams.getBillerId());
    } catch (InternalDependencyFailureException e) {
      return null;
    }
  }
}
