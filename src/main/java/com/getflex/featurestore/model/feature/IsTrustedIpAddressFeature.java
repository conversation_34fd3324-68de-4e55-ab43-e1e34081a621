package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.IsTrustedEntityBaseFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class IsTrustedIpAddressFeature extends IsTrustedEntityBaseFeature {


  public IsTrustedIpAddressFeature(OfflineFeatureRepo offlineFeatureRepo,
                                      IdentityService identityService) {
    super(offlineFeatureRepo, identityService);
  }

  @Override
  public String getDescription() {
    return "Check if given ip address is trusted, matching to the trusted_ip_address_to_customer_id offline feature. "
        + "primary key is ip address and feature value is the customer_public_id";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.IP_ADDRESS);
  }

  @Override
  protected String getOfflineFeatureName() {
    return "trusted_ip_address_to_customer_id";
  }

  @Override
  protected String getPrimaryKey(EvalParams evalParams) {
    return evalParams.getIpAddress();
  }
}
