package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;

@RegisterFeature
public class EstimatedBillAmountCentFromLatestOfferFeature extends BaseFeature {

  private final OfferService offerService;

  public EstimatedBillAmountCentFromLatestOfferFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offer = offerService.getOfferByCustomerId(evalParams.getCustomerId());
    if (offer == null) {
      throw new InternalDependencyFailureException("Failed to get latest offer");
    }
    if (offer.getEstimatedBillAmountCent() == null) {
      throw new InternalDependencyFailureException("estimatedBillAmountCent is null for latest offer");
    }
    return new FeatureOutput(offer.getEstimatedBillAmountCent());
  }

  @Override
  public String getDescription() {
    return "The customer's estimated bill amount (in cent notation) from their latest offer.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
