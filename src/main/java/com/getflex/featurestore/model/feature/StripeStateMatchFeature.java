package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.AddressStateAbbreviation;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.util.Comparator;
import java.util.List;

@RegisterFeature
public class StripeStateMatchFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;

  public StripeStateMatchFeature(IdentityService identityService, WalletService walletService) {
    this.identityService = identityService;
    this.walletService = walletService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    List<Card> cards = walletService.getCardsByFingerprint(evalParams.getCardFingerprint());
    String state = cards.stream().sorted(Comparator.comparing(Card::getDtCreated).reversed())
        .findFirst()
        .map(Card::getAddressState)
        .orElse(null);
    Boolean stripeStateMatch = AddressStateAbbreviation.statesMatch(state, customer.getState());
    return new FeatureOutput(stripeStateMatch);
  }

  @Override
  public String getDescription() {
    return "Check Stripe payment state and Flex customer profile state match";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.CARD_FINGERPRINT);
  }
}
