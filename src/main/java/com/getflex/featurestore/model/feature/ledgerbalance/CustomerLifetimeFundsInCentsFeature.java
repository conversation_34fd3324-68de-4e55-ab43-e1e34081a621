package com.getflex.featurestore.model.feature.ledgerbalance;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseLifetimeLedgerWalletCentsFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.function.Predicate;

@RegisterFeature
public class CustomerLifetimeFundsInCentsFeature extends BaseLifetimeLedgerWalletCentsFeature {


  public CustomerLifetimeFundsInCentsFeature(LedgerService ledgerService) {
    super(ledgerService);
  }

  protected Predicate<RecordLedgerWallet> getFilterPredicate() {
    return rlw ->
        rlw.getMoneyMovementTypeId() == MoneyMovementType.FUNDS_IN.getValue()
        && (rlw.getPaymentCategoryId() == MovementCategory.CHARGE.getValue()
            || rlw.getPaymentCategoryId() == MovementCategory.CAPTURE.getValue())
        && rlw.getPaymentStatusId() == PaymentState.SETTLED.getValue();
  }

  @Override
  public String getDescription() {
    return "This feature returns the customer lifetime funds-in amount in cents.";
  }
}
