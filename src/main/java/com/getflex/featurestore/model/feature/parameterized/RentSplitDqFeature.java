package com.getflex.featurestore.model.feature.parameterized;

import static com.getflex.featurestore.utils.FlexConstant.FLEX_TIMEZONE;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.service.delinquency.DelinquencyInfo;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import com.getflex.featurestore.service.delinquency.RentSplitDqInfo;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;

/**
 * Parameterized feature.
 * <br>
 * Parse pattern: RentSplitDqFeature_DQ{N}_{M}Months
 */
@Slf4j
@RegisterFeature(
    value = "RentSplitDqFeature_DQ{dqDays}_{lookbackMonths}Months",
    parameterType = RentSplitDqFeature.Parameters.class)
public class RentSplitDqFeature extends BaseFeature {

  public record Parameters(int dqDays, int lookbackMonths) {
  }

  private final DelinquencyService delinquencyService;
  private final int dqDays;
  private final int lookbackMonths;

  public RentSplitDqFeature(DelinquencyService delinquencyService, int dqDays, int lookbackMonths) {
    this(delinquencyService, new Parameters(dqDays, lookbackMonths));
  }

  public RentSplitDqFeature(DelinquencyService delinquencyService, Parameters parameters) {
    this.delinquencyService = delinquencyService;
    this.dqDays = parameters.dqDays();
    this.lookbackMonths = parameters.lookbackMonths();
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    
    // Calculate dqMonths based on DQ{N} convention
    int dqMonths = dqDays / 30;
    
    // Get delinquency info with parameterized values
    DelinquencyInfo delinquencyInfo = delinquencyService.getDelinquencyInfo(
        customerId, dqMonths, lookbackMonths, false);
    
    LocalDate rentSplitDqBpDate = null;
    if (delinquencyInfo.isDq() && delinquencyInfo.firstBpDqDate() != null) {
      // Convert OffsetDateTime to LocalDate in ET timezone
      rentSplitDqBpDate = delinquencyInfo.firstBpDqDate()
          .atZoneSameInstant(FLEX_TIMEZONE)
          .toLocalDate();
    }
    
    RentSplitDqInfo rentSplitDqInfo = RentSplitDqInfo.builder()
        .isDq(delinquencyInfo.isDq())
        .rentSplitDqBpDate(rentSplitDqBpDate)
        .build();
    
    log.info("RentSplitDqFeature evaluation completed customerId={} dqDays={} lookbackMonths={} isDq={} " 
        + "rentSplitDqBpDate={}", 
        customerId, dqDays, lookbackMonths, rentSplitDqInfo.isDq(), rentSplitDqInfo.rentSplitDqBpDate());
    
    return new FeatureOutput(rentSplitDqInfo);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public String getDescription() {
    return String.format("Get rent split delinquency information for DQ%d with %d months lookback", 
        dqDays, lookbackMonths);
  }
}
