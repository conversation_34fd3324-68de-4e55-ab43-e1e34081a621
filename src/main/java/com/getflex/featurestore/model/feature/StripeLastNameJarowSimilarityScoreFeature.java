package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.BaseJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RegisterFeature
public class StripeLastNameJarowSimilarityScoreFeature extends BaseJarowSimilarityScoreFeature {

  public StripeLastNameJarowSimilarityScoreFeature(IdentityService identityService) {
    super(identityService);
  }

  @Override
  protected String getLeftValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    String lastNameIdentity = customer.getLastName();
    if (StringUtils.isEmpty(lastNameIdentity)) {
      log.warn("lastName is null in identity service for customerId={}", evalParams.getCustomerId());
      return null;
    }
    return lastNameIdentity.replaceAll("\\s", "").toLowerCase();
  }

  @Override
  protected String getRightValue(EvalParams evalParams) {
    String[] namePartition = evalParams.getStripePaymentFullName().split(" ");
    if (namePartition.length < 2) {
      log.warn("Stripe payment full name does not contain last name, stripePaymentFullName={}",
          evalParams.getStripePaymentFullName());
      return null;
    }
    return namePartition[namePartition.length - 1].replaceAll("\\s", "").toLowerCase();
  }

  @Override
  public String getDescription() {
    return "Jaro-Winkler similarity score between Stripe payment last name and Flex identity customer last name";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_PAYMENT_FULL_NAME);
  }
}
