package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.util.List;

public abstract class CustomerCardMetricsBaseFeature extends BaseFeature {

  private final IdentityService identityService;
  private final WalletService walletService;

  public CustomerCardMetricsBaseFeature(IdentityService identityService, WalletService walletService) {
    this.identityService = identityService;
    this.walletService = walletService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    List<Card> cards = walletService.getCards(customer.getCustomerPublicId());
    if (cards == null || cards.isEmpty()) {
      return new FeatureOutput(0);
    }
    Integer result = calculateMetric(customer, cards);
    return new FeatureOutput(result);
  }

  protected abstract Integer calculateMetric(GetCustomerResponse customer, List<Card> cards);
}
