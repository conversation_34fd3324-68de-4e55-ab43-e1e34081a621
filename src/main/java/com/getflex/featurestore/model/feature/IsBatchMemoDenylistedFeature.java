package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;


@RegisterFeature
public class IsBatchMemoDenylistedFeature extends BaseFeature {

  private final AllowDenyListRepo allowDenyListRepo;

  public static final String BatchMemoDenylistUseCase = "deny_listed_dda_batch_memo";

  public IsBatchMemoDenylistedFeature(AllowDenyListRepo allowDenyListRepo) {
    this.allowDenyListRepo = allowDenyListRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    AllowDenyList entity = this.allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(
        BatchMemoDenylistUseCase, evalParams.getBatchMemo(), false
    );
    if (entity != null && !entity.isAllow()) {
      return new FeatureOutput(true);
    }
    return new FeatureOutput(false);
  }

  @Override
  public String getDescription() {
    return "If batch memo is in the denylist";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BATCH_MEMO);
  }
}
