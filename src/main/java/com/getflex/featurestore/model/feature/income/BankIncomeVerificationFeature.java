package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.EvalParamKey.CUSTOMER_ID;
import static com.getflex.featurestore.model.EvalParamKey.VERIFICATION_ID;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.FORBIDDEN_ACCOUNT_OWNER_PARTIAL_NAMES;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.ZERO_DEFAULT;
import static com.getflex.featurestore.model.feature.income.IncomeUtils.OBJECT_MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.model.feature.income.model.bank.BankModel;
import com.getflex.featurestore.model.feature.income.model.bank.VerifiedBankAccount;
import com.getflex.featurestore.model.feature.income.model.bank.VerifiedBankAccounts;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.verification.model.BankAccount;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.ValidationVerifiedBankAccounts;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import com.plaid.client.model.AccountType;
import com.plaid.client.model.BaseReport;
import com.plaid.client.model.BaseReportAccount;
import com.plaid.client.model.BaseReportAverageMonthlyBalances;
import com.plaid.client.model.BaseReportItem;
import com.plaid.client.model.CraBankIncomeSource;
import com.plaid.client.model.CraCheckReportBaseReportGetResponse;
import com.plaid.client.model.CraCheckReportIncomeInsightsGetResponse;
import com.plaid.client.model.CreditAmountWithCurrency;
import java.net.URISyntaxException;
import java.time.Clock;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

@Slf4j
@RegisterFeature
public class BankIncomeVerificationFeature extends BaseFeature {
  private final IncomeUtils incomeUtils;
  private final Clock etClock;
  private final VerificationService verificationService;

  public BankIncomeVerificationFeature(IncomeUtils incomeUtils, Clock etClock,
      VerificationService verificationService) {
    this.incomeUtils = incomeUtils;
    this.etClock = etClock;
    this.verificationService = verificationService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    try {
      BankModel bankFeatures = getBankFeatures(evalParams);
      return new FeatureOutput(bankFeatures);
    } catch (JsonProcessingException e) {
      log.error("unable to deserialize plaid payload {}", e.getMessage());
      throw new EventMetadataParsingException(e.getMessage());
    }
  }

  @Override
  public String getDescription() {
    return "Deserializes and calculates features from income verification results returned from Plaid";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(VERIFICATION_ID, CUSTOMER_ID);
  }

  private BankModel getBankFeatures(EvalParams evalParams) throws JsonProcessingException {
    Verification verification = verificationService.getVerification(evalParams.getVerificationId());
    Pair<CraCheckReportBaseReportGetResponse, CraCheckReportIncomeInsightsGetResponse> bankIncomeMetadata =
        getBankIncomeMetadata(verification);

    Double grossAnnualSelfReportedIncomeCent = incomeUtils.getSelfReportedAnnualGrossIncomeCents(
        evalParams.getVerificationId());

    VerifiedBankAccounts verifiedBankAccounts = verifyBankIncomeData(bankIncomeMetadata.getLeft().getReport()
            .getItems(), evalParams.getCustomerId(), verification);

    Double totalAverageDailyBalanceCent = getTotalAverageDailyBalanceCent(
        verifiedBankAccounts.getEligibleBankAccounts(), verification, bankIncomeMetadata.getLeft());

    boolean isCompleted = isCompleted(verification);

    // If verification has a COMPLETED status use VerificationContext numbers
    Double verifiedGrossMonthlyIncomeCent = isCompleted ? getCompletedVerifiedGrossMonthlyIncomeCent(verification)
        : calculateVerifiedAverageMonthlyGrossIncomeCents(
            verifiedBankAccounts.getEligibleBankAccounts(), bankIncomeMetadata.getRight());

    double grossMonthlySelfReportedIncome = grossAnnualSelfReportedIncomeCent / 12;
    Double grossMonthlyIncomeCent = Math.min(verifiedGrossMonthlyIncomeCent, grossMonthlySelfReportedIncome);

    return BankModel.builder()
        .grossMonthlyIncomeCent(grossMonthlyIncomeCent)
        .verifiedGrossMonthlyIncomeCent(verifiedGrossMonthlyIncomeCent)
        .grossAnnualSelfReportedIncomeCent(grossAnnualSelfReportedIncomeCent)
        .totalAverageDailyBalanceCent(totalAverageDailyBalanceCent)
        .verifiedBankAccounts(verifiedBankAccounts)
        .build();
  }

  private VerifiedBankAccounts verifyBankIncomeData(List<BaseReportItem> baseReportItems, Long customerId,
      Verification verification) {
    String customerName = incomeUtils.getCustomerFullName(customerId);

    List<VerifiedBankAccount> nonPersonalDepositoryBankAccounts = new ArrayList<>();
    List<VerifiedBankAccount> eligibleBankAccounts = new ArrayList<>();
    List<VerifiedBankAccount> needManualReviewBankAccounts = new ArrayList<>();

    ValidationVerifiedBankAccounts consolidatedVerifiedAccounts = getAlreadyValidatedBankAccounts(verification);

    for (BaseReportItem item : baseReportItems) {
      for (VerifiedBankAccount account : item.getAccounts().stream()
          .map(a -> OBJECT_MAPPER.convertValue(a, VerifiedBankAccount.class)).toList()) {
        if (isPersonalDepository(account)) {
          if (doesNameMatch(customerName, account, consolidatedVerifiedAccounts.getEligibleBankAccounts())) {
            eligibleBankAccounts.add(account);
          } else {
            nonPersonalDepositoryBankAccounts.add(account);
          }
        } else {
          nonPersonalDepositoryBankAccounts.add(account);
        }
      }
    }

    return VerifiedBankAccounts.builder().nonPersonalDepositoryBankAccounts(nonPersonalDepositoryBankAccounts)
        .eligibleBankAccounts(eligibleBankAccounts).needManualReviewBankAccounts(needManualReviewBankAccounts)
        .build();
  }

  private ValidationVerifiedBankAccounts getAlreadyValidatedBankAccounts(Verification verification) {
    ValidationVerifiedBankAccounts consolidated = new ValidationVerifiedBankAccounts()
        .eligibleBankAccounts(List.of())
        .nonPersonalDepositoryBankAccounts(List.of());

    if (isAlreadyValidated(verification)) {
      Optional<ValidationVerifiedBankAccounts> validatedBankAccounts = Optional.ofNullable(verification.getContext())
          .map(VerificationContext::getValidation)
          .map(Validation::getVerifiedBankAccounts);

      Optional<ValidationVerifiedBankAccounts> overriddenValidatedBankAccounts = Optional.ofNullable(
              verification.getContext())
          .map(VerificationContext::getValidationOverride)
          .map(Validation::getVerifiedBankAccounts);


      consolidated.eligibleBankAccounts(Stream.concat(validatedBankAccounts
              .map(ValidationVerifiedBankAccounts::getEligibleBankAccounts).orElse(List.of()).stream(),
          overriddenValidatedBankAccounts.map(ValidationVerifiedBankAccounts::getEligibleBankAccounts)
              .orElse(List.of()).stream()).toList());
      consolidated.nonPersonalDepositoryBankAccounts(Stream.concat(validatedBankAccounts
              .map(ValidationVerifiedBankAccounts::getNonPersonalDepositoryBankAccounts).orElse(List.of()).stream(),
          overriddenValidatedBankAccounts.map(ValidationVerifiedBankAccounts::getNonPersonalDepositoryBankAccounts)
              .orElse(List.of()).stream()).toList());
    }

    return consolidated;
  }

  private Double getTotalAverageDailyBalanceCent(List<VerifiedBankAccount> eligibleBankAccounts,
      Verification verification, CraCheckReportBaseReportGetResponse baseReport) {
    if (isCompleted(verification)) {
      Optional<Long> overriddenTotalAverageDailyBalanceCent = Optional.ofNullable(verification.getContext())
          .map(VerificationContext::getValidationOverride)
          .map(Validation::getTotalAverageDailyBalanceCent);

      Optional<Long> totalAverageDailyBalanceCent = Optional.ofNullable(verification.getContext())
          .map(VerificationContext::getValidation)
          .map(Validation::getTotalAverageDailyBalanceCent);

      return Double.valueOf(overriddenTotalAverageDailyBalanceCent.orElse(totalAverageDailyBalanceCent
          .orElse(ZERO_DEFAULT.longValue())));
    }

    return calculateTotalAverageDailyBalanceCents(eligibleBankAccounts, baseReport);
  }

  private Double getCompletedVerifiedGrossMonthlyIncomeCent(Verification verification) {
    Optional<Long> overriddenGrossMonthlyIncomeCent = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getValidationOverride)
        .map(Validation::getGrossMonthlyIncomeCent);

    Optional<Long> grossMonthlyIncomeCent = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getValidation)
        .map(Validation::getGrossMonthlyIncomeCent);

    return Double.valueOf(overriddenGrossMonthlyIncomeCent.orElse(grossMonthlyIncomeCent
        .orElse(ZERO_DEFAULT.longValue())));
  }

  private boolean doesNameMatch(String customerName, VerifiedBankAccount account, List<BankAccount> verifiedEligible) {
    return account.getOwners().stream().anyMatch(owner -> owner.getNames().stream().anyMatch(
        name -> incomeUtils.namesFuzzyMatch(customerName, name)))
        || verifiedEligible.stream().map(BankAccount::getAccountId).toList().contains(account.getAccountId());
  }

  private boolean isPersonalDepository(VerifiedBankAccount account) {
    return (account.getType().equals(AccountType.DEPOSITORY) && isPersonalBankAccount(account));
  }

  private boolean isPersonalBankAccount(VerifiedBankAccount account) {
    return account.getOwners().stream().noneMatch(owner ->
        owner.getNames().stream().noneMatch(name -> FORBIDDEN_ACCOUNT_OWNER_PARTIAL_NAMES.stream()
            .noneMatch(name.toLowerCase()::contains)));
  }

  private Double calculateTotalAverageDailyBalanceCents(List<VerifiedBankAccount> eligibleBankAccounts,
      CraCheckReportBaseReportGetResponse baseReport) {
    // Only look back at last 90 days
    LocalDate earliestDate = LocalDate.now(etClock).minusDays(91);

    // Only evaluate eligible accounts
    List<String> eligibleAccountIds = eligibleBankAccounts.stream().map(VerifiedBankAccount::getAccountId).toList();

    List<BaseReportItem> items = Optional.ofNullable(baseReport.getReport())
        .map(BaseReport::getItems).orElse(List.of());

    List<BaseReportAccount> accounts = items.stream().flatMap(item -> item.getAccounts().stream())
        .filter(account -> eligibleAccountIds.contains(account.getAccountId())).toList();

    List<Double> averageBalanceByAccount = new ArrayList<>();
    for (BaseReportAccount account : accounts) {
      Stream<BaseReportAverageMonthlyBalances> balances = Optional.ofNullable(
          account.getBalances().getAverageMonthlyBalances()).orElse(List.of()).stream()
          .filter(monthlyBalance -> LocalDate.parse(monthlyBalance.getEndDate()).isAfter(earliestDate));

      averageBalanceByAccount.add(balances.mapToDouble(balance -> Optional.ofNullable(balance.getAverageBalance()).map(
          CreditAmountWithCurrency::getAmount).orElse(ZERO_DEFAULT)).average().orElse(ZERO_DEFAULT) * 100);
    }

    return averageBalanceByAccount.stream().mapToDouble(Double::doubleValue).sum();
  }

  private Pair<CraCheckReportBaseReportGetResponse, CraCheckReportIncomeInsightsGetResponse> getBankIncomeMetadata(
      Verification verification) {

    String plaidReportS3Uri = Optional.ofNullable(verification.getContext())
        .map(VerificationContext::getPlaidReportS3Uri).orElseThrow(() ->
            new FeatureNotFoundException("Could not find S3 uri to fetch Plaid report from"));

    BankIncomeVerificationReports reports = downloadBankReports(plaidReportS3Uri);

    return Pair.of(reports.getBaseReportOutput(), reports.getIncomeInsightsOutput());
  }

  private BankIncomeVerificationReports downloadBankReports(String s3Uri) {
    try {
      return OBJECT_MAPPER.readValue(incomeUtils.downloadReports(s3Uri), BankIncomeVerificationReports.class);
    } catch (URISyntaxException | JsonProcessingException e) {
      throw new EventMetadataParsingException(e);
    }
  }

  private Double calculateVerifiedAverageMonthlyGrossIncomeCents(List<VerifiedBankAccount> eligibleBankAccounts,
      CraCheckReportIncomeInsightsGetResponse incomeInsightsGetResponse) {
    double averageMonthlyGrossIncomeCents = ZERO_DEFAULT;

    if (incomeInsightsGetResponse.getReport() != null) {

      // Only look back at last 90 days
      LocalDate earliestDate = LocalDate.now(etClock).minusDays(91);

      // Only evaluate eligible accounts
      List<String> eligibleAccountIds = eligibleBankAccounts.stream().map(VerifiedBankAccount::getAccountId).toList();

      List<CraBankIncomeSource> sources = incomeInsightsGetResponse.getReport().getItems() != null
          ? incomeInsightsGetResponse.getReport()
          .getItems().stream()
          .flatMap(item -> item.getBankIncomeSources().stream()
              .filter(source -> eligibleAccountIds.contains(source.getAccountId())))
          .filter(source -> Objects.requireNonNull(source.getEndDate()).isAfter(earliestDate))
          .toList() : List.of();

      averageMonthlyGrossIncomeCents = sources.stream()
          .map(CraBankIncomeSource::getHistoricalAverageMonthlyGrossIncome)
          .filter(Objects::nonNull)
          .reduce(Double::sum).orElse(ZERO_DEFAULT) * 100;
    }
    return incomeUtils.formatDouble(averageMonthlyGrossIncomeCents);
  }

  private boolean isAlreadyValidated(Verification verification) {
    return verification.getStatus() != null && List.of("MANUAL_REVIEW_SUBMITTED", "COMPLETED")
        .contains(verification.getStatus());
  }

  private boolean isCompleted(Verification verification) {
    return verification.getStatus() != null && "COMPLETED".equals(verification.getStatus());
  }
}
