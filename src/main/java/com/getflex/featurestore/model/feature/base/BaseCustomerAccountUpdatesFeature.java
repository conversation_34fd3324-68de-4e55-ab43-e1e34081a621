package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.CustomerAccountUpdateHistory;
import com.getflex.useraccount.model.UpdateHistoryRecord;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.function.Function;
import java.util.function.Predicate;

public abstract class BaseCustomerAccountUpdatesFeature extends BaseFeature {
  private final IdentityService identityService;
  private final UserAccountService userAccountService;
  private final LookbackDurationFeatureParams parameters;

  public BaseCustomerAccountUpdatesFeature(
      IdentityService identityService,
      UserAccountService userAccountService,
      LookbackDurationFeatureParams parameters) {
    super();
    this.identityService = identityService;
    this.userAccountService = userAccountService;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    String customerPublicId = customer.getCustomerPublicId();
    CustomerAccountUpdateHistory customerHistory =
        userAccountService.getUserAccountUpdateHistory(customerPublicId);

    if (customerHistory == null || customerHistory.getHistories() == null) {
      return new FeatureOutput(0);
    }

    String isoDurationTimeWindow = parameters.lookbackWindow();
    int numChanges =
        (int)
                customerHistory.getHistories().stream()
                    .filter(
                        record -> {
                          if (isoDurationTimeWindow != null && !isoDurationTimeWindow.isEmpty()) {
                            OffsetDateTime dtCreatedStartRange =
                                OffsetDateTime.now().minus(Duration.parse(isoDurationTimeWindow));
                            return record.getDateCreated() != null
                                && record.getDateCreated().isAfter(dtCreatedStartRange);
                          }
                          return true;
                        })
                    .sorted(
                        Comparator.comparing(
                                UpdateHistoryRecord::getDateCreated,
                                Comparator.nullsLast(Comparator.naturalOrder()))
                            .reversed())
                    .filter(getFilterPredicate())
                    .map(getMappingFunction())
                    .distinct()
                    .count() - 1; // Account for initial submission in number of unique entries.
    // For ex. if there's been only one distinct phone number throughout customer's history, number of
    // changes should be counted as 0.

    return new FeatureOutput(Math.max(0, numChanges));
  }

  protected abstract Predicate<UpdateHistoryRecord> getFilterPredicate();

  protected abstract Function<UpdateHistoryRecord, String> getMappingFunction();
}
