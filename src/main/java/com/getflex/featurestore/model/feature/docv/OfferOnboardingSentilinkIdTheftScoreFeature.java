package com.getflex.featurestore.model.feature.docv;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.OfferOnboardingFraudMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class OfferOnboardingSentilinkIdTheftScoreFeature extends BaseFeature {

  private final ObjectMapper objectMapper = new ObjectMapper();
  private final EventRepository eventRepository;

  public OfferOnboardingSentilinkIdTheftScoreFeature(EventRepository eventRepository) {
    this.eventRepository = eventRepository;
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(0, null);
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String entityId = evalParams.getOfferId() + "-" + evalParams.getOfferVersion().toString();
    Optional<Event> optionalEvent = eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
        entityId, EventName.ONBOARDING_FRAUD_SENTILINK_SCORES);

    if (optionalEvent.isEmpty()) {
      log.warn("No sentilink event for entity %s", entityId);
      return getDefaultValue();
    } else {
      try {
        Event event = optionalEvent.get();
        OfferOnboardingFraudMetadata meta = objectMapper.readValue(
            event.getMetadata(), OfferOnboardingFraudMetadata.class);
        return new FeatureOutput(meta.getSentilinkIdTheftScore());
      } catch (JsonProcessingException e) {
        log.warn("Missing required event metadata for entity %s", entityId);
        return getDefaultValue();
      }
    }
  }

  @Override
  public String getDescription() {
    return "Offer's latest Sentilink ID Theft Score";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.OFFER_VERSION, EvalParamKey.OFFER_ID);
  }
}
