package com.getflex.featurestore.model.feature.rentsplit;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Represents rent split customer information, including corresponding offer information. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RentSplitCustomerInfo {

  /** Whether the customer has any rent split offers. */
  @JsonProperty("is_rent_split")
  private boolean isRentSplit;

  /**
   * List of all rent split offers for the customer. Empty list if customer has no rent split
   * offers.
   */
  @JsonProperty("rent_split_offers")
  private List<RentSplitOfferInfo> rentSplitOffers;

  /** Whether the customer has any active or suspended rent split offers. */
  @JsonProperty("has_active_or_suspended_offer")
  private boolean hasActiveOrSuspendedOffer;
}
