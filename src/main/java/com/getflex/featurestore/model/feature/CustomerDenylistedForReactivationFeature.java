package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;

@RegisterFeature
public class CustomerDenylistedForReactivationFeature extends BaseFeature {

  private final IdentityService identityService;
  private final AllowDenyListRepo allowDenyListRepo;
  private static final String REACTIVATION_DENYLIST_USECASE = "deny_listed_reactivation_30_days";

  public CustomerDenylistedForReactivationFeature(IdentityService identityService,
      AllowDenyListRepo allowDenyListRepo) {
    this.identityService = identityService;
    this.allowDenyListRepo = allowDenyListRepo;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    GetCustomerResponse customer = identityService.getCustomer(customerId);
    Boolean isDenylisted = isCustomerDenylisted(customer.getCustomerPublicId());
    return new FeatureOutput(isDenylisted);
  }

  @Override
  public String getDescription() {
    return "If the customer is denylisted for reactivation";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private boolean isCustomerDenylisted(String customerPublicId) {
    return
        allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(REACTIVATION_DENYLIST_USECASE, customerPublicId, false)
            != null;
  }
}
