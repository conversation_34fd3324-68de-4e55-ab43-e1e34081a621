package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.offerv2.model.InternalOffer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class CustomerIsReappyIneligibleBaseFeature extends BaseFeature {
  protected final OfferService offerService;

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  public CustomerIsReappyIneligibleBaseFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long billerAccountId = evalParams.getBillerAccountId();
    InternalOffer offer = offerService.getOfferByBillerAccountId(billerAccountId);
    if (offer == null) {
      return new FeatureOutput(false);
    }

    if (offer.getOfferState() == InternalOffer.OfferStateEnum.CLOSED
        && offer.getDeactivationReasonDetail() != null
        && offer.getDeactivationReasonDetail().equals(this.getDeactivationReasonDetail())) {
      return new FeatureOutput(true);
    }
    return new FeatureOutput(false);
  }

  public abstract String getDeactivationReasonDetail();

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }
}
