package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.RecordLedger;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

public abstract class BaseWalletFundsCountFeature extends BaseFeature {

  protected final LedgerService ledgerService;
  protected final LookbackDurationFeatureParams params;

  public BaseWalletFundsCountFeature(LedgerService ledgerService, LookbackDurationFeatureParams params) {
    super();
    this.ledgerService = ledgerService;
    this.params = params;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    OffsetDateTime dtCreatedStartRange = OffsetDateTime.now().minus(Duration.parse(params.lookbackWindow()));
    var request = getRetrieveLedgerRequest(
        customerId,
        dtCreatedStartRange
    );
    List<RecordLedger> recordLedgers = ledgerService.retrieveLedgerByCustomerId(request);
    if (recordLedgers == null || recordLedgers.isEmpty()) {
      return new FeatureOutput(0);
    }
    var recordLedgersCount = (int) recordLedgers.stream()
        .filter(getFilterPredicate())
        .count();
    return new FeatureOutput(recordLedgersCount);
  }

  protected abstract APIretrieveLedgerByCustomerIdRequest getRetrieveLedgerRequest(
      Long customerId, OffsetDateTime dtCreatedStartRange
  );

  protected abstract Predicate<RecordLedger> getFilterPredicate();

  protected Boolean validChargePaymentCategory(RecordLedger recordLedger) {
    return recordLedger.getPaymentCategoryId() != null
        && recordLedger.getPaymentCategoryId() == MovementCategory.CHARGE.getValue();
  }

  protected Boolean validPaymentStatuses(
      RecordLedger recordLedger,
      PaymentState... validPaymentStatuses
  ) {
    return recordLedger.getPaymentStatusId() != null
        && Arrays.stream(validPaymentStatuses).anyMatch(
            status -> status.getValue() == recordLedger.getPaymentStatusId()
    );
  }

}
