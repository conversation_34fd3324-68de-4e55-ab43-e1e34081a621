package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.feature.base.BaseWalletFundsCountFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.IncludeRefundEnum;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.function.Predicate;

@RegisterFeature(
    value = "NumberOfSuccessfulFundsOutTimeRangeFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class)
public class NumberOfSuccessfulFundsOutTimeRangeFeature extends BaseWalletFundsCountFeature {


  public NumberOfSuccessfulFundsOutTimeRangeFeature(LedgerService ledgerService,
      LookbackDurationFeatureParams lookbackWindow) {
    super(ledgerService, lookbackWindow);
  }

  @Override
  protected APIretrieveLedgerByCustomerIdRequest getRetrieveLedgerRequest(Long customerId,
      OffsetDateTime dtCreatedStartRange) {
    return APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(customerId)
        .dtCreatedStartRange(dtCreatedStartRange)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .includeRefund(IncludeRefundEnum.EXCLUDE)
        .build();
  }

  @Override
  protected Predicate<RecordLedger> getFilterPredicate() {
    return this::validChargePaymentCategory;
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the number of funds-out transaction the user has had within "
        + "a specified time period. Feature should follow the convention as "
        + "NumberOfSuccessfulFundsOutTimeRangeFeature_TimeWindowIsoDurationFormat";
  }
}
