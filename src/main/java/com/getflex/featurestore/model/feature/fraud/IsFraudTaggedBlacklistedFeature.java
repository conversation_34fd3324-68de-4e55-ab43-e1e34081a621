package com.getflex.featurestore.model.feature.fraud;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;

@RegisterFeature
public class IsFraudTaggedBlacklistedFeature extends BaseFraudTaggedFeature {

  public IsFraudTaggedBlacklistedFeature(TaggingService taggingService) {
    super(taggingService);
  }

  @Override
  public FraudTagName getFraudTagName() {
    return FraudTagName.BLACKLISTED;
  }

  @Override
  public String getDescription() {
    return "Account tagged as fraud blacklisted";
  }
}
