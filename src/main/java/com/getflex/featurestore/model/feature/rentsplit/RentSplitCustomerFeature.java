package com.getflex.featurestore.model.feature.rentsplit;

import static com.getflex.featurestore.constant.ProductConstants.RENTAL_PRODUCT_ID;
import static com.getflex.featurestore.constant.ProductConstants.RENT_SPLIT_CATEGORY_ID;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class RentSplitCustomerFeature extends BaseFeature {

  private final OfferService offerService;

  public RentSplitCustomerFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();

    List<InternalOffer> offers = offerService.searchOfferByProduct(
        customerId,
        Set.of(RENTAL_PRODUCT_ID),
        Set.of(RENT_SPLIT_CATEGORY_ID),
        false);

    List<InternalOffer> rentSplitOffers =
        offers.stream().filter(this::overallStateFilter).toList();

    if (rentSplitOffers.isEmpty()) {
      return new FeatureOutput(
          RentSplitCustomerInfo.builder()
              .isRentSplit(false)
              .rentSplitOffers(Collections.emptyList())
              .hasActiveOrSuspendedOffer(false)
              .build());
    }

    List<RentSplitOfferInfo> offerInfos =
        rentSplitOffers.stream().map(this::buildRentSplitOfferInfo).toList();

    boolean hasActiveOrSuspendedOffer =
        rentSplitOffers.stream().anyMatch(this::isActiveOrSuspendedOffer);

    RentSplitCustomerInfo result =
        RentSplitCustomerInfo.builder()
            .isRentSplit(true)
            .rentSplitOffers(offerInfos)
            .hasActiveOrSuspendedOffer(hasActiveOrSuspendedOffer)
            .build();

    return new FeatureOutput(result);
  }

  private boolean overallStateFilter(InternalOffer offer) {
    if (offer == null) {
      return false;
    }

    return List.of(OverallState.ACTIVE, OverallState.SUSPENDED, OverallState.CANCELED)
        .contains(offer.getOverallState());
  }

  private boolean isActiveOrSuspendedOffer(InternalOffer offer) {
    return offer != null && (OverallState.ACTIVE.equals(offer.getOverallState())
        || OverallState.SUSPENDED.equals(offer.getOverallState()));
  }

  private RentSplitOfferInfo buildRentSplitOfferInfo(InternalOffer offer) {
    return RentSplitOfferInfo.builder()
        .offerId(offer.getOfferId())
        .offerVersion(offer.getOfferVersion())
        .offerState(offer.getOverallState())
        .build();
  }

  @Override
  public String getDescription() {
    return "Returns rent split customer information including all active/suspended/canceled rent"
        + " split offers (rental product ID = 1, rent split category ID = 1)";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
