package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.zendesk.ZendeskService;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUser;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUserTicketsResponse;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RegisterFeature
public class CustomerZendeskTicketsCountFeature extends BaseFeature {

  private final ZendeskService zendeskService;
  private final IdentityService identityService;

  public CustomerZendeskTicketsCountFeature(ZendeskService zendeskService, IdentityService identityService) {
    this.zendeskService = zendeskService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customerResponse = identityService.getCustomer(evalParams.getCustomerId());
    if (StringUtils.isEmpty(customerResponse.getEmail())) {
      log.warn("Customer email is empty for customerId={}", evalParams.getCustomerId());
      return new FeatureOutput(0);
    }

    ZendeskUser zendeskUser = zendeskService.getUserByEmail(customerResponse.getEmail());
    if (zendeskUser == null) {
      log.warn("Unable to find zendesk user for customerId={}", evalParams.getCustomerId());
      return new FeatureOutput(0);
    }
    ZendeskUserTicketsResponse userTicketsResponse = zendeskService.getTicketsByUserId(zendeskUser.getId());
    if (userTicketsResponse == null) {
      log.warn("Unable to find zendesk tickets for customerId={}, zendeskUserId={}", evalParams.getCustomerId(),
          zendeskUser.getId());
      return new FeatureOutput(0);
    }
    return new FeatureOutput(userTicketsResponse.getCount());
  }

  @Override
  public String getDescription() {
    return "The number of tickets the customer has submitted.";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
