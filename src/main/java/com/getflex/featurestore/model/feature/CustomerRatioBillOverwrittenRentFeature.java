package com.getflex.featurestore.model.feature;

import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.GetCustomerBillResponse;
import java.util.Comparator;
import java.util.Optional;

@RegisterFeature
public class CustomerRatioBillOverwrittenRentFeature extends BaseFeature {

  private final IdentityService identityService;
  private final PaymentService paymentService;
  private final AutopayService autopayService;

  public CustomerRatioBillOverwrittenRentFeature(
      IdentityService identityService,
      PaymentService paymentService,
      AutopayService autopayService
  ) {
    this.identityService = identityService;
    this.paymentService = paymentService;
    this.autopayService = autopayService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.DOUBLE;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    SearchAutopayTasksResponse autopayTasks;
    try {
      autopayTasks = autopayService.getCurrentAutoPay(
          customer.getCustomerPublicId(), FeatureUtils.getCurrentBpDateTime().toLocalDate()
      );
    } catch (InternalDependencyFailureException e) {
      return new FeatureOutput(0D);
    }
    if (autopayTasks == null || autopayTasks.getAutopayTasks() == null || autopayTasks.getAutopayTasks().isEmpty()) {
      return new FeatureOutput(0D);
    }
    Optional<GetCustomerBillResponse> maybeCustomerBill = autopayTasks.getAutopayTasks().stream()
        .filter(autopayTask -> autopayTask.getBillTransactionId() != null)
        .filter(autopayTask -> autopayTask.getPayDate() != null)
        .max(Comparator.comparing(GetAutopayTaskResponse::getPayDate))
        .map(autopayTask ->
            paymentService.getCustomerBill(
                customer.getCustomerPublicId(),
                autopayTask.getBillTransactionId()
            )
        ).filter(this::validBill);
    if (maybeCustomerBill.isPresent()) {
      GetCustomerBillResponse customerBill = maybeCustomerBill.get();
      Double result = customerBill.getAmountOverwrite().doubleValue() / customerBill.getAmount().doubleValue();
      return new FeatureOutput(result);
    }
    return new FeatureOutput(0D);
  }

  @Override
  public String getDescription() {
    return "Customer's ratio of original bill vs bill overwritten";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  private Boolean validBill(GetCustomerBillResponse bill) {
    return bill != null && bill.getAmount() != null && bill.getAmountOverwrite() != null
        && bill.getAmount() > 0 && bill.getAmountOverwrite() > 0;
  }
}
