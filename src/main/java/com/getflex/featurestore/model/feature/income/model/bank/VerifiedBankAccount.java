package com.getflex.featurestore.model.feature.income.model.bank;

import com.plaid.client.model.AccountType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VerifiedBankAccount {
  String accountId;
  String name;
  String officialName;
  AccountType type;
  List<VerifiedBankAccountOwner> owners;
  String subtype;
}
