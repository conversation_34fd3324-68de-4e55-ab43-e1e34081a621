package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.similarity.LevenshteinDistance;

@Slf4j
@RegisterFeature
public class EditDistanceToStripePaymentLastNameFeature extends BaseFeature {

  private final IdentityService identityService;

  public EditDistanceToStripePaymentLastNameFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    if (StringUtils.isEmpty(evalParams.getStripePaymentFullName())) {
      log.warn("stripePaymentFullName is empty, returning defaultValue={}", getDefaultValue().value());
      return getDefaultValue();
    }

    GetCustomerResponse customer = this.identityService.getCustomer(evalParams.getCustomerId());
    String lastNameIdentity = customer.getLastName();
    if (StringUtils.isEmpty(lastNameIdentity)) {
      log.warn("lastName is null in identity service for customerId={}", evalParams.getCustomerId());
      return getDefaultValue();
    }

    lastNameIdentity = lastNameIdentity.replaceAll("\\s", "").toLowerCase();
    String[] namePartition = evalParams.getStripePaymentFullName().split(" ");
    if (namePartition.length < 2) {
      log.warn("Stripe payment full name does not contain last name, stripePaymentFullName={}",
          evalParams.getStripePaymentFullName());
      return getDefaultValue();
    }
    String lastNameStripe = namePartition[namePartition.length - 1].replaceAll("\\s", "")
        .toLowerCase();

    Integer distance = LevenshteinDistance.getDefaultInstance().apply(lastNameStripe, lastNameIdentity);
    return new FeatureOutput(distance, null);
  }

  @Override
  public String getDescription() {
    return "Levenshtein edit distance between the last name of the Flex customer account and the last name on the "
        + "payment method";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_PAYMENT_FULL_NAME);
  }

  @Override
  protected FeatureOutput getDefaultValue() {
    return new FeatureOutput(-1, null);
  }
}
