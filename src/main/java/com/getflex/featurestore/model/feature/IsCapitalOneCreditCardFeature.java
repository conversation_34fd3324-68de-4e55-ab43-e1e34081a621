package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.TestSupport;
import java.util.List;

@RegisterFeature
public class IsCapitalOneCreditCardFeature extends BaseFeature {

  private static final String CREDIT_CARD_TYPE = "credit";
  private static final String CAPITAL_ONE_PARTIAL_ISSUER = "capitalone";

  // https://stripe.com/docs/testing?testing-method=card-numbers#cards
  // American Express test card ***************
  public static final List<String> CARD_FINGERPRINT_DENYLIST = List.of("8yXB8jhnS2KW92Va");

  private final TestSupport testSupport;

  public IsCapitalOneCreditCardFeature(TestSupport testSupport) {
    this.testSupport = testSupport;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    String cardFingerprint = evalParams.getCardFingerprint();
    String cardType = evalParams.getCardType();
    String cardIssuer = evalParams.getCardIssuer();

    if (cardIssuer != null) {
      cardIssuer = cardIssuer.replaceAll("\\s", "").toLowerCase();
    } else {
      cardIssuer = "";
    }

    if ((cardType.equals(CREDIT_CARD_TYPE) && cardIssuer.contains(CAPITAL_ONE_PARTIAL_ISSUER))
        || (!testSupport.isProdEnvironment() && CARD_FINGERPRINT_DENYLIST.contains(cardFingerprint))) {
      return new FeatureOutput(true, "{\"reason_detail\": \"CapitalOneCreditCardNotSupported\"}");
    }

    return new FeatureOutput(false, null);
  }

  @Override
  public String getDescription() {
    return "Check if the card is a Capital One credit card";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CARD_FINGERPRINT, EvalParamKey.CARD_TYPE, EvalParamKey.CARD_ISSUER);
  }
}
