package com.getflex.featurestore.model.feature;

import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.payment.model.GetCustomerBillResponse;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class CustomerCurrentBillAmountFeature extends BaseFeature {

  private final OfferService offerService;
  private final AutopayService autopayService;
  private final PaymentService paymentService;
  private final IdentityService identityService;

  public CustomerCurrentBillAmountFeature(OfferService offerService, AutopayService autopayService,
      PaymentService paymentService, IdentityService identityService) {
    this.offerService = offerService;
    this.autopayService = autopayService;
    this.paymentService = paymentService;
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.LONG;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    if (customer == null) {
      throw new IllegalArgumentException("Customer not found");
    }
    InternalOffer currentOffer = offerService.getOfferByCustomerId(customer.getCustomerId());
    if (currentOffer == null || currentOffer.getBillerAccountId() == null) {
      throw new IllegalArgumentException("Offer not found");
    }
    SearchAutopayTasksResponse tasks;
    try {
      tasks = autopayService.getCurrentAutoPay(
          customer.getCustomerPublicId(), FeatureUtils.getCurrentBpDateTime().toLocalDate());
    } catch (InternalDependencyFailureException e) {
      log.warn("no autopay task found for customer {}, default to 0", customer.getCustomerId());
      return new FeatureOutput(0L);
    }

    if (tasks == null || tasks.getAutopayTasks() == null || tasks.getAutopayTasks().isEmpty()) {
      log.warn("no autopay task found for customer {}, default to 0", customer.getCustomerId());
      return new FeatureOutput(0L);
    }
    Optional<Long> maxBillAmount = tasks.getAutopayTasks().stream().map(
        task -> {
          GetCustomerBillResponse bill = paymentService.getCustomerBill(
              customer.getCustomerPublicId(), task.getBillTransactionId()
          );
          if (bill != null && bill.getAmount() != null) {
            return bill.getAmount();
          }
          return 0L;
        }
    ).filter(Objects::nonNull).max(Long::compareTo);
    if (maxBillAmount.isEmpty()) {
      log.warn("no bill amount found for customer {}", customer.getCustomerId());
      return new FeatureOutput(0L);
    }
    return new FeatureOutput(maxBillAmount.get());
  }

  @Override
  public String getDescription() {
    return "customer's current bill amount (including overwrite)";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }
}
