package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;

@RegisterFeature(
    value = "TotalFundsInAmountFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class TotalFundsInAmountFeature extends BaseFeature {

  private final LedgerUtils ledgerUtils;
  private final LookbackDurationFeatureParams parameters;

  public TotalFundsInAmountFeature(LedgerUtils ledgerUtils, LookbackDurationFeatureParams parameters) {
    this.ledgerUtils = ledgerUtils;
    this.parameters = parameters;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    OffsetDateTime dtCreatedStartRange = OffsetDateTime.now().minus(Duration.parse(parameters.lookbackWindow()));
    List<RecordLedger> settledFundsIn = ledgerUtils.getFundsInRecords(
        evalParams.getCustomerId(),
        dtCreatedStartRange
    );
    if (settledFundsIn == null || settledFundsIn.isEmpty()) {
      return new FeatureOutput(0);
    }
    var totalSettledFundsInAmount = (int) settledFundsIn.stream()
        .mapToLong(RecordLedger::getAmount)
        .sum();
    return new FeatureOutput(totalSettledFundsInAmount);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public String getDescription() {
    return "Total funds in amount in past specified time window";
  }
}
