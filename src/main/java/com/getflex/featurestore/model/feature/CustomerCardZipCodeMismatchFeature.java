package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.feature.base.CustomerCardMetricsBaseFeature;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.util.List;
import java.util.Objects;

@RegisterFeature
public class CustomerCardZipCodeMismatchFeature extends CustomerCardMetricsBaseFeature {

  public CustomerCardZipCodeMismatchFeature(IdentityService identityService, WalletService walletService) {
    super(identityService, walletService);
  }

  @Override
  protected Integer calculateMetric(GetCustomerResponse customer, List<Card> cards) {
    return (int) cards
        .stream()
        .map(Card::getAddressZipcode)
        .filter(Objects::nonNull)
        .filter(zipcode -> !zipcode.equals(customer.getZip()))
        .count();
  }

  @Override
  public String getDescription() {
    return "Number of customer's cards added with zip codes different from customer's billing zip code";
  }
}
