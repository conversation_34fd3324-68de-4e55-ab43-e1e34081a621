package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.identity.model.GetCustomerResponse;


@RegisterFeature
public class StripeNameMismatchFeature extends BaseFeature {

  private final IdentityService identityService;

  public StripeNameMismatchFeature(IdentityService identityService) {
    this.identityService = identityService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = this.identityService.getCustomer(evalParams.getCustomerId());
    String firstNameIdentity = customer.getFirstName().replaceAll("\\s", "").toLowerCase();
    String lastNameIdentity = customer.getLastName().replaceAll("\\s", "").toLowerCase();

    String[] namePartition = evalParams.getStripePaymentFullName().split(" ");

    if (namePartition.length < 2) {
      return new FeatureOutput(false, null);
    }

    String firstNameStripe = namePartition[0].replaceAll("\\s", "").toLowerCase();
    String lastNameStripe = namePartition[namePartition.length - 1].replaceAll("\\s", "").toLowerCase();

    if (!firstNameStripe.equals(firstNameIdentity) && !lastNameStripe.equals(lastNameIdentity)) {
      return new FeatureOutput(true, null);
    }

    return new FeatureOutput(false, null);
  }

  @Override
  public String getDescription() {
    return "Check if the firstName and lastName in the Stripe mismatch the firstName and lastName "
      + "in the identity service";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.STRIPE_PAYMENT_FULL_NAME);
  }

  @Override
  public FeatureOutput getDefaultValue() {
    return new FeatureOutput(false, null);
  }
}
