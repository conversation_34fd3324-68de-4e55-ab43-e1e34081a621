package com.getflex.featurestore.model.feature.parameterized;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.feature.base.BaseCustomerAccountUpdatesFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.useraccount.model.UpdateHistoryRecord;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;

@RegisterFeature
@RegisterFeature(
    value = "NumberOfEmailChangesFeature_{lookbackWindow}",
    parameterType = LookbackDurationFeatureParams.class
)
public class NumberOfEmailChangesFeature extends BaseCustomerAccountUpdatesFeature {

  public NumberOfEmailChangesFeature(IdentityService identityService,
      UserAccountService userAccountService) {
    super(identityService, userAccountService, new LookbackDurationFeatureParams(null));
  }

  public NumberOfEmailChangesFeature(IdentityService identityService,
      UserAccountService userAccountService, LookbackDurationFeatureParams parameters) {
    super(identityService, userAccountService, parameters);
  }

  @Override
  protected Predicate<UpdateHistoryRecord> getFilterPredicate() {
    return record -> record.getEmail() != null;
  }

  @Override
  protected Function<UpdateHistoryRecord, String> getMappingFunction() {
    return record -> Objects.requireNonNull(record.getEmail()).toLowerCase();
  }

  @Override
  public String getDescription() {
    return "Parameterized feature to check the number of times a customer has changed their email address."
        + " Unless specified, time window defaults to lifetime of customer's account."
        + " Time window should follow the convention as"
        + " NumberOfEmailChangesFeature_TimeWindowIsoDurationFormat";
  }
}
