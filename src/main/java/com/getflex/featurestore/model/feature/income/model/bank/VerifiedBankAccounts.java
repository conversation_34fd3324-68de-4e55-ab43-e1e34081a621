package com.getflex.featurestore.model.feature.income.model.bank;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VerifiedBankAccounts {
  List<VerifiedBankAccount> nonPersonalDepositoryBankAccounts;
  List<VerifiedBankAccount> eligibleBankAccounts;
  List<VerifiedBankAccount> needManualReviewBankAccounts;
}
