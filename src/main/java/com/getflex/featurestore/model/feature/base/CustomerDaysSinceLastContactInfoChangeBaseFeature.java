package com.getflex.featurestore.model.feature.base;

import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.UpdateHistoryRecord;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;

public abstract class CustomerDaysSinceLastContactInfoChangeBaseFeature extends BaseFeature {

  private final IdentityService identityService;
  private final UserAccountService userAccountService;
  private static final int DEFAULT_DAYS_SINCE_LAST_CHANGE = 999999;

  protected CustomerDaysSinceLastContactInfoChangeBaseFeature(IdentityService identityService,
      UserAccountService userAccountService) {
    this.identityService = identityService;
    this.userAccountService = userAccountService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    GetCustomerResponse customer = identityService.getCustomer(evalParams.getCustomerId());
    List<UpdateHistoryRecord> historyRecords = userAccountService.getUserAccountUpdateHistory(
        customer.getCustomerPublicId()).getHistories();
    // There are a few legitimate customers without history, set as default value
    int days = DEFAULT_DAYS_SINCE_LAST_CHANGE;
    if (historyRecords != null && !historyRecords.isEmpty()) {
      historyRecords.sort(Comparator.comparing(UpdateHistoryRecord::getDateCreated).reversed());
      // if contact info never updated, use oldest record time
      days = daysSinceUpdate(historyRecords.get(historyRecords.size() - 1));
      for (int i = 1; i < historyRecords.size(); i++) {
        // find the most recent change
        if (!getContactInfoFieldValue(historyRecords.get(i - 1)).equals(
            getContactInfoFieldValue(historyRecords.get(i)))) {
          days = daysSinceUpdate(historyRecords.get(i - 1));
          break;
        }
      }
    }
    return new FeatureOutput(days, null);
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  public int daysSinceUpdate(UpdateHistoryRecord updateRecord) {
    try {
      OffsetDateTime nowEasternOffset = OffsetDateTime.now(ZoneId.of("America/New_York"));
      OffsetDateTime createdDate = updateRecord.getDateCreated().withOffsetSameInstant(ZoneOffset.UTC);
      return Math.toIntExact(ChronoUnit.DAYS.between(createdDate, nowEasternOffset));
    } catch (Exception e) {
      throw new InternalServiceBadDataException(
          "Error in calculation of days since last contact info update. updateDate=%s"
              .formatted(updateRecord.getDateCreated()));
    }
  }

  public abstract String getContactInfoFieldValue(UpdateHistoryRecord updateHistoryRecord);
}
