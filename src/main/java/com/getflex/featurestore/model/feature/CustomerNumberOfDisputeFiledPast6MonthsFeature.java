package com.getflex.featurestore.model.feature;

import static java.time.OffsetDateTime.now;

import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class CustomerNumberOfDisputeFiledPast6MonthsFeature extends BaseFeature {

  private DisputeService disputeService;

  public CustomerNumberOfDisputeFiledPast6MonthsFeature(DisputeService disputeService) {
    super();
    this.disputeService = disputeService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CUSTOMER_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Long customerId = evalParams.getCustomerId();
    return new FeatureOutput(disputeService.getTotalNumberOfDispute(customerId, now().minusMonths(6)));
  }

  @Override
  public String getDescription() {
    return "This feature returns the number of disputes for a customer in the past 6 months.";
  }
}
