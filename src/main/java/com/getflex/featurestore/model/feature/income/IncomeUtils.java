package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.feature.income.IncomeConstants.DF;
import static com.getflex.featurestore.model.feature.income.IncomeConstants.LEVENSHTEIN_DISTANCE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.identity.model.GetCustomerResponse;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

@Slf4j
@Component
public class IncomeUtils {
  public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
      .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
      .enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL)
      .registerModule(new JavaTimeModule());

  private static final int SIMILARITY_SCORE_THRESHOLD = 3;

  private final IdentityService identityService;
  private final EventRepository eventRepository;
  private final S3Client s3Client;

  public IncomeUtils(IdentityService identityService, EventRepository eventRepository, S3Client s3Client) {
    this.identityService = identityService;
    this.eventRepository = eventRepository;
    this.s3Client = s3Client;
  }

  public Double getSelfReportedAnnualGrossIncomeCents(String verificationId) throws JsonProcessingException {
    Event selfReportedIncome = getEvent(verificationId, EventName.SELF_REPORTED_INCOME_VERIFICATION);
    IncomeMetadata incomeMetadata = deserializeMetadata(selfReportedIncome.getMetadata(), IncomeMetadata.class);

    return Double.parseDouble(incomeMetadata.getEstimatedGrossAnnualIncomeCents());
  }

  public String getCustomerFullName(Long customerId) {
    GetCustomerResponse customer = identityService.getCustomer(customerId);
    if (customer == null) {
      throw new InternalDependencyFailureException("Customer not found");
    }
    return String.format("%s %s", customer.getFirstName(), customer.getLastName());
  }

  public Event getEvent(String entityId, EventName name) {
    return eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(entityId, name)
        .orElseThrow(() -> {
          String errorMsg = String.format("No event found for entity id %s and name %s",
              entityId, name);
          log.error(errorMsg);
          return new FeatureNotFoundException(errorMsg);
        });
  }

  public String downloadReports(String s3Uri) throws URISyntaxException {
    URI uri = new URI(s3Uri);
    String host = uri.getHost();
    String bucket = host.split("\\.")[0]; // Extract bucket name
    String key = uri.getPath().substring(1); // Extract key (strip leading '/')

    return s3Client.getObjectAsBytes(GetObjectRequest.builder().bucket(bucket).key(key).build()).asUtf8String();
  }

  public <T> T deserializeMetadata(String metadata, Class<T> classType) throws JsonProcessingException {
    return OBJECT_MAPPER.readValue(metadata, classType);
  }

  public boolean namesFuzzyMatch(String exactName, String incomingName) {
    if (StringUtils.isEmpty(incomingName)) {
      return true;
    } else if (StringUtils.isEmpty(exactName)) {
      return false;
    }

    // Convert to lower case and remove all special characters
    String[] tokensInFile = exactName.toLowerCase().replaceAll("[^a-z\\s]", "").split("\\s+");
    String[] tokensToCompare = incomingName.toLowerCase().replaceAll("[^a-z\\s]", "").split("\\s+");

    // Use the shorter array as reference to iterate in order to avoid null pointers.
    String[] shorter = tokensInFile.length <= tokensToCompare.length ? tokensInFile : tokensToCompare;
    String[] larger = tokensInFile.length > tokensToCompare.length ? tokensInFile : tokensToCompare;

    for (String base : shorter) {
      List<Double> distances = new ArrayList<>();
      for (String compareTo : larger) {
        distances.add(Double.valueOf(LEVENSHTEIN_DISTANCE.apply(base, compareTo)));
      }

      if (distances.stream().min(Double::compare).orElse(Double.MAX_VALUE) < SIMILARITY_SCORE_THRESHOLD) {
        return true;
      }
    }

    return false;
  }

  public Double formatDouble(Double number) {
    return Double.valueOf(DF.format(number));
  }
}
