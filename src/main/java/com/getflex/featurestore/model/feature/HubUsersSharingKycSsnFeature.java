package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.partnerhub.model.User;
import com.getflex.verification.model.Kyc;
import java.util.List;

@RegisterFeature
public class HubUsersSharingKycSsnFeature extends BaseFeature {

  public static final String HUBUSER_PREFIX = "hubuser:";
  private final VerificationService verificationService;
  private final PartnerHubService partnerHubService;

  public HubUsersSharingKycSsnFeature(VerificationService verificationService, PartnerHubService partnerHubService) {
    this.verificationService = verificationService;
    this.partnerHubService = partnerHubService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.ARRAY;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.KYC_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    Kyc kyc = verificationService.getKyc(evalParams.getKycId());
    List<User> hubUsers = verificationService.findKycSharingSameSsn(kyc).stream()
        .map(Kyc::getEntityName)
        .filter(entityName -> entityName.startsWith(HUBUSER_PREFIX))
        .map(entityName -> Long.parseLong(entityName.substring(HUBUSER_PREFIX.length())))
        .distinct()
        .map(partnerHubService::getHubUser)
        .toList();
    return new FeatureOutput(hubUsers);
  }

  @Override
  public String getDescription() {
    return "Get hub users sharing SSN with specified KYC (via SSN HMAC)";
  }
}
