package com.getflex.featurestore.model.feature;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.model.OfflineFeatureIsCardIssuerBlockedValue;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.featurestore.utils.TestSupport;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RegisterFeature
public class IsCardIssuerBlockedFeature extends BaseFeature {

  // https://stripe.com/docs/testing?testing-method=card-numbers#cards
  // American Express test card ***************
  public static final List<String> CARD_FINGERPRINT_DENYLIST = List.of("8yXB8jhnS2KW92Va");
  public static final String OFFLINE_FEATURE_NAME = "card_issuer_blocked";
  public static final String ERROR_MESSAGE = "Unfortunately, we are unable to accept %s %s cards"
      + " at this time. Please use a different card.";

  private final ObjectMapper objectMapper = new ObjectMapper();
  private final OfflineFeatureRepo offlineFeatureRepo;
  private final TestSupport testSupport;

  public IsCardIssuerBlockedFeature(OfflineFeatureRepo offlineFeatureRepo, TestSupport testSupport) {
    super();
    this.offlineFeatureRepo = offlineFeatureRepo;
    this.testSupport = testSupport;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.BOOLEAN;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.CARD_FINGERPRINT, EvalParamKey.CARD_TYPE, EvalParamKey.CARD_ISSUER);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    String cardFingerprint = evalParams.getCardFingerprint();
    String cardType = evalParams.getCardType().toLowerCase();
    String cardIssuer = evalParams.getCardIssuer().replaceAll("\\s", "").toLowerCase();

    // block American Express test card in non-prod environment
    if ((!testSupport.isProdEnvironment() && CARD_FINGERPRINT_DENYLIST.contains(cardFingerprint))) {
      String errorMessage = String.format(ERROR_MESSAGE, "American Express", "credit");
      return new FeatureOutput(true, "{\"reason_detail\": \"" + errorMessage + "\"}");
    }

    OfflineFeatureIsCardIssuerBlockedValue feature = this.getValueFromOfflineFeature(cardIssuer);

    if (feature == null) {
      return new FeatureOutput(false, null);
    }

    if ((cardType.equals("credit") && feature.getIsCreditCardBlocked())
        || (cardType.equals("debit") && feature.getIsDebitCardBlocked())) {
      String errorMessage = String.format(ERROR_MESSAGE, feature.getCardIssuer(), cardType);
      return new FeatureOutput(true, "{\"reason_detail\": \"" + errorMessage + "\"}");
    }

    return new FeatureOutput(false, null);
  }

  public OfflineFeatureIsCardIssuerBlockedValue getValueFromOfflineFeature(String primaryKey) {
    Optional<OfflineFeature> feature = this.offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        IsCardIssuerBlockedFeature.OFFLINE_FEATURE_NAME, primaryKey);

    if (feature.isEmpty()) {
      return null;
    }

    try {
      return objectMapper.readValue(feature.get().getFeatureValue(), OfflineFeatureIsCardIssuerBlockedValue.class);
    } catch (JsonProcessingException e) {
      throw new InternalDependencyFailureException(
          String.format("Failed to parse IsCardBlocked's value for primaryKey %s", primaryKey));
    }
  }

  @Override
  public String getDescription() {
    return "This feature checks if the card issuer is blocked";
  }
}
