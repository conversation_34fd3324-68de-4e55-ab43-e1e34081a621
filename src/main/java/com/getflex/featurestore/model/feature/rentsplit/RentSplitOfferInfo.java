package com.getflex.featurestore.model.feature.rentsplit;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.getflex.offerv2.model.OverallState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Represents information about a single rent split offer. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RentSplitOfferInfo {

  /** The offer ID. */
  @JsonProperty("offer_id")
  private String offerId;

  /** The offer version. */
  @JsonProperty("offer_version")
  private Long offerVersion;

  /** The overall state of the offer (e.g., ACTIVE, SUSPENDED, CANCELED). */
  @JsonProperty("offer_state")
  private OverallState offerState;
}
