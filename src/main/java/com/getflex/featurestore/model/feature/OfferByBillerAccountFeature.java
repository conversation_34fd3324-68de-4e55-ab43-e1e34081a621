package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.offerv2.model.InternalOffer;
import lombok.extern.slf4j.Slf4j;

/**
 * Find Offer By Biller Account Id (and optionally offer version)
 */
@Slf4j
@RegisterFeature
public class OfferByBillerAccountFeature extends BaseFeature {

  private final OfferService offerService;

  public OfferByBillerAccountFeature(OfferService offerService) {
    this.offerService = offerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.OBJECT;
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return Any.of(EvalParamKey.BILLER_ACCOUNT_ID);
  }

  @Override
  public FeatureOutput getValue(EvalParams evalParams) {
    InternalOffer offer = offerService.getOfferByBillerAccountIdAndOfferVersion(
        evalParams.getBillerAccountId(),
        evalParams.getOfferVersion());

    if (offer == null) {
      return new FeatureOutput("Offer not found");
    }

    return new FeatureOutput(offer);
  }

  @Override
  public String getDescription() {
    return "This feature returns the offer from GetOfferByBillerAccount";
  }
}
