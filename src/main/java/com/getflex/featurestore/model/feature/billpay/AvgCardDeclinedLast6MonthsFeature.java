package com.getflex.featurestore.model.feature.billpay;

import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.register.RegisterFeature;

@RegisterFeature
public class AvgCardDeclinedLast6MonthsFeature extends BillPayOfflineFeature {

  public AvgCardDeclinedLast6MonthsFeature(IdentityService identityService, OfflineFeatureRepo offlineFeatureRepo) {
    super(identityService, offlineFeatureRepo);
  }

  @Override
  public String getDescription() {
    return "Average number of card declined for the last 6 months";
  }

  @Override
  protected String getOfflineFeatureName() {
    return "avg_card_declined_last_6_months";
  }
}
