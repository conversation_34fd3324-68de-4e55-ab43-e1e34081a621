package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import com.getflex.featurestore.register.RegisterFeature;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;

@RegisterFeature
public class BillTransactionDownpaymentChargedAmountCentsFeature extends BaseFeature {

  private final LedgerService ledgerService;

  public BillTransactionDownpaymentChargedAmountCentsFeature(LedgerService ledgerService) {
    super();
    this.ledgerService = ledgerService;
  }

  @Override
  public FeatureTypeEnum getType() {
    return FeatureTypeEnum.INT;
  }

  @Override
  protected FeatureOutput getValue(EvalParams evalParams) {
    List<RecordLedger> downpaymentRecords = ledgerService.getDownpaymentRecords(
        evalParams.getBillTransactionId(), LedgerService.MovementCategory.CHARGE);
    if (downpaymentRecords == null || downpaymentRecords.isEmpty()) {
      return new FeatureOutput(0);
    }
    long total = downpaymentRecords.stream()
        .filter(r -> r.getPaymentStatusId() == PaymentState.SETTLED.getValue())
        .mapToLong(RecordLedger::getAmount).sum();
    return new FeatureOutput((int) total);
  }

  @Override
  public String getDescription() {
    return "total number of downpayment charged amount in cents";
  }

  @Override
  public ParamRequired getRequiredEvalParamKeys() {
    return All.of(EvalParamKey.BILL_TRANSACTION_ID);
  }
}
