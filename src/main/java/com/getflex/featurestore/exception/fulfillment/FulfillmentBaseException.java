package com.getflex.featurestore.exception.fulfillment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.exception.metadata.FeatureExceptionMetadata;

public class FulfillmentBaseException extends RuntimeException {

  private static final ObjectMapper objectMapper = new ObjectMapper().disable(
      DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

  public FulfillmentBaseException(String exceptionType, String message) {
    super(addExceptionType(exceptionType, message));
  }

  public static String addExceptionType(String exceptionType, String errorMessage) {
    FeatureExceptionMetadata metadata = new FeatureExceptionMetadata(exceptionType, errorMessage);
    try {
      return objectMapper.writeValueAsString(metadata);
    } catch (JsonProcessingException e) {
      throw new InternalServiceBadDataException("Error while processing the exception metadata %s".formatted(e));
    }
  }
}
