package com.getflex.featurestore.exception;

/**
 * Exception thrown when a feature encounters an unexpected or invalid state.
 * This represents a 409 Conflict response.
 */
public class UnexpectedFeatureStateException extends RuntimeException {
  
  public UnexpectedFeatureStateException(String message) {
    super(message);
  }
  
  public UnexpectedFeatureStateException(String message, Throwable cause) {
    super(message, cause);
  }
}
