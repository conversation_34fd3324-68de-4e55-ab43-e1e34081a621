package com.getflex.featurestore.exception;

import lombok.Getter;

/**
 * Thrown when the metadata json value from an Event object cannot be correctly parsed
 */
@Getter
public class EventMetadataParsingException extends RuntimeException {

  private final int code = 400;

  public EventMetadataParsingException(String cause) {
    super(cause);
  }

  public EventMetadataParsingException(Throwable throwable) {
    super(throwable);
  }
}
