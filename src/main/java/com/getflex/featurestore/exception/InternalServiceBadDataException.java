package com.getflex.featurestore.exception;

import lombok.Getter;

/**
 * Thrown when we receive bad data from the database or a request.
 * This exception indicates that the data is invalid or malformed,
 * such as a string that cannot be converted to an integer, or data
 * that exceeds allowed limits.
 */
@Getter
public class InternalServiceBadDataException extends RuntimeException {

  private final int code = 400;

  public InternalServiceBadDataException(String cause) {
    super(cause);
  }
}
