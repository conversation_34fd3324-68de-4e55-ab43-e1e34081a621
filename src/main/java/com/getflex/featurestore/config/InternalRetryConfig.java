package com.getflex.featurestore.config;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.google.common.annotations.VisibleForTesting;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Objects;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class InternalRetryConfig {

  @Bean
  public RetryConfig retryConfig(ServiceConfig config) {
    return RetryConfig.custom()
        .maxAttempts(config.getInternalOpenApiMaxRetries())
        .waitDuration(config.getInternalOpenApiRetryDelay())
        .retryOnException(InternalRetryConfig::openApiRetryCondition)
        .build();
  }

  @Bean
  public RetryRegistry retryRegistry(ServiceConfig config) {
    return RetryRegistry.of(retryConfig(config));
  }

  /**
   * Retry OpenAPI APIException on these conditions:
   * <ul>
   *   <li>5xx error</li>
   *   <li>any IOException, including http connect timeout, http2 GOAWAY...</li>
   * </ul>
   *
   * @param throwable
   * @return TRUE - should retry
   */
  @VisibleForTesting
  public static boolean openApiRetryCondition(Throwable throwable) {
    if (throwable instanceof InternalDependencyFailureException) {
      throwable = throwable.getCause();
    }
    if (throwable == null) {
      // this is possible when returned response doesn't make sense, retry won't be helpful in such case
      return false;
    }
    Class<? extends Throwable> exceptionClass = throwable.getClass();
    if (!Objects.equals(exceptionClass.getSimpleName(), "ApiException")) {
      return false;
    }
    if (throwable.getCause() instanceof IOException) {
      return true;
    }
    try {
      Method getCodeMethod = exceptionClass.getMethod("getCode");
      Integer httpStatusCode = (Integer) getCodeMethod.invoke(throwable);
      return httpStatusCode >= 500;
    } catch (InvocationTargetException | NoSuchMethodException | IllegalAccessException ignored) {
      // shouldn't happen, but it's safe to skip retry here
    }
    return false;
  }
}
