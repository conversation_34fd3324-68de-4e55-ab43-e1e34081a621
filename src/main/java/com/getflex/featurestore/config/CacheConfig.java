package com.getflex.featurestore.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@EnableCaching
public class CacheConfig {

  public static final String FRAUD_TAG_CACHE = "fraudTagCache";
  public static final String AUTOPAY_CACHE = "autoPayCache";
  public static final String LEDGER_CACHE = "ledgerCache";
  public static final String DS_SCORE_CACHE = "dsScoreCache";
  public static final String PORTAL_CACHE = "portalCache";
  public static final String OFFER_CACHE = "offerCache";
  public static final String IDENTITY_CACHE = "identityCache";
  public static final String DISPUTE_CACHE = "disputeCache";
  public static final String ALLOY_REPORT_CACHE = "alloyReportCache";

  private static final int DEFAULT_INITIAL_CAPACITY = 100;
  private static final int DEFAULT_MAX_CAPACITY = 200;

  @Primary
  @Bean
  public CacheManager cacheManager(List<CaffeineCache> caches) {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();

    // we dont allow any generic cache instance to be registered, only the ones defined in this config
    cacheManager.setCacheNames(caches.stream().map(CaffeineCache::getName).collect(Collectors.toList()));
    for (CaffeineCache cache : caches) {
      cacheManager.registerCustomCache(cache.getName(), cache.getNativeCache());
    }

    return cacheManager;
  }

  @Bean
  CaffeineCache autoPayCache() {
    return new CaffeineCache(AUTOPAY_CACHE,
        Caffeine.newBuilder()
            // TODO: set appropriate cache expiry time based on the requirement
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache fraudTagCache() {
    return new CaffeineCache(FRAUD_TAG_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache dsScoreCache() {
    return new CaffeineCache(DS_SCORE_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache ledgerCache() {
    return new CaffeineCache(LEDGER_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache disputeCache() {
    return new CaffeineCache(DISPUTE_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache portalCache() {
    return new CaffeineCache(PORTAL_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache offerCache() {
    return new CaffeineCache(OFFER_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache identityCache() {
    return new CaffeineCache(IDENTITY_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }

  @Bean
  CaffeineCache alloyReportCache() {
    return new CaffeineCache(ALLOY_REPORT_CACHE,
        Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(DEFAULT_INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAX_CAPACITY)
            .build());
  }
}
