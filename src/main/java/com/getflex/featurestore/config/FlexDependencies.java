package com.getflex.featurestore.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.autopay.api.AutopayApi;
import com.getflex.billPaymentMethod.openapi.BillPaymentMethodApi;
import com.getflex.billing.api.v2.api.BillingServicePublicApi;
import com.getflex.credit.client.LoanManagementApi;
import com.getflex.decisionengine.async.api.GetCheckpointDecisionLogsApi;
import com.getflex.dispute.api.DisputeApi;
import com.getflex.featurestore.constant.FeatureStoreConstant;
import com.getflex.fulfillment.api.FulfillmentsApi;
import com.getflex.identity.api.IdentityApi;
import com.getflex.identity.api.v2.IdentityV2Api;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.offerv2.api.OfferApi;
import com.getflex.partnerhub.openapi.PrivateApi;
import com.getflex.payment.api.PaymentApi;
import com.getflex.rentcredibility.api.RentCredibilityApi;
import com.getflex.settlement.api.SettlementApi;
import com.getflex.tagging.api.TaggingApi;
import com.getflex.useraccount.api.UserAccountApi;
import com.getflex.verification.api.KycApi;
import com.getflex.verification.api.VerificationApi;
import com.getflex.wallet.api.WalletApi;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.net.http.HttpClient;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.NativeWebRequest;

@Configuration
public class FlexDependencies {

  @Bean
  public DisputeApi getDisputeApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new DisputeApi(initializeDisputeApiClient(config, nativeWebRequest));
  }

  private com.getflex.dispute.client.ApiClient initializeDisputeApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.dispute.client.ApiClient apiClient = new com.getflex.dispute.client.ApiClient();
    apiClient.updateBaseUri(config.getDisputeUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getDisputeTimeout());
    return apiClient;
  }

  @Bean
  public LedgerApi getLedgerApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new LedgerApi(initializeLedgerApiClient(config, nativeWebRequest));
  }

  private com.getflex.ledger.client.ApiClient initializeLedgerApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.ledger.client.ApiClient apiClient = new com.getflex.ledger.client.ApiClient();
    apiClient.updateBaseUri(config.getLedgerUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getLedgerTimeout());
    return apiClient;
  }

  @Bean
  public RentCredibilityApi getRentCredibilityApi(ServiceConfig config,
      @NotNull NativeWebRequest nativeWebRequest) {
    return new RentCredibilityApi(initializeRentCredibilityApiClient(config, nativeWebRequest));
  }

  private com.getflex.rentcredibility.client.ApiClient initializeRentCredibilityApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.rentcredibility.client.ApiClient apiClient
        = new com.getflex.rentcredibility.client.ApiClient();
    apiClient.updateBaseUri(config.getRentCredibilityUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getRentCredibilityTimeout());
    return apiClient;
  }

  @Bean
  public FulfillmentsApi getFulfillmentsApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new FulfillmentsApi(initializeFulfillmentsApiClient(config, nativeWebRequest));
  }

  private com.getflex.fulfillment.client.ApiClient initializeFulfillmentsApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.fulfillment.client.ApiClient apiClient = new com.getflex.fulfillment.client.ApiClient();
    apiClient.updateBaseUri(config.getFulfillmentUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getLedgerTimeout());
    return apiClient;
  }

  @Bean
  public SettlementApi getSettlementApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new SettlementApi(initializSettlementApiClient(config, nativeWebRequest));
  }

  private com.getflex.settlement.client.ApiClient initializSettlementApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.settlement.client.ApiClient apiClient = new com.getflex.settlement.client.ApiClient();
    apiClient.updateBaseUri(config.getSettlementUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getLedgerTimeout());
    return apiClient;
  }

  @Bean
  public IdentityApi getIdentityApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new IdentityApi(initializeIdentityApiClient(config, nativeWebRequest));
  }

  private com.getflex.identity.client.ApiClient initializeIdentityApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.identity.client.ApiClient apiClient = new com.getflex.identity.client.ApiClient();
    apiClient.updateBaseUri(config.getIdentityUrl() + "/v1");
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getIdentityTimeout());
    return apiClient;
  }

  @Bean
  public IdentityV2Api getIdentityV2Api(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new IdentityV2Api(initializeIdentityV2ApiClient(config, nativeWebRequest));
  }

  private com.getflex.identity.client.v2.ApiClient initializeIdentityV2ApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.identity.client.v2.ApiClient apiClient = new com.getflex.identity.client.v2.ApiClient();
    apiClient.updateBaseUri(config.getIdentityUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getIdentityTimeout());
    return apiClient;
  }

  @Bean
  public OfferApi getOfferApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new OfferApi(initializeOfferApiClient(config, nativeWebRequest));
  }

  private com.getflex.offerv2.client.ApiClient initializeOfferApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.offerv2.client.ApiClient apiClient = new com.getflex.offerv2.client.ApiClient();
    apiClient.updateBaseUri(config.getOfferUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getOfferTimeout());
    return apiClient;
  }

  @Bean
  public BillingServicePublicApi getBillingServicePublicApi(ServiceConfig config,
      @NotNull NativeWebRequest nativeWebRequest) {
    return new BillingServicePublicApi(initializeBillingApiClient(config, nativeWebRequest));
  }

  private com.getflex.billing.api.v2.client.ApiClient initializeBillingApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    final ObjectMapper objectMapper =
        new ObjectMapper()
            .registerModule(
                new JavaTimeModule()
                    .addDeserializer(OffsetDateTime.class, new BillingOffsetTimeDeserializer()));
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    final com.getflex.billing.api.v2.client.ApiClient apiClient =
        new com.getflex.billing.api.v2.client.ApiClient(
            HttpClient.newBuilder(), objectMapper, config.getBillingUrl());
    apiClient.updateBaseUri(config.getBillingUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getBillingTimeout());
    return apiClient;
  }

  static class BillingOffsetTimeDeserializer extends JsonDeserializer<OffsetDateTime> {
    @Override
    public OffsetDateTime deserialize(JsonParser p, DeserializationContext ctxt)
        throws IOException {
      try {
        return LocalDateTime.parse(
                p.getText(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"))
            .atOffset(ZoneOffset.UTC);
      } catch (DateTimeParseException e) {
        return LocalDateTime.parse(p.getText(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            .atOffset(ZoneOffset.UTC);
      }
    }
  }

  @Bean
  public WalletApi getWalletApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new WalletApi(initializeWalletApiClient(config, nativeWebRequest));
  }

  private com.getflex.wallet.client.ApiClient initializeWalletApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.wallet.client.ApiClient apiClient = new com.getflex.wallet.client.ApiClient();
    apiClient.updateBaseUri(config.getWalletUrl());
    apiClient.setRequestInterceptor(builder -> builder.header(FeatureStoreConstant.CORRELATION_ID,
        nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getWalletTimeout());
    return apiClient;
  }

  @Bean
  public TaggingApi getTaggingApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new TaggingApi(initializeTaggingApiClient(config, nativeWebRequest));
  }

  private com.getflex.tagging.client.ApiClient initializeTaggingApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.tagging.client.ApiClient apiClient = new com.getflex.tagging.client.ApiClient();
    apiClient.updateBaseUri(config.getTaggingUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getTaggingTimeout());
    return apiClient;
  }

  @Bean
  public UserAccountApi getUserAccountApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new UserAccountApi(initializeUserAccountApiClient(config, nativeWebRequest));
  }

  private com.getflex.useraccount.client.ApiClient initializeUserAccountApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.useraccount.client.ApiClient apiClient = new com.getflex.useraccount.client.ApiClient();
    apiClient.updateBaseUri(config.getUserAccountUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getUserAccountTimeout());
    return apiClient;
  }

  @Bean
  public PaymentApi getPaymentApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new PaymentApi(initializePaymentApiClient(config, nativeWebRequest));
  }

  private com.getflex.payment.client.ApiClient initializePaymentApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.payment.client.ApiClient apiClient = new com.getflex.payment.client.ApiClient();
    apiClient.updateBaseUri(config.getPaymentUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getPaymentTimeout());
    return apiClient;
  }

  @Bean
  public AutopayApi getAutopayApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new AutopayApi(initializeAutopayApiClient(config, nativeWebRequest));
  }

  private com.getflex.autopay.client.ApiClient initializeAutopayApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.autopay.client.ApiClient apiClient = new com.getflex.autopay.client.ApiClient();
    apiClient.updateBaseUri(config.getAutopayUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getAutopayTimeout());
    return apiClient;
  }

  @Bean
  public BillPaymentMethodApi getBillPaymentMethodApi(
      ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new BillPaymentMethodApi(initializeBillPaymentMethodApiClient(config, nativeWebRequest));
  }

  private com.getflex.billPaymentMethod.client.ApiClient initializeBillPaymentMethodApiClient(
      ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.billPaymentMethod.client.ApiClient apiClient = new com.getflex.billPaymentMethod.client.ApiClient();
    apiClient.updateBaseUri(config.getAutopayUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getAutopayTimeout());
    return apiClient;
  }

  @Bean
  public VerificationApi getVerificationApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new VerificationApi(initializeVerificationApi(config, nativeWebRequest));
  }

  @Bean
  public KycApi getKycApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new KycApi(initializeVerificationApi(config, nativeWebRequest));
  }

  private com.getflex.verification.client.ApiClient initializeVerificationApi(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.verification.client.ApiClient apiClient = new com.getflex.verification.client.ApiClient();
    apiClient.updateBaseUri(config.getVerificationUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getVerificationTimeout());
    return apiClient;
  }

  @Bean
  public GetCheckpointDecisionLogsApi getGetCheckpointDecisionLogsApi(
      ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new GetCheckpointDecisionLogsApi(initializeGetCheckpointDecisionLogsApi(config, nativeWebRequest));
  }

  private com.getflex.decisionengine.async.client.ApiClient initializeGetCheckpointDecisionLogsApi(
      ServiceConfig config, NativeWebRequest nativeWebRequest) {
    com.getflex.decisionengine.async.client.ApiClient apiClient =
        new com.getflex.decisionengine.async.client.ApiClient();
    apiClient.updateBaseUri(config.getDecisionEngineUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getDecisionEngineTimeout());
    return apiClient;
  }

  @Bean
  public PrivateApi getPartnerHubApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new PrivateApi(initializePartnerHubClient(config, nativeWebRequest));
  }

  private com.getflex.partnerhub.client.ApiClient initializePartnerHubClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.partnerhub.client.ApiClient apiClient = new com.getflex.partnerhub.client.ApiClient();
    apiClient.updateBaseUri(config.getPartnerHubUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getPartnerHubTimeout());
    return apiClient;
  }

  @Bean
  public LoanManagementApi getCreditManagementApi(ServiceConfig config, @NotNull NativeWebRequest nativeWebRequest) {
    return new LoanManagementApi(initializeCreditManagementApiClient(config, nativeWebRequest));
  }

  private com.getflex.credit.client.ApiClient initializeCreditManagementApiClient(ServiceConfig config,
      NativeWebRequest nativeWebRequest) {
    com.getflex.credit.client.ApiClient apiClient = new com.getflex.credit.client.ApiClient();
    apiClient.updateBaseUri(config.getCreditManagementUrl());
    apiClient.setRequestInterceptor(builder ->
        builder.header(FeatureStoreConstant.CORRELATION_ID,
            nativeWebRequest.getHeader(FeatureStoreConstant.CORRELATION_ID)));
    apiClient.setConnectTimeout(config.getInternalOpenApiConnectTimeout());
    apiClient.setReadTimeout(config.getCreditManagementTimeout());
    return apiClient;
  }
}
