package com.getflex.featurestore.config;

import com.getflex.featurestore.filter.CustomThreadPoolExecutor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.TimeUnit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ThreadPoolConfig {

  private static final int CORE_POOL_SIZE = 24;
  private static final int MAXIMUM_POOL_SIZE = 300;
  private static final long KEEP_ALIVE_TIME = 60L;

  @Bean
  public ExecutorService newCachedThreadPool() {
    return new CustomThreadPoolExecutor(CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, KEEP_ALIVE_TIME,
        TimeUnit.SECONDS, new SynchronousQueue<>());
  }
}
