package com.getflex.featurestore.config;

import static com.getflex.featurestore.utils.FlexConstant.FLEX_TIMEZONE;

import com.getflex.featurestore.integration.socure.cache.SocureCache;
import com.getflex.featurestore.integration.socure.s3.SocureS3Repo;
import com.google.common.io.Resources;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.util.List;
import java.util.function.Function;
import org.springframework.cache.CacheManager;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
public class AppConfig {

  @Bean(name = "transactionManager")
  public PlatformTransactionManager dbTransactionManager() {
    return new JpaTransactionManager();
  }

  @Bean
  Clock etClock() {
    return Clock.system(FLEX_TIMEZONE);
  }

  @Bean(name = "alloyReportDownloader")
  public Function<String, String> alloyReportDownloader() {
    return url -> {
      try {
        return Resources.toString(new URL(url), StandardCharsets.UTF_8);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    };
  }

  @Bean
  public CacheManager socureCacheManager(SocureS3Repo alloyS3Repo) {
    SimpleCacheManager cacheManager = new SimpleCacheManager();
    cacheManager.setCaches(List.of(new SocureCache("socureCache", alloyS3Repo)));
    return cacheManager;
  }
}
