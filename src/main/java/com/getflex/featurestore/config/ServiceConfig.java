package com.getflex.featurestore.config;

import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.integration.flex.tagging.TaggingService.TaggingNamespace;
import java.time.Duration;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.With;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties
@ConfigurationProperties("flex")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@With
public class ServiceConfig {

  private String billingUrl;
  private String disputeUrl;
  private String identityUrl;
  private String offerUrl;
  private String walletUrl;
  private String ledgerUrl;
  private String rentCredibilityUrl;
  private String taggingUrl;
  private String userAccountUrl;
  private String verificationUrl;
  private String autopayUrl;
  private String paymentUrl;
  private String fulfillmentUrl;
  private String settlementUrl;
  private String eventsQueueUrl;
  private String decisionEngineUrl;
  private String partnerHubUrl;
  private String creditManagementUrl;
  /**
   * Applicable to dependencies in the same datacenter. This should be pretty low (like 2 seconds).
   */
  private Duration internalOpenApiConnectTimeout;
  private Duration internalOpenApiRetryDelay;
  private Integer internalOpenApiMaxRetries;
  private Duration billingTimeout;
  private Duration disputeTimeout;
  private Duration identityTimeout;
  private Duration offerTimeout;
  private Duration walletTimeout;
  private Duration ledgerTimeout;
  private Duration rentCredibilityTimeout;
  private Duration taggingTimeout;
  private Duration userAccountTimeout;
  private Duration verificationTimeout;
  private Duration autopayTimeout;
  private Duration billPaymentMethodTimeout;
  private Duration paymentTimeout;
  private Duration decisionEngineTimeout;
  private Duration partnerHubTimeout;
  private Duration creditManagementTimeout;

  private Map<Version, String> flexScoreEndpoint;
  private String fraudTransModelEndpoint;
  private String ftmV2Endpoint;
  private String ftmV3Endpoint;
  private String proxyRentModelEndpoint;

  private Map<TaggingNamespace, Long> taggingNamespaceIds;

  private String socureUrl;
  private String socureApiKey;
  private String socureBucket;
  private Duration socureTimeoutSecond;

  private Integer s3SingleAttemptTimeoutSecond;
  private Integer s3OperationTimeoutSecond;

  private String zendeskUrl;
  private String zendeskApiKey;

  private Duration ddbSingleAttemptTimeout;
  private Duration ddbOperationTimeout;
}
