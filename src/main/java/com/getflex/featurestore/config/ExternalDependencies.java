package com.getflex.featurestore.config;

import java.net.http.HttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ExternalDependencies {

  @Bean(name = "socureHttpClient")
  public HttpClient socureHttpClient(ServiceConfig config) {
    return HttpClient.newBuilder()
        .connectTimeout(config.getSocureTimeoutSecond())
        .build();
  }

  @Bean(name = "zendeskHttpClient")
  public HttpClient zendeskHttpClient() {
    return HttpClient.newHttpClient();
  }
}
