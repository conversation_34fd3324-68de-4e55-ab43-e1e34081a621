package com.getflex.featurestore.config;

import com.getflex.featurestore.dao.model.DdbEvent;
import java.time.Duration;
import java.util.Arrays;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClientBuilder;
import software.amazon.awssdk.services.sqs.SqsClient;

@Configuration
public class AwsDependencies {

  public static final Region FLEX_REGION = Region.US_EAST_1;

  @Bean
  public SageMakerRuntimeClient getSageMakerRuntimeClient() {
    SageMakerRuntimeClientBuilder builder = SageMakerRuntimeClient
        .builder()
        .region(FLEX_REGION);
    return builder.build();
  }

  @Bean
  public SqsClient getSqsClient() {
    return SqsClient.builder()
        .region(FLEX_REGION)
        .build();
  }

  @Bean
  public DynamoDbClient dynamoDbClient(ServiceConfig serviceConfig, Environment environment) {
    DynamoDbClientBuilder builder = DynamoDbClient.builder()
        .region(FLEX_REGION)
        .overrideConfiguration(ClientOverrideConfiguration.builder()
            .apiCallAttemptTimeout(serviceConfig.getDdbSingleAttemptTimeout())
            .apiCallTimeout(serviceConfig.getDdbOperationTimeout())
            .build());
    if (Arrays.asList(environment.getActiveProfiles()).contains("local")) {
      String awsProfile = "DevPowerUser-986067545241"; // Change to your configured profile
      builder.credentialsProvider(
          ProfileCredentialsProvider.builder().profileName((awsProfile)).build()
      );
    }
    return builder.build();
  }

  @Bean
  public DynamoDbEnhancedClient dynamoDbEnhancedClient(DynamoDbClient dynamoDbClient) {
    return DynamoDbEnhancedClient.builder()
        .dynamoDbClient(dynamoDbClient)
        .build();
  }

  @Bean
  public DynamoDbTable<DdbEvent> getDdbEventTable(DynamoDbEnhancedClient dynamoDbEnhancedClient) {
    return dynamoDbEnhancedClient.table("events_store",
        TableSchema.fromImmutableClass(DdbEvent.class));
  }

  @Bean
  public S3Client s3Client(ServiceConfig serviceConfig) {
    return S3Client.builder()
        .region(FLEX_REGION)
        .overrideConfiguration(ClientOverrideConfiguration.builder()
            .apiCallAttemptTimeout(Duration.ofSeconds(serviceConfig.getS3SingleAttemptTimeoutSecond()))
            .apiCallTimeout(Duration.ofSeconds(serviceConfig.getS3OperationTimeoutSecond()))
            .build())
        .build();
  }
}
