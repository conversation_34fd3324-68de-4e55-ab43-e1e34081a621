package com.getflex.featurestore.dao.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EntityType;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.exception.EntityTypeNotFoundException;
import com.getflex.featurestore.exception.EventCategoryNotFoundException;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.EventNameNotFoundException;
import com.getflex.featurestore.model.PostEventRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class PostEventRequestConverter {

  public static Event convertToEvent(PostEventRequest postEventRequest) {
    String requestMetadata = postEventRequest.getMetadata();
    if (requestMetadata == null) {
      throw new IllegalArgumentException("EventMetadata cannot be null");
    }

    EventCategory eventCategory = convertToEventCategory(postEventRequest.getCategory());
    if (!isValidJson(requestMetadata, eventCategory)) {
      throw new EventMetadataParsingException(String.format("Could not parse EventMetadata=%s for category=%s",
          requestMetadata, eventCategory));
    }

    EventName eventName = convertToEventName(postEventRequest.getName());
    EntityType entityType = convertToEntityType(postEventRequest.getEntityType());
    String customerId = getCustomerId(postEventRequest.getCustomerId(), postEventRequest.getEntityId(), entityType);
    return Event.builder()
        .name(eventName)
        .category(eventCategory)
        .entityId(postEventRequest.getEntityId())
        .entityType(entityType)
        .customerId(customerId)
        .metadata(postEventRequest.getMetadata())
        .dtArrived(postEventRequest.getDtArrived())
        .build();
  }

  public static String getCustomerId(String customerId, String entityId, EntityType entityType) {
    if (StringUtils.isNotEmpty(customerId)) {
      return customerId;
    } else if (EntityType.CUSTOMER_ID.equals(entityType)) {
      return entityId;
    }
    return null;
  }

  private static EventCategory convertToEventCategory(String eventCategoryStr) {
    if (eventCategoryStr == null) {
      return null;
    }

    for (EventCategory eventCategory : EventCategory.values()) {
      if (eventCategory.toString().equals(eventCategoryStr)) {
        return eventCategory;
      }
    }

    throw new EventCategoryNotFoundException();
  }

  public static EventName convertToEventName(String requestEventName) {
    if (requestEventName == null) {
      return null;
    }

    for (EventName name : EventName.values()) {
      if (name.toString().equals(requestEventName)) {
        return name;
      }
    }

    throw new EventNameNotFoundException();
  }

  public static EntityType convertToEntityType(String entityTypeStr) {
    if (entityTypeStr == null) {
      return null;
    }

    for (EntityType entityType : EntityType.values()) {
      if (entityType.toString().equals(entityTypeStr)) {
        return entityType;
      }
    }

    throw new EntityTypeNotFoundException();
  }

  private static boolean isValidJson(String requestMetadata, EventCategory eventCategory) {
    Class<? extends EventMetadata> eventMetadataSubclass = EventMetadata.getEventMetadataSubclass(eventCategory);
    try {
      EventMetadata.OBJECT_MAPPER.readValue(requestMetadata, eventMetadataSubclass);
    } catch (JsonProcessingException e) {
      log.error("Error converting EventMetadata={} JSON to object using class={}", requestMetadata,
          eventMetadataSubclass.getName(), e);
      return false;
    }
    return true;
  }
}
