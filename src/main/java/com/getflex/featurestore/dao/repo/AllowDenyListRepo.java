package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.model.AllowDenyListPrimaryKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AllowDenyListRepo extends JpaRepository<AllowDenyList, AllowDenyListPrimaryKey> {

  AllowDenyList findFirstByUseCaseAndEntityAndAllow(String useCase, String entity, boolean allow);
}
