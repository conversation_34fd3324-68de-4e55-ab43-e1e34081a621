package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.GeoIpLocation;
import com.getflex.featurestore.dao.model.GeoLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GeoIpLocationRepo extends JpaRepository<GeoIpLocation, Integer> {

  @Query(
      value = "SELECT gcb.network as network, gcb.latitude as latitude, gcb.longitude as longitude, "
          + "gl.city_name as cityName, gl.country_name as countryName, "
          + "gl.country_iso_code as countryIsoCode, gl.continent_code as continentCode, "
          + "gl.subdivision_1_iso_code as subdivision1IsoCode, "
          + "gl.subdivision_1_name as subdivision1Name, "
          + "gl.subdivision_2_iso_code as subdivision2IsoCode, "
          + "gl.subdivision_2_name as subdivision2Name "
          + "FROM ("
          + "   SELECT * "
          + "   FROM geolite_city_blocks "
          + "   WHERE LENGTH(INET6_ATON(:ipAddress)) = LENGTH(network_start) "
          + "   AND INET6_ATON(:ipAddress) >= network_start "
          + "   ORDER BY network_start DESC "
          + "   LIMIT 1"
          + ") AS gcb "
          + "JOIN geolite_city_locations_en gl "
          + "ON gcb.geoname_id = gl.geoname_id "
          + "WHERE INET6_ATON(:ipAddress) <= gcb.network_last",
      nativeQuery = true
  )
  GeoLocation findGeoLocationDataByIpAddress(@Param("ipAddress") String ipAddress);
}