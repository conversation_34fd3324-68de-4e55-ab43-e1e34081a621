package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.Coordinates;
import com.getflex.featurestore.dao.model.GeoIpNetwork;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GeoIpNetworkRepo extends JpaRepository<GeoIpNetwork, Integer> {

  @Query(
      value = "SELECT latitude, longitude "
          + "FROM ("
          + "   SELECT * "
          + "   FROM geolite_city_blocks "
          + "   WHERE LENGTH(INET6_ATON(:ipAddress)) = LENGTH(network_start) "
          + "   AND INET6_ATON(:ipAddress) >= network_start "
          + "   ORDER BY network_start DESC "
          + "   LIMIT 1"
          + ") AS gcb "
          + "WHERE INET6_ATON(:ipAddress) <= gcb.network_last",
      nativeQuery = true
  )
  Coordinates findByIpAddressInRange(@Param("ipAddress") String ipAddress);

}