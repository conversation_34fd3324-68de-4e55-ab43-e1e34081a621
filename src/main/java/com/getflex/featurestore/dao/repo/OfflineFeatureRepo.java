package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.model.OfflineFeatureNamePrimaryKey;
import com.getflex.o11y.annotation.TimeMetric;
import java.util.List;
import java.util.Optional;
import org.springframework.data.repository.CrudRepository;

public interface OfflineFeatureRepo extends CrudRepository<OfflineFeature, OfflineFeatureNamePrimaryKey> {

  @TimeMetric
  Optional<OfflineFeature> findFirstByFeatureNameAndPrimaryKey(String featureName, String primaryKey);

  @TimeMetric
  List<OfflineFeature> findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(String featureName,
      String primaryKey);

  Optional<OfflineFeature> findFirstByPrimaryKeyStartingWithOrderByDtCreatedDesc(String primaryKey);
}
