package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.Coordinates;
import com.getflex.featurestore.dao.model.UsAddresses;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UsAddressesRepo extends JpaRepository<UsAddresses, Integer> {

  Coordinates findFirstByZipOrderByCityAscLongitudeDescLatitudeDesc(String zip);

}