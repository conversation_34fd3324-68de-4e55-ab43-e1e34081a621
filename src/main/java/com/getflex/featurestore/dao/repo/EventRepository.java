package com.getflex.featurestore.dao.repo;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EventRepository extends JpaRepository<Event, Integer> {
  Optional<Event> findFirstByNameAndCustomerIdOrderByDtArrivedDesc(EventName eventName, String customerId);

  List<Event> findAllByNameAndCustomerIdOrderByDtArrivedDesc(EventName eventName, String customerId);

  List<Event> findAllByNameAndEntityIdOrderByDtArrivedDesc(EventName eventName, String entityId);

  List<Event> findAllByNameAndCustomerIdAndEntityIdOrderByDtArrivedDesc(
      EventName eventName,
      String customerId,
      String entityId
  );

  Optional<Event> findFirstByEntityIdAndNameOrderByDtArrivedDesc(String entityId, EventName name);

  @Query(value = "SELECT * FROM events "
      + "WHERE name = :eventName "
      + "AND entity_id = :entityId "
      + "AND dt_arrived >= :cutoffDate "
      + "ORDER BY dt_arrived DESC", 
      nativeQuery = true)
  List<Event> findAllByNameAndEntityIdWithCutoffOrderByDtArrivedDesc(
      @Param("eventName") String eventName,
      @Param("entityId") String entityId,
      @Param("cutoffDate") String cutoffDate
  );
}
