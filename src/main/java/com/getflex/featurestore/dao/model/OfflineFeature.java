package com.getflex.featurestore.dao.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "offline_feature")
@Data
@IdClass(OfflineFeatureNamePrimaryKey.class)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class OfflineFeature implements Serializable {

  @Id
  @Column(name = "feature_name")
  private String featureName;

  @Id
  @Column(name = "primary_key")
  private String primaryKey;

  @Column(name = "feature_value")
  private String featureValue;

  @Column(name = "dt_created")
  private OffsetDateTime dtCreated;
}
