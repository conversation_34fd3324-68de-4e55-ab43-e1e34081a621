package com.getflex.featurestore.dao.model.event.eventmetadata;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.dao.model.event.EventCategory;

public abstract class EventMetadata {

  public static final ObjectMapper OBJECT_MAPPER =
      new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
          .registerModule(new JavaTimeModule());

  public static Class<? extends EventMetadata> getEventMetadataSubclass(EventCategory eventCategory) {
    return switch (eventCategory) {
      case DEVICE_DATA -> DeviceMetadata.class;
      case ESTIMATED_GROSS_ANNUAL_INCOME, SELF_REPORTED_INCOME -> IncomeMetadata.class;
      case INCOME_VERIFICATION_REQUESTED -> IncomeVerificationRequestedMetadata.class;
      case INCOME_VERIFICATION_RECEIVED -> IncomeVerificationReceivedMetadata.class;
      case VANTAGE_SCORE -> VantageScoreMetadata.class;
      case ELIGIBILITY_VERIFICATION -> EligibilityVerificationMetadata.class;
      case LEDGER_BALANCE_CENTS -> LedgerBalanceCentsMetadata.class;
      case BILL_TRANSACTION_DISPUTED_DAYS -> BillTransactionDisputedDaysMetadata.class;
      case BANK_INCOME -> BankIncomeVerificationMetadata.class;
      case PAY_STUB_INCOME -> PayStubIncomeVerificationMetadata.class;
      case ONBOARDING_KYC -> OfferKycMetadata.class;
      case ONBOARDING_FRAUD -> OfferOnboardingFraudMetadata.class;
      case ONBOARDING_CREDIT_REPORT_INCOMPLETE -> CreditReportIncompleteMetadata.class;
      case RESTING_PERIOD -> RestingPeriodMetadata.class;
      case STRIPE_SETUP_ATTEMPT -> StripeSetupAttemptMetadata.class;
    };
  }
}
