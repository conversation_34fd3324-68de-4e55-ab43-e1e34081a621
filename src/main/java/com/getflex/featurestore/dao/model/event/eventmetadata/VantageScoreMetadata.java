package com.getflex.featurestore.dao.model.event.eventmetadata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_EMPTY) // Include only non-null and non-empty values on json serialization
public class VantageScoreMetadata extends EventMetadata {

  Integer vantageScore; // Expected values include: score ranging 300-850, also special value 1, 4, and null
  String reasonCode1;
  String reasonCode2;
  String reasonCode3;
  String reasonCode4;
  String alloyToken;
  String s3Url;

  boolean deceasedFlag;
  FrozenCreditFlag frozenCredit;
  NoScoreReason noScoreReason;
  OffsetDateTime updatedAt;

  public enum FrozenCreditFlag {
    FROZEN,
    SUPPRESSED,
  }

  public enum NoScoreReason {
    NO_MATCH_FOUND,
    CREDIT_PROFILE_TOO_THIN,
  }
}
