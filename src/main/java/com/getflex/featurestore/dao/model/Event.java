package com.getflex.featurestore.dao.model;

import com.getflex.featurestore.dao.model.event.EntityType;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.EventName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "events")
public class Event {
  @Id
  private long id;

  @Column(name = "name")
  @Enumerated(EnumType.STRING)
  private EventName name;

  @Column(name = "category")
  @Enumerated(EnumType.STRING)
  private EventCategory category;

  @Column(name = "entity_id")
  private String entityId;

  @Column(name = "entity_type")
  @Enumerated(EnumType.STRING)
  private EntityType entityType;

  @Column(name = "customer_id")
  private String customerId;

  @Column(name = "metadata", columnDefinition = "json")
  private String metadata;

  @Column(name = "dt_arrived")
  @Temporal(TemporalType.TIMESTAMP)
  private OffsetDateTime dtArrived;

  @Column(name = "dt_created", insertable = false, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private OffsetDateTime dtCreated;
}
