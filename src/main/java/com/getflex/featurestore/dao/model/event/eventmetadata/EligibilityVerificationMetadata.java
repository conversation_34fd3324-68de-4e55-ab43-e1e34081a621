package com.getflex.featurestore.dao.model.event.eventmetadata;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EligibilityVerificationMetadata extends EventMetadata {

  private String offerId;
  private Long estimatedBillAmountCents;
  private Long offerVersion;
  private String exceptionType;
  private String exceptionMessage;
}
