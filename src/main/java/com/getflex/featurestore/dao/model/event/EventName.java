package com.getflex.featurestore.dao.model.event;

public enum EventName {
  EVALUATE_OFFER_DEVICE_DATA,
  EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME,
  PLAID_INCOME_VERIFICATION_REQUESTED,
  <PERSON><PERSON><PERSON>_INCOME_VERIFICATION_RECEIVED,
  EVALUATE_OFFER_VANTAGE_SCORE,
  BANK_INCOME_VERIFICATION,
  PAY_STUB_INCOME_VERIFICATION,
  SELF_REPORTED_INCOME_VERIFICATION,
  ONBOARDING_KYC_REFER,
  ONBOARDING_FRAUD_SENTILINK_SCORES,
  ONBOARDING_FRAUD_RESTING,
  ON<PERSON>ARDING_CREDIT_RESTING,
  MOVE_IN_ELIGIBILITY_RESTING,
  STRIPE_SETUP_ATTEMPT,
  WALLET_ADD_FUND_DEVICE_DATA,
  WALLET_WITHDRAW_FUND_DEVICE_DATA,
  DDA_TO_VC_SWITCH_DEVICE_DATA,
  CREDIT_REPORT_INCOMPLETE,
  ADD_CARD_DEVICE_DATA,
}
