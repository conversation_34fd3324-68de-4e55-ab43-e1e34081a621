package com.getflex.featurestore.dao.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.OffsetDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "geolite_city_blocks")
@Builder(toBuilder = true)
public class GeoIpNetwork {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "network_start")
  private byte[] networkStart;

  @Column(name = "network_last")
  private byte[] networkLast;

  @Column(name = "geoname_id")
  private Integer geonameId;

  @Column(name = "registered_country_geoname_id")
  private Integer registeredCountryGeonameId;

  @Column(name = "represented_country_geoname_id")
  private Integer representedCountryGeonameId;

  @Column(name = "is_anonymous_proxy")
  private Boolean isAnonymousProxy;

  @Column(name = "is_satellite_provider")
  private Boolean isSatelliteProvider;

  @Column(name = "postal_code")
  private String postalCode;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "accuracy_radius")
  private Integer accuracyRadius;

  @Column(name = "is_anycast")
  private Boolean isAnycast;

  @Column(name = "import_id")
  private Long importId;

  @Column(name = "created_at")
  private OffsetDateTime createdAt;

  @Column(name = "updated_at")
  private OffsetDateTime updatedAt;

}