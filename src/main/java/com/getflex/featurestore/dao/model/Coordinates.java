package com.getflex.featurestore.dao.model;

public interface Coordinates {

  Double getLatitude();

  Double getLongitude();

  /**
   * Calculates the distance in miles between this coordinate and another using the Haversine formula.
   *
   * @param other The other coordinates to calculate distance to
   * @return Distance in miles
   */
  default double haversine(Coordinates other) {
    // Input validation
    if (this.getLatitude() == null || this.getLongitude() == null
        || other.getLatitude() == null || other.getLongitude() == null) {
      throw new IllegalArgumentException("Coordinates cannot have null latitude or longitude.");
    }

    if (Double.isNaN(this.getLatitude()) || Double.isInfinite(this.getLatitude())
        || Double.isNaN(this.getLongitude()) || Double.isInfinite(this.getLongitude())) {
      throw new IllegalArgumentException("Current coordinate's latitude or longitude is NaN or Infinity.");
    }

    if (Double.isNaN(other.getLatitude()) || Double.isInfinite(other.getLatitude())
        || Double.isNaN(other.getLongitude()) || Double.isInfinite(other.getLongitude())) {
      throw new IllegalArgumentException("Other coordinate's latitude or longitude is NaN or Infinity.");
    }

    double radius = 3958.8;

    double latitude1Radians = Math.toRadians(this.getLatitude());
    double longitude1Radians = Math.toRadians(this.getLongitude());
    double latitude2Radians = Math.toRadians(other.getLatitude());
    double longitude2Radians = Math.toRadians(other.getLongitude());

    double latitudeDifference = latitude2Radians - latitude1Radians;
    double longitudeDifference = longitude2Radians - longitude1Radians;

    double a = Math.pow(Math.sin(latitudeDifference / 2.0), 2)
        + Math.pow(Math.sin(longitudeDifference / 2.0), 2)
        * Math.cos(latitude1Radians)
        * Math.cos(latitude2Radians);
    double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return radius * c;
  }

}