package com.getflex.featurestore.dao.model;

/**
 * Geolocation data. geolite_city_blocks include latitude and longitude. geolite_city_locations_en contains the rest of
 * the geolocation data.
 */
public interface GeoLocation {

  String getNetwork();

  Double getLatitude();

  Double getLongitude();

  String getCityName();

  String getCountryName();

  String getCountryIsoCode();

  String getContinentCode();

  String getSubdivision1IsoCode();

  String getSubdivision1Name();

  String getSubdivision2IsoCode();

  String getSubdivision2Name();
}