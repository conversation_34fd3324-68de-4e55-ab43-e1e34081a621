package com.getflex.featurestore.dao.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "geolite_city_locations_en")
@Builder(toBuilder = true)
public class GeoIpLocation {

  @Id
  @Column(name = "geoname_id")
  private Integer geonameId;

  @Column(name = "locale_code")
  private String localeCode;

  @Column(name = "continent_code")
  private String continentCode;

  @Column(name = "continent_name")
  private String continentName;

  @Column(name = "country_iso_code")
  private String countryIsoCode;

  @Column(name = "country_name")
  private String countryName;

  @Column(name = "subdivision_1_iso_code")
  private String subdivision1IsoCode;

  @Column(name = "subdivision_1_name")
  private String subdivision1Name;

  @Column(name = "subdivision_2_iso_code")
  private String subdivision2IsoCode;

  @Column(name = "subdivision_2_name")
  private String subdivision2Name;

  @Column(name = "city_name")
  private String cityName;

  @Column(name = "metro_code")
  private Integer metroCode;

  @Column(name = "time_zone")
  private String timeZone;

  @Column(name = "is_in_european_union")
  private Boolean isInEuropeanUnion;
}
