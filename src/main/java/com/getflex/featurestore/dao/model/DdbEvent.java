package com.getflex.featurestore.dao.model;

import java.time.Instant;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.Value;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbImmutable;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * Model for serializing/deserializing checkpoint logs to/from DynamoDB.
 * The index names referenced here must match those created in [terraform/modules/dynamodb/tables.tf]
 */
@Value
@Builder(builderClassName = "DdbEventBuilder")
@DynamoDbImmutable(builder = DdbEvent.DdbEventBuilder.class)
public class DdbEvent {

  String customerId;
  String name;
  @Builder.Default
  String dtCreated = Instant.now().toString();
  @Builder.Default
  String id = UUID.randomUUID().toString();
  String entityId;
  String entityType;
  String metadata;
  @Getter
  String category;
  @Getter
  String dtArrived;

  @NonNull
  @DynamoDbPartitionKey
  @DynamoDbAttribute("customer_id")
  public String getCustomerId() {
    return customerId;
  }

  @DynamoDbSortKey
  @DynamoDbAttribute("name#timestamp#id")
  @DynamoDbSecondarySortKey(indexNames = "entity_id_name_timestamp_id_index")
  public String getCompositeSortKey() {
    return String.format("%s#%s#%s", name, dtCreated, id);
  }

  @DynamoDbAttribute("category#timestamp#id")
  @DynamoDbSecondarySortKey(indexNames = "customerId_category_timestamp_id_index")
  public String getCategoryTimestampIdCompositeSortKey() {
    return String.format("%s#%s#%s", category, dtCreated, id);
  }

  @NonNull
  public String getDtCreated() {
    return dtCreated;
  }

  @NonNull
  public String getId() {
    return id;
  }

  @NonNull
  public String getName() {
    return name;
  }

  @NonNull
  @DynamoDbSecondaryPartitionKey(indexNames = "entity_id_name_timestamp_id_index")
  public String getEntityId() {
    return entityId;
  }

  @NonNull
  public String getEntityType() {
    return entityType;
  }

  @NonNull
  public String getMetadata() {
    return metadata;
  }

  public static class DdbEventBuilder {
    String dtCreated;
    String id;
    String customerId;
    String name;
    String entityId;
    String entityType;
    String metadata;
    String category;
    String dtArrived;

    // Required by DynamoDB Enhanced Client
    public DdbEventBuilder() {}
    
    public DdbEventBuilder customerId(String customerId) {
      this.customerId = customerId;
      return this;
    }

    public DdbEventBuilder name(String name) {
      this.name = name;
      return this;
    }

    public DdbEventBuilder entityType(String entityType) {
      this.entityType = entityType;
      return this;
    }

    public DdbEventBuilder metadata(String metadata) {
      this.metadata = metadata;
      return this;
    }

    public DdbEventBuilder category(String category) {
      this.category = category;
      return this;
    }

    public DdbEventBuilder dtArrived(String dtArrived) {
      this.dtArrived = dtArrived;
      return this;
    }

    public DdbEventBuilder dtCreated(String dtCreated) {
      this.dtCreated = dtCreated;
      return this;
    }

    public DdbEventBuilder id(String id) {
      this.id = id;
      return this;
    }

    public DdbEventBuilder entityId(String entityId) {
      this.entityId = entityId;
      return this;
    }

    public DdbEventBuilder compositeSortKey(String key) {
      if (key != null) {
        String[] parts = key.split("#", 3);
        if (parts.length == 3) {
          this.name = parts[0];
          this.dtCreated = parts[1];
          this.id = parts[2];
        }
      }
      return this;
    }

    public DdbEventBuilder categoryTimestampIdCompositeSortKey(String key) {
      if (key != null) {
        String[] parts = key.split("#", 3);
        if (parts.length == 3) {
          this.category = parts[0];
          this.dtCreated = parts[1];
          this.id = parts[2];
        }
      }
      return this;
    }
  }
}
