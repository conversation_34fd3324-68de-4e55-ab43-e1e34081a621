package com.getflex.featurestore.dao.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "us_addresses")
public class UsAddresses {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "number")
  private String number;

  @Column(name = "street_directional_prefix")
  private String streetDirectionalPrefix;

  @Column(name = "street")
  private String street;

  @Column(name = "street_type")
  private String streetType;

  @Column(name = "street_directional_suffix")
  private String streetDirectionalSuffix;

  @Column(name = "unit")
  private String unit;

  @Column(name = "city")
  private String city;

  @Column(name = "state")
  private String state;

  @Column(name = "zip")
  private String zip;

}