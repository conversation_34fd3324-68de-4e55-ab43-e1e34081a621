package com.getflex.featurestore.dao.model;

import ch.qos.logback.core.model.processor.ChainedModelFilter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "allow_deny_list")
@Data
@IdClass(AllowDenyListPrimaryKey.class)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class AllowDenyList implements Serializable {

  @Id
  @Column(name = "use_case", nullable = false)
  private String useCase;

  @Id
  @Column(name = "entity", nullable = false)
  private String entity;

  @Column(name = "allow", nullable = false)
  private boolean allow;
}
