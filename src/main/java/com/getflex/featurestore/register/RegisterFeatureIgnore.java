package com.getflex.featurestore.register;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Ignores this feature during registration by default.
 * To register the feature, remove this annotation OR provide custom TypeFilter to the AnnotatedFeatureResolver.
 * Primarily used for testing purposes. This can be placed on base classes to prevent all children from being
 * registered.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface RegisterFeatureIgnore {

}
