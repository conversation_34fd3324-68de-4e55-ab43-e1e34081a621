package com.getflex.featurestore.register;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Repeatable(RegisterMultipleFeatures.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RegisterFeature {

  /**
   * The name of the feature. Can include parameter values as {value}
   * Each value should map to a field in the parameterType class.
   */
  String value() default "";

  /**
   * The parameter type which
   */
  Class<?> parameterType() default Void.class;
}
