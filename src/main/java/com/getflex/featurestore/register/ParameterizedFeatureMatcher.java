package com.getflex.featurestore.register;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.web.util.UriTemplate;

/**
 * Matches features by parameterized name, e.g. "MyFeature-{param1}-{param2}"
 * Params are autowired into the features constructor
 */
class ParameterizedFeatureMatcher<FeatureT extends BaseFeature, ParamsT> implements FeatureMatcher<FeatureT> {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .registerModule(new JavaTimeModule());

  private final Class<FeatureT> featureClass;
  private final UriTemplate uriTemplate;
  private final Class<ParamsT> paramsClass;
  private final AutowireCapableBeanFactory beanFactory;

  public ParameterizedFeatureMatcher(Class<FeatureT> featureClass, String featureName,
      Class<ParamsT> params, AutowireCapableBeanFactory beanFactory) {
    this.featureClass = featureClass;
    this.paramsClass = params;
    this.beanFactory = beanFactory;
    this.uriTemplate = new UriTemplate(featureName);

    Set<String> allParamNames = new HashSet<>(this.uriTemplate.getVariableNames());
    PropertyDescriptor[] paramProps = BeanUtils.getPropertyDescriptors(paramsClass);
    for (var name : allParamNames) {
      boolean found = false;
      for (var prop : paramProps) {
        if (prop.getName().equals(name)) {
          found = true;
          break;
        }
      }

      if (!found) {
        throw new FeatureRegistrationException("Feature " + featureClass.getName()
            + " has parameter '" + name + "' in its UriTemplate but no corresponding property in params class "
            + paramsClass.getName());
      }
    }
  }

  @Override
  public boolean matches(String name) {
    return uriTemplate.matches(name);
  }

  @Override
  public String getFeatureName() {
    return uriTemplate.toString();
  }

  @Override
  public Class<FeatureT> getFeatureClass() {
    return this.featureClass;
  }

  @Override
  public FeatureT createBlankInstance() {
    AbstractAutowireCapableBeanFactory localizedBeanFactory = new DefaultListableBeanFactory(beanFactory);

    ParamsT paramsInstance = OBJECT_MAPPER.convertValue(Map.of(), paramsClass);
    localizedBeanFactory.registerSingleton(paramsClass.getName(), paramsInstance);

    return autoWireAndConstruct(uriTemplate.toString(), featureClass, localizedBeanFactory);
  }

  @Override
  public FeatureT createInstance(String name) {
    Map<String, String> params = uriTemplate.match(name);
    ParamsT paramsInstance = OBJECT_MAPPER.convertValue(params, paramsClass);

    // we want a child bean factory so that we can register the params instance and not pollute the parent instance
    AbstractAutowireCapableBeanFactory localizedBeanFactory = new DefaultListableBeanFactory(beanFactory);
    localizedBeanFactory.registerSingleton(paramsClass.getSimpleName(), paramsInstance);
    FeatureT result = autoWireAndConstruct(name, featureClass, localizedBeanFactory);

    localizedBeanFactory.destroySingleton(paramsClass.getSimpleName());
    return result;
  }
}
