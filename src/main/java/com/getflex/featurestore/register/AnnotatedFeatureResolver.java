package com.getflex.featurestore.register;

import com.getflex.featurestore.model.feature.base.BaseFeature;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.trie.PatriciaTrie;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;

public class AnnotatedFeatureResolver {

  /**
   * Trie for searching what feature is the closest match for a given feature
   * without iterating all the feature matchers.
   */
  private final PatriciaTrie<FeatureMatcher<?>> featureSearchTri;

  public AnnotatedFeatureResolver(AutowireCapableBeanFactory beanFactory) {
    this(beanFactory, createDefaultScanner());
  }

  /**
   * Constructor that scans the classpath for classes annotated with @RegisterFeature
   * Additional filters can be provided to further narrow down the scan to restrict to certain packages or class types
   *
   * @param beanFactory Bean factory to use for creating feature instances
   * @param scanner If provided will use this scanner instead of the default one
   */
  public AnnotatedFeatureResolver(@NotNull AutowireCapableBeanFactory beanFactory,
      @NotNull ClassPathScanningCandidateComponentProvider scanner) {
    Objects.requireNonNull(beanFactory, "beanFactory must not be null");

    List<FeatureMatcher<?>> featureMatchers = findAnnotatedFeatures(beanFactory, scanner);
    featureSearchTri = new PatriciaTrie<>();
    for (FeatureMatcher<?> featureMatcher : featureMatchers) {
      featureSearchTri.put(featureMatcher.getFeatureName(), featureMatcher);
    }
  }

  public Collection<FeatureMatcher<?>> getFeatureMatchers() {
    return this.featureSearchTri.values();
  }

  public BaseFeature resolveFeature(String name) {
    FeatureMatcher<?> featureMatcher = featureSearchTri.selectValue(name);
    if (featureMatcher != null && featureMatcher.matches(name)) {
      return featureMatcher.createInstance(name);
    }
    return null;
  }

  private static ClassPathScanningCandidateComponentProvider createDefaultScanner() {
    ClassPathScanningCandidateComponentProvider scanner =
        new ClassPathScanningCandidateComponentProvider(false);
    // include only classes annotated with @RegisterFeature
    scanner.addIncludeFilter(new AnnotationTypeFilter(RegisterFeature.class));
    scanner.addIncludeFilter(new AnnotationTypeFilter(RegisterMultipleFeatures.class));
    // exclude classes annotated with @RegisterFeatureIgnore
    scanner.addExcludeFilter(new AnnotationTypeFilter(RegisterFeatureIgnore.class));
    return scanner;
  }

  @SuppressWarnings("unchecked")
  private static List<FeatureMatcher<?>> findAnnotatedFeatures(AutowireCapableBeanFactory beanFactory,
      ClassPathScanningCandidateComponentProvider scanner) {
    List<FeatureMatcher<?>> featureMatchers = new ArrayList<>();

    for (BeanDefinition bd : scanner.findCandidateComponents("com.getflex.featurestore")) {
      try {
        Class<?> annotatedClass = Class.forName(bd.getBeanClassName());
        if (!BaseFeature.class.isAssignableFrom(annotatedClass)) {
          throw new FeatureRegistrationException("Bad annotation on class " + annotatedClass.getName()
              + " is annotated with @RegisterFeature but does not extend BaseFeature");
        }

        Class<BaseFeature> featureClass = (Class<BaseFeature>) annotatedClass;

        // check for multiple registrations
        RegisterMultipleFeatures registerMultipleFeatures = featureClass.getAnnotation(RegisterMultipleFeatures.class);
        if (registerMultipleFeatures != null) {
          for (RegisterFeature registerFeature : registerMultipleFeatures.value()) {
            FeatureMatcher<?> matcher = createMatcher(registerFeature, featureClass, beanFactory);
            featureMatchers.add(checkForDuplicateFeatureMatcher(matcher, featureMatchers));
          }
        }

        RegisterFeature registerFeature = featureClass.getAnnotation(RegisterFeature.class);
        if (registerFeature != null) {
          FeatureMatcher<?> matcher = createMatcher(registerFeature, featureClass, beanFactory);
          featureMatchers.add(checkForDuplicateFeatureMatcher(matcher, featureMatchers));
        }

      } catch (ClassNotFoundException e) {
        // shouldn't get class not founds unless there's some classpath issue or our scanner is messed up
        throw new FeatureRegistrationException("Could not find class for feature", e);
      }
    }

    return featureMatchers;
  }

  private static FeatureMatcher<?> createMatcher(RegisterFeature registerFeature, Class<BaseFeature>  featureClass,
      AutowireCapableBeanFactory beanFactory) {
    // allow registration to omit value and default to class name
    final String featureName = registerFeature.value().isBlank()
        ? featureClass.getSimpleName() : registerFeature.value();

    if (Void.class.equals(registerFeature.parameterType()) || registerFeature.parameterType() == null) {
      if (featureName.contains("{")) {
        throw new FeatureRegistrationException("Feature " + featureClass.getName() + " has parameterized name '"
            + featureName + "' but parameterType is Void. Property parameterType is required.");
      }
      return new StringFeatureMatcher<>(featureClass, featureName, beanFactory);
    } else {
      return new ParameterizedFeatureMatcher<>(featureClass, featureName,
          registerFeature.parameterType(), beanFactory);
    }
  }

  private static FeatureMatcher<?> checkForDuplicateFeatureMatcher(FeatureMatcher<?> newMatcher,
      List<FeatureMatcher<?>> existingMatchers) {
    final String featureName = newMatcher.getFeatureName();
    // since this should only be ran once at startup, we can do a simple O(n^2) check for duplicates
    // dont want to use a tri or a set because duplicates might show up in strange ways
    for (FeatureMatcher<?> existingMatcher : existingMatchers) {
      if (existingMatcher.matches(featureName) || newMatcher.matches(existingMatcher.getFeatureName())) {
        throw new FeatureRegistrationException("Failed to register featureName=" + featureName
            + " for class=" + newMatcher.getFeatureClass().getName() + ". Feature name overlaps with existing"
            + " featureName=" + existingMatcher.getFeatureName() + " for class="
            + existingMatcher.getFeatureClass().getName());
      }
    }
    return newMatcher;
  }
}
