package com.getflex.featurestore.register;

import com.getflex.featurestore.model.feature.base.BaseFeature;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;

/**
 * Matches features by exact string name.
 */
class StringFeatureMatcher<T extends BaseFeature> implements FeatureMatcher<T> {

  private final Class<T> featureClass;
  private final String featureName;
  private final AutowireCapableBeanFactory beanFactory;

  public StringFeatureMatcher(Class<T> featureClass, String featureName,
      AutowireCapableBeanFactory beanFactory) {
    if (featureName == null || featureName.isBlank()) {
      throw new IllegalArgumentException("featureName must not be null or blank");
    }
    this.featureClass = featureClass;
    this.featureName = featureName;
    this.beanFactory = beanFactory;
  }

  @Override
  public boolean matches(String featureName) {
    return this.featureName.equals(featureName);
  }

  @Override
  public String getFeatureName() {
    return featureName;
  }

  @Override
  public Class<T> getFeatureClass() {
    return this.featureClass;
  }

  @Override
  public T createBlankInstance() {
    return autoWireAndConstruct(featureName, featureClass, beanFactory);
  }

  @Override
  public T createInstance(String name) {
    return autoWireAndConstruct(name, featureClass, beanFactory);
  }
}
