package com.getflex.featurestore.register;


import com.getflex.featurestore.model.feature.base.BaseFeature;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;

public interface FeatureMatcher<T extends BaseFeature> {

  /**
   * Returns true if this matcher matches the given feature name.
   * If true a new instance of the feature can be created with createInstance()
   *
   * @param featureName
   * @return
   */
  boolean matches(String featureName);

  /**
   * The full feature name, including parameters if any.
   *
   * @return
   */
  String getFeatureName();

  /**
   * The class of the feature this matcher will produce
   *
   * @return
   */
  Class<T> getFeatureClass();

  /**
   * Creates a blank instance of the feature, without setting any parameters. Used
   * because we need to get some information off the features during startup
   *
   * @return
   */
  T createBlankInstance();

  /**
   * Creates an instance of the feature
   *
   * @param name
   * @return
   */
  T createInstance(String name);

  @SuppressWarnings("unchecked")
  default T autoWireAndConstruct(String name, Class<T> featureClass, AutowireCapableBeanFactory beanFactory) {
    try {
      T instance = (T) beanFactory.autowire(featureClass, AutowireCapableBeanFactory.AUTOWIRE_CONSTRUCTOR, true);

      // try and set the "name" property on our new object.
      try {
        PropertyDescriptor nameProp = BeanUtils.getPropertyDescriptor(featureClass, "name");
        if (nameProp != null) {
          Method writeMethod = BeanUtils.getWriteMethodParameter(nameProp).getMethod();
          if (writeMethod != null) {
            writeMethod.invoke(instance, name);
          }
        }
      } catch (Exception e) {
        throw new FeatureRegistrationException("Failed to set name property on feature class "
            + featureClass.getName(), e);
      }

      return instance;
    } catch (BeanCreationException e) {
      throw new FeatureRegistrationException("Failed to create instance of feature " + featureClass.getName()
          + ", ensure you have the right constructors setup for your feature", e);
    }
  }
}
