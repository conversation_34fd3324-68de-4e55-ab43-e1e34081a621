package com.getflex.featurestore.filter;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

public class CustomThreadPoolExecutor extends ThreadPoolExecutor {

  public CustomThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
      BlockingQueue<Runnable> workQueue) {
    super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
  }

  @Override
  public <T> List<Future<T>> invokeAll(@NotNull Collection<? extends Callable<T>> tasks) throws InterruptedException {
    List<CallableWithRequestContext<T>> callablesWithReqInfo = tasks.stream().map(
        task -> new CallableWithRequestContext<>(task, RequestContextHolder.currentRequestAttributes())
    ).toList();
    return super.invokeAll(callablesWithReqInfo);
  }

  @Override
  public void execute(@NotNull Runnable command) {
    RunnableWithRequestContext runnable = new RunnableWithRequestContext(command,
        RequestContextHolder.currentRequestAttributes());
    super.execute(runnable);
  }

  private record CallableWithRequestContext<T>(Callable<T> callable, RequestAttributes requestAttributes) implements
      Callable<T> {

    private CallableWithRequestContext(Callable<T> callable, RequestAttributes requestAttributes) {
      this.callable = callable;
      this.requestAttributes = copy(requestAttributes);
    }

    private RequestAttributes copy(RequestAttributes requestAttributes) {
      // RequestAttributes needs to be copied as it will be garbage collected when origin request will complete.
      return requestAttributes;
    }

    @Override
    public T call() throws Exception {
      try {
        RequestContextHolder.setRequestAttributes(requestAttributes);
        return callable.call();
      } finally {
        RequestContextHolder.resetRequestAttributes();
      }
    }
  }

  private record RunnableWithRequestContext(Runnable runnable, RequestAttributes requestAttributes) implements
      Runnable {

    private RunnableWithRequestContext(Runnable runnable, RequestAttributes requestAttributes) {
      this.runnable = runnable;
      this.requestAttributes = copy(requestAttributes);
    }

    private RequestAttributes copy(RequestAttributes requestAttributes) {
      // RequestAttributes needs to be copied as it will be garbage collected when origin request will complete.
      return requestAttributes;
    }

    @Override
    public void run() {
      try {
        RequestContextHolder.setRequestAttributes(requestAttributes);
        runnable.run();
      } finally {
        RequestContextHolder.resetRequestAttributes();
      }
    }

  }
}
