package com.getflex.featurestore.filter;

import static com.getflex.featurestore.constant.FeatureStoreConstant.CORRELATION_ID;
import static com.getflex.featurestore.constant.FeatureStoreConstant.CORRELATION_ID_KEY;
import static com.getflex.featurestore.constant.FeatureStoreConstant.REQUEST_ID;
import static com.getflex.featurestore.constant.FeatureStoreConstant.REQUEST_ID_KEY;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j
@WebFilter(urlPatterns = "/v1/*")
@Component
public class ApiFilter extends OncePerRequestFilter {

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
      FilterChain filterChain) throws ServletException, IOException {
    MutableHttpServletRequest newRequest = new MutableHttpServletRequest(request);
    if (newRequest.getHeader(REQUEST_ID) == null
        || newRequest.getHeader(REQUEST_ID).isEmpty()) {
      newRequest.putHeader(REQUEST_ID, UUID.randomUUID().toString());
    }
    if (newRequest.getHeader(CORRELATION_ID) == null
        || newRequest.getHeader(CORRELATION_ID).isEmpty()) {
      newRequest.putHeader(CORRELATION_ID, UUID.randomUUID().toString());
    }
    if (response.getHeader(REQUEST_ID) == null
        || response.getHeader(REQUEST_ID).isEmpty()) {
      response.addHeader(REQUEST_ID, newRequest.getHeader(REQUEST_ID));
    }
    if (response.getHeader(CORRELATION_ID) == null
        || response.getHeader(CORRELATION_ID).isEmpty()) {
      response.addHeader(CORRELATION_ID, newRequest.getHeader(CORRELATION_ID));
    }

    MDC.put(REQUEST_ID_KEY, newRequest.getHeader(REQUEST_ID));
    MDC.put(CORRELATION_ID_KEY, newRequest.getHeader(CORRELATION_ID));
    if (request.getMethod().equalsIgnoreCase("POST")
        || request.getMethod().equalsIgnoreCase("PUT")) {
      log.debug("received write api call {} to feature-store {}", request.getMethod(), request.getRequestURI());
    }
    try {
      filterChain.doFilter(newRequest, response);
    } finally {
      MDC.clear();
    }
  }
}
