<Configuration>
  <springProfile name="(dev|qa|prod|stg|int) &amp; !developer">
    <appender name="jsonConsoleAppender" class="ch.qos.logback.core.ConsoleAppender">
      <encoder class="net.logstash.logback.encoder.LogstashEncoder">
        <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
          <maxDepthPerThrowable>10</maxDepthPerThrowable>
          <maxLength>2048</maxLength>
          <shortenedClassNameLength>20</shortenedClassNameLength>
          <rootCauseFirst>true</rootCauseFirst>
          <inlineHash>true</inlineHash>
        </throwableConverter>
      </encoder>
    </appender>
    <root level="INFO">
      <appender-ref ref="jsonConsoleAppender"/>
    </root>
  </springProfile>
  <springProfile name="local,unit_test,developer">
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
      <encoder>
        <pattern>[%d{HH:mm:ss.SSS}] [%highlight(%-5level)] [%t] [req_id:%X{requestId}] [cor_id:%X{correlationId}] %logger{0}.%method:%line - %message %mdc %n
        </pattern>
      </encoder>
    </appender>
    <root level="INFO">
      <appender-ref ref="console"/>
    </root>
  </springProfile>
</Configuration>
