spring:
  config:
    activate:
      on-profile: int

flex:
  dispute-url: https://dispute.int.getflex.org/v1
  identity-url: https://identity.int.getflex.org
  offer-url: https://offer.int.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.int.getflex.org/v1
  tagging-url: https://tagging.int.getflex.org/v1
  ledger-url: https://ledger.int.getflex.org/v1
  user-account-url: https://user-account.int.getflex.org/v1
  verification-url: https://verification.int.getflex.org/v1
  autopay-url: https://autopay.int.getflex.org/v1
  payment-url: https://payment.int.getflex.org/v1
  fulfillment-url: https://fulfillment.int.getflex.org/v1
  settlement-url: https://settlement.int.getflex.org/v1
  rent-credibility-url: https://rent-credibility.int.getflex.org
  decision-engine-url: https://decision-engine.int.getflex.org/v1
  partner-hub-url: https://partner-hub-api.int.getflex.org/v1
  credit-management-url: https://credit-management.int.getflex.org
  flex-score-endpoint:
    V5: "RiskCreditModelEndpoint-6hjSgfizlFPE"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  socure-bucket: flex2-feature-store-socure-cache-int

  tagging-fraud-namespace-id: 100  # placeholder until records added in tagging service DB
  zendesk-url: https://getflex1689306643.zendesk.com
