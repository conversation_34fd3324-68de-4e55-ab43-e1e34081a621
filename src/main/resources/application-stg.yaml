spring:
  config:
    activate:
      on-profile: stg

flex:
  dispute-url: https://dispute.stg.getflex.org/v1
  identity-url: https://identity.stg.getflex.org
  offer-url: https://offer.stg.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.stg.getflex.org/v1
  tagging-url: https://tagging.stg.getflex.org/v1
  ledger-url: https://ledger.stg.getflex.org/v1
  user-account-url: https://user-account.stg.getflex.org/v1
  verification-url: https://verification.stg.getflex.org/v1
  autopay-url: https://autopay.stg.getflex.org/v1
  payment-url: https://payment.stg.getflex.org/v1
  fulfillment-url: https://fulfillment.stg.getflex.org/v1
  settlement-url: https://settlement.stg.getflex.org/v1
  rent-credibility-url: https://rent-credibility.stg.getflex.org
  decision-engine-url: https://decision-engine.stg.getflex.org/v1
  partner-hub-url: https://partner-hub-api.stg.getflex.org/v1
  credit-management-url: https://credit-management.stg.getflex.org
  flex-score-endpoint:
    V5: "RiskCreditModelEndpoint-FKUf9VIxfK5f"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  zendesk-url: https://getflex1689306643.zendesk.com
  socure-bucket: flex2-feature-store-socure-cache-stg
