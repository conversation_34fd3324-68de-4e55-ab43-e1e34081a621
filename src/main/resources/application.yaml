spring:
  profiles:
    active: local
  web:
    resources:
      add-mappings: false
  application:
    name: feature-store

  jpa:
    properties:
      hibernate:
        jdbc:
          # Make sure DB session is always on UTC (mostly for dev computer)
          time_zone: UTC
      javax:
        persistence:
          query:
            # If we upgrade spring boot to 3.x, use
            # `spring.jpa.properties.jakarta.persistence.query.timeout` instead.
            timeout: 60000
    open-in-view: false

  datasource:
    primary:
      driver-class-name: 'com.mysql.cj.jdbc.Driver'
      url: 'jdbc:mysql://${AURORA_HOST}:${AURORA_PORT}/${AURORA_DB}?enabledTLSProtocols=TLSv1.2'
      username: ${AURORA_USERNAME}
      password: ${AURORA_PASSWORD}
    replica:
      driver-class-name: 'com.mysql.cj.jdbc.Driver'
      url: 'jdbc:mysql://${AURORA_REPLICA_HOST}:${AURORA_PORT}/${AURORA_DB}?enabledTLSProtocols=TLSv1.2'
      username: ${AURORA_USERNAME}
      password: ${AURORA_PASSWORD}
    hikari:
      keepalive-time: 1500000

server:
 shutdown: graceful
 tomcat:
   keep-alive-timeout: 70000
   mbeanregistry:
     enabled: true

management:
  endpoint:
    health:
      probes:
        enabled: true
    env:
      enabled: true
  metrics:
    export:
      datadog:
        api-key: ${DD_API_KEY}
        application-key: ${DD_APP_KEY}
    tags:
      application: feature-store
      env: ${spring.profiles.active}
      region: ${aws.region}
    web:
      server:
        request:
          autotime:
            enabled: true
            percentiles-histogram: true
            percentiles: 0.5,0.75,0.95,0.99

statsd:
  agent:
    host: ${DD_AGENT_HOST}
    port: ${DD_STATSD_PORT}

flex:
  # dependency timeout and retry
  internal-open-api-connect-timeout: PT1S
  internal-open-api-retry-delay: PT0.5S
  internal-open-api-max-retries: 3
  billing-timeout: PT6S
  dispute-timeout: PT6S
  identity-timeout: PT6S
  offer-timeout: PT6S
  wallet-timeout: PT6S
  ledger-timeout: PT6S
  tagging-timeout: PT6S
  user-account-timeout: PT6S
  autopay-timeout: PT6S
  bill-payment-method-timeout: PT6S
  verification-timeout: PT6S
  payment-timeout: PT6S
  rent-credibility-timeout: PT6S
  decision-engine-timeout: PT7S
  partner-hub-timeout: PT2S
  credit-management-timeout: PT6S
  flex-score-endpoint:
    V6: "FlexScore6BureauModelEndpoint"
    V7: "FlexScore7CreditModelEndpoint"
  fraud-trans-model-endpoint: "FlexFraudTransModelEndpoint"
  ftm-v2-endpoint: "FlexFTM2ModelEndpoint"
  ftm-v3-endpoint: "FlexFTM3ModelEndpoint"
  proxy-rent-model-endpoint: "FlexProxyRentModelEndpoint"
  tagging-namespace-ids:
    DEFAULT: 1
    RISK_FRAUD: 100
  socure-url: https://sandbox.socure.com/api/3.0/EmailAuthScore
  socure-api-key: ${SOCURE_API_KEY}
  cipher:
    keyArn: ${CIPHER_KEY_ARN}
    mode: live
    cacheEnabled: true
  zendesk-url: https://getflex.zendesk.com
  zendesk-api-key: ${ZENDESK_API_KEY}
  s3-single-attempt-timeout-second: 3
  s3-operation-timeout-second: 7
  socure-timeout-second: PT8S
  ddb-operation-timeout: PT7S
  ddb-single-attempt-timeout: PT7S
# deployment:
#   feature-store-service:
#     enabled: true
