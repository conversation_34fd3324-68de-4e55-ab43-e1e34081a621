spring:
  config:
    activate:
      on-profile: local
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  datasource:
    primary:
      driver-class-name: 'com.mysql.cj.jdbc.Driver'
      url: '*****************************************'
      username: feature_store
      password: password
    replica:
      driver-class-name: 'com.mysql.cj.jdbc.Driver'
      url: '*****************************************'
      username: feature_store
      password: password

statsd:
  agent:
    host: 127.0.0.1
    port: 8125

server:
  port: 8082

management:
  datadog:
    metrics:
      export:
        # disable datadog for local development
        enabled: false
flex:
  dispute-url: https://dispute.dev.getflex.org/v1
  identity-url: https://identity.dev.getflex.org
  offer-url: https://offer.dev.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.dev.getflex.org/v1
  tagging-url: https://tagging.dev.getflex.org/v1
  ledger-url: https://ledger.dev.getflex.org/v1
  user-account-url: https://user-account.dev.getflex.org/v1
  verification-url: https://verification.dev.getflex.org/v1
  autopay-url: https://autopay.dev.getflex.org/v1
  payment-url: https://payment.dev.getflex.org/v1
  fulfillment-url: https://fulfillment.dev.getflex.org/v1
  settlement-url: https://settlement.dev.getflex.org/v1
  rent-credibility-url: https://rent-credibility.dev.getflex.org
  decision-engine-url: https://decision-engine.dev.getflex.org/v1
  partner-hub-url: https://partner-hub-api.dev.getflex.org/v1
  credit-management-url: https://credit-management.dev.getflex.org
  socure-bucket: flex2-feature-store-socure-cache-dev

  flex-score-endpoint:
    V5: "FlexScore5CreditModelEndpoint"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  cipher:
    keyArn: arn:aws:kms:us-east-1:************:key/95f87ba7-f003-48d6-8fa3-f5172334353b
  zendesk-url: https://getflex1689306643.zendesk.com
  zendesk-api-key: mock-api-key
