spring:
  config:
    activate:
      on-profile: prod

flex:
  dispute-url: https://dispute.prod.getflex.org/v1
  identity-url: https://identity.prod.getflex.org
  offer-url: https://offer.prod.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.prod.getflex.org/v1
  tagging-url: https://tagging.prod.getflex.org/v1
  ledger-url: https://ledger.prod.getflex.org/v1
  user-account-url: https://user-account.prod.getflex.org/v1
  verification-url: https://verification.prod.getflex.org/v1
  autopay-url: https://autopay.prod.getflex.org/v1
  payment-url: https://payment.prod.getflex.org/v1
  fulfillment-url: https://fulfillment.prod.getflex.org/v1
  settlement-url: https://settlement.prod.getflex.org/v1
  rent-credibility-url: https://rent-credibility.prod.getflex.org
  decision-engine-url: https://decision-engine.prod.getflex.org/v1
  partner-hub-url: https://partner-hub-api.prod.getflex.org/v1
  credit-management-url: https://credit-management.prod.getflex.org
  flex-score-endpoint:
    V5: "RiskCreditModelEndpoint-T0x8IeBfWQEk"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  socure-url: https://service.socure.com/api/3.0/EmailAuthScore
  socure-bucket: flex2-feature-store-socure-cache-prod
