spring:
  config:
    activate:
      on-profile: qa

flex:
  dispute-url: https://dispute.qa.getflex.org/v1
  identity-url: https://identity.qa.getflex.org
  offer-url: https://offer.qa.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.qa.getflex.org/v1
  tagging-url: https://tagging.qa.getflex.org/v1
  ledger-url: https://ledger.qa.getflex.org/v1
  user-account-url: https://user-account.qa.getflex.org/v1
  verification-url: https://verification.qa.getflex.org/v1
  autopay-url: https://autopay.qa.getflex.org/v1
  payment-url: https://payment.qa.getflex.org/v1
  fulfillment-url: https://fulfillment.qa.getflex.org/v1
  settlement-url: https://settlement.qa.getflex.org/v1
  rent-credibility-url: https://rent-credibility.qa.getflex.org
  decision-engine-url: https://decision-engine.qa.getflex.org/v1
  partner-hub-url: https://partner-hub-api.qa.getflex.org/v1
  credit-management-url: https://credit-management.qa.getflex.org
  flex-score-endpoint:
    V5: "RiskCreditModelEndpoint-QSIB7C4sJcAQ"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  zendesk-url: https://getflex1689306643.zendesk.com
  socure-bucket: flex2-feature-store-socure-cache-qa
