spring:
  config:
    activate:
      on-profile: dev
flex:
  dispute-url: https://dispute.dev.getflex.org/v1
  identity-url: https://identity.dev.getflex.org
  offer-url: https://offer.dev.getflex.org/v1
  billing-url: http://billing-lb-service.flex2.svc.cluster.local
  wallet-url: https://wallet.dev.getflex.org/v1
  tagging-url: https://tagging.dev.getflex.org/v1
  ledger-url: https://ledger.dev.getflex.org/v1
  user-account-url: https://user-account.dev.getflex.org/v1
  verification-url: https://verification.dev.getflex.org/v1
  autopay-url: https://autopay.dev.getflex.org/v1
  payment-url: https://payment.dev.getflex.org/v1
  fulfillment-url: https://fulfillment.dev.getflex.org/v1
  settlement-url: https://settlement.dev.getflex.org/v1
  rent-credibility-url: https://rent-credibility.dev.getflex.org
  decision-engine-url: https://decision-engine.dev.getflex.org/v1
  partner-hub-url: https://partner-hub-api.dev.getflex.org/v1
  credit-management-url: https://credit-management.dev.getflex.org
  flex-score-endpoint:
    V5: "FlexScore5CreditModelEndpoint"
  events-queue-url: https://sqs.us-east-1.amazonaws.com/************/feature-store-risk-ltv-events
  zendesk-url: https://getflex1689306643.zendesk.com
  socure-bucket: flex2-feature-store-socure-cache-dev
