package com.getflex.featurestore.integration.flex;

import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class FlexScoreModelV5Test {

  @Test
  void v5InputSerializer_firstAndLastFeature() {
    String result = Version.V5.getInputSerializer()
        .apply(Map.of(FlexScoreModelExtractedFeature.EADM09_P02E, 123, FlexScoreModelExtractedFeature.EADS142_G224C,
            0.456));
    Assertions.assertTrue(result.matches("123(,-99999999)*,+0\\.456"));
  }
}
