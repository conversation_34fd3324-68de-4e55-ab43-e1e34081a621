package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.api.BillingServicePublicApi;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.identity.client.ApiException;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillingServiceTest {
  @InjectMocks
  BillingService service;

  @Mock
  BillingServicePublicApi api;

  @Test
  void testGetPropertyById() throws ApiException, com.getflex.billing.api.v2.client.ApiException {
    List<ComGetflexBillingControllerV2PropertyControllerPropertyResponse> response = List.of(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse()
            .billingIntegrationType(BillingIntegrationTypeEnum.FLEX_ANYWHERE));
    when(api.getProperties(1L)).thenReturn(response);

    var result = service.getPropertyById(1L);

    assertEquals(BillingIntegrationTypeEnum.FLEX_ANYWHERE, result.getBillingIntegrationType());
  }

  @Test
  void getPropertyByIdentityBillerId_success() throws com.getflex.billing.api.v2.client.ApiException {
    when(api.getPropertyByBillerId(123L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse()
            .billingIntegrationType(BillingIntegrationTypeEnum.DIRECT_INTEGRATION)
            .isOutOfNetwork(false)
            .location(null));

    PropertyInfo result = service.getPropertyByIdentityBillerId(123L);

    assertEquals(BillingIntegrationTypeEnum.DIRECT_INTEGRATION, result.integrationType());
    assertFalse(result.isOutOfNetwork());
    assertEquals(null, result.location());
  }

  @Test
  void getPropertyByIdentityBillerId_failure() throws com.getflex.billing.api.v2.client.ApiException {
    when(api.getPropertyByBillerId(123L)).thenThrow(new com.getflex.billing.api.v2.client.ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getPropertyByIdentityBillerId(123L));
  }
}
