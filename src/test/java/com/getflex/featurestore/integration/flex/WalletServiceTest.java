package com.getflex.featurestore.integration.flex;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.wallet.api.WalletApi;
import com.getflex.wallet.api.WalletApi.APIgetCardsByFingerprintRequest;
import com.getflex.wallet.api.WalletApi.APIgetCardsRequest;
import com.getflex.wallet.client.ApiException;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetCardsByFingerprintResponse;
import com.getflex.wallet.model.GetCardsResponse;
import com.getflex.wallet.model.GetConnectAccountResponse;
import com.getflex.wallet.model.GetDefaultCardResponse;
import com.getflex.wallet.model.GetDistinctPaymentMethodResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class WalletServiceTest {
  @InjectMocks
  WalletService service;

  @Mock
  WalletApi api;

  @Test
  void getDistinctPaymentMethodByCustomerId() throws ApiException {
    when(api.getDistinctPaymentMethodByCustomer(1L)).thenReturn(
          new GetDistinctPaymentMethodResponse().paymentMethodStripeIds(List.of("test-id-1", "test-id-2")));

    var result = service.getDistinctPaymentMethodByCustomerId(1L);

    assertEquals("test-id-1", result.get(0));
    assertEquals("test-id-2", result.get(1));
  }

  @Test
  void getConnectAccount() throws ApiException {
    when(api.getConnectAccount("1")).thenReturn(
        new GetConnectAccountResponse().connectAccountId("1")
    );
    Boolean result = service.hasStripeConnectAccount("1");
    assertEquals(true, result);
  }

  @Test
  void getConnectAccount_NullResponse() throws ApiException {
    when(api.getConnectAccount("1")).thenReturn(null);
    Boolean result = service.hasStripeConnectAccount("1");
    assertEquals(false, result);
  }

  @Test
  void getConnectAccount_NotFoundException() throws ApiException {
    when(api.getConnectAccount("1"))
        .thenThrow(new ApiException(404, "Not Found"));
    Boolean result = service.hasStripeConnectAccount("1");
    assertEquals(false, result);
  }

  @Test
  void getConnectAccount_Exception() throws ApiException {
    when(api.getConnectAccount("1")).thenThrow(new ApiException());
    assertThrows(InternalDependencyFailureException.class, () -> service.hasStripeConnectAccount("1"));
  }

  @Test
  void getCardDetails() throws ApiException {
    when(api.getCardDetails(1L)).thenReturn(
        new Card().cardType("debit")
    );
    Card result = service.getCard(1L);
    assertEquals(new Card().cardType("debit"), result);
  }

  @Test
  void getCardDetails_NullResponse() throws ApiException {
    when(api.getCardDetails(1L)).thenReturn(null);
    Card result = service.getCard(1L);
    assertEquals(null, result);
  }

  @Test
  void getCardDetails_NotFoundException() throws ApiException {
    when(api.getCardDetails(1L))
        .thenThrow(new ApiException(404, "Not Found"));
    Card result = service.getCard(1L);
    assertEquals(null, result);
  }

  @Test
  void getCardDetails_Exception() throws ApiException {
    when(api.getCardDetails(1L)).thenThrow(new ApiException());
    assertThrows(InternalDependencyFailureException.class, () -> service.getCard(1L));
  }

  @Test
  void getDefaultCard() throws ApiException {
    when(api.getDefaultCard("customerPublicId")).thenReturn(
        new GetDefaultCardResponse()
            .card(new Card().cardType("debit"))
    );
    var result = service.getDefaultCard("customerPublicId");
    assertEquals(new GetDefaultCardResponse().card(new Card().cardType("debit")), result);
  }

  @Test
  void getDefaultCard_NullResponse() throws ApiException {
    when(api.getDefaultCard("customerPublicId")).thenReturn(null);
    var result = service.getDefaultCard("customerPublicId");
    assertNull(null, result);
  }

  @Test
  void getDefaultCard_NotFoundException() throws ApiException {
    when(api.getDefaultCard("customerPublicId"))
        .thenThrow(new ApiException(404, "Not Found"));
    var result = service.getDefaultCard("customerPublicId");
    assertNull(result);
  }

  @Test
  void getDefaultCard_Exception() throws ApiException {
    when(api.getDefaultCard("customerPublicId")).thenThrow(new ApiException());
    assertThrows(InternalDependencyFailureException.class,
        () -> service.getDefaultCard("customerPublicId")
    );
  }

  @Test
  void getCards() throws ApiException {
    doReturn(
        new GetCardsResponse()
            .cards(List.of(new Card().fingerprint("fingerprint")))
    ).when(api).getCards(any(APIgetCardsRequest.class));

    var result = service.getCards("customerPublicId");
    assertEquals(1, result.size());
    assertEquals("fingerprint", result.get(0).getFingerprint());
  }

  @Test
  void getCards_NotFoundException() throws ApiException {
    doThrow(new ApiException(404, "Not Found")).when(api).getCards(any(APIgetCardsRequest.class));
    var result = service.getCards("customerPublicId");
    assertEquals(0, result.size());
  }

  @Test
  void getCards_Exception() throws ApiException {
    doThrow(new ApiException()).when(api).getCards(any(APIgetCardsRequest.class));
    assertThrows(InternalDependencyFailureException.class,
        () -> service.getCards("customerPublicId")
    );
  }

  @Test
  void getCardsByFingerprint() throws ApiException {
    doReturn(
        new GetCardsByFingerprintResponse()
            .cards(List.of(new Card().fingerprint("fingerprint")))
    ).when(api).getCardsByFingerprint("fingerprint");

    var result = service.getCardsByFingerprint("fingerprint");
    assertEquals(1, result.size());
    assertEquals("fingerprint", result.get(0).getFingerprint());
  }

  @Test
  void getCardsByFingerprint_NotFoundException() throws ApiException {
    doThrow(new ApiException(404, "Not Found"))
        .when(api).getCardsByFingerprint("fingerprint");
    var result = service.getCardsByFingerprint("fingerprint");
    assertEquals(0, result.size());
  }

  @Test
  void getCardsByFingerprint_Exception() throws ApiException {
    doThrow(new ApiException()).when(api)
        .getCardsByFingerprint("fingerprint");
    assertThrows(InternalDependencyFailureException.class,
        () -> service.getCardsByFingerprint("fingerprint")
    );
  }
}
