package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.payment.api.PaymentApi;
import com.getflex.payment.client.ApiException;
import com.getflex.payment.model.DeclineCodeEnum;
import com.getflex.payment.model.Get3dsAuthenticationsResponse;
import com.getflex.payment.model.GetCustomerBillResponse;
import com.getflex.payment.model.GetTotalUserPaymentDeclinedResponse;
import com.getflex.payment.model.Model3dsAuthRecord;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PaymentServiceTest {

  String customerPublicId = "customerPublicId";

  @InjectMocks
  PaymentService service;

  @Mock
  PaymentApi api;

  @Test
  void getTotalUserPaymentsDeclined() throws ApiException {
    GetTotalUserPaymentDeclinedResponse response = new GetTotalUserPaymentDeclinedResponse()
        .totalUserPaymentsDeclined(1L);
    when(api.getTotalUserPaymentsDeclined(any(), any())).thenReturn(response);

    var result = service.getTotalUserPaymentsDeclined(1L, DeclineCodeEnum.CARD_NOT_SUPPORTED);

    assertEquals(1, result);
  }

  @Test
  void getTotalUserPaymentsDeclined__failure404() throws ApiException {
    when(api.getTotalUserPaymentsDeclined(any(), any()))
        .thenThrow(new ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getTotalUserPaymentsDeclined(1L, DeclineCodeEnum.CARD_NOT_SUPPORTED);

    assertEquals(0, result);
  }

  @Test
  void getTotalUserPaymentsDeclined__failureGeneric() throws ApiException {
    when(api.getTotalUserPaymentsDeclined(any(), any()))
        .thenThrow(new ApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "error"));

    assertThrows(InternalDependencyFailureException.class, () -> service
        .getTotalUserPaymentsDeclined(1L, DeclineCodeEnum.CARD_NOT_SUPPORTED));
  }

  @Test
  void getTotalUserPaymentsDeclinedV2() throws ApiException {
    GetTotalUserPaymentDeclinedResponse response = new GetTotalUserPaymentDeclinedResponse()
        .totalUserPaymentsDeclined(1L);
    when(api.getTotalUserPaymentsDeclinedV2(any(), any(), any())).thenReturn(response);

    var result = service.getTotalUserPaymentsDeclinedV2(
        1L,
        List.of(DeclineCodeEnum.CARD_NOT_SUPPORTED),
        OffsetDateTime.now()
    );

    assertEquals(1, result);
  }

  @Test
  void getTotalUserPaymentsDeclinedV2__failure404() throws ApiException {
    when(api.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenThrow(new ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getTotalUserPaymentsDeclinedV2(
        1L,
        List.of(DeclineCodeEnum.CARD_NOT_SUPPORTED),
        OffsetDateTime.now()
    );

    assertEquals(0, result);
  }

  @Test
  void getTotalUserPaymentsDeclinedV2__failureGeneric() throws ApiException {
    when(api.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenThrow(new ApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "error"));

    assertThrows(InternalDependencyFailureException.class,
        () -> service.getTotalUserPaymentsDeclinedV2(
            1L,
            List.of(DeclineCodeEnum.CARD_NOT_SUPPORTED),
            OffsetDateTime.now())
    );
  }

  @Test
  void getCustomerBill() throws ApiException {
    GetCustomerBillResponse response = new GetCustomerBillResponse().billTransactionId("test");
    when(api.getCustomerBillByTransactionId(any())).thenReturn(response);

    var result = service.getCustomerBill("test", "test");

    assertEquals(response, result);
  }

  @Test
  void getCustomerBill__failureGeneric() throws ApiException {
    when(api.getCustomerBillByTransactionId(any()))
        .thenThrow(new ApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "error"));

    assertThrows(InternalDependencyFailureException.class, () -> service
        .getCustomerBill("test", "test"));
  }

  @Test
  void get3dsAuth() throws ApiException {
    when(api.get3dsAuth(customerPublicId)).thenReturn(
        new Get3dsAuthenticationsResponse().data(
            List.of(new Model3dsAuthRecord())
        )
    );

    var result = service.get3dsAuthRecords(customerPublicId);

    assertEquals(1, result.size());
  }

  @Test
  void get3dsAuth_nullResponseEmptyList() throws ApiException {
    when(api.get3dsAuth(customerPublicId)).thenReturn(null);

    var result = service.get3dsAuthRecords(customerPublicId);

    assertEquals(0, result.size());
  }

  @Test
  void get3dsAuth__failure404() throws ApiException {
    when(api.get3dsAuth(customerPublicId))
        .thenThrow(new ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.get3dsAuthRecords(customerPublicId);

    assertEquals(0, result.size());
  }

}
