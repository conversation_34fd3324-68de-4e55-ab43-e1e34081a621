package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.settlement.api.SettlementApi;
import com.getflex.settlement.client.ApiException;
import com.getflex.settlement.model.DdaDecision;
import com.getflex.settlement.model.GetDdaDecisionsResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SettlementServiceTest {
  @InjectMocks
  SettlementService service;

  @Mock
  SettlementApi api;

  @Test
  void getDdaDecisionsByBillTransactionId() throws ApiException {
    GetDdaDecisionsResponse response = new GetDdaDecisionsResponse()
        .ddaDecisions(List.of(new DdaDecision().billTransactionId("1")));
    when(api.getDdaDecisionsForCustomer(any())).thenReturn(response);

    var result = service.getDdaDecisionsByBillTransactionId("test");

    assertEquals(1, result.size());
  }

  @Test
  void getDdaDecisionsByBillTransactionId_emptyList() throws ApiException {
    when(api.getDdaDecisionsForCustomer(any())).thenReturn(null);

    var result = service.getDdaDecisionsByBillTransactionId("test");

    assertEquals(null, result);
  }

  @Test
  void getDdaDecisionsByBillTransactionId_failure404() throws ApiException {
    when(api.getDdaDecisionsForCustomer(any()))
        .thenThrow(new com.getflex.settlement.client.ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getDdaDecisionsByBillTransactionId("test");

    assertEquals(null, result);
  }

}
