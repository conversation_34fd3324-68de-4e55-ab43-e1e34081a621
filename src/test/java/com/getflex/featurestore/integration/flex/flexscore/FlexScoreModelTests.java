package com.getflex.featurestore.integration.flex.flexscore;

import static com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.validateDecileThreshold;
import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.integration.flex.flexscore.model.ModelOutput;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelFeatureFormulaParams;
import com.getflex.featurestore.utils.Metrics;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FlexScoreModelTests {

  public static final InvokeEndpointResponse SAGEMAKER_RESPONSE = InvokeEndpointResponse.builder()
      .contentType("application/json").body(SdkBytes.fromUtf8String("""
          {
            "predictions": {
              "data": 0.5
            },
            "explanations" : {
              "kernel_shap" : [ [
                {
                   "attributions" : [ {
                     "attribution" : [ 1.0 ]
                   } ],
                   "feature_header" : "eadm09_p02e"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 2.0 ]
                   } ],
                   "feature_header" : "eads05_agg602"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 3.0 ]
                   } ],
                   "feature_header" : "vantage30_score"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 4.0 ]
                   } ],
                   "feature_header" : "eads520b_rev322"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 5.0 ]
                   } ],
                   "feature_header" : "revolving_accts_60_dq_l24m"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 6.0 ]
                   } ],
                   "feature_header" : "aadm86_linkf193"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 7.0 ]
                   } ],
                   "feature_header" : "total_original_balance_open_good_installment"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ -8.0E2 ]
                   } ],
                   "feature_header" : "months_on_file"
                 }, {
                   "attributions" : [ {
                     "attribution" : [ 0.9E1 ]
                   } ],
                   "feature_header" : "eads07_balmag01"
                 } ] ]
            }
          }
          """)).build();

  public static final InvokeEndpointResponse SAGEMAKER_INVALID_SCORE_RESPONSE = InvokeEndpointResponse.builder()
      .contentType("application/json").body(SdkBytes.fromUtf8String("""
          {
            "predictions": {
              "data": 1.1
            }
          }
          """)).build();
  @InjectMocks
  FlexScoreModel model;

  @Mock
  Metrics metrics;

  @Mock
  SageMakerRuntimeClient sageMaker;

  Version modelVersion = Mockito.mock(Version.class);

  @Spy
  ServiceConfig serviceConfig = new ServiceConfig().withFlexScoreEndpoint(Map.of(modelVersion, "epS"));

  @Captor
  ArgumentCaptor<InvokeEndpointRequest> sageMakerRequest;

  @Mock
  FlexScoreModelExtractedFeature feature1;

  @Mock
  FlexScoreModelExtractedFeature feature2;

  @Mock(answer = Answers.RETURNS_DEEP_STUBS)
  AlloyReport alloyReport;

  @Mock
  PropertyInfo propertyInfo;

  @BeforeEach
  void setupVersionAndFeature() {
    when(modelVersion.getContentType()).thenReturn("text/csv");
    when(modelVersion.getFeatureList()).thenReturn(
        List.of(feature1, feature2, FlexScoreModelExtractedFeature.VANTAGE30_SCORE));
    when(modelVersion.getInputSerializer()).thenReturn(
        map -> map.get(feature1) + "," + map.get(feature2) + "," + map.get(
            FlexScoreModelExtractedFeature.VANTAGE30_SCORE));
    when(modelVersion.getScoreDecileThreshold()).thenReturn(ModelSpec.V5_SCORE_DECILE_THRESHOLD);
    lenient().when(feature1.apply(any())).thenReturn(123);
    lenient().when(feature2.apply(any())).thenReturn(0.123);
  }

  @Test
  void execute_success() {
    when(sageMaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(SAGEMAKER_RESPONSE);
    when(
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getVantagescore30().getScore()
    ).thenReturn(650);
    FlexScoreModelFeatureFormulaParams params =
        FlexScoreModelFeatureFormulaParams.builder().alloyReport(alloyReport).propertyInfo(propertyInfo).build();
    ModelOutput result = model.execute(modelVersion, params);

    assertEquals(0.5, result.flexScore());
    verify(sageMaker).invokeEndpoint(sageMakerRequest.capture());
    assertEquals("epS", sageMakerRequest.getValue().endpointName());
    assertEquals("text/csv", sageMakerRequest.getValue().contentType());
    assertEquals("`true`", sageMakerRequest.getValue().enableExplanations());
    assertEquals("123,0.123,650", sageMakerRequest.getValue().body().asUtf8String());
    assertEquals(Set.of("MR002", "MR009", "MR004", "MR012"), result.rejectionCodes());
  }

  @Test
  void getMemberCode() throws JsonMappingException, JsonProcessingException, IOException {
    String jsonString = new String(getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "01-input.json")
        .readAllBytes(), StandardCharsets.UTF_8);

    ObjectMapper objectMapper = new ObjectMapper()
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .registerModule(new Jdk8Module())
        .registerModule(new JavaTimeModule());

    AlloyReport alloyReport1 = objectMapper.readValue(jsonString, AlloyReport.class);

    String memberCode = model.getMemberCode(alloyReport1);
    assertEquals("4601416", memberCode);
  }

  @Test
  void execute_noVantageScore() {
    when(sageMaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(SAGEMAKER_RESPONSE);
    when(
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getVantagescore30().getScore()
    ).thenReturn(null);
    FlexScoreModelFeatureFormulaParams params =
        FlexScoreModelFeatureFormulaParams.builder().alloyReport(alloyReport).propertyInfo(propertyInfo).build();
    ModelOutput result = model.execute(modelVersion, params);

    assertEquals(-5.0, result.flexScore());
    verify(sageMaker).invokeEndpoint(sageMakerRequest.capture());
    assertEquals(Set.of("MR003"), result.rejectionCodes());
  }

  @ParameterizedTest
  @ValueSource(ints = {1, 4})
  void execute_deceasedOrThinFile(int vantageScore) {
    when(sageMaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(SAGEMAKER_RESPONSE);
    when(
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getVantagescore30().getScore()
    ).thenReturn(vantageScore);
    FlexScoreModelFeatureFormulaParams params =
        FlexScoreModelFeatureFormulaParams.builder().alloyReport(alloyReport).propertyInfo(propertyInfo).build();
    ModelOutput result = model.execute(modelVersion, params);

    assertEquals(-1.0, result.flexScore());
    verify(sageMaker).invokeEndpoint(sageMakerRequest.capture());
    assertEquals(Set.of("MR003"), result.rejectionCodes());
  }

  @Test
  void execute_invalidScore() {
    when(sageMaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(SAGEMAKER_INVALID_SCORE_RESPONSE);
    when(
        alloyReport.getFormattedResponses().getTransunionCredit().getData().getVantagescore30().getScore()
    ).thenReturn(650);
    FlexScoreModelFeatureFormulaParams params =
        FlexScoreModelFeatureFormulaParams.builder().alloyReport(alloyReport).propertyInfo(propertyInfo).build();
    Assertions.assertThrows(RuntimeException.class, () -> model.execute(modelVersion, params));
  }

  @Test
  void execute_wrongResponseContentType() {
    when(sageMaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(
        InvokeEndpointResponse.builder().contentType("text/csv").body(SdkBytes.fromUtf8String("0.5,,,")).build());
    FlexScoreModelFeatureFormulaParams params =
        FlexScoreModelFeatureFormulaParams.builder().alloyReport(alloyReport).propertyInfo(propertyInfo).build();
    assertThrows(RuntimeException.class, () -> model.execute(modelVersion, params));
  }

  @Test
  void validateDecileThreshold_notEnoughThresholds() {
    assertThrows(IllegalArgumentException.class, () -> validateDecileThreshold(
        List.of(0.026756, 0.040660, 0.056562, 0.074639, 0.095708, 0.119252, 0.149153, 0.189817, 0.252896)));
  }

  @Test
  void validateDecileThreshold_tooManyThresholds() {
    assertThrows(IllegalArgumentException.class, () -> validateDecileThreshold(
        List.of(0.026756, 0.040660, 0.056562, 0.074639, 0.095708, 0.119252, 0.149153, 0.189817, 0.252896, 0.3, 1.0)));
  }

  @Test
  void validateDecileThreshold_duplicateThreshold() {
    assertThrows(IllegalArgumentException.class, () -> validateDecileThreshold(
        List.of(0.026756, 0.040660, 0.056562, 0.074639, 0.074639, 0.119252, 0.149153, 0.189817, 0.252896, 1.0)));
  }

  @Test
  void validateDecileThreshold_decreasingThreshold() {
    assertThrows(IllegalArgumentException.class, () -> validateDecileThreshold(
        List.of(0.026756, 0.040660, 0.056562, 0.074639, 0.073639, 0.119252, 0.149153, 0.189817, 0.252896, 1.0)));
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getRejectionCodes_filterOutNegativeSt24s(boolean negativeAttribution) throws JsonProcessingException {
    JsonNode json = new ObjectMapper().readTree("""
        {
          "predictions": {
            "data": 0.5
          },
          "explanations": {
            "kernel_shap": [
              [
                {
                  "attributions": [
                    {
                      "attribution": [
                        -8.0E2
                      ]
                    }
                  ],
                  "feature_header": "months_on_file"
                },
                {
                  "attributions": [
                    {
                      "attribution": [
                        %s0.9E1
                      ]
                    }
                  ],
                  "feature_header": "eads142_st24s"
                }
              ]
            ]
          }
        }
        """.formatted(negativeAttribution ? "-" : ""));
    assertEquals(negativeAttribution ? Set.of("MR008") : Set.of("MR008", "MR017"), model.getRejectionCodes(4, json));
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      AGG504,MR003
      AT28B,MR019
      BKC320,MR007
      REV255,MR011
      AT24S,MR003
      G411S,MR004
      PAYMNT07,MR011
      G224C,MR002
      VANTAGE_SCORE,MR006
      """)
  void getRejectionCodes_featureHeaderMapping(String featureHeader, String mrCode) throws JsonProcessingException {
    JsonNode json = new ObjectMapper().readTree("""
        {
          "predictions": {
            "data": 0.5
          },
          "explanations": {
            "kernel_shap": [
              [
                {
                  "attributions": [
                    {
                      "attribution": [
                        0.9E1
                      ]
                    }
                  ],
                  "feature_header": "%s"
                }
              ]
            ]
          }
        }
        """.formatted(featureHeader));
    assertEquals(Set.of(mrCode), model.getRejectionCodes(4, json));
  }
}
