package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.verification.api.KycApi;
import com.getflex.verification.api.VerificationApi;
import com.getflex.verification.api.VerificationApi.APIgetLatestIncomeVerificationRequest;
import com.getflex.verification.client.ApiException;
import com.getflex.verification.model.Kyc;
import com.getflex.verification.model.SearchKyc200Response;
import com.getflex.verification.model.Verification;
import java.util.List;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class VerificationServiceTest {
  private static final Kyc KYC = new Kyc()
      .entityName("hubuser:1234")
      .verifiedSsnHmac512("verifiedSsnHmac512");

  @InjectMocks
  VerificationService service;

  @Mock
  KycApi kycApi;

  @Mock
  VerificationApi verificationApi;

  @Mock
  Verification verification;

  @Mock
  Kyc kyc;

  @SneakyThrows
  @Test
  void getVerification_success() {
    String verificationId = UUID.randomUUID().toString();
    when(verificationApi.getLatestIncomeVerification((APIgetLatestIncomeVerificationRequest) any())).thenReturn(
        verification);

    assertSame(verification, service.getVerification(verificationId));
  }

  @SneakyThrows
  @Test
  void getVerification_apiException() {
    String verificationId = UUID.randomUUID().toString();
    when(verificationApi.getLatestIncomeVerification((APIgetLatestIncomeVerificationRequest) any())).thenThrow(
        new ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getVerification(verificationId));
  }

  @SneakyThrows
  @Test
  void getKyc_success() {
    String kycId = UUID.randomUUID().toString();
    when(kycApi.getKyc(kycId)).thenReturn(kyc);

    assertSame(kyc, service.getKyc(kycId));
  }

  @SneakyThrows
  @Test
  void getKyc_notFound() {
    String kycId = UUID.randomUUID().toString();
    when(kycApi.getKyc(kycId)).thenThrow(new ApiException(404, "No KYC with given ID"));

    assertThrows(InternalServiceBadDataException.class, () -> service.getKyc(kycId));
  }

  @SneakyThrows
  @Test
  void getKyc_otherApiException() {
    String kycId = UUID.randomUUID().toString();
    when(kycApi.getKyc(kycId)).thenThrow(new ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getKyc(kycId));
  }

  @SneakyThrows
  @Test
  void findKycSharingSameSsn_success() {
    Kyc anotherUser = new Kyc().entityName("hubuser:1345");

    when(kycApi.searchKyc(KYC.getVerifiedSsnHmac512(), null, null)).thenReturn(
        new SearchKyc200Response().addItemsItem(KYC).nextToken("1"));
    when(kycApi.searchKyc(KYC.getVerifiedSsnHmac512(), "1", null)).thenReturn(
        new SearchKyc200Response().addItemsItem(anotherUser));

    List<Kyc> result = service.findKycSharingSameSsn(KYC);

    assertEquals(1, result.size());
    assertSame(anotherUser, result.get(0));
  }

  @SneakyThrows
  @Test
  void findKycSharingSameSsn_openApiException() {
    when(kycApi.searchKyc(KYC.getVerifiedSsnHmac512(), null, null)).thenThrow(new ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.findKycSharingSameSsn(KYC));
  }
}
