package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.utils.Metrics;
import com.getflex.rentcredibility.api.RentCredibilityApi;
import com.getflex.rentcredibility.model.ScoreResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class DsScoreServiceTest {

  @InjectMocks
  DsScoreService service;

  @Mock
  RentCredibilityApi api;

  @Mock
  Metrics metrics;

  @Test
  void getScoreTest()
      throws com.getflex.rentcredibility.client.ApiException {
    ScoreResponse resp = new ScoreResponse().score(54);
    when(api.scoreRequestV1ScorePost(any())).thenReturn(resp);

    int result = service.getBatchMemoRentCredibilityScore("batchMemo");

    assertEquals(54, result);
  }
}
