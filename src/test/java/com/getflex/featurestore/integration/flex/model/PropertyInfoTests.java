package com.getflex.featurestore.integration.flex.model;

import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.FLEX_ANYWHERE;
import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.P2P;
import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.PORTAL;
import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.REALPAGE;
import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.RENTMANAGER;
import static com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum.YARDI;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerLocationResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import java.math.BigDecimal;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;

public class PropertyInfoTests {

  @ParameterizedTest
  @ValueSource(strings = {"DIRECT_INTEGRATION", "YARDI", "REALPAGE", "FLEX_ANYWHERE"})
  void getFlexScoreSixNetworkIndicator_inNetwork(BillingIntegrationTypeEnum integrationTypeEnum) {
    assertEquals(1, new PropertyInfo(integrationTypeEnum, false, null).getFlexScoreSixNetworkIndicator());
  }

  @Test
  void getFlexScoreSixNetworkIndicator_flexAnywhere() {
    assertEquals(3, new PropertyInfo(FLEX_ANYWHERE, true, null).getFlexScoreSixNetworkIndicator());
  }

  @Test
  void getFlexScoreSixNetworkIndicator_p2p() {
    assertEquals(3, new PropertyInfo(P2P, null, null).getFlexScoreSixNetworkIndicator());
  }

  @ParameterizedTest
  @ValueSource(strings = {"UNKNOWN", "PORTAL"})
  void getFlexScoreSixNetworkIndicator_outOfNetwork(BillingIntegrationTypeEnum integrationTypeEnum) {
    assertEquals(2, new PropertyInfo(integrationTypeEnum, true, null).getFlexScoreSixNetworkIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_nullIntegration(boolean outOfNetwork) {
    assertEquals(5, new PropertyInfo(null, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_flexAnywhere(boolean outOfNetwork) {
    assertEquals(1, new PropertyInfo(FLEX_ANYWHERE, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @Test
  void getFlexScoreSixIntegrationIndicator_p2p() {
    assertEquals(1, new PropertyInfo(P2P, true, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_portal(boolean outOfNetwork) {
    assertEquals(2, new PropertyInfo(PORTAL, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_realPage(boolean outOfNetwork) {
    assertEquals(5, new PropertyInfo(REALPAGE, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_rentManager(boolean outOfNetwork) {
    assertEquals(3, new PropertyInfo(RENTMANAGER, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFlexScoreSixIntegrationIndicator_yardi(boolean outOfNetwork) {
    assertEquals(4, new PropertyInfo(YARDI, outOfNetwork, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @ValueSource(strings = {"UNKNOWN", "RESMAN", "ENTRATA", "MRI", "PARTNERMANUAL", "DIRECT_INTEGRATION"})
  void getFlexScoreSixIntegrationIndicator_otherIntegration(BillingIntegrationTypeEnum integrationType) {
    assertEquals(5, new PropertyInfo(integrationType, true, null).getFlexScoreSixIntegrationIndicator());
  }

  @ParameterizedTest
  @MethodSource("providerGetInNetworkIndicator")
  void getInNetworkIndicator(BillingIntegrationTypeEnum integrationType, boolean isOutOfNetwork, int expected) {
    PropertyInfo propertyInfo = new PropertyInfo(integrationType, isOutOfNetwork, null);
    assertEquals(expected, propertyInfo.getInNetworkIndicator());
  }

  private static Stream<Arguments> providerGetInNetworkIndicator() {
    return Stream.of(
        Arguments.of(BillingIntegrationTypeEnum.FLEX_ANYWHERE, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.FLEX_ANYWHERE, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.PORTAL, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.PORTAL, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.P2P, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.P2P, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.YARDI, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.YARDI, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.RENTMANAGER, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.RENTMANAGER, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.REALPAGE, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.REALPAGE, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.DIRECT_INTEGRATION, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.DIRECT_INTEGRATION, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.UNKNOWN, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.UNKNOWN, true, 0),
        Arguments.of(null, false, 1),
        Arguments.of(null, true, 0)
    );
  }

  @Test
  void testLocationFields() {
    PropertyInfo propertyInfoWithNullLocation = new PropertyInfo(
        BillingIntegrationTypeEnum.DIRECT_INTEGRATION, false, null);
    assertNull(propertyInfoWithNullLocation.location());

    ComGetflexBillingControllerV2PropertyControllerLocationResponse location =
        new ComGetflexBillingControllerV2PropertyControllerLocationResponse()
            .address1("123 Main St")
            .address2("Apt 4B")
            .city("San Francisco")
            .state("CA")
            .zip("94105")
            .latitude(new BigDecimal("37.7749"))
            .longitude(new BigDecimal("-122.4194"));
    
    PropertyInfo propertyInfoWithLocation = new PropertyInfo(
        BillingIntegrationTypeEnum.YARDI, true, location);
    
    assertEquals(BillingIntegrationTypeEnum.YARDI, propertyInfoWithLocation.integrationType());
    assertEquals(true, propertyInfoWithLocation.isOutOfNetwork());
    
    assertEquals("123 Main St", propertyInfoWithLocation.location().getAddress1());
    assertEquals("Apt 4B", propertyInfoWithLocation.location().getAddress2());
    assertEquals("San Francisco", propertyInfoWithLocation.location().getCity());
    assertEquals("CA", propertyInfoWithLocation.location().getState());
    assertEquals("94105", propertyInfoWithLocation.location().getZip());
    assertEquals(new BigDecimal("37.7749"), propertyInfoWithLocation.location().getLatitude());
    assertEquals(new BigDecimal("-122.4194"), propertyInfoWithLocation.location().getLongitude());
  }

}
