package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.useraccount.api.UserAccountApi;
import com.getflex.useraccount.client.ApiException;
import com.getflex.useraccount.model.UserAccount;
import com.getflex.useraccount.model.UserAccountResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class UserAccountServiceTest {

  @InjectMocks
  UserAccountService service;

  @Mock
  UserAccountApi api;

  UserAccountResponse userAccountResponse = new UserAccountResponse().userAccount(
      new UserAccount().customerPublicId("customer_public_id").email("<EMAIL>").firstName("firstName").lastName(
          "LastName").emailVerified(true));

  @Test
  void testGetUserAccount() throws ApiException {
    when(api.getUserAccountByPublicId("customer_public_id")).thenReturn(userAccountResponse);
    UserAccount result = service.getUserAccountByPublicId("customer_public_id");
    assertEquals("<EMAIL>", result.getEmail());
    assertEquals("firstName", result.getFirstName());
    assertEquals("LastName", result.getLastName());
    assertEquals("customer_public_id", result.getCustomerPublicId());
    assertEquals(true, result.getEmailVerified());
  }
}
