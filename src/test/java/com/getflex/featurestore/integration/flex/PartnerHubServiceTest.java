package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.IndefiniteHubUserException;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.partnerhub.client.ApiException;
import com.getflex.partnerhub.model.User;
import com.getflex.partnerhub.openapi.PrivateApi;
import java.util.List;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PartnerHubServiceTest {
  private static final long HUB_USER_ID = 1234L;

  @InjectMocks
  PartnerHubService service;

  @Mock
  PrivateApi privateApi;

  @Mock
  User user;

  @Mock
  User anotherUser;

  @SneakyThrows
  @Test
  void getHubUser_success() {
    when(privateApi.getUser(HUB_USER_ID)).thenReturn(user);

    assertSame(user, service.getHubUser(HUB_USER_ID));
  }

  @SneakyThrows
  @Test
  void getHubUser_apiException() {
    when(privateApi.getUser(HUB_USER_ID)).thenThrow(new ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getHubUser(HUB_USER_ID));
  }

  @SneakyThrows
  @Test
  void getHubUserByPmcId_success() {
    when(privateApi.getUsersForPmc(123L, true)).thenReturn(List.of(user));

    assertSame(user, service.getHubUserByPmcId(123L).get());
  }

  @SneakyThrows
  @Test
  void getHubUserByPmcId_indefiniteHubUser() {
    when(privateApi.getUsersForPmc(123L, true)).thenReturn(List.of(user, anotherUser));

    assertThrows(IndefiniteHubUserException.class, () -> service.getHubUserByPmcId(123L));
  }

  @SneakyThrows
  @Test
  void getHubUserByPmcId_apiException() {
    when(privateApi.getUsersForPmc(123L, true)).thenThrow(new ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getHubUserByPmcId(123L));
  }
}
