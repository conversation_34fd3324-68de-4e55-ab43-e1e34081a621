package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.fulfillment.api.FulfillmentsApi;
import com.getflex.fulfillment.client.ApiException;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FulfillmentServiceTest {
  @InjectMocks
  FulfillmentService service;

  @Mock
  FulfillmentsApi api;


  @Test
  void getStatusCode() throws ApiException {
    GetStatusResponseV2 response = new GetStatusResponseV2().codes(null);
    when(api.getStatus2(any())).thenReturn(response);

    var result = service.getStatusCode("test");

    assertEquals(response, result);
  }

  @Test
  void getStatusCode__failureGeneric() throws ApiException {
    when(api.getStatus2(any()))
        .thenThrow(new ApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "error"));
    try {
      service.getStatusCode("test");
    } catch (InternalDependencyFailureException ignored) {
      return;
    }
  }

}
