package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.ERROR_METRIC;
import static com.getflex.featurestore.utils.ObservabilityConstants.SUCCESS_METRIC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.getflex.autopay.api.AutopayApi;
import com.getflex.autopay.api.AutopayApi.APIqueryAutopayTasksByBillPayDateRequest;
import com.getflex.autopay.api.AutopayApi.APIqueryAutopayTasksByBillPayDateRequest.Builder;
import com.getflex.autopay.client.ApiException;
import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.autopay.model.WalletConfigurationResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class AutopayServiceTest {

  @InjectMocks
  AutopayService service;

  @Mock
  AutopayApi api;

  @Mock
  Metrics metrics;

  @Test
  void testGetCustomerWalletConfigured() throws ApiException {
    Long customerId = 1L;
    when(api.getWalletConfiguration(customerId)).thenReturn(new WalletConfigurationResponse().walletConfigured(true));
    doNothing().when(metrics).increment(
        AutopayService.class,
        String.format(SUCCESS_METRIC, "getCustomerWalletConfiguration")
    );
    Boolean result = service.getCustomerWalletConfigured(customerId);
    assert (result);
  }

  @Test
  void testGetCustomerWalletConfiguredException() throws ApiException {
    Long customerId = 1L;
    when(api.getWalletConfiguration(customerId)).thenThrow(new ApiException());
    doNothing().when(metrics).increment(
        AutopayService.class,
        String.format(ERROR_METRIC, "getCustomerWalletConfiguration")
    );
    try {
      service.getCustomerWalletConfigured(customerId);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch the wallet configuration for customer id 1");
    }
  }

  @Test
  void testGetCurrentAutoPay() throws ApiException {
    String customerPublicId = "test";
    LocalDate bpDate = LocalDate.of(2022, 1, 1);
    ArgumentCaptor<APIqueryAutopayTasksByBillPayDateRequest> requestCaptor = ArgumentCaptor.forClass(
        APIqueryAutopayTasksByBillPayDateRequest.class);
    when(api.queryAutopayTasksByBillPayDate(requestCaptor.capture()))
        .thenReturn(new SearchAutopayTasksResponse().autopayTasks(List.of(
            new GetAutopayTaskResponse().billTransactionId("btx1"),
            new GetAutopayTaskResponse().billTransactionId("btx2"))));
    SearchAutopayTasksResponse result = service.getCurrentAutoPay(customerPublicId, bpDate);
    assertEquals(result.getAutopayTasks().size(), 2);
  }

  @Test
  void testGetCurrentAutoPayException() throws ApiException {
    String customerPublicId = "test";
    LocalDate bpDate = LocalDate.of(2022, 1, 1);
    ArgumentCaptor<APIqueryAutopayTasksByBillPayDateRequest> requestCaptor = ArgumentCaptor.forClass(
        APIqueryAutopayTasksByBillPayDateRequest.class);
    when(api.queryAutopayTasksByBillPayDate(requestCaptor.capture()))
        .thenThrow(new ApiException());
    try {
      service.getCurrentAutoPay(customerPublicId, bpDate);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch autopay for customerPublicId test");
    }
  }
}
