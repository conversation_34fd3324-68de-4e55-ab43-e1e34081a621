package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.SUCCESS_METRIC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.getflex.cipher.util.CipherUtil;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.identity.api.IdentityApi;
import com.getflex.identity.api.v2.IdentityV2Api;
import com.getflex.identity.client.ApiException;
import com.getflex.identity.model.AccountBillerDto;
import com.getflex.identity.model.AccountSearchData;
import com.getflex.identity.model.AccountSearchResult;
import com.getflex.identity.model.AgType;
import com.getflex.identity.model.BillerAccountRecord;
import com.getflex.identity.model.BillerAccountStatusEnumDto;
import com.getflex.identity.model.BillerAccountStatusRecord;
import com.getflex.identity.model.BillerAccountStatusRecord.StatusEnum;
import com.getflex.identity.model.BillerDto;
import com.getflex.identity.model.BillerTypeEnumDto;
import com.getflex.identity.model.CustomerAgreementRecord;
import com.getflex.identity.model.CustomerLookup;
import com.getflex.identity.model.CustomerLookupWithNullableFields;
import com.getflex.identity.model.DuplicateAddress;
import com.getflex.identity.model.FinancialPartnerRecord;
import com.getflex.identity.model.FinancialPartnerType;
import com.getflex.identity.model.GetAccountResponse;
import com.getflex.identity.model.GetBillerAccountStatusHistoryResponse;
import com.getflex.identity.model.GetCustomerAgreementFilters;
import com.getflex.identity.model.GetCustomerAgreementRequest;
import com.getflex.identity.model.GetCustomerAgreementResponse;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.identity.model.GetPropertyNameResponse;
import com.getflex.identity.model.LookupCustomerResponse;
import com.getflex.identity.model.SearchFinancialPartnerRequest;
import com.getflex.identity.model.SearchFinancialPartnerResponse;
import com.getflex.identity.model.v2.BillerAccountSearchRequest;
import com.getflex.identity.model.v2.BillerAccountSearchResponse;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IdentityServiceTest {

  @InjectMocks IdentityService service;

  @Mock IdentityApi api;

  @Mock IdentityV2Api v2Api;

  @Mock CipherUtil cipherUtil;

  @Mock Metrics metrics;

  GetCustomerResponse customer = new GetCustomerResponse().customerId(3L).email("<EMAIL>");

  GetAccountResponse billerAccount =
      new GetAccountResponse()
          .currentAccount(
              new AccountBillerDto()
                  .biller(
                      new BillerDto().billerType(BillerTypeEnumDto.OUT_OF_NETWORK).billerId(1L)));

  @Test
  void testGetCustomer() throws ApiException {
    when(api.getCustomer(3L)).thenReturn(customer);
    doNothing()
        .when(metrics)
        .increment(IdentityService.class, String.format(SUCCESS_METRIC, "getCustomer"));
    var result = service.getCustomer(3L);
    assertEquals("<EMAIL>", result.getEmail());
  }

  @Test
  void getBillerAccount_success() throws com.getflex.identity.client.v2.ApiException {
    ExtendedBillerAccountData billerAccount = new ExtendedBillerAccountData();
    when(v2Api.searchBillerAccounts(new BillerAccountSearchRequest().billerAccountId(123L)))
        .thenReturn(new BillerAccountSearchResponse().addBillerAccountsItem(billerAccount));

    ExtendedBillerAccountData result = service.getBillerAccount(123L);

    assertSame(billerAccount, result);
  }

  @Test
  void getBillerAccount_failure() throws com.getflex.identity.client.v2.ApiException {
    when(v2Api.searchBillerAccounts((BillerAccountSearchRequest) any()))
        .thenThrow(new com.getflex.identity.client.v2.ApiException());

    assertThrows(InternalDependencyFailureException.class, () -> service.getBillerAccount(123L));
  }

  @Test
  void getBillerByBillerAccountId_success() throws ApiException {
    BillerDto biller = new BillerDto();
    when(api.getBillerAccountById(123L))
        .thenReturn(new GetAccountResponse().currentAccount(new AccountBillerDto().biller(biller)));

    BillerDto result = service.getBillerByBillerAccountId(123L);

    assertSame(biller, result);
  }

  @Test
  void getBillerByBillerAccountId_failure() throws ApiException {
    when(api.getBillerAccountById(123L)).thenThrow(new ApiException());

    assertThrows(
        InternalDependencyFailureException.class, () -> service.getBillerByBillerAccountId(123L));
  }

  @Test
  void getBillerAccountsByCustomerId_success() throws com.getflex.identity.client.v2.ApiException {
    BillerAccountSearchRequest request = new BillerAccountSearchRequest().customerId(123L);
    BillerAccountSearchResponse response =
        new BillerAccountSearchResponse()
            .addBillerAccountsItem(
                new ExtendedBillerAccountData()
                    .customerId(123L)
                    .status(BillerAccountStatusEnumDto.ACTIVE.name()));
    when(v2Api.searchBillerAccounts(request)).thenReturn(response);
    var result = service.getBillerAccountsByCustomerId(123L);
    assertSame(
        request.getCustomerId(),
        Objects.requireNonNull(response.getBillerAccounts()).get(0).getCustomerId());
    assertEquals(
        BillerAccountStatusEnumDto.ACTIVE.name(),
        Objects.requireNonNull(result.get(0).getStatus()));
  }

  @Test
  void getBillerAccountsByCustomerId_notFound() throws com.getflex.identity.client.v2.ApiException {
    BillerAccountSearchRequest request = new BillerAccountSearchRequest().customerId(123L);
    when(v2Api.searchBillerAccounts(request))
        .thenThrow(
            new com.getflex.identity.client.v2.ApiException(
                HttpStatus.NOT_FOUND.value(), "Not found"));
    var result = service.getBillerAccountsByCustomerId(123L);
    assertEquals(0, result.size());
  }

  @Test
  void getBillerAccountsByCustomerId_failure() throws com.getflex.identity.client.v2.ApiException {
    BillerAccountSearchRequest request = new BillerAccountSearchRequest().customerId(123L);
    when(v2Api.searchBillerAccounts(request))
        .thenThrow(new com.getflex.identity.client.v2.ApiException());
    assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getBillerAccountsByCustomerId(123L));
  }

  @Test
  void getBillerAccountStatusHistory_success() throws ApiException {
    GetBillerAccountStatusHistoryResponse response =
        new GetBillerAccountStatusHistoryResponse()
            .addBillerAccountStatusRecordsItem(
                new BillerAccountStatusRecord().customerId(123L).status(StatusEnum.CLOSED));
    when(api.getBillerAccountStatusHistory(123L)).thenReturn(response);
    var result = service.getBillerAccountStatusHistory(123L);
    assertEquals(StatusEnum.CLOSED, result.get(0).getStatus());
  }

  @Test
  void getBillerAccountStatusHistory_notFound() throws ApiException {
    when(api.getBillerAccountStatusHistory(123L))
        .thenThrow(new ApiException(HttpStatus.NOT_FOUND.value(), "Not found"));
    var result = service.getBillerAccountStatusHistory(123L);
    assertEquals(0, result.size());
  }

  @Test
  void getBillerAccountStatusHistory_failure() throws ApiException {
    when(api.getBillerAccountStatusHistory(123L)).thenThrow(new ApiException());
    assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getBillerAccountStatusHistory(123L));
  }

  @Test
  void getCompleteCustomerInfo_success() throws ApiException {
    EvalParams evalParams =
        new EvalParams().customerId(3L).dobCipher("EncryptedDOB").ssnCipher("EncryptedSSN");
    when(api.lookupCustomer(new CustomerLookupWithNullableFields().customerId(3L)))
        .thenReturn(new LookupCustomerResponse().firstName("testUser"));
    when(cipherUtil.decryptWithCache("EncryptedDOB")).thenReturn("DOB");
    when(cipherUtil.decryptWithCache("EncryptedSSN")).thenReturn("SSN");

    var result = service.getCompleteCustomerInfo(evalParams);

    assertEquals("DOB", result.getDateOfBirth());
    assertEquals("SSN", result.getSsn());
    assertEquals("testUser", result.getFirstName());
  }

  @Test
  void getCompleteCustomerInfo_failure() throws ApiException {
    EvalParams evalParams = new EvalParams().customerId(3L);
    when(api.lookupCustomer(new CustomerLookupWithNullableFields().customerId(3L)))
        .thenThrow(new ApiException());

    assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getCompleteCustomerInfo(evalParams));
  }

  @Test
  void getPropertyNameByBillerAccountId() throws ApiException {
    when(api.getPropertyName(123L, null))
        .thenReturn(new GetPropertyNameResponse().propertyName("testPropertyName"));
    String result = service.getPropertyNameByBillerAccountId(123L);
    assertEquals(result, "testPropertyName");
  }

  @Test
  void getPropertyNameByBillerAccountId_NotFoundException() throws ApiException {
    when(api.getPropertyName(123L, null)).thenThrow(new ApiException(404, "Not Found"));

    String result = service.getPropertyNameByBillerAccountId(123L);
    assertEquals(result, "");
  }

  @Test
  void getPropertyNameByBillerAccountId_Exception() throws ApiException {
    when(api.getPropertyName(123L, null)).thenThrow(new ApiException());

    assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getPropertyNameByBillerAccountId(123L));
  }

  @Test
  void getActiveBillerAccountByCustomerId() throws ApiException {
    when(api.accountSearch(any(AccountSearchData.class)))
        .thenReturn(
            new AccountSearchResult()
                .accounts(List.of(new BillerAccountRecord().billerAccountId(123L))));

    BillerAccountRecord result = service.getActiveBillerAccountByCustomerId(123L);
    assertNotNull(result);
    assertEquals(123L, result.getBillerAccountId());
  }

  @Test
  void getCustomerFlexAnywhereDuplicatedAddress() throws ApiException {
    when(api.customersWithDuplicateAddresses(any()))
        .thenReturn(List.of(new DuplicateAddress().customerId(123L)));

    Integer result = service.getCustomerFlexAnywhereDuplicatedAddress(123L);
    assertEquals(1, result);
  }

  @Test
  void getFinancialPartner() throws ApiException {
    when(api.searchFinancialPartner(
            new SearchFinancialPartnerRequest().lookup(new CustomerLookup().customerId(1L))))
        .thenReturn(
            new SearchFinancialPartnerResponse()
                .financialPartners(
                    List.of(
                        new FinancialPartnerRecord()
                            .historical(false)
                            .financialPartnerType(FinancialPartnerType.BLUE_RIDGE_BANK))));
    FinancialPartnerType result = service.getFinancialPartner(1L);
    assertEquals(FinancialPartnerType.BLUE_RIDGE_BANK, result);
  }

  @Test
  void getFinancialPartner_NullFinancialPartners() throws ApiException {
    when(api.searchFinancialPartner(
            new SearchFinancialPartnerRequest().lookup(new CustomerLookup().customerId(1L))))
        .thenReturn(new SearchFinancialPartnerResponse().financialPartners(null));
    FinancialPartnerType result = service.getFinancialPartner(1L);
    assertEquals(FinancialPartnerType.NULL, result);
  }

  @Test
  void getFinancialPartner_NullHistorical() throws ApiException {
    when(api.searchFinancialPartner(
            new SearchFinancialPartnerRequest().lookup(new CustomerLookup().customerId(1L))))
        .thenReturn(
            new SearchFinancialPartnerResponse()
                .financialPartners(
                    List.of(
                        new FinancialPartnerRecord()
                            .historical(null)
                            .financialPartnerType(FinancialPartnerType.BLUE_RIDGE_BANK))));
    FinancialPartnerType result = service.getFinancialPartner(1L);
    assertEquals(FinancialPartnerType.NULL, result);
  }

  @Test
  void getFinancialPartner_NoFinancialPartners() throws ApiException {
    when(api.searchFinancialPartner(
            new SearchFinancialPartnerRequest().lookup(new CustomerLookup().customerId(1L))))
        .thenReturn(new SearchFinancialPartnerResponse());
    FinancialPartnerType result = service.getFinancialPartner(1L);
    assertEquals(FinancialPartnerType.NULL, result);
  }

  @Test
  void getFinancialPartner_Exception() throws ApiException {
    when(api.searchFinancialPartner(
            new SearchFinancialPartnerRequest().lookup(new CustomerLookup().customerId(1L))))
        .thenThrow(new ApiException());
    assertThrows(InternalDependencyFailureException.class, () -> service.getFinancialPartner(1L));
  }

  @Test
  void getCustomerAgreements() throws ApiException {
    List<AgType> agTypes = List.of(AgType.STRIPE_CONNECT_AGREEMENT);
    GetCustomerAgreementRequest getCustomerAgreementRequest =
        new GetCustomerAgreementRequest()
            .customerLookup(new CustomerLookup().customerId(1L))
            .filters(
                new GetCustomerAgreementFilters()
                    .agreementTypes(agTypes)
                    .newestCustomerAgreementsOnly(true));
    List<CustomerAgreementRecord> customerAgreementRecords =
        List.of(new CustomerAgreementRecord().agreementType(AgType.STRIPE_CONNECT_AGREEMENT));
    GetCustomerAgreementResponse customerAgreementResponse =
        new GetCustomerAgreementResponse().customerAgreements(customerAgreementRecords);
    when(api.getCustomerAgreements(getCustomerAgreementRequest))
        .thenReturn(customerAgreementResponse);

    List<CustomerAgreementRecord> result = service.getCustomerAgreements(1L, agTypes);
    assertEquals(customerAgreementRecords, result);
  }

  @Test
  void getCustomerAgreements_NotFoundException() throws ApiException {
    List<AgType> agTypes = List.of(AgType.STRIPE_CONNECT_AGREEMENT);
    GetCustomerAgreementRequest getCustomerAgreementRequest =
        new GetCustomerAgreementRequest()
            .customerLookup(new CustomerLookup().customerId(1L))
            .filters(
                new GetCustomerAgreementFilters()
                    .agreementTypes(agTypes)
                    .newestCustomerAgreementsOnly(true));
    when(api.getCustomerAgreements(getCustomerAgreementRequest))
        .thenThrow(new ApiException(404, "Not Found"));
    List<CustomerAgreementRecord> result = service.getCustomerAgreements(1L, agTypes);
    assertEquals(List.of(), result);
  }

  @Test
  void getCustomerAgreements_Exception() throws ApiException {
    List<AgType> agTypes = List.of(AgType.STRIPE_CONNECT_AGREEMENT);
    GetCustomerAgreementRequest getCustomerAgreementRequest =
        new GetCustomerAgreementRequest()
            .customerLookup(new CustomerLookup().customerId(1L))
            .filters(
                new GetCustomerAgreementFilters()
                    .agreementTypes(agTypes)
                    .newestCustomerAgreementsOnly(true));
    when(api.getCustomerAgreements(getCustomerAgreementRequest)).thenThrow(new ApiException());
    assertThrows(
        InternalDependencyFailureException.class, () -> service.getCustomerAgreements(1L, agTypes));
  }
}
