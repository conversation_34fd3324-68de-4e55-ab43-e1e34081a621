package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.offerv2.api.OfferApi;
import com.getflex.offerv2.api.OfferApi.APIfindOffersBySsnRequest;
import com.getflex.offerv2.api.OfferApi.APIgetCustomerOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIgetFirstEverAcceptedOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIgetOfferByBillerAccountRequest;
import com.getflex.offerv2.api.OfferApi.APIgetRootOfferRequest;
import com.getflex.offerv2.api.OfferApi.APIsearchOfferRequest;
import com.getflex.offerv2.client.ApiException;
import com.getflex.offerv2.model.FindOffersBySsnResponse;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class OfferServiceTest {
  @InjectMocks
  OfferService service;
  @Mock
  OfferApi api;

  InternalOffer offer1 = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED).customerId(10L);
  InternalOffer offer2 = new InternalOffer().offerId("test-id-2").offerState(OfferStateEnum.REJECTED).customerId(10L);

  @Test
  void getOffer() throws ApiException {
    when(api.getOffer("123", null)).thenReturn(offer1);

    InternalOffer result = service.getOffer("123", null);

    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getOffer__failure() throws ApiException {
    when(api.getOffer("123", null))
        .thenThrow(new ApiException("error"));

    try {
      service.getOffer("123", null);
    } catch (InternalDependencyFailureException e) {
      assertEquals("Could not fetch offers using ID 123", e.getMessage());
    }
  }

  @Test
  void getOffer__notFound() throws ApiException {
    when(api.getOffer("123", null)).thenThrow(new ApiException(404, "error"));

    assertNull(service.getOffer("123", null));
  }

  @Test
  void getFirstEverAcceptedOffer() throws ApiException {
    ArgumentCaptor<APIgetFirstEverAcceptedOfferRequest> requestCaptor = ArgumentCaptor
        .forClass(APIgetFirstEverAcceptedOfferRequest.class);
    when(api.getFirstEverAcceptedOffer(requestCaptor.capture())).thenReturn(offer1);

    InternalOffer result = service.getFirstEverAcceptedOffer(10L);

    assertEquals(10L, requestCaptor.getValue().customerId());
    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getFirstEverAcceptedOffer__failure() throws ApiException {
    when(api.getFirstEverAcceptedOffer(Mockito.<OfferApi.APIgetFirstEverAcceptedOfferRequest>any()))
        .thenThrow(new ApiException("error"));

    try {
      service.getFirstEverAcceptedOffer(10L);
    } catch (InternalDependencyFailureException e) {
      assertEquals("Could not fetch first ever accepted offer for customerId 10",
          e.getMessage());
    }
  }

  @Test
  void getFirstEverAcceptedOffer__notFound() throws ApiException {
    when(api.getFirstEverAcceptedOffer(Mockito.<OfferApi.APIgetFirstEverAcceptedOfferRequest>any()))
        .thenThrow(new ApiException(404, "error"));

    InternalOffer result = service.getFirstEverAcceptedOffer(10L);
    assertNull(result);
  }

  @Test
  void searchOffer() throws ApiException {
    ArgumentCaptor<APIsearchOfferRequest> requestCaptor = ArgumentCaptor.forClass(APIsearchOfferRequest.class);
    when(api.searchOffer(requestCaptor.capture())).thenReturn(List.of(offer1, offer2));

    List<InternalOffer> result = service.searchOffer(10L);

    assertEquals(10L, requestCaptor.getValue().customerId());
    assertEquals("test-id-1", result.get(0).getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.get(0).getOfferState());
    assertEquals("test-id-2", result.get(1).getOfferId());
    assertEquals(OfferStateEnum.REJECTED, result.get(1).getOfferState());
  }

  @Test
  void searchOffer__failure() throws ApiException {
    when(api.searchOffer(Mockito.<OfferApi.APIsearchOfferRequest>any())).thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.searchOffer(10L));
  }

  @Test
  void searchOfferAllVersions() throws ApiException {
    ArgumentCaptor<APIsearchOfferRequest> requestCaptor = ArgumentCaptor.forClass(APIsearchOfferRequest.class);
    when(api.searchOffer(requestCaptor.capture())).thenReturn(List.of(offer1, offer2));

    List<InternalOffer> result = service.searchOffer(10L, true);

    assertEquals(10L, requestCaptor.getValue().customerId());
    assertEquals("test-id-1", result.get(0).getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.get(0).getOfferState());
    assertEquals("test-id-2", result.get(1).getOfferId());
    assertEquals(OfferStateEnum.REJECTED, result.get(1).getOfferState());
  }

  @Test
  void searchOfferAllVersions__failure() throws ApiException {
    when(api.searchOffer(Mockito.<OfferApi.APIsearchOfferRequest>any())).thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.searchOffer(10L, true));
  }

  @Test
  void getOfferByBillerAccountId() throws ApiException {
    ArgumentCaptor<APIgetOfferByBillerAccountRequest> requestCaptor = ArgumentCaptor
        .forClass(APIgetOfferByBillerAccountRequest.class);
    when(api.getOfferByBillerAccount(requestCaptor.capture())).thenReturn(offer1);

    InternalOffer result = service.getOfferByBillerAccountId(1L);

    assertEquals(1L, requestCaptor.getValue().billerAccountId());
    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getOfferByBillerAccountId__failure() throws ApiException {
    when(api.getOfferByBillerAccount(Mockito.<OfferApi.APIgetOfferByBillerAccountRequest>any()))
        .thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getOfferByBillerAccountId(1L));
  }

  @Test
  void getRootOffer() throws ApiException {
    ArgumentCaptor<APIgetRootOfferRequest> requestCaptor = ArgumentCaptor
        .forClass(APIgetRootOfferRequest.class);
    when(api.getRootOffer(requestCaptor.capture())).thenReturn(offer1);

    InternalOffer result = service.getRootOffer("test-id-1", 2L);

    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getRootOffer__failure() throws ApiException {
    when(api.getRootOffer(Mockito.<OfferApi.APIgetRootOfferRequest>any())).thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getRootOffer("test-id-1", 2L));
  }

  @Test
  void getOfferByBillerAccountIdAndOfferVersion() throws ApiException {
    ArgumentCaptor<APIgetOfferByBillerAccountRequest> requestCaptor = ArgumentCaptor
        .forClass(APIgetOfferByBillerAccountRequest.class);
    when(api.getOfferByBillerAccount(requestCaptor.capture())).thenReturn(offer1);

    InternalOffer result = service.getOfferByBillerAccountIdAndOfferVersion(1L, 2L);

    assertEquals(1L, requestCaptor.getValue().billerAccountId());
    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getOfferByBillerAccountIdAndOfferVersion__failure() throws ApiException {
    when(api.getOfferByBillerAccount(Mockito.<OfferApi.APIgetOfferByBillerAccountRequest>any()))
        .thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service
        .getOfferByBillerAccountIdAndOfferVersion(1L, 2L));
  }

  @Test
  void getOfferByBillerAccountIdAndOfferVersion__notFound() throws ApiException {
    when(api.getOfferByBillerAccount(Mockito.<OfferApi.APIgetOfferByBillerAccountRequest>any())).thenThrow(
        new ApiException(404, "error"));

    assertNull(service.getOfferByBillerAccountIdAndOfferVersion(1L, 2L));
  }

  @Test
  void getOfferByCustomerId() throws ApiException {
    ArgumentCaptor<APIgetCustomerOfferRequest> requestCaptor = ArgumentCaptor
        .forClass(APIgetCustomerOfferRequest.class);
    when(api.getCustomerOffer(requestCaptor.capture())).thenReturn(offer1);

    InternalOffer result = service.getOfferByCustomerId(10L);

    assertEquals(10L, requestCaptor.getValue().customerId());
    assertEquals("test-id-1", result.getOfferId());
    assertEquals(OfferStateEnum.ACCEPTED, result.getOfferState());
  }

  @Test
  void getOfferByCustomerId__failure() throws ApiException {
    when(api.getCustomerOffer(Mockito.<OfferApi.APIgetCustomerOfferRequest>any()))
        .thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getOfferByCustomerId(10L));
  }

  @Test
  void getOfferBySsnHmac() throws ApiException {
    ArgumentCaptor<APIfindOffersBySsnRequest> requestCaptor = ArgumentCaptor
        .forClass(APIfindOffersBySsnRequest.class);
    when(api.findOffersBySsn(requestCaptor.capture()))
        .thenReturn(new FindOffersBySsnResponse().offers(List.of(offer1)));

    List<InternalOffer> offers = service.getOfferBySsnHmac(1L, "123");

    assertEquals(1L, requestCaptor.getValue().customerId());
    assertEquals("123", requestCaptor.getValue().ssnHmac());
    assertEquals(1, offers.size());
  }

  @Test
  void getOfferBySsnHmac__failure() throws ApiException {
    when(api.findOffersBySsn(Mockito.any()))
        .thenThrow(new ApiException("error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getOfferBySsnHmac(1L, "123"));
  }
}
