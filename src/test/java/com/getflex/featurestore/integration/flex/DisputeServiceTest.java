package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.getflex.dispute.api.DisputeApi;
import com.getflex.dispute.model.Dispute;
import com.getflex.dispute.model.SearchDisputeResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.offerv2.client.ApiException;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class DisputeServiceTest {
  @InjectMocks
  DisputeService service;

  @Mock
  DisputeApi api;

  @Test
  void getLostDisputesTest() throws ApiException, com.getflex.dispute.client.ApiException {
    SearchDisputeResponse disputeResponse = new SearchDisputeResponse().disputes(List.of(new Dispute().id(1L)));
    when(api.disputesByCustomer(10L, List.of(), List.of("Lost"), null, null)).thenReturn(disputeResponse);

    var result = service.getLostDisputes(10L);

    assertEquals(1, result.size());
  }

  @Test
  void getLostDisputesTest__failure404() throws ApiException, com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), List.of("Lost"), null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getLostDisputes(10L);

    assertEquals(0, result.size());
  }

  @Test
  void getLostDisputesTest__failureGeneric() throws ApiException, com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), List.of("Lost"), null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.BAD_REQUEST.value(), "error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getLostDisputes(10L));
  }

  @Test
  void getTotalNumberOfDispute() throws ApiException, com.getflex.dispute.client.ApiException {
    SearchDisputeResponse disputeResponse = new SearchDisputeResponse().disputes(List.of(new Dispute().id(1L)));
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenReturn(disputeResponse);

    var result = service.getTotalNumberOfDispute(10L, null);

    assertEquals(1, result);
  }

  @Test
  void getTotalNumberOfDispute__emptyList() throws ApiException, com.getflex.dispute.client.ApiException {
    SearchDisputeResponse disputeResponse = new SearchDisputeResponse().disputes(null);
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null)).thenReturn(disputeResponse);

    var result = service.getTotalNumberOfDispute(10L, null);

    assertEquals(0, result);
  }

  @Test
  void getTotalNumberOfDispute__failure404() throws ApiException, com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getTotalNumberOfDispute(10L, null);

    assertEquals(0, result);
  }

  @Test
  void getTotalNumberOfDispute__failureGeneric() throws ApiException, com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.BAD_REQUEST.value(), "error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getTotalNumberOfDispute(10L, null));
  }


  @Test
  void getAllDisputesByCustomerId() throws com.getflex.dispute.client.ApiException {
    SearchDisputeResponse disputeResponse = new SearchDisputeResponse().disputes(List.of(new Dispute().id(1L)));
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenReturn(disputeResponse);

    var result = service.getAllDisputesByCustomerId(10L);

    assertEquals(1, result.size());
  }

  @Test
  void getAllDisputesByCustomerId__failure404() throws com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.NOT_FOUND.value(), "error"));

    var result = service.getAllDisputesByCustomerId(10L);

    assertEquals(0, result.size());
  }

  @Test
  void getAllDisputesByCustomerId__failureGeneric() throws com.getflex.dispute.client.ApiException {
    when(api.disputesByCustomer(10L, List.of(), DisputeService.ALL_STATUSES, null, null))
        .thenThrow(new com.getflex.dispute.client.ApiException(HttpStatus.BAD_REQUEST.value(), "error"));

    assertThrows(InternalDependencyFailureException.class, () -> service.getAllDisputesByCustomerId(10L));
  }

}
