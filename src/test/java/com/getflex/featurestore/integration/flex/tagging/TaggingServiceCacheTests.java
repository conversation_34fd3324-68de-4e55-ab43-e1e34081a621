package com.getflex.featurestore.integration.flex.tagging;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.CacheConfig;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.flex.tagging.TaggingService.TaggingNamespace;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.tagging.api.TaggingApi;
import com.getflex.tagging.client.ApiException;
import com.getflex.tagging.model.CustomerTagDataItem;
import com.getflex.tagging.model.GetCustomerTagListResponse;
import com.getflex.tagging.model.GetCustomerTagResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;

@SpringBootTest(classes = {CacheConfig.class, TaggingServiceCacheTests.Config.class})
@ContextConfiguration
class TaggingServiceCacheTests {

  @Configuration
  public static class Config {

    @MockBean
    private Metrics metrics;

    @MockBean
    private TaggingApi taggingApi;

    @MockBean
    private ServiceConfig serviceConfig;

    @Bean
    TaggingService taggingService() {
      return new TaggingService(taggingApi, metrics, serviceConfig);
    }
  }

  @Autowired
  Metrics metrics;
  @Autowired
  TaggingApi taggingApi;
  @Autowired
  ServiceConfig serviceConfig;

  @Autowired
  TaggingService taggingService;

  @Test
  void getFraudTags_ShouldUseCache() throws ApiException {
    Long customerId = 1L;

    List<CustomerTagDataItem> mockedResponseTags1 = List.of(
        new CustomerTagDataItem().tagName("tag1").tagNamespaceId(100L)
    );
    List<CustomerTagDataItem> mockedResponseTags2 = List.of(
        new CustomerTagDataItem().tagName("tag2").tagNamespaceId(100L)
    );
    when(serviceConfig.getTaggingNamespaceIds()).thenReturn(new HashMap<>(Map.of(TaggingNamespace.RISK_FRAUD, 100L)));
    when(taggingApi.getAllTagsForCustomer(eq(customerId), anyLong(), anyList(), eq(true)))
        .thenReturn(new GetCustomerTagListResponse().tags(mockedResponseTags1),
            new GetCustomerTagListResponse().tags(mockedResponseTags2));

    List<CustomerTagDataItem> firstCallResult = taggingService.getFraudTags(customerId);
    assertEquals(mockedResponseTags1, firstCallResult);

    // assert second call still using cached value.
    List<CustomerTagDataItem> secondCallResult = taggingService.getFraudTags(customerId);
    assertEquals(mockedResponseTags1, secondCallResult);

    // assert external API is called once.
    verify(taggingApi, times(1)).getAllTagsForCustomer(eq(customerId), anyLong(), anyList(), eq(true));

  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void getFraudTags_Success(boolean hasTag) throws ApiException {
    Long customerId = 2L;

    List<CustomerTagDataItem> mockedResponseTags1 = List.of(
        new CustomerTagDataItem().tagName("tag1").tagNamespaceId(100L)
    );
    GetCustomerTagListResponse resp = new GetCustomerTagListResponse();
    if (hasTag) {
      resp.tags(mockedResponseTags1);
    }
    when(serviceConfig.getTaggingNamespaceIds()).thenReturn(new HashMap<>(Map.of(TaggingNamespace.RISK_FRAUD, 100L)));
    when(taggingApi.getAllTagsForCustomer(eq(customerId), anyLong(), anyList(), eq(true)))
        .thenReturn(resp);
    List<CustomerTagDataItem> result = taggingService.getFraudTags(customerId);
    assertEquals(mockedResponseTags1, result);
  }

  @Test
  void getFraudTags_TagNotFound() throws ApiException {
    Long customerId = 4L;
    when(serviceConfig.getTaggingNamespaceIds()).thenReturn(new HashMap<>(Map.of(TaggingNamespace.RISK_FRAUD, 100L)));
    when(taggingApi.getAllTagsForCustomer(eq(customerId), anyLong(), anyList(), eq(true)))
        .thenThrow(new ApiException(404, "Not found"));
    List<CustomerTagDataItem> result = taggingService.getFraudTags(customerId);
    assertEquals(List.of(), result);
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void customerHasTag_Success(boolean hasTag) throws ApiException {
    Long customerId = 1L;
    String tagName = "tag1";
    GetCustomerTagResponse resp = new GetCustomerTagResponse();

    if (hasTag) {
      resp.customerTag(new CustomerTagDataItem());
    }
    when(taggingApi.checkCustomerTagByName(customerId, 1L, tagName, true))
        .thenReturn(resp);

    boolean result = taggingService.customerHasTag(customerId, tagName);
    assertEquals(hasTag, result);
  }

  @Test
  void customerHasTag_TagNotFound() throws ApiException {
    Long customerId = 1L;
    String tagName = "tag1";
    when(taggingApi.checkCustomerTagByName(customerId, 1L, tagName, true))
        .thenThrow(new ApiException(404, "Not found"));

    boolean result = taggingService.customerHasTag(customerId, tagName);
    assertFalse(result);
  }
}
