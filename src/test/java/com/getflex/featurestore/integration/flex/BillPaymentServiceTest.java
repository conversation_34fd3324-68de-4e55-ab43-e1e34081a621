package com.getflex.featurestore.integration.flex;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.billPaymentMethod.client.ApiException;
import com.getflex.billPaymentMethod.model.BillPaymentMethodResponse;
import com.getflex.billPaymentMethod.model.MethodType;
import com.getflex.billPaymentMethod.openapi.BillPaymentMethodApi;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.utils.Metrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillPaymentServiceTest {

  @InjectMocks
  BillPaymentService service;

  @Mock
  BillPaymentMethodApi api;

  @Mock
  Metrics metrics;

  private String customerPublicId;
  private String billTransactionId;

  @BeforeEach
  void setUp() {
    customerPublicId = "test-customer-public-id";
    billTransactionId = "test-bill-transaction-id";
  }

  @Test
  void getFlexAnywhereBillPaymentType_returnsVC_whenResponseMethodTypeIsVC() throws ApiException {
    // Arrange
    BillPaymentMethodResponse response = new BillPaymentMethodResponse()
        .methodType(MethodType.VC);
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenReturn(response);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act
    MethodType result = service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId);

    // Assert
    assertEquals(MethodType.VC, result);
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_success")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_returnsDDA_whenResponseMethodTypeIsDDA() throws ApiException {
    // Arrange
    BillPaymentMethodResponse response = new BillPaymentMethodResponse()
        .methodType(MethodType.DDA);
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenReturn(response);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act
    MethodType result = service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId);

    // Assert
    assertEquals(MethodType.DDA, result);
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_success")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_returnsDDA_whenResponseMethodTypeIsNull() throws ApiException {
    // Arrange
    BillPaymentMethodResponse response = new BillPaymentMethodResponse()
        .methodType(null);
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenReturn(response);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act
    MethodType result = service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId);

    // Assert
    assertEquals(MethodType.DDA, result);
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_success")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_returnsDDA_whenResponseIsEmpty() throws ApiException {
    // Arrange
    BillPaymentMethodResponse response = new BillPaymentMethodResponse();
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenReturn(response);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act
    MethodType result = service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId);

    // Assert
    assertEquals(MethodType.DDA, result);
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_success")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_throwsException_whenApiThrowsException() throws ApiException {
    // Arrange
    ApiException apiException = new ApiException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "API Error");
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenThrow(apiException);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act & Assert
    InternalDependencyFailureException exception = assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId)
    );

    assertEquals(
        "Could not fetch the flex anywhere bill payment type for customer test-customer-public-id, btxId test-bill-transaction-id",
        exception.getMessage()
    );
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_error")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_throwsException_whenApiThrowsNotFound() throws ApiException {
    // Arrange
    ApiException apiException = new ApiException(HttpStatus.NOT_FOUND.value(), "Not Found");
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenThrow(apiException);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act & Assert
    InternalDependencyFailureException exception = assertThrows(
        InternalDependencyFailureException.class,
        () -> service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId)
    );

    assertEquals(
        "Could not fetch the flex anywhere bill payment type for customer test-customer-public-id, btxId test-bill-transaction-id",
        exception.getMessage()
    );
    verify(api, times(1)).getBillPaymentMethod(customerPublicId, 1L, billTransactionId);
    verify(metrics, times(1)).increment(
        eq(BillPaymentService.class),
        eq("getFlexAnywhereBillPaymentType_error")
    );
  }

  @Test
  void getFlexAnywhereBillPaymentType_callsApiWithCorrectParameters() throws ApiException {
    // Arrange
    BillPaymentMethodResponse response = new BillPaymentMethodResponse()
        .methodType(MethodType.DDA);
    
    when(api.getBillPaymentMethod(customerPublicId, 1L, billTransactionId))
        .thenReturn(response);
    doNothing().when(metrics).increment(eq(BillPaymentService.class), anyString());

    // Act
    service.getFlexAnywhereBillPaymentType(customerPublicId, billTransactionId);

    // Assert
    verify(api, times(1)).getBillPaymentMethod(
        eq(customerPublicId), 
        eq(1L), 
        eq(billTransactionId)
    );
  }
}
