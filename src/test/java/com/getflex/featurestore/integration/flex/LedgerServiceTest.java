package com.getflex.featurestore.integration.flex;

import static com.getflex.featurestore.utils.ObservabilityConstants.ERROR_METRIC;
import static com.getflex.featurestore.utils.ObservabilityConstants.SUCCESS_METRIC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.ledger.api.LedgerApi;
import com.getflex.ledger.api.LedgerApi.APIretrieveLedgerByCustomerIdRequest;
import com.getflex.ledger.model.GetCreditLineBalanceResponse;
import com.getflex.ledger.model.GetLedgerResponse;
import com.getflex.ledger.model.GetLedgerWalletResponse;
import com.getflex.ledger.model.GetOutstandingBalanceResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class LedgerServiceTest {

  @InjectMocks
  LedgerService service;

  @Mock
  LedgerApi api;

  @Mock
  Metrics metrics;

  @Test
  void getCustomerCreditBalance()
      throws com.getflex.ledger.client.ApiException {
    when(api.getCreditLineBalance(1L)).thenReturn(
        new GetCreditLineBalanceResponse().creditLineBalance(1000L)
    );

    var result = service.getCustomerCreditBalance(1L);
    assertEquals(result, 1000L);
  }

  @Test
  void getCustomerCreditBalanceException()
      throws com.getflex.ledger.client.ApiException {
    when(api.getCreditLineBalance(1L)).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(any(), any());

    try {
      service.getCustomerCreditBalance(1L);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer credit line balance 1");
    }
  }

  @Test
  void testGetCustomerCreditLinePendingBalance()
      throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    Long creditLinePendingBalance = 1000L;
    when(api.getCreditLineBalance(customerId)).thenReturn(
        new GetCreditLineBalanceResponse().creditLinePendingBalance(creditLinePendingBalance)
    );
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(SUCCESS_METRIC, "getCustomerCreditLinePendingBalance")
    );
    Long result = service.getCustomerCreditLinePendingBalance(customerId);
    assertEquals(result, creditLinePendingBalance);
  }

  @Test
  void testGetCustomerCreditLinePendingBalanceException()
      throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    when(api.getCreditLineBalance(customerId)).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getCustomerCreditLinePendingBalance")
    );

    try {
      service.getCustomerCreditLinePendingBalance(customerId);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer credit line pending balance 1");
    }
  }

  @Test
  void testGetCustomerOutstandingBalance() throws com.getflex.ledger.client.ApiException {
    String customerPublicId = "test_public_id";
    Long outstandingBalance = 1000L;
    when(api.getOutstandingBalance(customerPublicId, false)).thenReturn(
        new GetOutstandingBalanceResponse().totalAmount(outstandingBalance)
    );
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(SUCCESS_METRIC, "getCustomerOutstandingBalance")
    );
    Long result = service.getCustomerOutstandingBalance(customerPublicId);
    assertEquals(result, outstandingBalance);
  }

  @Test
  void testGetCustomerOutstandingBalanceException() throws com.getflex.ledger.client.ApiException {
    String customerPublicId = "test_public_id";
    when(api.getOutstandingBalance(customerPublicId, false)).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getCustomerOutstandingBalance")
    );

    try {
      service.getCustomerOutstandingBalance(customerPublicId);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer outstanding balance test_public_id");
    }
  }

  @Test
  void getLedgersByCustomerId() throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    GetLedgerResponse response = new GetLedgerResponse().transactions(List.of(
        new RecordLedger().fromParentIdentityId(1L),
        new RecordLedger().fromParentIdentityId(1L)
    ));
    when(api.retrieveLedgerByCustomerId(any())).thenReturn(response);

    List<RecordLedger> result = service.getLedgersByCustomerId(customerId, true);
    assertFalse(result.isEmpty());
    assertEquals(result.size(), 2);
  }

  @Test
  void getLedgersByCustomerIdException() throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    when(api.retrieveLedgerByCustomerId(any())).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getLedgersForCustomerId")
    );

    try {
      service.getLedgersByCustomerId(customerId, true);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer transactions for customer id %s".formatted(customerId));
    }

  }

  @Test
  void getLedgersByBillTransactionId() throws com.getflex.ledger.client.ApiException {
    String billTransactionId = "1";
    GetLedgerResponse response = new GetLedgerResponse().transactions(List.of(
        new RecordLedger().fromParentIdentityId(1L),
        new RecordLedger().fromParentIdentityId(1L)
    ));
    when(api.retrieveLedgerByBillTransactionId(any())).thenReturn(response);

    List<RecordLedger> result = service.getLedgersByBillTransactionId(billTransactionId,
        PaymentState.BILL_PAID, MoneyMovementType.CUSTOMER_CREDIT);
    assertFalse(result.isEmpty());
    assertEquals(result.size(), 2);
  }

  @Test
  void getLedgersByBillTransactionIdException() throws com.getflex.ledger.client.ApiException {
    String billTransactionId = "1";
    MoneyMovementType moneyMovementType = MoneyMovementType.CUSTOMER_CREDIT;
    when(api.retrieveLedgerByBillTransactionId(any())).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getLedgersByBillTransactionId")
    );

    try {
      service.getLedgersByBillTransactionId(billTransactionId, PaymentState.BILL_PAID, moneyMovementType);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer transactions for btxId %s, type %s"
          .formatted(billTransactionId, moneyMovementType.getValue()));
    }
  }

  @Test
  void getDownpaymentRecords() throws com.getflex.ledger.client.ApiException {
    String billTransactionId = "1";
    GetLedgerResponse response = new GetLedgerResponse().transactions(List.of(
        new RecordLedger().fromParentIdentityId(1L),
        new RecordLedger().fromParentIdentityId(1L)
    ));
    when(api.getDownpayByTransactionId(any())).thenReturn(response);

    List<RecordLedger> result = service.getDownpaymentRecords(billTransactionId, MovementCategory.CAPTURE);
    assertFalse(result.isEmpty());
    assertEquals(result.size(), 2);
  }

  @Test
  void getDownpaymentRecordsException() throws com.getflex.ledger.client.ApiException {
    String billTransactionId = "1";
    MovementCategory category = MovementCategory.CAPTURE;
    when(api.getDownpayByTransactionId(any())).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getDownpaymentByBillTransactionId")
    );

    try {
      service.getDownpaymentRecords(billTransactionId, category);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(), "Could not fetch customer transactions for btxId %s, category %s".formatted(
          billTransactionId,
          category.getValue()));
    }
  }


  @Test
  void getWalletRecordLedgerByCustomerId() throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    GetLedgerWalletResponse response = new GetLedgerWalletResponse().transactions(List.of(
        new RecordLedgerWallet().fromParentIdentityId(1L),
        new RecordLedgerWallet().fromParentIdentityId(1L)
    ));
    when(api.getWalletRecordLedger(any())).thenReturn(response);

    List<RecordLedgerWallet> result = service.getWalletRecordLedgerByCustomerId(customerId);
    assertFalse(result.isEmpty());
    assertEquals(result.size(), 2);
  }

  @Test
  void getWalletRecordLedgerByCustomerIdException() throws com.getflex.ledger.client.ApiException {
    Long customerId = 1L;
    when(api.getWalletRecordLedger(any())).thenThrow(new com.getflex.ledger.client.ApiException());
    doNothing().when(metrics).increment(
        LedgerService.class,
        String.format(ERROR_METRIC, "getSettledWalletRecordLedger")
    );

    try {
      service.getWalletRecordLedgerByCustomerId(customerId);
    } catch (InternalDependencyFailureException e) {
      assertEquals(e.getMessage(),
          "Could not fetch settled wallet record ledger for customer id %s".formatted(customerId));
    }
  }

  @Test
  void retrieveLedgerByCustomerId() throws com.getflex.ledger.client.ApiException {
    when(api.retrieveLedgerByCustomerId(any()))
        .thenReturn(new GetLedgerResponse()
            .transactions(
                List.of(new RecordLedger().paymentCategoryId(MovementCategory.CHARGE.getValue()))
            )
        );
    List<RecordLedger> result = service.retrieveLedgerByCustomerId(any());
    assertEquals(1, result.size());
  }

  @Test
  void retrieveLedgerByCustomerId_EmptyList() throws com.getflex.ledger.client.ApiException {
    when(api.retrieveLedgerByCustomerId(any())).thenReturn(new GetLedgerResponse().transactions(List.of()));
    List<RecordLedger> result = service.retrieveLedgerByCustomerId(any());
    assertEquals(0, result.size());
  }

  @Test
  void retrieveLedgerByCustomerId_NotFoundException() throws com.getflex.ledger.client.ApiException {
    when(api.retrieveLedgerByCustomerId(any())).thenThrow(
        new com.getflex.ledger.client.ApiException(404, "Not Found")
    );
    APIretrieveLedgerByCustomerIdRequest request = APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(1L)
        .build();
    List<RecordLedger> result = service.retrieveLedgerByCustomerId(request);
    assertEquals(0, result.size());
  }

  @Test
  void retrieveLedgerByCustomerId_Exception() throws com.getflex.ledger.client.ApiException {
    when(api.retrieveLedgerByCustomerId(any())).thenThrow(new com.getflex.ledger.client.ApiException());
    APIretrieveLedgerByCustomerIdRequest request = APIretrieveLedgerByCustomerIdRequest.newBuilder()
        .customerId(1L)
        .build();
    assertThrows(InternalDependencyFailureException.class,
        () -> service.retrieveLedgerByCustomerId(request)
    );
  }
}
