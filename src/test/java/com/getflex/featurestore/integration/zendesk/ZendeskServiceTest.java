package com.getflex.featurestore.integration.zendesk;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUser;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUserTicketsResponse;
import com.getflex.featurestore.utils.Metrics;
import java.io.IOException;
import java.io.InputStream;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ZendeskServiceTest {

  public static final String API_KEY = "MockApiKey";
  @InjectMocks
  ZendeskService service;

  @Mock
  HttpClient httpClient;

  @Spy
  ServiceConfig serviceConfig = new ServiceConfig().withZendeskUrl("https://zendesk.com").withZendeskApiKey(API_KEY);

  @Mock
  HttpResponse<Object> httpResponse;

  @Mock
  Metrics metrics;

  @Test
  public void getUserByEmail_success() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource =
        this.getClass().getResourceAsStream("/zendesk/responses/user_search_response.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(200);

    ZendeskUser zendeskUser = service.getUserByEmail("<EMAIL>");
    ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
    verify(httpClient, times(1)).send(requestCaptor.capture(), any());

    Assertions.assertEquals(zendeskUser.getId(), "1510036073222");
    Assertions.assertEquals(zendeskUser.getName(), "Test User");
    Assertions.assertNotNull(zendeskUser.getUserFields());

    HttpRequest request = requestCaptor.getValue();
    String encodedAuth = Base64.getEncoder()
        .encodeToString(serviceConfig.getZendeskApiKey().getBytes(StandardCharsets.UTF_8));
    assertEquals("Basic " + encodedAuth, request.headers().firstValue("authorization").get());
  }

  @Test
  public void getTicketsByUserId_success() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource =
        this.getClass().getResourceAsStream("/zendesk/responses/user_tickets_response.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(200);

    ZendeskUserTicketsResponse ticketsResponse = service.getTicketsByUserId("testUserId");
    ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
    verify(httpClient, times(1)).send(requestCaptor.capture(), any());

    Assertions.assertNotNull(ticketsResponse.getTickets());
    Assertions.assertEquals(ticketsResponse.getCount(), 10);

    HttpRequest request = requestCaptor.getValue();
    String encodedAuth = Base64.getEncoder()
        .encodeToString(serviceConfig.getZendeskApiKey().getBytes(StandardCharsets.UTF_8));
    assertEquals("Basic " + encodedAuth, request.headers().firstValue("authorization").get());
  }

}
