package com.getflex.featurestore.integration.socure.cache;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.integration.socure.model.SocureRequest;
import com.getflex.featurestore.integration.socure.s3.SocureS3Repo;
import java.util.Optional;
import java.util.concurrent.Callable;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SocureCacheTests {

  static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

  static final SocureRequest SOCURE_REQUEST = SocureRequest.builder()
      .customerUserId("123")
      .build();

  @InjectMocks
  SocureCache socureCache;

  @Mock
  SocureS3Repo socureS3Repo;

  @Test
  public void put_success() {
    socureCache.put(SOCURE_REQUEST, OBJECT_MAPPER.createObjectNode().put("a", "b"));

    Mockito.verify(socureS3Repo).save(eq(123L), any(), eq("{\"a\":\"b\"}"));
  }

  @Test
  public void get_success() {
    when(socureS3Repo.load(eq(123L), any())).thenReturn(Optional.of("{}"));

    var result = socureCache.get(SOCURE_REQUEST);

    assertNotNull(result.get());
  }

  @Test
  public void get_cacheMiss() {
    when(socureS3Repo.load(eq(123L), any())).thenReturn(Optional.empty());

    var result = socureCache.get(SOCURE_REQUEST);

    assertNull(result);
  }

  @Test
  public void get_withType() {
    when(socureS3Repo.load(eq(123L), any())).thenReturn(Optional.of("{}"));

    var result = socureCache.get(SOCURE_REQUEST, JsonNode.class);

    assertInstanceOf(JsonNode.class, result);
  }

  @Test
  public void get_withValueLoaderCacheHit() {
    when(socureS3Repo.load(eq(123L), any())).thenReturn(Optional.of("{}"));

    var result = socureCache.get(SOCURE_REQUEST, () -> {
      throw new RuntimeException("Should not get here");
    });

    assertInstanceOf(JsonNode.class, result);
  }

  @Test
  public void get_withValueLoaderCacheMiss() {
    when(socureS3Repo.load(eq(123L), any())).thenReturn(Optional.empty());

    var result = socureCache.get(SOCURE_REQUEST, () -> OBJECT_MAPPER.createObjectNode().put("a", "b"));

    assertEquals("b", result.get("a").textValue());
  }
}
