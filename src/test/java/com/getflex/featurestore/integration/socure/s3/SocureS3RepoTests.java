package com.getflex.featurestore.integration.socure.s3;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.ServiceConfig;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Utilities;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SocureS3RepoTests {

  @InjectMocks
  private SocureS3Repo alloyS3Repo;

  @Mock
  private S3Client s3Client;

  @Spy
  private ServiceConfig serviceConfig = new ServiceConfig().withSocureBucket("socureBucket");

  @Test
  public void testSaveReport() throws IOException {
    String s3Uri = alloyS3Repo.save(123L, "sha512", "{}");

    Assertions.assertEquals("s3://socureBucket/customer:123/sha512.json", s3Uri);

    ArgumentCaptor<PutObjectRequest> putObjectRequestArgumentCaptor = ArgumentCaptor.forClass(PutObjectRequest.class);
    ArgumentCaptor<RequestBody> requestBodyArgumentCaptor = ArgumentCaptor.forClass(RequestBody.class);
    verify(s3Client, times(1)).putObject(putObjectRequestArgumentCaptor.capture(),
        requestBodyArgumentCaptor.capture());
    Assertions.assertEquals("socureBucket", putObjectRequestArgumentCaptor.getValue().bucket());
    Assertions.assertEquals("customer:123/sha512.json", putObjectRequestArgumentCaptor.getValue().key());
    try (InputStream inputStream = requestBodyArgumentCaptor.getValue().contentStreamProvider().newStream()) {
      Assertions.assertEquals("{}", new String(inputStream.readAllBytes(), StandardCharsets.UTF_8));
    }
  }

  @Test
  public void testLoadReport() {
    when(s3Client.getObjectAsBytes((GetObjectRequest) any())).thenReturn(
        ResponseBytes.fromByteArray(GetObjectResponse.builder().build(),
            "testContent".getBytes(StandardCharsets.UTF_8)));

    String rawReport = alloyS3Repo.load(123L, "sha512").get();
    Assertions.assertEquals("testContent", rawReport);
  }
}
