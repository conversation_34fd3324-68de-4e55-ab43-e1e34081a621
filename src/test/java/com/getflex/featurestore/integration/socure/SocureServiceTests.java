package com.getflex.featurestore.integration.socure;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.socure.exception.InvalidPiiException;
import com.getflex.featurestore.integration.socure.exception.InvalidPiiException.InputField;
import com.getflex.featurestore.integration.socure.exception.SocureIoFailureException;
import com.getflex.featurestore.integration.socure.exception.SocureServerErrorException;
import com.getflex.featurestore.integration.socure.exception.SocureThrottlingException;
import com.getflex.featurestore.integration.socure.exception.UnknownSocureFailureException;
import com.getflex.featurestore.integration.socure.model.Module;
import com.getflex.featurestore.integration.socure.model.SocureRequest;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.featurestore.utils.StringSubscriber;
import java.io.IOException;
import java.io.InputStream;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublisher;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodySubscriber;
import java.net.http.HttpResponse.BodySubscribers;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SocureServiceTests {

  public static final String AUTH_KEY = "Basic 1234";
  public static final String IP_ADDRESS = "***********";
  @InjectMocks
  SocureService service;

  @Mock
  HttpClient httpClient;

  @Spy
  ServiceConfig serviceConfig = new ServiceConfig().withSocureUrl("https://socure.com").withSocureApiKey(AUTH_KEY);

  @Mock
  HttpResponse<Object> httpResponse;

  ObjectMapper objectMapper = new ObjectMapper();
  private final CustomerInfo customerIdentity = CustomerInfo.builder()
      .customerId(123L)
      .firstName("John")
      .lastName("Doe")
      .emailAddress("<EMAIL>")
      .phoneNumber("12065555555")
      .addressLine1("123 Main St")
      .addressLine2("Apt 1")
      .city("New York")
      .state("NY")
      .postalCode("10001")
      .ssn("***********")
      .dateOfBirth("2000-01-01")
      .build();

  @Test
  void runKyc_success() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource = this.getClass().getResourceAsStream("/socure/responses/success.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(200);

    var result = service.runKyc(SocureService.buildSocureRequest(customerIdentity,
        IP_ADDRESS));

    ArgumentCaptor<HttpRequest> requestCaptor = ArgumentCaptor.forClass(HttpRequest.class);
    verify(httpClient, times(1)).send(requestCaptor.capture(), any());

    HttpRequest request = requestCaptor.getValue();
    assertEquals(AUTH_KEY, request.headers().firstValue("authorization").get());
    BodySubscriber<String> bodySubscriber = BodySubscribers.ofString(StandardCharsets.UTF_8);
    request.bodyPublisher().get().subscribe(new StringSubscriber(bodySubscriber));
    SocureRequest socureRequest = objectMapper.readValue(bodySubscriber.getBody().toCompletableFuture().join(),
        SocureRequest.class);
    assertEquals("John", socureRequest.getFirstName());
    assertEquals("Doe", socureRequest.getSurName());
    assertEquals("<EMAIL>", socureRequest.getEmail());
    assertEquals("12065555555", socureRequest.getMobileNumber());
    assertEquals("123 Main St", socureRequest.getPhysicalAddress());
    assertEquals("Apt 1", socureRequest.getPhysicalAddress2());
    assertEquals("New York", socureRequest.getCity());
    assertEquals("NY", socureRequest.getState());
    assertEquals("10001", socureRequest.getZip());
    assertEquals("***********", socureRequest.getNationalId());
    assertEquals("2000-01-01", socureRequest.getDob());
    assertEquals("GLBA_502(e)", socureRequest.getDisclosurePurpose());
  }

  @Test
  void toRequestBody_stateMapping() throws JsonProcessingException {
    BodyPublisher req = service.toRequestBody(
        SocureService.buildSocureRequest(CustomerInfo.builder().customerId(123L).state("_New York?").build(),
            IP_ADDRESS));
    BodySubscriber<String> bodySubscriber = BodySubscribers.ofString(StandardCharsets.UTF_8);
    req.subscribe(new StringSubscriber(bodySubscriber));
    SocureRequest socureRequest = objectMapper.readValue(bodySubscriber.getBody().toCompletableFuture().join(),
        SocureRequest.class);
    assertEquals("NY", socureRequest.getState());
  }

  @Test
  void toRequestBody_stateMappingFailure() {
    try {
      service.toRequestBody(SocureService.buildSocureRequest(CustomerInfo.builder().state("New Kork").build(),
          IP_ADDRESS));
      fail();
    } catch (InvalidPiiException e) {
      assertEquals(InputField.STATE, e.getInvalidField());
    }
  }

  @Test
  void runKyc_invalidEmail() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource = this.getClass().getResourceAsStream("/socure/responses/invalid_email.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(400);

    try {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
      fail();
    } catch (InvalidPiiException e) {
      assertEquals(InputField.EMAIL, e.getInvalidField());
    }
  }

  @Test
  void runKyc_invalidZip() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource = this.getClass().getResourceAsStream("/socure/responses/invalid_zip.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(400);

    try {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
      fail();
    } catch (InvalidPiiException e) {
      assertEquals(InputField.ZIP, e.getInvalidField());
    }
  }

  @Test
  void runKyc_invalidState() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    try (InputStream resource = this.getClass().getResourceAsStream("/socure/responses/invalid_state.json")) {
      String rawResponse = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
      when(httpResponse.body()).thenReturn(rawResponse);
    }
    when(httpResponse.statusCode()).thenReturn(400);

    try {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
      fail();
    } catch (InvalidPiiException e) {
      assertEquals(InputField.OTHER, e.getInvalidField());
    }
  }

  @Test
  void runKyc_invalidResponseJson() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    when(httpResponse.statusCode()).thenReturn(200);
    when(httpResponse.body()).thenReturn("{");

    assertInstanceOf(JsonProcessingException.class,
        assertThrows(RuntimeException.class, () -> service.runKyc(SocureService.buildSocureRequest(customerIdentity,
            IP_ADDRESS))).getCause());
  }

  @Test
  void runKyc_unknownFailure() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    when(httpResponse.statusCode()).thenReturn(403);

    try {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
      fail();
    } catch (UnknownSocureFailureException e) {
      assertEquals(403, e.getStatusCode());
    }
  }

  @Test
  void runKyc_throttling() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    when(httpResponse.statusCode()).thenReturn(429);

    assertThrows(SocureThrottlingException.class, () -> {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
    });
  }

  @Test
  void runKyc_ioException() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenThrow(new IOException("GOAWAY received"));

    assertThrows(SocureIoFailureException.class, () -> {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
    });
  }

  @ParameterizedTest
  @ValueSource(ints = {500, 599})
  void runKyc_5xxError(int statusCode) throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenReturn(httpResponse);
    when(httpResponse.statusCode()).thenReturn(statusCode);
    when(httpResponse.body()).thenReturn("""
        {"status":"Error","referenceId":"8daa902f-0da0-41f3-9804-c56245bc390c","msg":"Processing timed out"}""");

    assertTrue(assertThrows(SocureServerErrorException.class, () -> {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
    }).getMessage().contains("Processing timed out"));
  }

  @Test
  void runKyc_interruptedException() throws IOException, InterruptedException {
    when(httpClient.send(any(), any())).thenThrow(new InterruptedException());

    assertThrows(RuntimeException.class, () -> {
      service.runKyc(SocureService.buildSocureRequest(customerIdentity,
          IP_ADDRESS));
    });

    assertTrue(Thread.interrupted());
  }
}
