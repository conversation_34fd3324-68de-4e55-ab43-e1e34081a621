package com.getflex.featurestore.integration.sagemaker.model;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.CmmV2ScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkAbuseScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkIdentityTheftScoreFeature;
import com.getflex.featurestore.model.feature.CustomerUnderwritingScoresMaxFeature;
import com.getflex.featurestore.model.feature.StripeFirstNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchV2Feature;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelV2InputTest {

  @Mock
  FeatureFactory featureFactory;

  @Mock
  BaseFeature baseFeature;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;

  @Mock
  DecisionEngineService decisionEngineService;

  long customerId = 123L;

  EvalParams evalParams = new EvalParams().customerId(customerId);

  @Test
  public void cmmV2Score() {
    when(featureFactory.getFeature(CmmV2ScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Double expected = 1.0;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.DOUBLE));

    Number actual = FraudTransactionModelV2Input.CMM2_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertEquals(expected, actual);
  }

  @Test
  public void cmmV2Score_missing() {
    when(featureFactory.getFeature(CmmV2ScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Double expected = -1.0;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.DOUBLE));

    Number actual = FraudTransactionModelV2Input.CMM2_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertNull(actual);
  }

  @Test
  public void postalMatchFlagV2_match() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setZip("12345");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripeZipCode("12345");

    Number actual = FraudTransactionModelV2Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void postalMatchFlagV2_mismatch() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setZip("67890");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripeZipCode("12345");

    Number actual = FraudTransactionModelV2Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(2, actual);
  }

  @Test
  public void postalMatchFlagV2_missingStripeZip() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));
    evalParams.setStripeZipCode(null);

    Number actual = FraudTransactionModelV2Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(3, actual);
  }

  @Test
  public void sigmaScore() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));
    InternalOffer offer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext()
        .isRootEvaluation(true);
    offer.setEvaluationContext(evalContext);
    Double expected = 0.45;
    evalContext.setSocureSigmaScore(expected);
    when(offerService.searchOffer(any(), any())).thenReturn(List.of(offer));

    Number actual = FraudTransactionModelV2Input.SIGMA_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(expected, actual);
  }

  @Test
  public void sigmaScore_missing() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));

    Number actual = FraudTransactionModelV2Input.SIGMA_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void addressRiskScore() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));
    InternalOffer offer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext()
        .isRootEvaluation(true);
    offer.setEvaluationContext(evalContext);
    Float expected = 0.45f;
    evalContext.setSocureAddressRiskScore(expected);
    when(offerService.searchOffer(any(), any())).thenReturn(List.of(offer));

    Number actual = FraudTransactionModelV2Input.ADDRESSRISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(expected, actual);
  }

  @Test
  public void addressRiskScore_missing() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));

    Number actual = FraudTransactionModelV2Input.ADDRESSRISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void fnameJarow() {
    when(featureFactory.getFeature(StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName()))
        .thenReturn(new StripeFirstNameJarowSimilarityScoreFeature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setFirstName("Greg");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripePaymentFullName("Greg Test");

    Number actual = FraudTransactionModelV2Input.FNAME_JAROW.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(100.0, actual);
  }

  @Test
  public void fnameJarow_missing() {
    when(featureFactory.getFeature(StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName()))
        .thenReturn(new StripeFirstNameJarowSimilarityScoreFeature(identityService));

    Number actual = FraudTransactionModelV2Input.FNAME_JAROW.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void abuseScoreZero() {
    when(featureFactory.getFeature(CustomerSentilinkAbuseScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Integer expected = 0;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.INT));

    Number actual = FraudTransactionModelV2Input.ABUSE_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertNull(actual);
  }

  @Test
  public void abuseScoreNonzero() {
    when(featureFactory.getFeature(CustomerSentilinkAbuseScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Integer expected = 1000;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.INT));

    Number actual = FraudTransactionModelV2Input.ABUSE_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertEquals(expected, actual);
  }

  @Test
  public void identityTheftScoreZero() {
    when(featureFactory.getFeature(CustomerSentilinkIdentityTheftScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Integer expected = 0;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.INT));

    Number actual = FraudTransactionModelV2Input.IDENTITY_THEFT_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertNull(actual);
  }

  @Test
  public void identityTheftScoreNonZero() {
    when(featureFactory.getFeature(CustomerSentilinkIdentityTheftScoreFeature.class.getSimpleName()))
        .thenReturn(baseFeature);
    Integer expected = 1000;
    when(baseFeature.fetchFeatureValue(any()))
        .thenReturn(new FeatureValue().value(expected).type(FeatureTypeEnum.INT));

    Number actual = FraudTransactionModelV2Input.IDENTITY_THEFT_SCORE
        .getModelInputValueFunction().apply(featureFactory, evalParams);

    Assertions.assertEquals(expected, actual);
  }

  @Test
  public void zeroUnderwritingScores() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));
    InternalOffer offer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    offer.setEvaluationContext(evalContext);
    evalContext.setSocureSigmaScore(0.0);
    evalContext.setSocureAddressRiskScore(0.0f);
    evalContext.setSocureEmailRiskScore(0.0f);
    evalContext.setSocureNameAddressCorrelationScore(0.0f);
    evalContext.setSocureNameEmailCorrelationScore(0.0f);
    evalContext.setSocureNamePhoneCorrelationScore(0.0f);
    evalContext.setSocurePhoneRiskScore(0.0f);
    evalContext.setSocureSyntheticScore(0.0f);
    when(offerService.searchOffer(any(), any())).thenReturn(List.of(offer));

    Number sigmaScore = FraudTransactionModelV2Input.SIGMA_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number addressRiskScore = FraudTransactionModelV2Input.ADDRESSRISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number emailRiskScore = FraudTransactionModelV2Input.EMAILRISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number nameAddressScore = FraudTransactionModelV2Input.NAMEADDRESSCORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number nameEmailScore = FraudTransactionModelV2Input.NAMEEMAILCORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number namePhoneScore = FraudTransactionModelV2Input.NAMEPHONECORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number phoneRiskScore = FraudTransactionModelV2Input.PHONERISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number syntheticScore = FraudTransactionModelV2Input.SYNTHETIC_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNull(sigmaScore);
    Assertions.assertNull(addressRiskScore);
    Assertions.assertNull(emailRiskScore);
    Assertions.assertNull(nameAddressScore);
    Assertions.assertNull(nameEmailScore);
    Assertions.assertNull(namePhoneScore);
    Assertions.assertNull(phoneRiskScore);
    Assertions.assertNull(syntheticScore);
  }
}
