package com.getflex.featurestore.integration.sagemaker.model;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsStreetCheckEnum;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.CmmScoreFeature;
import com.getflex.featurestore.model.feature.CustomerSentilinkAbuseScoreFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFirstNameFeature;
import com.getflex.featurestore.model.feature.EditDistanceToStripePaymentLastNameFeature;
import com.getflex.featurestore.model.feature.SocureMobileNumberScoreFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelInputFeatureTest {

  @Mock
  FeatureFactory featureFactory;

  @Mock
  OfferService offerService;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  IdentityService identityService;

  private static final Long customerId = 123L;
  private static final EvalParams evalParams = new EvalParams();

  @BeforeAll
  public static void setup() {
    evalParams.setCustomerId(customerId);
  }

  @Test
  public void abuseScoreValue() {
    when(featureFactory.getFeature(CustomerSentilinkAbuseScoreFeature.class.getSimpleName())).thenReturn(
        new CustomerSentilinkAbuseScoreFeature(offerService)
    );
    InternalOffer internalOffer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    Integer expected = 510;
    evalContext.setSentilinkAbuseScore(expected);
    internalOffer.setEvaluationContext(evalContext);
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(internalOffer);

    Number value = FraudTransactionModelInput.ABUSE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(expected, value);
  }

  @Test
  public void abuseScoreValue_null() {
    when(featureFactory.getFeature(CustomerSentilinkAbuseScoreFeature.class.getSimpleName())).thenReturn(
        new CustomerSentilinkAbuseScoreFeature(offerService));

    InternalOffer internalOffer = new InternalOffer();
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(internalOffer);

    Number value = FraudTransactionModelInput.ABUSE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(value);
  }

  @Test
  public void cmmScoreValue() {
    when(featureFactory.getFeature(CmmScoreFeature.class.getSimpleName())).thenReturn(
        new CmmScoreFeature(offlineFeatureRepo));
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("cmm_score")
            .primaryKey("123").featureValue("{\"score\": 0.05}")
            .build());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey("cmm_score", "123"))
        .thenReturn(feature);

    Number value = FraudTransactionModelInput.FLEX_CUST_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0.05, value);
  }

  @Test
  public void cmmScoreValue_null() {
    when(featureFactory.getFeature("CmmScoreFeature")).thenReturn(
        new CmmScoreFeature(offlineFeatureRepo));
    Optional<OfflineFeature> feature = Optional.empty();
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey("cmm_score", "123"))
        .thenReturn(feature);

    Number value = FraudTransactionModelInput.FLEX_CUST_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(value);
  }

  @Test
  public void cardFundingType_credit() {
    EvalParams evalParams1 = new EvalParams();
    evalParams1.setCardType("CREDIT");

    Number value = FraudTransactionModelInput.CARD_FUNDING_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams1);
    Assertions.assertEquals(1, value);
  }

  @Test
  public void cardFundingType_debit() {
    EvalParams evalParams1 = new EvalParams();
    evalParams1.setCardType("debit");

    Number value = FraudTransactionModelInput.CARD_FUNDING_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams1);
    Assertions.assertEquals(0, value);
  }

  @ParameterizedTest
  @CsvSource({
      "pass, 1",
      "fail, 2",
      "unavailable, 3",
      "unchecked, 4",
      ", 5"
  })
  public void addressCheckFlag(String addressCheckString, Integer expected) {
    EvalParams evalParams1 = new EvalParams();
    StripeAvsStreetCheckEnum addressCheck = null;
    if (addressCheckString != null) {
      addressCheck = StripeAvsStreetCheckEnum.fromValue(addressCheckString);
    }
    evalParams1.setStripeAvsStreetCheck(addressCheck);
    Number value = FraudTransactionModelInput.ADDRESS_CHECK_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams1);

    Assertions.assertEquals(expected, value);
  }

  @ParameterizedTest
  @CsvSource({
      "12345, 1",
      "54321, 2"
  })
  public void postalMatchFlag(String zipCode, Integer expected) {
    when(featureFactory.getFeature("StripeZipCodeMatchFeature")).thenReturn(
        new StripeZipCodeMatchFeature(identityService));
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().zip("12345"));
    EvalParams evalParams1 = new EvalParams();
    evalParams1.setCustomerId(customerId);
    evalParams1.setStripeZipCode(zipCode);

    Number value = FraudTransactionModelInput.POSTAL_MATCH_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams1);
    Assertions.assertEquals(expected, value);
  }

  @Test
  public void postalMatchFlag_missingStripeZip() {
    when(featureFactory.getFeature("StripeZipCodeMatchFeature")).thenReturn(
        new StripeZipCodeMatchFeature(identityService));
    Number value = FraudTransactionModelInput.POSTAL_MATCH_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertEquals(3, value);
  }

  @Test
  public void mobileVerify_defaultValue() {
    when(featureFactory.getFeature("SocureMobileNumberScoreFeature")).thenReturn(
        new SocureMobileNumberScoreFeature(offerService));
    Number value = FraudTransactionModelInput.MOBILE_VERIFY.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNull(value);
  }

  @Test
  public void firstNameEditDistance_default() {
    EditDistanceToStripePaymentFirstNameFeature f = Mockito.mock(EditDistanceToStripePaymentFirstNameFeature.class);
    FeatureValue featureValue = new FeatureValue();
    featureValue.setType(FeatureTypeEnum.INT);
    featureValue.setValue(-1);
    when(f.fetchFeatureValue(any())).thenReturn(featureValue);
    when(featureFactory.getFeature(EditDistanceToStripePaymentFirstNameFeature.class.getSimpleName())).thenReturn(f);

    Number value = FraudTransactionModelInput.FNAME_EDIT_DISTANCE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(value);
  }

  @Test
  public void lastNameEditDistance_default() {
    EditDistanceToStripePaymentLastNameFeature f = Mockito.mock(EditDistanceToStripePaymentLastNameFeature.class);
    FeatureValue featureValue = new FeatureValue();
    featureValue.setType(FeatureTypeEnum.INT);
    featureValue.setValue(-1);
    when(f.fetchFeatureValue(any())).thenReturn(featureValue);
    when(featureFactory.getFeature(EditDistanceToStripePaymentLastNameFeature.class.getSimpleName())).thenReturn(f);

    Number value = FraudTransactionModelInput.LNAME_EDIT_DISTANCE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(value);
  }
}
