package com.getflex.featurestore.integration.sagemaker.model;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsZipCheckEnum;
import com.getflex.featurestore.model.feature.CustomerUnderwritingScoresMaxFeature;
import com.getflex.featurestore.model.feature.StripeFirstNameJarowSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.StripeZipCodeMatchV2Feature;
import com.getflex.featurestore.model.feature.TotalSuccessfulPaymentsFeature;
import com.getflex.featurestore.model.feature.ZipCheckFailFeature;
import com.getflex.featurestore.model.feature.base.BaseCustomerAccountUpdatesFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInsufficientFundsLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInvalidAccountLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.BpRateLast12MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.BpRateLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast12MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast6MonthsFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsSinceFirstSignupFeature;
import com.getflex.featurestore.model.feature.billpay.MonthsSinceLastSignupFeature;
import com.getflex.featurestore.model.feature.billpay.PaySuccessRateLast6MonthsFeature;
import com.getflex.featurestore.model.feature.parameterized.CardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.CardVelocityExceededCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfEmailChangesFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfFundsInAttemptsFeature;
import com.getflex.featurestore.model.feature.parameterized.NumberOfPhoneChangesFeature;
import com.getflex.featurestore.model.feature.parameterized.RiskyCardDeclinesCountFeature;
import com.getflex.featurestore.model.feature.parameterized.TotalFundsInAmountFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import com.getflex.useraccount.model.CustomerAccountUpdateHistory;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelV3InputTest {

  @Mock
  FeatureFactory featureFactory;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;

  @Mock
  PaymentService paymentService;

  @Mock
  LedgerService ledgerService;

  @Mock
  LedgerUtils ledgerUtils;

  @Mock
  UserAccountService userAccountService;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  DecisionEngineService decisionEngineService;

  Long customerId = 123L;
  EvalParams evalParams = new EvalParams().customerId(customerId);
  String windowPt24H = "PT24H";
  String windowP7D = "P7D";

  @Test
  public void totalSuccessfulPayment() {
    when(featureFactory.getFeature(TotalSuccessfulPaymentsFeature.class.getSimpleName()))
        .thenReturn(new TotalSuccessfulPaymentsFeature(offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.TOTAL_SUCESSFUL_PAYMENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void bpRateLast6Months() {
    when(featureFactory.getFeature(BpRateLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new BpRateLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.BP_RATE_LAST_6MONTHS.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void bpRateLast12Months() {
    when(featureFactory.getFeature(BpRateLast12MonthsFeature.class.getSimpleName()))
        .thenReturn(new BpRateLast12MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.BP_RATE_LAST_12MONTHS.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void paySuccessRateLast6Monthd() {
    when(featureFactory.getFeature(PaySuccessRateLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new PaySuccessRateLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.PAY_SUCCESS_RATE_6MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void avgInsufficientFundsCardDeclined() {
    when(featureFactory.getFeature(AvgCardDeclinedInsufficientFundsLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new AvgCardDeclinedInsufficientFundsLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.AVG_DECLINE_INSUFF_FUNDS_6MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void monthsSinceFirstSignup() {
    when(featureFactory.getFeature(MonthsSinceFirstSignupFeature.class.getSimpleName()))
        .thenReturn(new MonthsSinceFirstSignupFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.MONTHS_SINCE_FIRST_SIGNUP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void monthsSinceLastSignup() {
    when(featureFactory.getFeature(MonthsSinceLastSignupFeature.class.getSimpleName()))
        .thenReturn(new MonthsSinceLastSignupFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.MONTHS_SINCE_LAST_SIGNUP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void avgCardDeclinedLast6Months() {
    when(featureFactory.getFeature(AvgCardDeclinedLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new AvgCardDeclinedLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.AVG_DECLINED_CARD_6MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void avgInvalidAccountCardDeclinedLast6Months() {
    when(featureFactory.getFeature(AvgCardDeclinedInvalidAccountLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new AvgCardDeclinedInvalidAccountLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.AVG_INVALID_ACCT_6MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void monthsInactiveLast12Months() {
    when(featureFactory.getFeature(MonthsInactiveLast12MonthsFeature.class.getSimpleName()))
        .thenReturn(new MonthsInactiveLast12MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.MONTHS_INACTIVE_12MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void monthsInactiveLast6Months() {
    when(featureFactory.getFeature(MonthsInactiveLast6MonthsFeature.class.getSimpleName()))
        .thenReturn(new MonthsInactiveLast6MonthsFeature(identityService, offlineFeatureRepo));
    OfflineFeature offlineFeature = new OfflineFeature();
    offlineFeature.setFeatureValue("1");
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(any(), any()))
        .thenReturn(Optional.of(offlineFeature));

    Number actual = FraudTransactionModelV3Input.MONTHS_INACTIVE_6MON.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1d, actual);
  }

  @Test
  public void totalFundsInAmountP7D() {
    when(featureFactory.getFeature(TotalFundsInAmountFeature.class.getSimpleName() + "_" + windowP7D))
        .thenReturn(new TotalFundsInAmountFeature(ledgerUtils, new LookbackDurationFeatureParams(windowPt24H)));
    when(ledgerUtils.getFundsInRecords(any(), any())).thenReturn(List.of(new RecordLedger().amount(1L)));

    Number actual = FraudTransactionModelV3Input.TOT_FUNDSIN_AMT_7D.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void totalFundsInAmountPt24H() {
    when(featureFactory.getFeature(TotalFundsInAmountFeature.class.getSimpleName() + "_" + windowPt24H))
        .thenReturn(new TotalFundsInAmountFeature(ledgerUtils, new LookbackDurationFeatureParams(windowPt24H)));
    when(ledgerUtils.getFundsInRecords(any(), any())).thenReturn(List.of(new RecordLedger().amount(1L)));

    Number actual = FraudTransactionModelV3Input.TOT_FUNDSIN_AMT_24H.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }
  
  @Test
  public void cardDeclinesCountPt24H() {
    when(featureFactory.getFeature(CardDeclinesCountFeature.class.getSimpleName() + "_" + windowPt24H))
        .thenReturn(new CardDeclinesCountFeature(paymentService, new LookbackDurationFeatureParams(windowPt24H)));
    when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(10);

    Number actual = FraudTransactionModelV3Input.NUM_BANKDECLINE_24H.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(10, actual);
  }

  @Test
  public void cardDeclinesCountP7D() {
    when(featureFactory.getFeature(CardDeclinesCountFeature.class.getSimpleName() + "_" + windowP7D))
        .thenReturn(new CardDeclinesCountFeature(paymentService, new LookbackDurationFeatureParams(windowP7D)));

    when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(10);

    Number actual = FraudTransactionModelV3Input.NUM_BANKDECLINE_7D.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(10, actual);
  }

  @Test
  public void riskyCardDeclinesCountPt24H() {
    when(featureFactory.getFeature(RiskyCardDeclinesCountFeature.class.getSimpleName() + "_" + windowPt24H))
        .thenReturn(new RiskyCardDeclinesCountFeature(paymentService, new LookbackDurationFeatureParams(windowPt24H)));

    when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(10);

    Number actual = FraudTransactionModelV3Input.NUM_RISKYTRANS_DECLINE_24H.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(10, actual);
  }

  @Test
  public void riskyCardDeclinesCountP7D() {
    when(featureFactory.getFeature(RiskyCardDeclinesCountFeature.class.getSimpleName() + "_" + windowP7D))
        .thenReturn(new RiskyCardDeclinesCountFeature(paymentService, new LookbackDurationFeatureParams(windowP7D)));

    when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(10);

    Number actual = FraudTransactionModelV3Input.NUM_RISKYTRANS_DECLINE_7D.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(10, actual);
  }

  @Test
  public void velocityExceededCardDeclinesCountP7D() {
    when(featureFactory.getFeature(
        CardVelocityExceededCardDeclinesCountFeature.class.getSimpleName() + "_" + windowP7D)
    ).thenReturn(new CardVelocityExceededCardDeclinesCountFeature(paymentService,
        new LookbackDurationFeatureParams(windowP7D)));

    when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(10);

    Number actual = FraudTransactionModelV3Input.NUM_VELOCITY_DECLINE_7D.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(10, actual);
  }

  @Test
  public void fundsInAttemptsPt24H() {
    when(featureFactory.getFeature(
        NumberOfFundsInAttemptsFeature.class.getSimpleName() + "_" + windowPt24H)
    ).thenReturn(new NumberOfFundsInAttemptsFeature(ledgerService, new LookbackDurationFeatureParams(windowPt24H)));

    when(ledgerService.retrieveLedgerByCustomerId(any()))
        .thenReturn(List.of());

    Number actual = FraudTransactionModelV3Input.NUM_FUNDSIN_ATTEMPTS_24H.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void fundsInAttemptsP7D() {
    when(featureFactory.getFeature(
        NumberOfFundsInAttemptsFeature.class.getSimpleName() + "_" + windowP7D)
    ).thenReturn(new NumberOfFundsInAttemptsFeature(ledgerService, new LookbackDurationFeatureParams(windowP7D)));

    when(ledgerService.retrieveLedgerByCustomerId(any()))
        .thenReturn(List.of());

    Number actual = FraudTransactionModelV3Input.NUM_FUNDSIN_ATTEMPTS_7D.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void numberPhoneChangeNoWindow() {
    when(featureFactory.getFeature(
        NumberOfPhoneChangesFeature.class.getSimpleName())
    ).thenReturn(new NumberOfPhoneChangesFeature(identityService, userAccountService,
        new LookbackDurationFeatureParams("")));
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(userAccountService.getUserAccountUpdateHistory(any())).thenReturn(new CustomerAccountUpdateHistory());

    Number actual = FraudTransactionModelV3Input.NUM_PHONE_CHANGE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void emailChangeNoWindow() {
    when(featureFactory.getFeature(
        NumberOfEmailChangesFeature.class.getSimpleName())
    ).thenReturn(new NumberOfEmailChangesFeature(identityService, userAccountService,
        new LookbackDurationFeatureParams("")));
    when(identityService.getCustomer(customerId)).thenReturn(new GetCustomerResponse());
    when(userAccountService.getUserAccountUpdateHistory(any())).thenReturn(new CustomerAccountUpdateHistory());

    Number actual = FraudTransactionModelV3Input.NUM_EMAIL_CHANGE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void postalMatchFlagV2_match() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setZip("12345");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripeZipCode("12345");

    Number actual = FraudTransactionModelV3Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void postalMatchFlagV2_mismatch() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setZip("67890");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripeZipCode("12345");

    Number actual = FraudTransactionModelV3Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(2, actual);
  }

  @Test
  public void validZipCheckFail() {
    evalParams.setStripeAvsZipCheck(StripeAvsZipCheckEnum.FAIL);
    when(featureFactory.getFeature(ZipCheckFailFeature.class.getSimpleName()))
        .thenReturn(new ZipCheckFailFeature());
    Number actual = FraudTransactionModelV3Input.ZIPCHECK_FAIL.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void invalidZipCheckFail() {
    evalParams.setStripeAvsZipCheck(StripeAvsZipCheckEnum.PASS);
    when(featureFactory.getFeature(ZipCheckFailFeature.class.getSimpleName()))
        .thenReturn(new ZipCheckFailFeature());
    Number actual = FraudTransactionModelV3Input.ZIPCHECK_FAIL.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void validCardFundingFlag() {
    evalParams.cardType("credit");
    Number actual = FraudTransactionModelV3Input.CARD_FUNDING_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void invalidCardFundingFlag() {
    evalParams.cardType("debit");
    Number actual = FraudTransactionModelV3Input.CARD_FUNDING_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void validFundsInFlag() {
    evalParams.setCheckpointName("WalletAddFundCheckpoint");

    Number actual = FraudTransactionModelV3Input.FUNDSIN_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1, actual);
  }

  @Test
  public void invalidFundsInFlag() {
    evalParams.setCheckpointName("WalletWithdrawFundsCheckpoint");

    Number actual = FraudTransactionModelV3Input.FUNDSIN_FLAG.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void postalMatchFlagV2_missingStripeZip() {
    when(featureFactory.getFeature(StripeZipCodeMatchV2Feature.class.getSimpleName()))
        .thenReturn(new StripeZipCodeMatchV2Feature(identityService));
    evalParams.setStripeZipCode(null);

    Number actual = FraudTransactionModelV3Input.POSTAL_MATCH_FLAG_V2.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(3, actual);
  }

  @Test
  public void fnameJarow() {
    when(featureFactory.getFeature(StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName()))
        .thenReturn(new StripeFirstNameJarowSimilarityScoreFeature(identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    response.setFirstName("Dennis");
    when(identityService.getCustomer(customerId)).thenReturn(response);
    evalParams.setStripePaymentFullName("Dennis Test");

    Number actual = FraudTransactionModelV3Input.FNAME_JAROW.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(100.0, actual);
  }

  @Test
  public void fnameJarow_missing() {
    when(featureFactory.getFeature(StripeFirstNameJarowSimilarityScoreFeature.class.getSimpleName()))
        .thenReturn(new StripeFirstNameJarowSimilarityScoreFeature(identityService));

    Number actual = FraudTransactionModelV3Input.FNAME_JAROW.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void zeroUnderwritingScores() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));
    InternalOffer offer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    offer.setEvaluationContext(evalContext);
    evalContext.setSocureSigmaScore(0.0);
    evalContext.setSocureSyntheticScore(0.0f);
    evalContext.setSocurePhoneRiskScore(0.0f);
    evalContext.setSentilinkIdTheftScore(0);
    evalContext.setSentilinkAbuseScore(0);
    evalContext.sentilinkFirstPartySyntheticScore(0);
    evalContext.socureEmailRiskScore(0.0f);
    evalContext.sentilinkThirdPartySyntheticScore(0);
    evalContext.socureNamePhoneCorrelationScore(0.0f);
    evalContext.socureNameEmailCorrelationScore(0.0f);
    evalContext.socureFraudReasonCodes(Set.of("R617", "R665", "I161", "R021"));
    when(offerService.searchOffer(any(), any())).thenReturn(List.of(offer));
    Number maxSigmaScore = FraudTransactionModelV3Input.MAX_SOCURE_SIGMA_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSyntheticScore = FraudTransactionModelV3Input.MAX_SYNTHETIC_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxPhoneRiskScore = FraudTransactionModelV3Input.MAX_PHONERISK_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkIdTheftScore = FraudTransactionModelV3Input.MAX_SENTILINK_ID_THEFT_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkAbuseScore = FraudTransactionModelV3Input.MAX_SENTILINK_ABUSE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkFirstPartySyntheticScore = FraudTransactionModelV3Input
        .MAX_SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxEmailRiskScore = FraudTransactionModelV3Input.MAX_EMAILRISK_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkThirdPartySyntheticScore = FraudTransactionModelV3Input
        .MAX_SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxNamePhoneCorrelation = FraudTransactionModelV3Input.MAX_NAMEPHONECORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureR617 = FraudTransactionModelV3Input.MAX_SOCURE_R617.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxRiskyCarrierInd = FraudTransactionModelV3Input.MAX_RISKY_CARRIER_IND.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureR665 = FraudTransactionModelV3Input.MAX_SOCURE_R665.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxNameEmailCorrelation = FraudTransactionModelV3Input.MAX_NAMEEMAILCORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureI161 = FraudTransactionModelV3Input.MAX_SOCURE_I161.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(maxSigmaScore);
    Assertions.assertNull(maxSyntheticScore);
    Assertions.assertNull(maxPhoneRiskScore);
    Assertions.assertNull(maxSentilinkIdTheftScore);
    Assertions.assertNull(maxSentilinkAbuseScore);
    Assertions.assertNull(maxSentilinkFirstPartySyntheticScore);
    Assertions.assertNull(maxEmailRiskScore);
    Assertions.assertNull(maxSentilinkThirdPartySyntheticScore);
    Assertions.assertNull(maxNamePhoneCorrelation);
    Assertions.assertNull(maxSocureR617);
    Assertions.assertNull(maxRiskyCarrierInd);
    Assertions.assertNull(maxSocureR665);
    Assertions.assertNull(maxNameEmailCorrelation);
    Assertions.assertNull(maxSocureI161);
  }

  @Test
  public void nullEvaluationContext() {
    when(featureFactory.getFeature(CustomerUnderwritingScoresMaxFeature.class.getSimpleName()))
        .thenReturn(new CustomerUnderwritingScoresMaxFeature(offerService, decisionEngineService));
    InternalOffer offer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    offer.setEvaluationContext(evalContext);
    when(offerService.searchOffer(any(), any())).thenReturn(List.of(offer));
    Number maxSigmaScore = FraudTransactionModelV3Input.MAX_SOCURE_SIGMA_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSyntheticScore = FraudTransactionModelV3Input.MAX_SYNTHETIC_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxPhoneRiskScore = FraudTransactionModelV3Input.MAX_PHONERISK_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkIdTheftScore = FraudTransactionModelV3Input.MAX_SENTILINK_ID_THEFT_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkAbuseScore = FraudTransactionModelV3Input.MAX_SENTILINK_ABUSE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkFirstPartySyntheticScore = FraudTransactionModelV3Input
        .MAX_SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxEmailRiskScore = FraudTransactionModelV3Input.MAX_EMAILRISK_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSentilinkThirdPartySyntheticScore = FraudTransactionModelV3Input
        .MAX_SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE
        .getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxNamePhoneCorrelation = FraudTransactionModelV3Input.MAX_NAMEPHONECORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureR617 = FraudTransactionModelV3Input.MAX_SOCURE_R617.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxRiskyCarrierInd = FraudTransactionModelV3Input.MAX_RISKY_CARRIER_IND.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureR665 = FraudTransactionModelV3Input.MAX_SOCURE_R665.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxNameEmailCorrelation = FraudTransactionModelV3Input.MAX_NAMEEMAILCORRELATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Number maxSocureI161 = FraudTransactionModelV3Input.MAX_SOCURE_I161.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(maxSigmaScore);
    Assertions.assertNull(maxSyntheticScore);
    Assertions.assertNull(maxPhoneRiskScore);
    Assertions.assertNull(maxSentilinkIdTheftScore);
    Assertions.assertNull(maxSentilinkAbuseScore);
    Assertions.assertNull(maxSentilinkFirstPartySyntheticScore);
    Assertions.assertNull(maxEmailRiskScore);
    Assertions.assertNull(maxSentilinkThirdPartySyntheticScore);
    Assertions.assertNull(maxNamePhoneCorrelation);
    Assertions.assertNull(maxSocureR617);
    Assertions.assertNull(maxRiskyCarrierInd);
    Assertions.assertNull(maxSocureR665);
    Assertions.assertNull(maxNameEmailCorrelation);
    Assertions.assertNull(maxSocureI161);
  }
}
