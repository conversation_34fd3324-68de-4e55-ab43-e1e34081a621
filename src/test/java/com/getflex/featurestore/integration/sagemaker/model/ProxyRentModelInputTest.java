package com.getflex.featurestore.integration.sagemaker.model;

import static com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature.OBJECT_MAPPER;
import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.CreditReportFeature;
import com.getflex.featurestore.model.feature.IsInNetworkFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.proxyrent.CityNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.CityNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.PropertyBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.PropertyRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ProxyRentUtils;
import com.getflex.featurestore.model.feature.proxyrent.StateNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StateNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StreetNameBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.StreetNameRentMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ZipCodeBpMeanFeature;
import com.getflex.featurestore.model.feature.proxyrent.ZipCodeRentMeanFeature;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.utils.AlloyUtils;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Optional;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ProxyRentModelInputTest {

  @Mock
  FeatureFactory featureFactory;

  @Mock
  BillingService billingService;

  @Mock
  ProxyRentUtils proxyRentUtils;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  AlloyUtils alloyUtils;

  long customerId = 123L;

  EvalParams evalParams = new EvalParams().customerId(customerId).billerId(1L).alloyReportUrl("url");

  @Test
  public void vantageScoreInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);

    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Number value = ProxyRentModelInput.VANTAGE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, 573);
  }

  @Test
  public void vantageScoreNegativeOneInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "07-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);
    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Assertions.assertNull(ProxyRentModelInput.VANTAGE_SCORE.getModelInputValueFunction()
        .apply(featureFactory, evalParams));
  }

  @Test
  public void uwMonthInput() {
    Number value = ProxyRentModelInput.UW_MONTH.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, LocalDate.now().getMonthValue());
  }

  @ParameterizedTest
  @MethodSource("provideAcquisitionChannelInput_inNetwork")
  public void acquisitionChannelInput_inNetwork(BillingIntegrationTypeEnum integrationType, 
      boolean propertyOutOfNetwork, Integer expected) {
    when(featureFactory.getFeature(IsInNetworkFeature.class.getSimpleName()))
        .thenReturn(new IsInNetworkFeature(billingService));
    PropertyInfo propertyInfo = new PropertyInfo(integrationType, propertyOutOfNetwork, null);
    when(billingService.getPropertyByIdentityBillerId(any())).thenReturn(propertyInfo);

    Number actual = ProxyRentModelInput.ACQUISITION_CHANNEL.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(expected, actual);
  }

  private static Stream<Arguments> provideAcquisitionChannelInput_inNetwork() {
    return Stream.of(
        Arguments.of(BillingIntegrationTypeEnum.PORTAL, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.PORTAL, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.FLEX_ANYWHERE, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.FLEX_ANYWHERE, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.P2P, false, 0),
        Arguments.of(BillingIntegrationTypeEnum.P2P, true, 0),
        Arguments.of(BillingIntegrationTypeEnum.YARDI, false, 1),
        Arguments.of(BillingIntegrationTypeEnum.YARDI, true, 0),
        Arguments.of(null, false, 1),
        Arguments.of(null, true, 0)
    );
  }

  @Test
  public void acquisitionChannelInput_billingError() {
    when(featureFactory.getFeature(IsInNetworkFeature.class.getSimpleName()))
        .thenReturn(new IsInNetworkFeature(billingService));
    when(billingService.getPropertyByIdentityBillerId(any()))
        .thenThrow(new InternalDependencyFailureException("failure"));

    Number actual = ProxyRentModelInput.ACQUISITION_CHANNEL.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(0, actual);
  }

  @Test
  public void creditHistoryDurationInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);

    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Number value = ProxyRentModelInput.CREDIT_HISTORY_DURATION.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, 262);
  }

  @Test
  public void collectionsBalanceNoMedicalInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);

    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Number value = ProxyRentModelInput.COLLECTIONS_BALANCE_NO_MEDICAL.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, 0L);
  }

  @Test
  public void tuDebtInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);

    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Number value = ProxyRentModelInput.TU_DEBT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, 1820D);
  }

  @Test
  public void totalOutstandingBalanceOpenRevolvingInput() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    AlloyReport report = OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class);

    when(featureFactory.getFeature(CreditReportFeature.class.getSimpleName())).thenReturn(
        new CreditReportFeature(alloyUtils));
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(report);

    Number value = ProxyRentModelInput.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING.getModelInputValueFunction()
        .apply(featureFactory, evalParams);

    Assertions.assertNotNull(value);
    Assertions.assertEquals(value, 660L);
  }

  @Test
  public void propertyMeanRentInput() {
    String featureName = "property_rent_mean_feature";
    when(featureFactory.getFeature(PropertyRentMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId())).thenReturn("1");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "1"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void propertyMeanRentInput_noOfflineFeature() {
    String featureName = "property_rent_mean_feature";
    when(featureFactory.getFeature(PropertyRentMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId())).thenReturn("1");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "1"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void propertyMeanRentInput_noBillerData() {
    String featureName = "property_rent_mean_feature";
    when(featureFactory.getFeature(PropertyRentMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId()))
        .thenThrow(new InternalDependencyFailureException("error!"));
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void streetMeanRentInput() {
    String featureName = "street_name_rent_mean_feature";
    when(featureFactory.getFeature(StreetNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new StreetNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_1111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_1111"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.STREET_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void streetMeanRentInput_noOfflineFeature() {
    String featureName = "street_name_rent_mean_feature";
    when(featureFactory.getFeature(StreetNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new StreetNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_1111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_1111"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.STREET_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void zipMeanRentInput() {
    String featureName = "zip_code_rent_mean_feature";
    when(featureFactory.getFeature(ZipCodeRentMeanFeature.class.getSimpleName()))
        .thenReturn(new ZipCodeRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_1111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_1111"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.ZIP5_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void zipMeanRentInput_noOfflineFeature() {
    String featureName = "zip_code_rent_mean_feature";
    when(featureFactory.getFeature(ZipCodeRentMeanFeature.class.getSimpleName()))
        .thenReturn(new ZipCodeRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_1111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_1111"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.ZIP5_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void cityMeanRentInput() {
    String featureName = "city_name_rent_mean_feature";
    when(featureFactory.getFeature(CityNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new CityNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.CITY_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void cityMeanRentInput_noOfflineFeature() {
    String featureName = "city_name_rent_mean_feature";
    when(featureFactory.getFeature(CityNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new CityNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.CITY_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void stateMeanRentInput() {
    String featureName = "state_name_rent_mean_feature";
    when(featureFactory.getFeature(StateNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new StateNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.STATE_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void stateMeanRentInput_noOfflineFeature() {
    String featureName = "state_name_rent_mean_feature";
    when(featureFactory.getFeature(StateNameRentMeanFeature.class.getSimpleName()))
        .thenReturn(new StateNameRentMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.STATE_MEAN_RENT.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void propertyMeanBpInput() {
    String featureName = "property_rent_bp_feature";
    when(featureFactory.getFeature(PropertyBpMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId())).thenReturn("1");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "1"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void propertyMeanBpInput_noOfflineFeature() {
    String featureName = "property_rent_bp_feature";
    when(featureFactory.getFeature(PropertyBpMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId())).thenReturn("1");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "1"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void propertyMeanBpInput_noBillerData() {
    String featureName = "property_rent_bp_feature";
    when(featureFactory.getFeature(PropertyBpMeanFeature.class.getSimpleName()))
        .thenReturn(new PropertyBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams.getBillerId()))
        .thenThrow(new InternalDependencyFailureException("error!"));
    Number actual = ProxyRentModelInput.PROPERTY_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void streetMeanBpInput() {
    String featureName = "street_name_rent_bp_feature";
    when(featureFactory.getFeature(StreetNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new StreetNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.STREET_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void streetMeanBpInput_noOfflineFeature() {
    String featureName = "street_name_rent_bp_feature";
    when(featureFactory.getFeature(StreetNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new StreetNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn(String.valueOf(evalParams.getCustomerId()));
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.STREET_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void zipMeanBpInput() {
    String featureName = "zip_code_rent_bp_feature";
    when(featureFactory.getFeature(ZipCodeBpMeanFeature.class.getSimpleName()))
        .thenReturn(new ZipCodeBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams)).thenReturn("11111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "11111"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.ZIP5_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void zipMeanBpInput_noOfflineFeature() {
    String featureName = "zip_code_rent_bp_feature";
    when(featureFactory.getFeature(ZipCodeBpMeanFeature.class.getSimpleName()))
        .thenReturn(new ZipCodeBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams)).thenReturn("11111");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "11111")).thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.ZIP5_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void cityMeanBpInput() {
    String featureName = "city_name_rent_bp_feature";
    when(featureFactory.getFeature(CityNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new CityNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.CITY_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void cityMeanBpInput_noOfflineFeature() {
    String featureName = "city_name_rent_bp_feature";
    when(featureFactory.getFeature(CityNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new CityNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.CITY_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }

  @Test
  public void stateMeanBpInput() {
    String featureName = "state_name_rent_bp_feature";
    when(featureFactory.getFeature(StateNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new StateNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    OfflineFeature offlineFeature = OfflineFeature.builder().featureValue("1234.00").featureName(featureName).build();
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.of(offlineFeature));
    Number actual = ProxyRentModelInput.STATE_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertEquals(1234.00D, actual);
  }

  @Test
  public void stateMeanBpInput_noOfflineFeature() {
    String featureName = "state_name_rent_bp_feature";
    when(featureFactory.getFeature(StateNameBpMeanFeature.class.getSimpleName()))
        .thenReturn(new StateNameBpMeanFeature(proxyRentUtils, offlineFeatureRepo));
    when(proxyRentUtils.formatPrimaryKey(featureName, evalParams))
        .thenReturn("L123_11122");
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(featureName, "L123_11122"))
        .thenReturn(Optional.empty());
    Number actual = ProxyRentModelInput.STATE_MEAN_BP.getModelInputValueFunction()
        .apply(featureFactory, evalParams);
    Assertions.assertNull(actual);
  }
}
