package com.getflex.featurestore.dao.model;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import org.junit.jupiter.api.Test;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

class DdbEventTest {

  @Test
  void testBuilderAndGetters() {
    DdbEvent event = DdbEvent.builder()
        .customerId("cust1")
        .name("eventName")
        .dtCreated("2025-01-01T00:00:00Z")
        .id("id-123")
        .entityId("entity-321")
        .entityType("ENTITY_TYPE")
        .metadata("{\"foo\":\"bar\"}")
        .category("CATEGORY")
        .dtArrived("2025-01-01T01:00:00Z")
        .build();

    assertEquals("cust1", event.getCustomerId());
    assertEquals("eventName", event.getName());
    assertEquals("entity-321", event.getEntityId());
    assertEquals("ENTITY_TYPE", event.getEntityType());
    assertEquals("{\"foo\":\"bar\"}", event.getMetadata());
    assertEquals("CATEGORY", event.getCategory());
    assertEquals("2025-01-01T01:00:00Z", event.getDtArrived());
  }

  @Test
  void testCompositeSortKeyGetter() {
    DdbEvent event = DdbEvent.builder()
        .name("eventName")
        .dtCreated("2025-01-01T00:00:00Z")
        .id("id-123")
        .build();
    assertNotNull(event.getCompositeSortKey());
  }

  @Test
  void testCategoryTimestampIdCompositeSortKeyGetter() {
    DdbEvent event = DdbEvent.builder()
        .name("eventName")
        .category("categoryName")
        .dtCreated("2025-01-01T00:00:00Z")
        .id("id-123")
        .build();
    assertNotNull(event.getCategoryTimestampIdCompositeSortKey());
  }

  @Test
  void testCompositeSortKeyBuilderSetsFields() {
    DdbEvent event = DdbEvent.builder()
        .compositeSortKey("foo#bar#baz")
        .build();
    assertEquals("foo", event.getName());
  }

  @Test
  void testCategoryTimestampIdCompositeSortKeyBuilderSetsFields() {
    DdbEvent event = DdbEvent.builder()
        .categoryTimestampIdCompositeSortKey("foo#bar#baz")
        .build();
    assertEquals("foo", event.getCategory());
  }

  @Test
  void testTableSchemaFromClass() {
    assertDoesNotThrow(() -> {
      TableSchema<DdbEvent> schema = TableSchema.fromImmutableClass(DdbEvent.class);
      assertNotNull(schema);
    });
  }

  @Test
  void testBuilderDefaults() {
    DdbEvent event = DdbEvent.builder().build();
    assertNotNull(event.getDtCreated());
    assertNotNull(event.getId());
  }

  @Test
  void testDefaultValuesAndNulls() {
    DdbEvent event = DdbEvent.builder().build();
    assertNotNull(event.getDtCreated());
    assertNotNull(event.getId());
  }

  @Test
  void testToStringAndHashCode() {
    DdbEvent event1 = DdbEvent.builder().customerId("c1").build();
    DdbEvent event2 = DdbEvent.builder().customerId("c1").build();
    assertNotNull(event1.toString());
    assertNotNull(event1.hashCode());
    assertNotNull(event2.hashCode());
  }
}
