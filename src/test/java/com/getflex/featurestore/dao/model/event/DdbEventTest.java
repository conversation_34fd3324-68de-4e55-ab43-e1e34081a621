package com.getflex.featurestore.dao.model.event;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.getflex.featurestore.dao.model.DdbEvent;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;

class DdbEventTest {

  private static final TableSchema<DdbEvent> TABLE_SCHEMA =
      TableSchema.fromClass(DdbEvent.class);

  @Test
  void testDdbEventBuilder() {
    // Given
    String customerId = "test-customer-1";
    String name = "test-event";
    String dtCreated = "2025-01-01T00:00:00Z";
    String entityId = "entity-123";
    String entityType = "TEST_ENTITY";
    String metadata = "{\"key\":\"value\"}";
    String category = "DEVICE_DATA";
    String dtArrived = "2025-01-01T00:00:01Z";

    // When
    DdbEvent event = DdbEvent.builder()
        .customerId(customerId)
        .name(name)
        .dtCreated(dtCreated)
        .entityId(entityId)
        .entityType(entityType)
        .metadata(metadata)
        .category(category)
        .dtArrived(dtArrived)
        .build();

    // Then
    assertNotNull(event);
    assertEquals(customerId, event.getCustomerId());
    assertEquals(name, event.getName());
    assertNotNull(event.getId());
    assertFalse(event.getId().isEmpty());
    assertEquals(entityId, event.getEntityId());
    assertEquals(entityType, event.getEntityType());
    assertEquals(metadata, event.getMetadata());
    assertEquals(category, event.getCategory());
    assertEquals(dtArrived, event.getDtArrived());
  }

  @Test
  void testCompositeSortKey() {
    // Given
    String name = "test-event";
    String dtCreated = "2025-01-01T00:00:00Z";
    String id = "test-id-123";

    DdbEvent event = DdbEvent.builder()
        .customerId("test-customer-1")
        .name(name)
        .dtCreated(dtCreated)
        .id(id)
        .build();

    String compositeKey = event.getCompositeSortKey();
    assertNotNull(compositeKey);
  }

  @Test
  void testDynamoDbAnnotations() {
    // This test verifies that the DynamoDB annotations are correctly set up
    assertDoesNotThrow(() -> {
      // This will throw if the annotations are not properly set up
      TableSchema<DdbEvent> schema = TableSchema.fromClass(DdbEvent.class);
      assertNotNull(schema);
    });
  }
}
