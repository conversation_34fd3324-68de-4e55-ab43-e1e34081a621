package com.getflex.featurestore.service;

import static org.junit.Assert.assertEquals;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.utils.FulfillmentUtils;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.fulfillment.api.FulfillmentsApi;
import com.getflex.fulfillment.client.ApiException;
import com.getflex.fulfillment.model.FulfillmentInfo;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import java.time.LocalDate;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FulfillmentServiceTests {

  @InjectMocks FulfillmentService fulfillmentService;

  @Mock FulfillmentsApi fulfillmentsApi;

  // will be used to mock the metrics, do not delete it
  @Mock Metrics metrics;

  private final Long customerId = 1L;
  private final String billTransactionId = "2";
  private final Long fulfillmentId = 3L;

  /**
   * Test case for getFulfillmentByCustomerInBp
   *
   * @throws ApiException
   */
  @Test
  public void getFulfillmentByCustomerInBpTest() throws ApiException {
    FulfillmentInfo expected = new FulfillmentInfo();
    expected.setBillTransactionId(billTransactionId);

    LocalDate payDate = FulfillmentUtils.getPayDate();
    // mock data
    FulfillmentInfo mockResponse = expected;
    Mockito.when(fulfillmentsApi.queryByCustomerInBP(customerId, payDate, true, null))
        .thenReturn(mockResponse);

    FulfillmentInfo actual = fulfillmentService.getFulfillmentByCustomerInBp(customerId);
    assertEquals(expected, actual);
  }

  /**
   * Test case for getFulfillmentByCustomerInBp when ApiException is thrown
   *
   * @throws ApiException
   */
  @Test
  public void getFulfillmentByCustomerInBpTestThrowException() throws ApiException {
    LocalDate payDate = FulfillmentUtils.getPayDate();
    Mockito.when(fulfillmentsApi.queryByCustomerInBP(customerId, payDate, true, null))
        .thenThrow(new ApiException());
    Assert.assertThrows(
        InternalDependencyFailureException.class,
        () -> fulfillmentService.getFulfillmentByCustomerInBp(customerId)
    );
  }

  /**
   * Test case for getFulfillmentStatusByBillTransactionIdV2
   *
   * @throws ApiException
   */
  @Test
  public void getFulfillmentStatusByBillTransactionIdV2Test() throws ApiException {
    GetStatusResponseV2 expected = new GetStatusResponseV2();
    expected.setStatus(GetStatusResponseV2.StatusEnum.PAUSED);

    // mock data
    GetStatusResponseV2 mockResponse = expected;
    Mockito.when(fulfillmentsApi.getStatus2(billTransactionId, fulfillmentId, null)).thenReturn(mockResponse);

    GetStatusResponseV2 actual =
        fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
            billTransactionId, fulfillmentId);
    assertEquals(expected, actual);
  }

  /**
   * Test case for getFulfillmentStatusByBillTransactionIdV2 when ApiException is thrown
   *
   * @throws ApiException
   */
  @Test
  public void getFulfillmentStatusByBillTransactionIdV2TestThrowException() throws ApiException {
    FulfillmentInfo expectedFulfillmentInfo = new FulfillmentInfo();
    expectedFulfillmentInfo.setBillTransactionId(billTransactionId);
    Mockito.when(fulfillmentsApi.getStatus2(billTransactionId, fulfillmentId, null))
        .thenThrow(new ApiException());
    Assert.assertThrows(
        InternalDependencyFailureException.class,
        () -> fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(billTransactionId, fulfillmentId)
    );
  }
}
