package com.getflex.featurestore.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.Feature;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.IsTrustedDeviceIdFeature;
import com.getflex.featurestore.model.feature.IsTrustedPhoneNumberFeature;
import com.getflex.featurestore.model.feature.LatestEstimatedIncomeCentFeature;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.utils.Metrics;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FeatureServiceTests {

  @Spy
  @InjectMocks
  FeatureService featureService;
  @Mock
  FeatureFactory featureFactory;
  @Mock
  Metrics metrics;
  @Mock
  EventRepository eventRepository;
  @Mock
  OfflineFeatureRepo offlineFeatureRepo;
  @Mock
  IdentityService identityService;

  ObjectMapper objectMapper = new ObjectMapper();

  @Test
  public void getFeatureValues_oneFeature() throws JsonProcessingException {
    EvalParams evalParams = new EvalParams();
    Long customerId = 123L;
    Long estimatedIncome = 10000000L;
    evalParams.setCustomerId(customerId);
    setupLatestEstimatedIncomeCentFeature(customerId, estimatedIncome);

    CompletableFuture future = mockFeatureValueFuture(LatestEstimatedIncomeCentFeature.class.getSimpleName(),
        evalParams);
    when(featureService.submitTasks(anySet(), any(EvalParams.class))).thenReturn(List.of(future));
    MockedStatic<CompletableFuture> completableFutureCls = Mockito.mockStatic(CompletableFuture.class);
    completableFutureCls.when(() -> CompletableFuture.allOf(any(CompletableFuture[].class))).thenReturn(future);
    Set<String> featureNames = Set.of(LatestEstimatedIncomeCentFeature.class.getSimpleName());

    List<FeatureValue> featureValues = featureService.getFeatureValues(featureNames, evalParams);
    Assertions.assertEquals(1, featureValues.size());
    FeatureValue featureValue = featureValues.get(0);
    Assertions.assertEquals(LatestEstimatedIncomeCentFeature.class.getSimpleName(), featureValue.getName());
    Assertions.assertEquals(estimatedIncome.intValue(), featureValue.getValue());
    completableFutureCls.close();
  }

  @Test
  public void getFeatureValues_multipleFeatures() throws JsonProcessingException {
    Long customerId = 123L;
    Long estimatedIncome = 10000000L;
    String deviceId = "test-device-id";
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setDeviceId(deviceId);
    setupLatestEstimatedIncomeCentFeature(customerId, estimatedIncome);
    when(featureFactory.getFeature(ArgumentMatchers.matches(IsTrustedPhoneNumberFeature.class.getSimpleName())))
        .thenReturn(new IsTrustedPhoneNumberFeature(offlineFeatureRepo, identityService));
    when(featureFactory.getFeature(ArgumentMatchers.matches(IsTrustedDeviceIdFeature.class.getSimpleName())))
        .thenReturn(new IsTrustedDeviceIdFeature(offlineFeatureRepo, identityService));

    GetCustomerResponse response = new GetCustomerResponse();
    String phone = "test-phone-number";
    String customerPublicId = "test-customer-public-id";
    response.setPhone(phone);
    response.setCustomerPublicId(customerPublicId);
    when(identityService.getCustomer(customerId)).thenReturn(response);

    Optional<OfflineFeature> trustedPhoneFeature = Optional.of(
        OfflineFeature.builder().featureName("trusted_phone_number_to_customer_id")
            .primaryKey(phone).featureValue(customerPublicId)
            .build());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        ArgumentMatchers.matches("trusted_phone_number_to_customer_id"),
        ArgumentMatchers.matches(phone))
    ).thenReturn(trustedPhoneFeature);

    Optional<OfflineFeature> trustedDeviceIdFeature = Optional.of(
        OfflineFeature.builder().featureName("trusted_device_id_to_customer_id")
            .primaryKey(deviceId).featureValue(customerPublicId)
            .build());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        ArgumentMatchers.matches("trusted_device_id_to_customer_id"),
        ArgumentMatchers.matches(deviceId))
    ).thenReturn(trustedDeviceIdFeature);

    CompletableFuture future1 = mockFeatureValueFuture(LatestEstimatedIncomeCentFeature.class.getSimpleName(),
        evalParams);
    CompletableFuture future2 = mockFeatureValueFuture(IsTrustedPhoneNumberFeature.class.getSimpleName(), evalParams);
    CompletableFuture future3 = mockFeatureValueFuture(IsTrustedDeviceIdFeature.class.getSimpleName(), evalParams);
    when(featureService.submitTasks(anySet(), any(EvalParams.class))).thenReturn(List.of(future1, future2, future3));
    MockedStatic<CompletableFuture> completableFutureCls = Mockito.mockStatic(CompletableFuture.class);
    completableFutureCls.when(() -> CompletableFuture.allOf(any(CompletableFuture[].class))).thenReturn(future1);

    Set<String> featureNames = Set.of(LatestEstimatedIncomeCentFeature.class.getSimpleName(),
        IsTrustedPhoneNumberFeature.class.getSimpleName(),
        IsTrustedDeviceIdFeature.class.getSimpleName());
    List<FeatureValue> featureValues = featureService.getFeatureValues(featureNames, evalParams);

    Assertions.assertEquals(3, featureValues.size());
    for (FeatureValue featureValue : featureValues) {
      if (LatestEstimatedIncomeCentFeature.class.getSimpleName().equals(featureValue.getName())) {
        Assertions.assertEquals(estimatedIncome.intValue(), featureValue.getValue());
      } else if (IsTrustedPhoneNumberFeature.class.getSimpleName().equals(featureValue.getName())
          || IsTrustedDeviceIdFeature.class.getSimpleName().equals(featureValue.getName())) {
        Assertions.assertEquals(Boolean.TRUE, featureValue.getValue());
      }
    }
    completableFutureCls.close();
  }

  @Test
  public void getFeatures() {
    BaseFeature expectedFeature = new LatestEstimatedIncomeCentFeature(eventRepository);
    List<BaseFeature> featuresList = List.of(expectedFeature);
    Set<EvalParamKey> evalParamKeySet = Set.of(EvalParamKey.CUSTOMER_ID);
    when(featureFactory.getFeatures(eq(evalParamKeySet))).thenReturn(featuresList);

    List<Feature> actual = featureService.getFeatures(evalParamKeySet);
    Assertions.assertEquals(1, actual.size());
    Feature actualFeature = actual.get(0);
    Assertions.assertEquals(actualFeature.getName(), expectedFeature.getName());
    Assertions.assertEquals(actualFeature.getType(), expectedFeature.getType());
    Assertions.assertEquals(actualFeature.getDescription(), expectedFeature.getDescription());
  }

  @Test
  public void getFeatures_emptyKeys() {
    Set<EvalParamKey> evalParamKeys = Set.of(EvalParamKey.class.getEnumConstants());
    featureService.getFeatures(Collections.emptySet());
    verify(featureFactory, times(1)).getFeatures(eq(evalParamKeys));
  }

  private CompletableFuture mockFeatureValueFuture(String featureName, EvalParams evalParams) {
    BaseFeature feature = featureFactory.getFeature(featureName);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    CompletableFuture future = mock(CompletableFuture.class);
    when(future.join()).thenReturn(featureValue);
    return future;
  }

  private void setupLatestEstimatedIncomeCentFeature(Long customerId, Long estimatedIncome)
      throws JsonProcessingException {
    when(featureFactory.getFeature(ArgumentMatchers.matches(LatestEstimatedIncomeCentFeature.class.getSimpleName())))
        .thenReturn(new LatestEstimatedIncomeCentFeature(eventRepository));
    Event incomeEvent = Event.builder()
        .metadata(objectMapper.writeValueAsString(
            IncomeMetadata.builder().estimatedGrossAnnualIncomeCents(estimatedIncome.toString()).build())
        )
        .build();
    when(eventRepository.findFirstByNameAndCustomerIdOrderByDtArrivedDesc(
        EventName.EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME, customerId.toString())
    ).thenReturn(Optional.of(incomeEvent));
  }
}
