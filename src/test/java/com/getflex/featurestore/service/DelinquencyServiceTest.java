package com.getflex.featurestore.service;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.service.delinquency.DelinquencyInfo;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.time.Clock;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DelinquencyServiceTest {

  @Mock
  private LedgerService ledgerService;

  @Mock
  private Clock etClock;

  @Test
  public void testExistingBehaviorUnchangedWithNonNullLookback() {
    // Set up clock mocks
    Instant fixedInstant = Instant.parse("2024-08-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    lenient().when(etClock.instant()).thenReturn(fixedInstant);
    lenient().when(etClock.getZone()).thenReturn(zoneId);

    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    
    // Add transaction within lookback period
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    
    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    
    // Test with 12 months lookback - existing behavior
    final DelinquencyService delinquencyService = new DelinquencyService(ledgerService, etClock);
    DelinquencyInfo result = delinquencyService.getDelinquencyInfo(customerId, 1, 12, false);
    
    assertNotNull(result);
    assertTrue(result.isDq());
  }

  @ParameterizedTest
  @MethodSource("provideNormalizeBpDateTestCases")
  void testNormalizeBpDate(
      OffsetDateTime inputDate,
      OffsetDateTime expectedNormalizedDate,
      String description
  ) {
    OffsetDateTime actualNormalizedDate = DelinquencyService.normalizeBpDate(inputDate);
    assertEquals(expectedNormalizedDate, actualNormalizedDate,
        "Failed for: " + description);
  }

  private static Stream<Arguments> provideNormalizeBpDateTestCases() {
    return Stream.of(
        // Dates in last 5 days of month - should normalize to first day of NEXT month
        Arguments.of(
            OffsetDateTime.of(2025, 1, 27, 10, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 2, 1, 10, 0, 0, 0, ZoneOffset.UTC),
            "Jan 27 (in last 5 days) -> Feb 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 2, 25, 8, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 3, 1, 8, 0, 0, 0, ZoneOffset.UTC),
            "Feb 25 non-leap year (in last 5 days) -> Mar 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 12, 28, 12, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2026, 1, 1, 12, 0, 0, 0, ZoneOffset.UTC),
            "Dec 28 (year boundary) -> Jan 1 next year"
        ),

        // Dates NOT in last 5 days - should normalize to first day of CURRENT month
        Arguments.of(
            OffsetDateTime.of(2025, 1, 15, 10, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 1, 1, 10, 0, 0, 0, ZoneOffset.UTC),
            "Jan 15 (middle of month) -> Jan 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 2, 1, 14, 30, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 2, 1, 14, 30, 0, 0, ZoneOffset.UTC),
            "Feb 1 (already first day) -> Feb 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 1, 26, 9, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 1, 1, 9, 0, 0, 0, ZoneOffset.UTC),
            "Jan 26 (NOT in last 5 days for 31-day month) -> Jan 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 2, 23, 16, 45, 30, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 2, 1, 16, 45, 30, 0, ZoneOffset.UTC),
            "Feb 23 (NOT in last 5 days for 28-day month) -> Feb 1"
        ),

        // Edge cases for different month lengths
        Arguments.of(
            OffsetDateTime.of(2025, 4, 26, 10, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 5, 1, 10, 0, 0, 0, ZoneOffset.UTC),
            "Apr 26 (30-day month, in last 5 days) -> May 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 5, 27, 10, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 6, 1, 10, 0, 0, 0, ZoneOffset.UTC),
            "May 27 (31-day month, in last 5 days) -> Jun 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2025, 5, 26, 10, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 5, 1, 10, 0, 0, 0, ZoneOffset.UTC),
            "May 26 (31-day month, NOT in last 5 days) -> May 1"
        )
    );
  }

  @ParameterizedTest
  @MethodSource("provideGetDelinquencyInfoNormalizationTestCases")
  void testGetDelinquencyInfo_returnsNormalizedBpDate(
      OffsetDateTime bpDate,
      OffsetDateTime expectedNormalizedDate,
      String description
  ) {
    // Set up clock mocks - set to a date after all test dates including year boundary
    Instant fixedInstant = Instant.parse("2025-05-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);

    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    // Add transaction with BP date
    txList.add(new RecordLedgerWallet()
        .billTransactionId("btx1")
        .customerId(customerId)
        .amount(2000L)
        .dtCreated(bpDate)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue())
        .paymentCategoryId(MovementCategory.ACH_TRANSFER.getValue()));

    // Add payment after due date to make it delinquent
    // For dqMonths=1, due date is bpDate + 2 months, set to first day of month
    // So payment needs to be after that to be delinquent
    // Also, payment must be less than the bill amount to remain delinquent
    OffsetDateTime dueDate = expectedNormalizedDate.plusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
    OffsetDateTime latePaymentDate = dueDate.plusDays(5); // Payment 5 days after due date
    txList.add(new RecordLedgerWallet()
        .billTransactionId("btx1")
        .customerId(customerId)
        .amount(1500L)  // Pay less than the full 2000L bill to remain delinquent
        .dtCreated(latePaymentDate)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue()));

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);

    // Test with 12 months lookback
    final DelinquencyService delinquencyService = new DelinquencyService(ledgerService, etClock);
    DelinquencyInfo result = delinquencyService.getDelinquencyInfo(customerId, 1, 12, false);

    assertNotNull(result, "Result should not be null for " + description);
    assertTrue(result.isDq());
    assertNotNull(result.firstBpDqDate(), "First BP DQ date should not be null for " + description);
    assertEquals(expectedNormalizedDate, result.firstBpDqDate());
  }

  private static Stream<Arguments> provideGetDelinquencyInfoNormalizationTestCases() {
    // Clock is set to May 10, 2025, lookback is 12 months
    // Cutoff date calculation: May 1, 2025 - 12 months - 5 days = April 26, 2024
    // So transactions need to be after April 26, 2024 (cutoff date)
    return Stream.of(
        // BP dates in last 5 days of month
        Arguments.of(
            OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC),
            "BP on Jun 28 (in last 5 days) -> normalized to Jul 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 6, 30, 14, 30, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 7, 1, 14, 30, 0, 0, ZoneOffset.UTC),
            "BP on Jun 30 (last day) -> normalized to Jul 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 7, 27, 9, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 8, 1, 9, 0, 0, 0, ZoneOffset.UTC),
            "BP on Jul 27 (in last 5 days) -> normalized to Aug 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 12, 27, 16, 45, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2025, 1, 1, 16, 45, 0, 0, ZoneOffset.UTC),
            "BP on Dec 27 (year boundary) -> normalized to Jan 1 next year"
        ),

        // BP dates NOT in last 5 days of month
        Arguments.of(
            OffsetDateTime.of(2024, 6, 15, 10, 15, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC),
            "BP on Jun 15 (middle of month) -> normalized to Jun 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 7, 1, 8, 0, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 7, 1, 8, 0, 0, 0, ZoneOffset.UTC),
            "BP on Jul 1 (already first day) -> stays Jul 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 8, 24, 11, 30, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 8, 1, 11, 30, 0, 0, ZoneOffset.UTC),
            "BP on Aug 24 (NOT in last 5 days) -> normalized to Aug 1"
        ),
        Arguments.of(
            OffsetDateTime.of(2024, 9, 25, 13, 15, 0, 0, ZoneOffset.UTC),
            OffsetDateTime.of(2024, 9, 1, 13, 15, 0, 0, ZoneOffset.UTC),
            "BP on Sep 25 (30-day month, NOT in last 5 days) -> normalized to Sep 1"
        ),

        // Different time zones
        Arguments.of(
            OffsetDateTime.of(2024, 6, 29, 10, 0, 0, 0, ZoneOffset.ofHours(-5)),
            OffsetDateTime.of(2024, 7, 1, 10, 0, 0, 0, ZoneOffset.ofHours(-5)),
            "BP on Jun 29 EST (in last 5 days) -> normalized to Jul 1 EST"
        )
    );
  }

  @Test
  public void testExcludeMoveInTransactions() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    // Add transaction within lookback period
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue()).productCategoryId(2L)
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);

    // Test with 12 months lookback - should be no dq because the only dq transaction is move in type
    final DelinquencyService delinquencyService = new DelinquencyService(ledgerService, etClock);
    DelinquencyInfo result = delinquencyService.getDelinquencyInfo(customerId, 1, 12, false);

    assertNotNull(result);
    assertFalse(result.isDq());
  }
}
