package com.getflex.featurestore.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EntityType;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.utils.Metrics;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.function.Consumer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.ActiveProfiles;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
@MockitoSettings(strictness = Strictness.LENIENT)
class EventServiceTests {

  @Mock
  private Metrics metrics;
  @Mock
  private DynamoDbEnhancedClient enhancedClient;
  @Mock
  private DynamoDbTable<DdbEvent> ddbTable;
  @Mock
  private DynamoDbIndex<DdbEvent> gsi;
  @Mock
  private SdkIterable<Page<DdbEvent>> sdkIterable;
  @Mock
  private Page<DdbEvent> mockPage;
  @Mock
  private ServiceConfig serviceConfig;
  @Mock
  private SqsClient sqsClient;
  @Mock
  private EventRepository eventRepository;

  private EventService eventService;

  @BeforeEach
  void setUp() {
    TableSchema<DdbEvent> tableSchema = TableSchema.fromClass(DdbEvent.class);
    Mockito.lenient().when(ddbTable.tableSchema()).thenReturn(tableSchema);
    eventService = new EventService(
        eventRepository,
        metrics,
        sqsClient,
        serviceConfig,
        enhancedClient,
        ddbTable
    );
  }

  @Test
  void testGetCustomerEventsByCategory() {
    Long customerId = 123L;
    String category = EventCategory.DEVICE_DATA.name();
    OffsetDateTime ts = OffsetDateTime.now();

    DdbEvent event = DdbEvent.builder().customerId("123").build();
    List<DdbEvent> items = List.of(event);

    when(ddbTable.index(EventService.CATEGORY_TIMESTAMP_ID_INDEX)).thenReturn(gsi);
    when(gsi.query(ArgumentMatchers.<Consumer<QueryEnhancedRequest.Builder>>any()))
        .thenReturn(sdkIterable);
    when(sdkIterable.iterator()).thenReturn(List.of(mockPage).iterator());
    when(mockPage.items()).thenReturn(items);

    List<DdbEvent> result = eventService.getCustomerEventsByCategory(customerId, category);
    assertEquals(1, result.size());
    assertEquals(event, result.get(0));
  }

  @Test
  void testGetCustomerEventsByCategoryWithTimestamp() {
    Long customerId = 123L;
    String category = EventCategory.DEVICE_DATA.name();
    OffsetDateTime ts = OffsetDateTime.now();

    DdbEvent event = DdbEvent.builder().customerId("123").build();
    List<DdbEvent> items = List.of(event);

    when(ddbTable.index(EventService.CATEGORY_TIMESTAMP_ID_INDEX)).thenReturn(gsi);
    when(gsi.query(ArgumentMatchers.<Consumer<QueryEnhancedRequest.Builder>>any()))
        .thenReturn(sdkIterable);
    when(sdkIterable.iterator()).thenReturn(List.of(mockPage).iterator());
    when(mockPage.items()).thenReturn(items);

    List<DdbEvent> result = eventService.getCustomerEventsByCategoryWithTimestamp(customerId, category, ts);
    assertEquals(1, result.size());
    assertEquals(event, result.get(0));
  }

  @Test
  void postDdbEvent_Success() {
    Event request = createTestEventRequest();
    eventService.postDdbEvent(request);
    verify(enhancedClient).transactWriteItems(any(TransactWriteItemsEnhancedRequest.class));
  }

  @Test
  void postDdbEvent_Failure() {
    Event request = createTestEventRequest();
    doThrow(new RuntimeException("Test exception"))
        .when(enhancedClient)
        .transactWriteItems(any(TransactWriteItemsEnhancedRequest.class));
    assertThrows(RuntimeException.class, () -> eventService.postDdbEvent(request));
  }

  @Test
  void postDdbEvent_VerifyDdbEventMapping() {
    Event request = createTestEventRequest();
    eventService.postDdbEvent(request);
    verify(enhancedClient).transactWriteItems(
        (TransactWriteItemsEnhancedRequest) argThat(transactRequest -> true));
  }

  @Test
  void postSqlEvent_logsAndThrowsOnException() {
    EventService service = new EventService(eventRepository,
        metrics, sqsClient, serviceConfig, enhancedClient, ddbTable);
    Event req = createTestEventRequest();
    doThrow(new RuntimeException("fail")).when(metrics).increment(any(), any());
    assertThrows(RuntimeException.class, () -> service.postSqlEvent(req));
  }

  @Test
  void postDdbEvent_logsAndThrowsOnOtherException() {
    doThrow(new RuntimeException("fail")).when(enhancedClient)
        .transactWriteItems(any(TransactWriteItemsEnhancedRequest.class));
    Event req = createTestEventRequest();
    assertThrows(RuntimeException.class, () -> eventService.postDdbEvent(req));
  }

  @Test
  void postSqlEvent_sync() {
    Event postEventRequest = createTestEventRequest();

    EventService eventService = new EventService(
        eventRepository,
        metrics,
        sqsClient,
        serviceConfig,
        enhancedClient,
        ddbTable
    );
    eventService.postSqlEvent(postEventRequest);
    verify(eventRepository, Mockito.times(1)).save(any());
  }

  @Test
  void postDdbEventAsync() {
    String sampleSqsMessageBody = "{\"version\":\"0\",\"id\":\"ba2aa526-0d9e-f253-4f02-a20281839cf9\","
        + "\"detail-type\":\"RiskEvent\",\"source\":\"feature-store.risk-ltv.events\","
        + "\"account\":\"************\",\"time\":\"2024-08-26T21:19:12Z\",\"region\":\"us-east-1\","
        + "\"resources\":[],\"detail\":{\"id\":0,\"name\":\"EVALUATE_OFFER_DEVICE_DATA\",\"category\":\"DEVICE_DATA\","
        + "\"entityId\":\"1234\",\"entityType\":\"CUSTOMER_ID\",\"customerId\":\"1234\","
        + "\"metadata\":\"{\\\"device_id\\\":\\\"1234\\\"}\",\"dtArrived\":1.724707150843252E9,\"dtCreated\":null}}";
    Message message = Message.builder()
        .body(sampleSqsMessageBody)
        .build();
    ReceiveMessageResponse receiveMessageResponse = ReceiveMessageResponse.builder()
        .messages(message)
        .build();
    when(sqsClient.receiveMessage(any(ReceiveMessageRequest.class))).thenReturn(receiveMessageResponse);

    EventService eventService = new EventService(
        eventRepository,
        metrics,
        sqsClient,
        serviceConfig,
        enhancedClient,
        ddbTable
    );
    eventService.postEventAsync();
    verify(enhancedClient, Mockito.times(1)).transactWriteItems(any(TransactWriteItemsEnhancedRequest.class));
  }

  private Event createTestEventRequest() {
    Event event = new Event();
    event.setCustomerId("test-customer");
    event.setName(EventName.EVALUATE_OFFER_DEVICE_DATA);
    event.setEntityId("test-entity");
    event.setEntityType(EntityType.CUSTOMER_ID);
    event.setCategory(EventCategory.DEVICE_DATA);
    event.setMetadata("{}");
    return event;
  }
}
