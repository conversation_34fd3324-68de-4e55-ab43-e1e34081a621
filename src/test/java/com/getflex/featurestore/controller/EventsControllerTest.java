package com.getflex.featurestore.controller;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

import com.getflex.featurestore.model.PostEventRequest;
import com.getflex.featurestore.service.EventService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

class EventsControllerTest {

  @Mock
  private EventService eventService;

  @InjectMocks
  private EventsController controller;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
    controller = new EventsController(eventService);
  }

  @Test
  void postEvent_nullRequest_throwsException() {
    assertThrows(NullPointerException.class, () -> controller.postEvent(null));
  }

  @Test
  void postEvent_Success() {
    PostEventRequest postEventRequest = new PostEventRequest()
        .name("EVALUATE_OFFER_DEVICE_DATA")
        .category("DEVICE_DATA")
        .entityId("CUSTOMER_ID")
        .metadata("{}");
    ResponseEntity<Object> response = controller.postEvent(postEventRequest);
    verify(eventService, Mockito.times(1)).postEvent(any());
    assertEquals(200, response.getStatusCodeValue());
  }
}
