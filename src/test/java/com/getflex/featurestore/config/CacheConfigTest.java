package com.getflex.featurestore.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import java.time.Duration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SpringBootTest(classes = {CacheConfig.class, CacheConfigTest.Config.class})
public class CacheConfigTest {

  private static final String UNIT_TEST_CACHE = "unitTestCache";
  private static final Duration UNIT_TEST_CACHE_EXPIRATION = Duration.ofSeconds(1);

  @Configuration
  public static class Config {
    // create our own test cache to unit test so we can control the expiration time
    @Bean
    CaffeineCache testCache() {
      return new CaffeineCache(UNIT_TEST_CACHE,
          Caffeine.newBuilder().expireAfterWrite(UNIT_TEST_CACHE_EXPIRATION).build());
    }
  }


  @Autowired
  private CacheManager cacheManager;

  @Test
  void cacheManger_uses_specific_bucket() throws InterruptedException {
    var cache = cacheManager.getCache(UNIT_TEST_CACHE);
    cache.invalidate();
    cache.put("foo", "bar");
    Assertions.assertEquals("bar", cache.get("foo", String.class));
    // sleep just over the expiry time to ensure the cache is cleared
    Thread.sleep(UNIT_TEST_CACHE_EXPIRATION.toMillis() + 1);
    Assertions.assertNull(cache.get("foo", String.class),
        "Cache should have expired after %d ms".formatted(UNIT_TEST_CACHE_EXPIRATION.toMillis()));
  }

  @Test
  void cacheManager_cannot_create_dynamic() throws InterruptedException {
    Assertions.assertNull(cacheManager.getCache("This cache doesnt exist"),
        "CacheManager should not create caches dynamically, only those defined in the configuration");
  }
}
