package com.getflex.featurestore.config;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sqs.SqsClient;

@SpringBootTest(classes = {AwsDependencies.class, ServiceConfig.class})
class AwsDependenciesTest {

  @Autowired
  private ApplicationContext context;

  @Test
  void allBeansPresentAndCorrectType() {
    Assertions.assertNotNull(context.getBean(DynamoDbClient.class));
    Assertions.assertNotNull(context.getBean(DynamoDbEnhancedClient.class));
    Assertions.assertNotNull(context.getBean(S3Client.class));
    Assertions.assertNotNull(context.getBean(SqsClient.class));
    Assertions.assertNotNull(context.getBean(SageMakerRuntimeClient.class));
    DynamoDbTable<?> table = context.getBean(DynamoDbTable.class);
    Assertions.assertNotNull(table);
    Assertions.assertTrue(table.tableName().contains("events_store"));
  }

  @Test
  void sqsClientBeanReturnsCorrectRegion() {
    SqsClient sqsClient = context.getBean(SqsClient.class);
    Assertions.assertEquals(AwsDependencies.FLEX_REGION, sqsClient.serviceClientConfiguration().region());
  }

  @Test
  void dynamoDbClientBeanHandlesLocalProfile() {
    // This test is limited by environment, but we check bean creation
    DynamoDbClient client = context.getBean(DynamoDbClient.class);
    Assertions.assertNotNull(client);
  }

  @Test
  void dynamoDbEnhancedClientBeanPresent() {
    DynamoDbEnhancedClient enhancedClient = context.getBean(DynamoDbEnhancedClient.class);
    Assertions.assertNotNull(enhancedClient);
  }
}
