package com.getflex.featurestore.model.flexscore;

import static com.getflex.featurestore.integration.flex.flexscore.ModelSpec.V7_FEATURE_LIST;
import static com.getflex.featurestore.model.flexscore.alloy.transunionraw.Credit.DOUBLE_PRECISION_DELTA;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.AADM86_LINKA006;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.IS_FROM_YARDI;
import static com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature.TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.integration.flex.flexscore.ModelSpec;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.CustomerOfferHistoryFeature;
import com.getflex.featurestore.model.feature.FlexScoreFiveFeature;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelFeatureFormulaParams;
import com.getflex.featurestore.utils.Metrics;
import com.google.common.base.Predicate;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FlexScoreInputFeatureTests {

  public static final String TEST_RESOURCE_FOLDER = "/flexscore-input-feature-extraction/";
  public static final List<FlexScoreModelExtractedFeature> NEW_V6_FEATURES = ModelSpec.V6_FEATURE_LIST.stream()
      .dropWhile(f -> f != AADM86_LINKA006).toList();
  public static final PropertyInfo PROPERTY_INFO = new PropertyInfo(BillingIntegrationTypeEnum.DIRECT_INTEGRATION,
      false, null);

  @InjectMocks
  FlexScoreModel flexScoreModel;

  Version modelVersion = Mockito.mock(Version.class);

  @Spy
  ServiceConfig serviceConfig = new ServiceConfig().withFlexScoreEndpoint(Map.of(modelVersion, "epS"));

  @Mock
  Metrics metrics;

  @Mock
  FeatureFactory featureFactory;

  /**
   * When new cases are added here, PLEASE MAKE SURE customer PII is removed from AlloyReport.
   *
   * @param testNumber
   * @throws IOException
   */
  @ParameterizedTest
  @ValueSource(strings = {"01", "02", "03", "04", "05"})
  void extractFeatures_dependencyFailure(String testNumber) throws IOException {
    AlloyReport alloyReport = FlexScoreFiveFeature.OBJECT_MAPPER.readValue(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-input.json"), AlloyReport.class);
    FlexScoreModelFeatureFormulaParams params = FlexScoreModelFeatureFormulaParams.builder()
        .alloyReport(alloyReport)
        .propertyInfo(PROPERTY_INFO)
        .evalParams(null)
        .featureFactory(null)
        .build();
    Map<FlexScoreModelExtractedFeature, Number> features = flexScoreModel.extractFeatures(params,
        ModelSpec.V5_FEATURE_LIST);

    JsonNode expectedOutputMap = FlexScoreFiveFeature.OBJECT_MAPPER.readTree(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-output.json"));

    validateFlexScoreFeatures(features, expectedOutputMap, FlexScoreInputFeatureTests::skipFeatureValidation);
  }

  /**
   * When new cases are added here, PLEASE MAKE SURE customer PII is removed from AlloyReport.
   *
   * @param testNumber
   * @throws IOException
   */
  @ParameterizedTest
  @ValueSource(strings = {"01", "02", "03", "04", "05"})
  void extractFeatures_newFlexScoreSixFeatures(String testNumber) throws IOException {
    AlloyReport alloyReport = FlexScoreFiveFeature.OBJECT_MAPPER.readValue(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-input.json"), AlloyReport.class);
    FlexScoreModelFeatureFormulaParams params = FlexScoreModelFeatureFormulaParams.builder()
        .alloyReport(alloyReport)
        .propertyInfo(PROPERTY_INFO)
        .evalParams(null)
        .featureFactory(null)
        .build();
    Map<FlexScoreModelExtractedFeature, Number> features = flexScoreModel.extractFeatures(params, NEW_V6_FEATURES);

    JsonNode expectedOutputMap = FlexScoreFiveFeature.OBJECT_MAPPER.readTree(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-v6-output.json"));

    validateFlexScoreFeatures(features, expectedOutputMap, null);
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      1,
      2,1775
      3,247
      4,
      5,
      """)
  void extractFeatures_total_monthly_pmt_open_installment(int testNumber, Long expectedTotalMonthlyPayment)
      throws IOException {
    AlloyReport alloyReport = FlexScoreFiveFeature.OBJECT_MAPPER.readValue(
        getClass().getResource(TEST_RESOURCE_FOLDER + "%02d-input.json".formatted(testNumber)), AlloyReport.class);
    FlexScoreModelFeatureFormulaParams params = FlexScoreModelFeatureFormulaParams.builder()
        .alloyReport(alloyReport)
        .propertyInfo(PROPERTY_INFO)
        .evalParams(null)
        .featureFactory(null)
        .build();
    Map<FlexScoreModelExtractedFeature, Number> features = flexScoreModel.extractFeatures(params,
        List.of(TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT));

    Assertions.assertEquals(expectedTotalMonthlyPayment, features.get(TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT));
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      08, REALPAGE, 1
      09, FLEX_ANYWHERE, 0
      10, YARDI, 0
      11, DIRECT_INTEGRATION, 0
      12, YARDI, 0
      """)
  void extractFeatures_FlexScoreSevenFeatures(String testNumber,
      BillingIntegrationTypeEnum billingIntegrationTypeEnum, int isReturningCustomer)
      throws IOException {
    AlloyReport alloyReport = FlexScoreFiveFeature.OBJECT_MAPPER.readValue(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-input.json"), AlloyReport.class);
    PropertyInfo propertyInfo = new PropertyInfo(billingIntegrationTypeEnum, false, null);
    FlexScoreModelFeatureFormulaParams params = FlexScoreModelFeatureFormulaParams.builder()
        .alloyReport(alloyReport)
        .propertyInfo(propertyInfo)
        .evalParams(null)
        .featureFactory(featureFactory)
        .build();

    setupCustomerType(isReturningCustomer);
    Map<FlexScoreModelExtractedFeature, Number> features = flexScoreModel.extractFeatures(params, V7_FEATURE_LIST);
    JsonNode expectedOutputMap = FlexScoreFiveFeature.OBJECT_MAPPER.readTree(
        getClass().getResource(TEST_RESOURCE_FOLDER + testNumber + "-v7-output.json"));

    validateFlexScoreFeatures(features, expectedOutputMap, null);
  }

  /**
   * Helper method to setup CustomerOfferHistoryFeature mocking for controlling IS_RETURNING_CUSTOMER value
   *
   * @param isReturningCustomer 1 if customer should be considered returning, 0 otherwise
   */
  private void setupCustomerType(int isReturningCustomer) {
    // Mock CustomerOfferHistoryFeature to control IS_RETURNING_CUSTOMER
    long daysSinceAccept = 0L; // any >=0
    long daysSinceCancel = isReturningCustomer == 1 ? 100L : 800L; // <=720 means returning
    CustomerOfferHistoryFeature.CustomerOfferHistory coh =
        new CustomerOfferHistoryFeature.CustomerOfferHistory(daysSinceAccept, daysSinceCancel);
    FeatureValue fv = new FeatureValue();
    fv.setValue(coh);
    BaseFeature cohFeature = Mockito.mock(BaseFeature.class);
    when(cohFeature.fetchFeatureValue(any())).thenReturn(fv);
    when(featureFactory.getFeature("CustomerOfferHistoryFeature")).thenReturn(cohFeature);
  }

  /**
   * Verify input feature values against a JSON object (map of feature values recorded in the past).
   *
   * @param features          Feature values generated by this service
   * @param expectedOutputMap Feature values generated by credit-features package
   * @param skipFeature       Optional - Return true if given feature should be skipped
   */
  public static void validateFlexScoreFeatures(Map<FlexScoreModelExtractedFeature, Number> features,
      JsonNode expectedOutputMap, Predicate<FlexScoreModelExtractedFeature> skipFeature) {
    for (Map.Entry<FlexScoreModelExtractedFeature, Number> feature : features.entrySet()) {
      if (skipFeature != null && skipFeature.apply(feature.getKey())) {
        continue;
      }

      JsonNode expected = expectedOutputMap.get(feature.getKey().name().toLowerCase());
      if (expected == null) {
        // original name is also supported
        expected = expectedOutputMap.get(feature.getKey().name());
      }

      if (expected == null || expected.isNull()) {
        // null case
        Assertions.assertNull(feature.getValue(), feature.getKey().name());
      } else {
        if (expected.isFloatingPointNumber()) {
          // float point number case
          Assertions.assertEquals(expected.asDouble(), feature.getValue().doubleValue(), DOUBLE_PRECISION_DELTA,
              feature.getKey().name());
        } else {
          // other number types
          Assertions.assertEquals(expected.asLong(),
              feature.getValue() != null ? feature.getValue().longValue() : null,
              feature.getKey().name());
        }
      }
    }
  }

  /**
   * A few features at end of {@link ModelSpec#V5_FEATURE_LIST} are not consistently included in the feature dump we
   * captured from offer record. This will skip validation of these feature.
   *
   * @param feature
   * @return
   */
  public static boolean skipFeatureValidation(FlexScoreModelExtractedFeature feature) {
    return feature.ordinal() >= IS_FROM_YARDI.ordinal();
  }


}
