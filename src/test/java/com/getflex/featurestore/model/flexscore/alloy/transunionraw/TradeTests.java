package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import static org.junit.jupiter.api.Assertions.assertFalse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature;
import org.junit.jupiter.api.Test;

public class TradeTests {
  @Test
  void sanityCheck_tradeWithoutDateOpened() throws JsonProcessingException {
    Trade trade = AbstractFlexScoreFeature.OBJECT_MAPPER.readValue("""
        {
          "ECOADesignator": "individual",
          "account": {
            "type": "CH"
          },
          "accountRating": "01",
          "creditLimit": "*********",
          "currentBalance": "*********",
          "dateEffective": {
            "$": {
              "estimatedCentury": "false",
              "estimatedDay": "false",
              "estimatedMonth": "false",
              "estimatedYear": "false"
            },
            "_": "2015-08-12"
          },
          "highCredit": "*********",
          "pastDue": "*********",
          "paymentHistory": "",
          "portfolioType": "revolving",
          "remark": {
            "code": "CBC",
            "type": "affiliate"
          },
          "subscriber": {
            "industryCode": "C",
            "memberCode": "01NZ8003",
            "name": {
              "unparsed": "CB/BUCKLE"
            }
          },
          "updateMethod": "automated"
        }
        """, Trade.class);

    assertFalse(trade.sanityCheck());
  }
}
