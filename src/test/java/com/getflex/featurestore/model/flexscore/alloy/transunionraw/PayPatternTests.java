package com.getflex.featurestore.model.flexscore.alloy.transunionraw;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.time.Period;
import org.junit.jupiter.api.Test;

public class PayPatternTests {
  PayPattern payPattern = new PayPattern("1112X23321221");

  @Test
  void testIllegalChars() {
    assertThrows(IllegalArgumentException.class, () -> new PayPattern("111101111"));
    assertThrows(IllegalArgumentException.class, () -> new PayPattern("1116111"));
    assertThrows(IllegalArgumentException.class, () -> new PayPattern("111A111"));
    assertThrows(IllegalArgumentException.class, () -> new PayPattern("111B111"));
  }

  @Test
  void trimOldRecord() {
    assertEquals("1112X", payPattern.trimOldRecord(Period.ofMonths(5)).getPattern());
  }

  @Test
  void numDqs() {
    assertEquals(2, payPattern.numDqs(2, null));
    assertEquals(1, payPattern.numDqs(3, null));
    assertEquals(0, payPattern.numDqs(4, null));
    assertEquals(0, payPattern.numDqs(5, null));

    assertEquals(0, payPattern.numDqs(3, 6));
    assertEquals(1, payPattern.numDqs(2, 6));
    assertEquals(1, payPattern.numDqs(2, 10));
    assertEquals(2, payPattern.numDqs(2, 11));
    assertEquals(1, payPattern.numDqs(3, 11));
  }
}
