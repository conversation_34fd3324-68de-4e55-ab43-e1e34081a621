package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsZipCheckEnum;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ZipCheckFailFeatureTest {

  @InjectMocks
  ZipCheckFailFeature feature;

  @ParameterizedTest
  @EnumSource(StripeAvsZipCheckEnum.class)
  public void testGetValue(StripeAvsZipCheckEnum zipCheckEnum) {
    EvalParams evalParams = new EvalParams();
    evalParams.setStripeAvsZipCheck(zipCheckEnum);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Integer expected = zipCheckEnum.equals(StripeAvsZipCheckEnum.FAIL) ? 1 : 0;
    assertEquals(expected, featureValue.getValue());
  }
}