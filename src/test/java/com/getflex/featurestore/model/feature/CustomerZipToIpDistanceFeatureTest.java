package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.Coordinates;
import com.getflex.featurestore.dao.repo.GeoIpNetworkRepo;
import com.getflex.featurestore.dao.repo.UsAddressesRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerZipToIpDistanceFeatureTest {

  @InjectMocks
  CustomerZipToIpDistanceFeature customerZipToIpDistanceFeature;

  @Mock
  IdentityService identityService;

  @Mock
  GeoIpNetworkRepo geoipNetworkRepo;

  @Mock
  UsAddressesRepo usAddressesRepo;

  @Test
  public void testGetValue_Success() {
    Long customerId = 1L;
    String ipAddress = "************";
    String zipCode = "10011";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    Coordinates ipCoordinates = Mockito.mock(Coordinates.class);
    Coordinates zipCodeCoordinates = Mockito.mock(Coordinates.class);
    GetCustomerResponse customer = new GetCustomerResponse().zip(zipCode);

    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(ipCoordinates);
    when(usAddressesRepo.findFirstByZipOrderByCityAscLongitudeDescLatitudeDesc(zipCode))
        .thenReturn(zipCodeCoordinates);
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(ipCoordinates.haversine(zipCodeCoordinates)).thenReturn(100.0);

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    assertEquals(100.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
  }

  @Test
  public void testGetValue_NullIpAddress_Returns99999() {
    Long customerId = 1L;

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(null);

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("IP address is null or empty, cannot calculate distance.", featureValue.getMetadata());

    verifyNoInteractions(geoipNetworkRepo);
    verifyNoInteractions(usAddressesRepo);
  }

  @Test
  public void testGetValue_EmptyIpAddress_Returns99999() {
    Long customerId = 1L;

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress("");

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("IP address is null or empty, cannot calculate distance.", featureValue.getMetadata());

    verifyNoInteractions(geoipNetworkRepo);
    verifyNoInteractions(usAddressesRepo);
  }

  @Test
  public void testGetValue_NullCustomer_Returns99999() {
    // The implementation will throw a NullPointerException when customer is null
    // BaseFeature.fetchFeatureValue catches it and returns the default value (99999)
    Long customerId = 1L;
    String ipAddress = "************";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    when(identityService.getCustomer(customerId)).thenReturn(null);
    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(Mockito.mock(Coordinates.class));

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    // The implementation throws NPE, but BaseFeature.fetchFeatureValue catches it
    // and returns 99999 as the default value
    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("Error processing Customer Zip to Ip Distance, returning default value.", featureValue.getMetadata());
  }

  @Test
  public void testGetValue_EmptyZipCode_Returns99999() {
    // The implementation will throw a NullPointerException when zip code is empty
    // BaseFeature.fetchFeatureValue catches it and returns the default value (99999)
    Long customerId = 1L;
    String ipAddress = "************";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    GetCustomerResponse customer = new GetCustomerResponse().zip("");

    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(Mockito.mock(Coordinates.class));

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    // The implementation throws NPE, but BaseFeature.fetchFeatureValue catches it
    // and returns 99999 as the default value
    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("No coordinates found for the zip code: ", featureValue.getMetadata());
  }

  @Test
  public void testGetValue_NoIpCoordinatesFound_Returns99999() {
    Long customerId = 1L;
    String ipAddress = "************";
    String zipCode = "10011";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    GetCustomerResponse customer = new GetCustomerResponse().zip(zipCode);

    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(null);

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("No coordinates found for the IP address: " + ipAddress, featureValue.getMetadata());

    verifyNoInteractions(usAddressesRepo);
  }

  @Test
  public void testGetValue_NoZipCoordinatesFound_Returns99999() {
    Long customerId = 1L;
    String ipAddress = "************";
    String zipCode = "10011";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    Coordinates ipCoordinates = Mockito.mock(Coordinates.class);
    GetCustomerResponse customer = new GetCustomerResponse().zip(zipCode);

    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(ipCoordinates);
    when(usAddressesRepo.findFirstByZipOrderByCityAscLongitudeDescLatitudeDesc(zipCode))
        .thenReturn(null);

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("No coordinates found for the zip code: " + zipCode, featureValue.getMetadata());
  }

  @Test
  public void testGetValue_HaversineException_Returns99999() {
    Long customerId = 1L;
    String ipAddress = "************";
    String zipCode = "10011";

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress(ipAddress);

    Coordinates ipCoordinates = Mockito.mock(Coordinates.class);
    Coordinates zipCodeCoordinates = Mockito.mock(Coordinates.class);
    GetCustomerResponse customer = new GetCustomerResponse().zip(zipCode);

    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(geoipNetworkRepo.findByIpAddressInRange(ipAddress)).thenReturn(ipCoordinates);
    when(usAddressesRepo.findFirstByZipOrderByCityAscLongitudeDescLatitudeDesc(zipCode))
        .thenReturn(zipCodeCoordinates);
    when(ipCoordinates.haversine(zipCodeCoordinates))
        .thenThrow(new IllegalArgumentException("Invalid coordinates"));

    FeatureValue featureValue = customerZipToIpDistanceFeature.fetchFeatureValue(evalParams);

    // The implementation returns null for haversine exceptions, but we expect 99999
    // This test will fail until the implementation is updated to return 99999
    assertEquals(99999.0, featureValue.getValue());
    assertEquals(FeatureTypeEnum.DOUBLE, featureValue.getType());
    assertEquals("Error calculating distance: Invalid coordinates", featureValue.getMetadata());
  }
}