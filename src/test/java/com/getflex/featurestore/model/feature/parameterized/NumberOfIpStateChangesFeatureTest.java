package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.GeoLocation;
import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.dao.repo.GeoIpLocationRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.BaseDeviceMetricFeature;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.service.EventService;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfIpStateChangesFeatureTest {
  @Mock EventService eventService;

  @Mock GeoIpLocationRepo geoIpLocationRepo;

  @Mock GeoLocation mockProjection;

  @Test
  public void testGetValueReturnsGeoLocationData() {
    Long customerId = 123L;
    String isoDuration = "P7D";

    when(eventService.getCustomerEventsByCategoryWithTimestamp(any(), any(), any()))
        .thenReturn(
            List.of(
                DdbEvent.builder()
                    .customerId("123")
                    .dtCreated(OffsetDateTime.now().toString())
                    .metadata(toJsonSafe(DeviceMetadata.builder().ipAddress("*******").build()))
                    .build()));
    when(mockProjection.getSubdivision1Name()).thenReturn("California");
    when(geoIpLocationRepo.findGeoLocationDataByIpAddress(any(String.class)))
        .thenReturn(mockProjection);

    var feature = new NumberOfIpStateChangesFeature(eventService, geoIpLocationRepo,
        new LookbackDurationFeatureParams(isoDuration));
    EvalParams evalParams = new EvalParams().customerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(1, featureValue.getValue());
  }

  @Test
  public void testGetValueReturnsNullGeoLocationData() {
    Long customerId = 123L;
    String isoDuration = "P7D";

    when(eventService.getCustomerEventsByCategoryWithTimestamp(any(), any(), any()))
        .thenReturn(
            List.of(
                DdbEvent.builder()
                    .customerId("123")
                    .dtCreated(OffsetDateTime.now().toString())
                    .metadata(toJsonSafe(DeviceMetadata.builder().ipAddress("*******").build()))
                    .build()));
    when(geoIpLocationRepo.findGeoLocationDataByIpAddress(any(String.class))).thenReturn(null);

    var feature = new NumberOfIpStateChangesFeature(eventService, geoIpLocationRepo,
        new LookbackDurationFeatureParams(isoDuration));
    EvalParams evalParams = new EvalParams().customerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(0, featureValue.getValue());
  }

  private static String toJsonSafe(DeviceMetadata deviceMetadata) {
    try {
      return new ObjectMapper().writeValueAsString(deviceMetadata);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
