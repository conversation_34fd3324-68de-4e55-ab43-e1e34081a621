package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class TotalLostDisputesFeatureTest {
  @InjectMocks
  TotalLostDisputesFeature feature;

  @Mock
  DisputeService disputeService;

  @Test
  public void testGetValue() {
    Long customerId = 10L;
    when(disputeService.getLostDisputes(customerId)).thenReturn(List.of(new Dispute().id(1L)));
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "TotalLostDisputesFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 1);
  }

}
