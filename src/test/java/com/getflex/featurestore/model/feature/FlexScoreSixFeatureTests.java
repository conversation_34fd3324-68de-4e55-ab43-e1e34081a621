package com.getflex.featurestore.model.feature;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * All logic (except {@link FlexScoreSixFeature#getDescription()}) of {@link FlexScoreSixFeature} is shared with
 * {@link FlexScoreFiveFeature} and it is already tested by {@link FlexScoreFiveFeatureTests}.
 */
@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FlexScoreSixFeatureTests {

  @InjectMocks
  FlexScoreSixFeature feature;

  @Test
  void getDescription() {
    Assertions.assertEquals("flexscore-6", feature.getDescription());
  }

}
