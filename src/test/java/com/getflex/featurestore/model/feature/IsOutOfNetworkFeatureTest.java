package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.BillerDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsOutOfNetworkFeatureTest {

  @InjectMocks
  IsOutOfNetworkFeature feature;
  @Mock
  IdentityService identityService;
  @Mock
  BillingService billingService;

  public static final String FEATURE_NAME = "IsOutOfNetworkFeature";
  public static final FeatureTypeEnum FEATURE_TYPE = FeatureTypeEnum.BOOLEAN;


  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValue(Boolean isOutOfNetwork) {
    Long billerAccountId = 1L;
    Long billerId = 2L;

    when(identityService.getBillerByBillerAccountId(billerAccountId)).thenReturn(new BillerDto().billerId(billerId));
    when(billingService.getPropertyById(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().isOutOfNetwork(isOutOfNetwork));
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), FEATURE_NAME);
    Assertions.assertEquals(featureValue.getValue(), isOutOfNetwork);
    Assertions.assertEquals(featureValue.getType(), FEATURE_TYPE);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  void testGetValueException() {
    Long billerAccountId = 1L;
    when(identityService.getBillerByBillerAccountId(billerAccountId)).thenThrow(new RuntimeException());
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), FEATURE_NAME);
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FEATURE_TYPE);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

}
