package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.validateFlexScoreFeatures;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel;
import com.getflex.featurestore.integration.flex.flexscore.ModelSpec;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelFeatureFormulaParams;
import com.getflex.featurestore.utils.Metrics;
import java.net.URI;
import java.util.Map;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Uri;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

/**
 * This integration test is driven by a CSV file, one can generate such CSV file using a SF query like this:
 * <pre>
 *   select
 *     *
 *   from
 *     (
 *         select
 *             id,
 *             version,
 *             evaluation_context:original_alloy_report_s3_uri::string as alloy_report_uri,
 *             evaluation_context:features::string as feature_map
 *         from
 *             FLEX2_BACKEND.risk_final_structured.offer
 *         where
 *             not is_null_value(evaluation_context:features)
 *             and not is_null_value(evaluation_context:original_alloy_report_s3_uri)
 *             and dt_created >= '2024-01-01'
 *     ) SAMPLE (100);
 * </pre>
 * Alloy report will be loaded from production s3 bucket (user MUST configure execution environment so that
 * {@link DefaultCredentialsProvider} works).
 * <ul>
 *   <li>On developer machine, AWS SSO module is already included as test dependency, you just need to set
 *   <code>AWS_PROFILE</code> environment variable to your local profile corresponding to production account PowerUser
 *   role</li>
 * </ul>
 * <br>
 * Flexscore input features extracted by logic of this service will be compared with those recorded in offer table
 * (feature_map column above).
 */
@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FlexScoreFiveFeatureIntegTest {

  static final S3Client s3 = S3Client.create();

  @InjectMocks
  FlexScoreModel flexScoreModel;

  @Mock
  Metrics metrics;

  /**
   * @param offerId      Not used by the test, but it helps identify the particular test case
   * @param offerVersion Not used by the test, but it helps identify the particular test case
   * @param s3Uri
   * @param featureMap
   * @throws JsonProcessingException
   */
  @ParameterizedTest
  @CsvFileSource(
      resources = "/flexscore-input-feature-extraction/integ/flex_score_features.csv",
      numLinesToSkip = 1,
      maxCharsPerColumn = ********
  )
  void testFeatureExtraction(String offerId, Long offerVersion, String s3Uri, String featureMap)
      throws JsonProcessingException {
    AlloyReport alloyReport = FlexScoreFiveFeature.OBJECT_MAPPER.readValue(loadS3Object(s3Uri), AlloyReport.class);
    FlexScoreModelFeatureFormulaParams params = FlexScoreModelFeatureFormulaParams.builder()
        .alloyReport(alloyReport)
        .propertyInfo(new PropertyInfo(BillingIntegrationTypeEnum.DIRECT_INTEGRATION, false, null))
        .evalParams(null)
        .featureFactory(null)
        .build();
    Map<FlexScoreModelExtractedFeature, Number> features = flexScoreModel.extractFeatures(params,
        ModelSpec.V5_FEATURE_LIST);
    validateFlexScoreFeatures(features, FlexScoreFiveFeature.OBJECT_MAPPER.readTree(featureMap), null);
  }

  private String loadS3Object(String s3Uri) {
    try {
      S3Uri uri = s3.utilities().parseUri(URI.create(s3Uri));

      GetObjectRequest objectRequest = GetObjectRequest.builder()
          .bucket(uri.bucket().get())
          .key(uri.key().get())
          .build();

      return s3.getObjectAsBytes(objectRequest).asUtf8String();
    } catch (S3Exception e) {
      throw new RuntimeException(e);
    }
  }
}
