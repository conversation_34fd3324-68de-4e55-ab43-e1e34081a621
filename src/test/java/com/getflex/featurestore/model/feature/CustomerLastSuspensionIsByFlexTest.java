package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerLastSuspensionIsByFlexTest {

  @InjectMocks
  CustomerLastSuspensionIsByFlexFeature feature;

  @Mock
  OfferService offerService;

  @Test
  public void testGetValueNoOffer() {
    Long customerId = 1234L;
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getLastSuspension(customerId)).thenReturn(null);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerLastSuspensionIsByFlexFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @ParameterizedTest
  @ValueSource(strings = {"UserInitiated", "FlexInitiated"})
  public void testGetValue(String deactivationReasonDetail) {
    Long customerId = 1234L;
    InternalOffer offer =
        new InternalOffer().customerId(customerId).offerId("offer-test")
            .deactivationReasonDetail(deactivationReasonDetail);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getLastSuspension(customerId)).thenReturn(offer);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerLastSuspensionIsByFlexFeature");
    Assertions.assertEquals(featureValue.getValue(), !deactivationReasonDetail.equals("UserInitiated"));
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

}
