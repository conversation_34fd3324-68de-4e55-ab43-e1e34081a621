package com.getflex.featurestore.model.feature.dispute;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DisputeAmountInCentsByIdFeatureTest {

  private static final Long DISPUTE_ID = 12345L;
  private static final Long DISPUTE_AMOUNT = 10000L; // $100.00 in cents

  @Mock
  private DisputeService disputeService;

  private DisputeAmountInCentsByIdFeature feature;
  private EvalParams evalParams;

  @BeforeEach
  void setUp() {
    feature = new DisputeAmountInCentsByIdFeature(disputeService);
    evalParams = new EvalParams().disputeId(DISPUTE_ID);
  }

  @Test
  void getType_shouldReturnLong() {
    assertEquals(FeatureTypeEnum.LONG, feature.getType());
  }

  @Test
  void getDescription_shouldReturnCorrectDescription() {
    assertEquals("Amount in cents for given dispute Id", feature.getDescription());
  }

  @Test
  void getValue_whenDisputeExists_shouldReturnDisputeAmount() {
    // Arrange
    Dispute dispute = new Dispute().disputeAmount(DISPUTE_AMOUNT);
    when(disputeService.getDisputeById(DISPUTE_ID)).thenReturn(dispute);

    // Act
    FeatureOutput result = feature.getValue(evalParams);

    // Assert
    assertEquals(DISPUTE_AMOUNT, result.value());
  }

  @Test
  void getRequiredEvalParamKeys_shouldRequireDisputeId() {
    // Act
    var requiredKeys = feature.getRequiredEvalParamKeys();

    // Assert - The actual implementation of ParamRequired doesn't expose getKeys(),
    // so we'll just verify it's not null and doesn't throw
    assertTrue(requiredKeys != null);
  }
}
