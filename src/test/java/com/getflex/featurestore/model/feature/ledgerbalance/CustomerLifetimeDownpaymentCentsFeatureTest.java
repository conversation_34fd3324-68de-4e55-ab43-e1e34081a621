package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeDownpaymentCentsFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record1.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet record2 = new RecordLedgerWallet();
    record2.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    record2.setAmount(3000L);
    record2.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record2.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet nonChargeCategoryRecord = new RecordLedgerWallet();
    nonChargeCategoryRecord.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    nonChargeCategoryRecord.setAmount(5000L);
    nonChargeCategoryRecord.setPaymentStatusId(PaymentState.SETTLED.getValue());
    nonChargeCategoryRecord.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet nonSettledTypeRecord = new RecordLedgerWallet();
    nonSettledTypeRecord.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    nonSettledTypeRecord.setAmount(7000L);
    nonSettledTypeRecord.setPaymentStatusId(PaymentState.DECLINED.getValue());
    nonSettledTypeRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet rp = new RecordLedgerWallet();
    rp.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    rp.setAmount(1000L);
    rp.setPaymentStatusId(PaymentState.SETTLED.getValue());
    rp.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    List<RecordLedgerWallet> records = List.of(record1, record2, nonSettledTypeRecord, nonChargeCategoryRecord, rp);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDownpaymentCentsFeature feature = new CustomerLifetimeDownpaymentCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(4000L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeDownpaymentCentsFeature feature = new CustomerLifetimeDownpaymentCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());


    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDownpaymentCentsFeature feature = new CustomerLifetimeDownpaymentCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }
}
