package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.verification.model.RiskProfileDto;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BusinessDuplicateTinFeatureTest {

  @InjectMocks
  private BusinessDuplicateTinFeature feature;

  @Mock
  private VerificationService verificationService;

  @Test
  void testGetValue_true() {
    String einHmac = "test-ein-hmac";
    EvalParams evalParams = new EvalParams();
    evalParams.setEinHmac(einHmac);

    RiskProfileDto mockRiskProfile = new RiskProfileDto();
    List<RiskProfileDto> mockRiskProfiles = List.of(mockRiskProfile);
    when(verificationService.findDuplicateRiskProfilesByTin(einHmac)).thenReturn(mockRiskProfiles);

    FeatureOutput result = feature.getValue(evalParams);

    assertEquals(true, result.value());
  }

  @Test
  void testGetValue_false() {
    String einHmac = "test-ein-hmac";
    EvalParams evalParams = new EvalParams();
    evalParams.setEinHmac(einHmac);

    when(verificationService.findDuplicateRiskProfilesByTin(einHmac)).thenReturn(Collections.emptyList());

    FeatureOutput result = feature.getValue(evalParams);

    assertEquals(false, result.value());
  }
}
