package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StripeNameMismatchFeatureTest {
  @InjectMocks
  StripeNameMismatchFeature feature;

  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @CsvSource(textBlock = """
      <PERSON>,false
      <PERSON>eca,false
      Gustavo Fonseca Mismatch,false
      Gustavo Mismatch,false
      Mismatch Mismatch,true
      """)
  public void getValue(String stripePaymentFullName, Boolean expectedValue) {
    Long customerId = 3L;
    GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId).email("<EMAIL>")
        .firstName("Gustavo").lastName("Fonseca").phone("**********");
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName(stripePaymentFullName);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "StripeNameMismatchFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), expectedValue);
  }
}
