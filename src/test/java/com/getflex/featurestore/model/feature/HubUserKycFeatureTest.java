package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.partnerhub.model.User;
import com.getflex.verification.model.Kyc;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class HubUserKycFeatureTest {

  @InjectMocks
  HubUserKycFeature feature;

  @Mock
  VerificationService verificationService;

  @Mock
  IdentityService identityService;

  @Mock
  BillingService billingService;

  @Mock
  PartnerHubService partnerHubService;

  @Mock
  Kyc kyc;

  @Test
  public void getValue_success_byHubUserKycId() {
    String kycId = UUID.randomUUID().toString();
    when(verificationService.getKyc(kycId)).thenReturn(kyc);

    assertSame(kyc, feature.getValue(new EvalParams().hubUserKycId(kycId)).value());
  }

  @Test
  public void getValue_success_byBillerAccountId() {
    final String kycId = UUID.randomUUID().toString();

    when(identityService.getBillerAccount(123L)).thenReturn(new ExtendedBillerAccountData().billerId(234L));
    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().pmcId(345L)
            .integrationType(IntegrationTypeEnum.P2P));
    when(partnerHubService.getHubUserByPmcId(345L)).thenReturn(Optional.of(new User().kycId(kycId)));
    when(verificationService.getKyc(kycId)).thenReturn(kyc);

    assertSame(kyc, feature.getValue(new EvalParams().billerAccountId(123L)).value());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(Any.of(EvalParamKey.HUB_USER_KYC_ID, EvalParamKey.BILLER_ACCOUNT_ID),
        feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
