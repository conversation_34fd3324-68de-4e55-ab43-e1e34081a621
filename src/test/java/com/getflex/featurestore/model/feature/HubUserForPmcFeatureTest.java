package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.partnerhub.model.User;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class HubUserForPmcFeatureTest {

  @InjectMocks
  private HubUserForPmcFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  BillingService billingService;

  @Mock
  PartnerHubService partnerHubService;

  @Test
  public void getValue_success() {
    when(identityService.getBillerAccount(123L)).thenReturn(new ExtendedBillerAccountData()
        .billerId(234L));
    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().pmcId(345L)
            .integrationType(IntegrationTypeEnum.P2P));
    User partnerHubUser = new User().email("<EMAIL>");
    when(partnerHubService.getHubUserByPmcId(345L)).thenReturn(Optional.of(partnerHubUser));

    assertSame(partnerHubUser, feature.getValue(new EvalParams().billerAccountId(123L)).value());
  }

  @Test
  public void getValue_notP2pProperty() {
    when(identityService.getBillerAccount(123L)).thenReturn(new ExtendedBillerAccountData()
        .billerId(234L));
    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().pmcId(345L)
            .integrationType(IntegrationTypeEnum.DIRECT_INTEGRATION));

    assertEquals("Not a P2P property", feature.getValue(new EvalParams().billerAccountId(123L)).value());
  }

  @Test
  public void getValue_noQualifiedHubUser() {
    when(identityService.getBillerAccount(123L)).thenReturn(new ExtendedBillerAccountData()
        .billerId(234L));
    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().pmcId(345L)
            .integrationType(IntegrationTypeEnum.P2P));
    when(partnerHubService.getHubUserByPmcId(345L)).thenReturn(Optional.empty());

    assertEquals("No qualified hub user associated with property", feature.getValue(new EvalParams()
        .billerAccountId(123L)).value());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(All.of(EvalParamKey.BILLER_ACCOUNT_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
