package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.client.ApiException;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerIsActiveStatusFeatureTest {
  @InjectMocks
  CustomerIsActiveStatusFeature feature;

  @Mock
  OfferService offerService;


  @ParameterizedTest
  @CsvSource(textBlock = """
      ACTIVE, true
      SUSPENDED, true
      PENDING, false
      CANCELED, false
      """)
  public void testCustomerIsActive(OverallState overallState, boolean expectedIsActive) {
    Long customerId = 123L;
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(
        new InternalOffer().customerId(customerId).overallState(overallState));
    EvalParams evalParams = new EvalParams().customerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "CustomerIsActiveStatusFeature");
    Assertions.assertEquals(featureValue.getValue(), expectedIsActive);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testCustomerIsActive_offerNotFound() {
    Long customerId = 123L;
    when(offerService.getOfferByCustomerId(customerId)).thenThrow(
        new InternalDependencyFailureException("Offer not found.", new ApiException(404, "Offer not found.")));
    EvalParams evalParams = new EvalParams().customerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "CustomerIsActiveStatusFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertFalse((boolean) featureValue.getValue());
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
