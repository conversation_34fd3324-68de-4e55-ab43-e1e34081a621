package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CardLinkageCountFeatureTest {
  @InjectMocks
  CardLinkageCountFeature feature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 3L;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean featureExists) {
    Optional<OfflineFeature> offlineFeature = Optional.of(
        OfflineFeature.builder().featureName("card_fingerprint_linkage")
            .primaryKey("fingerprint").featureValue("5").build());
    if (!featureExists) {
      offlineFeature = Optional.empty();
    }
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "card_fingerprint_linkage", "fingerprint")).thenReturn(offlineFeature);
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("fingerprint");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CardLinkageCountFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), 5);
    } else {
      Assertions.assertEquals(featureValue.getValue(), 0);
    }
  }
}
