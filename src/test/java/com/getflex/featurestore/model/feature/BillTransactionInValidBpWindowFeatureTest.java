package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.autopay.model.GetBpWindowResponse;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneOffset;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionInValidBpWindowFeatureTest {
  @InjectMocks
  BillTransactionInValidBpWindowFeature feature;

  @Mock
  AutopayService autopayService;

  @Test
  public void testBeforeBpWindow() {
    String btxId = "someBtxId";
    when(autopayService.getBpWindowByBillTransactionId(btxId)).thenReturn(
        new GetBpWindowResponse().bpStartDate("2024-01-27").bpEndDate("2024-02-05")
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);
    feature.clock = Clock.fixed(Instant.parse("2024-01-26T00:00:00Z"), ZoneOffset.of("-05:00"));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionInValidBpWindowFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testInBpWindow() {
    String btxId = "someBtxId";
    when(autopayService.getBpWindowByBillTransactionId(btxId)).thenReturn(
        new GetBpWindowResponse().bpStartDate("2024-07-27").bpEndDate("2024-08-05")
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);
    feature.clock = Clock.fixed(Instant.parse("2024-08-09T00:00:00Z"), ZoneOffset.of("-05:00"));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionInValidBpWindowFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testInBpWindowCloseToStart() {
    String btxId = "someBtxId";
    when(autopayService.getBpWindowByBillTransactionId(btxId)).thenReturn(
        new GetBpWindowResponse().bpStartDate("2024-07-27").bpEndDate("2024-08-05")
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);
    feature.clock = Clock.fixed(Instant.parse("2024-07-28T00:00:00Z"), ZoneOffset.of("-05:00"));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionInValidBpWindowFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testAfterBpWindow() {
    String btxId = "someBtxId";
    when(autopayService.getBpWindowByBillTransactionId(btxId)).thenReturn(
        new GetBpWindowResponse().bpStartDate("2024-01-27").bpEndDate("2024-02-05")
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);
    feature.clock = Clock.fixed(Instant.parse("2024-02-10T06:00:00Z"), ZoneOffset.of("-05:00"));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionInValidBpWindowFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
