package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.CustomerAccountUpdateHistory;
import com.getflex.useraccount.model.UpdateHistoryRecord;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDaysSinceLastEmailAddressChangeFeatureTest {

  @InjectMocks
  CustomerDaysSinceLastEmailAddressChangeFeature customerDaysSinceLastEmailAddressChangeFeature;
  @Mock
  UserAccountService userAccountService;
  @Mock
  IdentityService identityService;

  GetCustomerResponse customer = new GetCustomerResponse().customerId(1L).customerPublicId("customer_public_id");
  CustomerAccountUpdateHistory customerAccountUpdateHistory = new CustomerAccountUpdateHistory().histories(new
      ArrayList<>(
      Arrays.asList(
          new UpdateHistoryRecord().customerPublicId("customer_public_id").email("<EMAIL>").phone("*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(3)
                  .withOffsetSameInstant(ZoneOffset.UTC)),
          new UpdateHistoryRecord().customerPublicId("customer_public_id").email("<EMAIL>").phone("*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(7)
                  .withOffsetSameInstant(ZoneOffset.UTC)),
          new UpdateHistoryRecord().customerPublicId("customer_public_id").email("<EMAIL>").phone("*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(10)
                  .withOffsetSameInstant(ZoneOffset.UTC))
      )));

  GetCustomerResponse customerNoHistory = new GetCustomerResponse().customerId(2L).customerPublicId(
      "customer_public_id_no_history");
  CustomerAccountUpdateHistory customerAccountUpdateHistoryNoHistory = new CustomerAccountUpdateHistory().histories(new
      ArrayList<>());

  GetCustomerResponse customerNoUpdate = new GetCustomerResponse().customerId(3L).customerPublicId(
      "customer_public_id_no_update");
  CustomerAccountUpdateHistory customerAccountUpdateHistoryNoUpdate = new CustomerAccountUpdateHistory().histories(new
      ArrayList<>(
      Arrays.asList(
          new UpdateHistoryRecord().customerPublicId("customer_public_id_no_update").email("<EMAIL>").phone(
                  "*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(3)
                  .withOffsetSameInstant(ZoneOffset.UTC)),
          new UpdateHistoryRecord().customerPublicId("customer_public_id_no_update").email("<EMAIL>")
              .phone("*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(7)
                  .withOffsetSameInstant(ZoneOffset.UTC)),
          new UpdateHistoryRecord().customerPublicId("customer_public_id_no_update").email("<EMAIL>")
              .phone("*********")
              .dateCreated(OffsetDateTime.now(ZoneId.of("America/New_York")).minusDays(10)
                  .withOffsetSameInstant(ZoneOffset.UTC))
      )));

  @Test
  public void testGetValue() {
    Long customerId = 1L;
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(userAccountService.getUserAccountUpdateHistory("customer_public_id")).thenReturn(
        customerAccountUpdateHistory);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = customerDaysSinceLastEmailAddressChangeFeature.fetchFeatureValue(evalParams);

    assertEquals(featureValue.getName(), "CustomerDaysSinceLastEmailAddressChangeFeature");
    assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    assertEquals(featureValue.getValue(), 3);
  }

  @Test
  public void testGetValueNoHistory() {
    Long customerId = 2L;
    when(identityService.getCustomer(customerId)).thenReturn(customerNoHistory);
    when(userAccountService.getUserAccountUpdateHistory("customer_public_id_no_history")).thenReturn(
        customerAccountUpdateHistoryNoHistory);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = customerDaysSinceLastEmailAddressChangeFeature.fetchFeatureValue(evalParams);

    assertEquals(featureValue.getName(), "CustomerDaysSinceLastEmailAddressChangeFeature");
    assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    assertEquals(featureValue.getValue(), 999999);
  }

  @Test
  public void testGetValueNoUpdate() {
    Long customerId = 3L;
    when(identityService.getCustomer(customerId)).thenReturn(customerNoUpdate);
    when(userAccountService.getUserAccountUpdateHistory("customer_public_id_no_update")).thenReturn(
        customerAccountUpdateHistoryNoUpdate);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = customerDaysSinceLastEmailAddressChangeFeature.fetchFeatureValue(evalParams);

    assertEquals(featureValue.getName(), "CustomerDaysSinceLastEmailAddressChangeFeature");
    assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    assertEquals(featureValue.getValue(), 10);
  }
}
