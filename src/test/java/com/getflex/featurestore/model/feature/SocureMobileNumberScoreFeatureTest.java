package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SocureMobileNumberScoreFeatureTest {
  @InjectMocks
  SocureMobileNumberScoreFeature feature;

  @Mock
  OfferService offerService;

  @Test
  public void testGetValueWithRootEvaluation() {
    Long billerAccountId = 1L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().isRootEvaluation(true).socureMobileNumberScore(0.99F));
    when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offerResponse);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setCustomerId(123L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "SocureMobileNumberScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.99D);
  }

  @Test
  public void testGetValueWithoutRootEvaluation() {
    Long billerAccountId = 1L;
    when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(new InternalOffer().offerId("test-id-1")
        .offerState(OfferStateEnum.ACCEPTED).evaluationContext(new InternalOfferAllOfEvaluationContext()
            .isRootEvaluation(false).rootOfferId("test-id-1").rootOfferVersion(1L)));

    when(offerService.getRootOffer("test-id-1", 1L)).thenReturn(
        new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
            .evaluationContext(new InternalOfferAllOfEvaluationContext()
                .isRootEvaluation(true).socureMobileNumberScore(0.88F)));

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setCustomerId(123L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "SocureMobileNumberScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.88D);
  }

  @Test
  public void testGetValueWithoutRootOfferId() {
    Long billerAccountId = 1L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
            .evaluationContext(
                    new InternalOfferAllOfEvaluationContext().isRootEvaluation(false).socureMobileNumberScore(0.99F));
    when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offerResponse);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setCustomerId(123L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "SocureMobileNumberScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.99D);
  }

  @Test
  public void getValueFromCustomerIdFallback() {
    Long customerId = 123L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().isRootEvaluation(true).socureMobileNumberScore(0.99F));
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(offerResponse);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "SocureMobileNumberScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.99D);
  }

  @Test
  public void getValueFromBillerAccountId_404NotFoundError() {
    Long billerAccountId = 456L;
    when(offerService.getOfferByBillerAccountId(eq(billerAccountId)))
        .thenThrow(new InternalDependencyFailureException("404 - Specified offer (version) doesn't exist"));
    Long customerId = 123L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().isRootEvaluation(true).socureMobileNumberScore(0.99F));
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(offerResponse);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "SocureMobileNumberScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.99D);
  }
}
