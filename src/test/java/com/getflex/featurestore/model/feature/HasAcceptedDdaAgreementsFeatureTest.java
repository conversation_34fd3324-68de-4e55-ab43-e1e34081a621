package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.AgType;
import com.getflex.identity.model.CustomerAgreementRecord;
import com.getflex.identity.model.FinancialPartnerType;
import java.util.List;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class HasAcceptedDdaAgreementsFeatureTest {

  @InjectMocks
  HasAcceptedDdaAgreementsFeature hasAcceptedDdaAgreementsFeature;

  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean hasAcceptedDdaAgreements) {
    Long customerId = 1L;
    List<AgType> agTypes = List.of(AgType.BRB_DDA_AGREEMENT, AgType.STRIPE_CONNECT_AGREEMENT);
    List<CustomerAgreementRecord> customerAgreementRecords = List.of();
    if (hasAcceptedDdaAgreements) {
      customerAgreementRecords = List.of(
          new CustomerAgreementRecord().agreementType(AgType.BRB_DDA_AGREEMENT),
          new CustomerAgreementRecord().agreementType(AgType.STRIPE_CONNECT_AGREEMENT)
      );
    }
    when(identityService.getFinancialPartner(customerId)).thenReturn(FinancialPartnerType.BLUE_RIDGE_BANK);
    when(identityService.getCustomerAgreements(customerId, agTypes)).thenReturn(customerAgreementRecords);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = hasAcceptedDdaAgreementsFeature.fetchFeatureValue(evalParams);
    assertEquals(hasAcceptedDdaAgreements, featureValue.getValue());
  }

}
