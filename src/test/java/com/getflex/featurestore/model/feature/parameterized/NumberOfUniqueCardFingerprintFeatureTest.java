package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfUniqueCardFingerprintFeatureTest {

  @Mock
  IdentityService identityService;

  @Mock
  WalletService walletService;

  @ParameterizedTest
  @MethodSource("cardTestCases")
  public void testGetValue(
      List<Card> cards,
      String lookbackWindow,
      Integer expectedNumberOfUniqueCardFingerprint
  ) {
    Long customerId = 1L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse()
            .customerId(customerId)
            .customerPublicId(customerPublicId)
    );
    when(walletService.getCards(customerPublicId)).thenReturn(cards);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    var featureValue = new NumberOfUniqueCardFingerprintFeature(
        identityService, walletService, new LookbackDurationFeatureParams(lookbackWindow)
    ).fetchFeatureValue(evalParams);
    assertEquals(expectedNumberOfUniqueCardFingerprint, featureValue.getValue());
    assertEquals(FeatureTypeEnum.INT, featureValue.getType());
  }

  static Stream<Arguments> cardTestCases() {
    var now = OffsetDateTime.now();
    return Stream.of(
        Arguments.of(List.of(), "P60D", 0),
        Arguments.of(
            List.of(
                new Card()
                    .fingerprint("fingerprint1")
                    .dtCreated(now.minusDays(61))
            ),
            "P60D",
            0
        ),
        Arguments.of(
            List.of(
                new Card()
                    .fingerprint("fingerprint1")
                    .dtCreated(now),
                new Card()
                    .fingerprint("fingerprint1")
                    .dtCreated(now)
            ),
            "P60D",
            1
        ),
        Arguments.of(
            List.of(
                new Card()
                    .fingerprint("fingerprint1")
                    .dtCreated(now.minusDays(29)),
                new Card()
                    .fingerprint("fingerprint2")
                    .dtCreated(now.minusDays(28))
            ),
            "P30D",
            2
        )
    );
  }
}
