package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeCancelledPayBillerCentsFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.CANCELED.getValue());
    record1.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet record2 = new RecordLedgerWallet();
    record2.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record2.setAmount(3000L);
    record2.setPaymentStatusId(PaymentState.CANCELED.getValue());
    record2.setPaymentCategoryId(MovementCategory.CAPTURE.getValue());

    // This record should be excluded because it's not canceled
    RecordLedgerWallet nonCanceledRecord = new RecordLedgerWallet();
    nonCanceledRecord.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    nonCanceledRecord.setAmount(2000L);
    nonCanceledRecord.setPaymentStatusId(PaymentState.SETTLED.getValue());
    nonCanceledRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    // This record should be excluded because it's not PAY_BILLER type
    RecordLedgerWallet nonPayBillerRecord = new RecordLedgerWallet();
    nonPayBillerRecord.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    nonPayBillerRecord.setAmount(1000L);
    nonPayBillerRecord.setPaymentStatusId(PaymentState.CANCELED.getValue());
    nonPayBillerRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    // This record should be excluded because it's declined, not canceled
    RecordLedgerWallet declinedRecord = new RecordLedgerWallet();
    declinedRecord.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    declinedRecord.setAmount(500L);
    declinedRecord.setPaymentStatusId(PaymentState.DECLINED.getValue());
    declinedRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    List<RecordLedgerWallet> records = List.of(record1, record2, nonCanceledRecord,
        nonPayBillerRecord, declinedRecord);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeCancelledPayBillerCentsFeature feature =
        new CustomerLifetimeCancelledPayBillerCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(4000L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeCancelledPayBillerCentsFeature feature =
        new CustomerLifetimeCancelledPayBillerCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.CANCELED.getValue());
    // No payment category set - this simulates legacy records

    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeCancelledPayBillerCentsFeature feature =
        new CustomerLifetimeCancelledPayBillerCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }
}
