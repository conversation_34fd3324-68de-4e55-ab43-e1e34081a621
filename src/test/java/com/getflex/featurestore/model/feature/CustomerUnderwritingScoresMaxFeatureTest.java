package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.decisionengine.async.model.CheckpointDecisionLog;
import com.getflex.decisionengine.async.model.GetCheckpointDecisionLogsResponse;
import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.output.CustomerUnderwritingScoresOutput;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerUnderwritingScoresMaxFeatureTest {

  @InjectMocks
  CustomerUnderwritingScoresMaxFeature feature;

  @Mock
  OfferService offerService;

  @Mock
  DecisionEngineService decisionEngineService;

  private static final Long CUSTOMER_ID = 123L;
  private static final EvalParams EVAL_PARAMS = new EvalParams();

  @BeforeAll
  public static void setup() {
    EVAL_PARAMS.setCustomerId(CUSTOMER_ID);
  }

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<InternalOffer> offers,
      Map<String, Object> expectedScores
  ) {
    when(offerService.searchOffer(eq(CUSTOMER_ID), eq(true))).thenReturn(offers);
    when(decisionEngineService.getCheckpointDecisionLogs(any(), any(), any()))
        .thenReturn(
            CompletableFuture.completedFuture(
                new GetCheckpointDecisionLogsResponse().checkpointDecisionLogs(
                    new ArrayList<>(
                        List.of(
                            new CheckpointDecisionLog()
                                .featureValues(
                                    Map.of(
                                        "SocureFeature",
                                        Map.of()
                                    )
                                )
                        )
                    )
                )
            )
        );
    FeatureOutput output = feature.getValue(EVAL_PARAMS);

    CustomerUnderwritingScoresOutput result = (CustomerUnderwritingScoresOutput) output.value();
    Assertions.assertEquals(
        (Double) expectedScores.get("expectedSigmaScore"),
        result.getSigmaScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedAddressRiskScore"),
        result.getAddressRiskScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedEmailRiskScore"),
        result.getEmailRiskScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedNameAddressCorrelationScore"),
        result.getNameAddressCorrelationScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedNameEmailCorrelationScore"),
        result.getNameEmailCorrelationScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedPhoneRiskScore"),
        result.getPhoneRiskScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedSyntheticScore"),
        result.getSyntheticScore()
    );
    Assertions.assertEquals(
        (Float) expectedScores.get("expectedNamePhoneCorrelationScore"),
        result.getNamePhoneCorrelationScore()
    );
    Assertions.assertEquals(
        (Integer) expectedScores.get("expectedFlexScoreDecile"),
        result.getFlexScoreDecile()
    );
    Assertions.assertEquals(
        (Integer) expectedScores.get("expectedFraudReasonCodeR617"),
        result.getFraudReasonCodeR617()
    );
    Assertions.assertEquals(
        (Integer) expectedScores.get("expectedFraudReasonCodeR665"),
        result.getFraudReasonCodeR665()
    );
    Assertions.assertEquals(
        (Integer) expectedScores.get("expectedFraudReasonCodeI161"),
        result.getFraudReasonCodeI161()
    );
    Assertions.assertEquals(
        (Integer) expectedScores.get("expectedFraudReasonCodeR021"),
        result.getFraudReasonCodeR021()
    );
    if (result.getSentilinkScores() != null) {
      Assertions.assertEquals(
          (Integer) expectedScores.get("expectedFirstPartySyntheticScore"),
          result.getSentilinkScores().getFirstPartySyntheticScore()
      );
      Assertions.assertEquals(
          (Integer) expectedScores.get("expectedThirdPartySyntheticScore"),
          result.getSentilinkScores().getThirdPartySyntheticScore()
      );
      Assertions.assertEquals(
          (Integer) expectedScores.get("expectedAbuseScore"),
          result.getSentilinkScores().getAbuseScore()
      );
      Assertions.assertEquals(
          (Integer) expectedScores.get("expectedIdTheftScore"),
          result.getSentilinkScores().getIdTheftScore()
      );
    }
  }

  private static Stream<Arguments> testCases() {
    return Stream.of(
        Arguments.of(
            List.of(),
            expectedScores(
                0.0,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
        ),
        Arguments.of(
            List.of(
                new InternalOffer()
                    .evaluationContext(
                        new InternalOfferAllOfEvaluationContext()
                            .isRootEvaluation(false)
                    )
            ),
            expectedScores(
                0.0,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
        ),
        Arguments.of(
            List.of(
                offer(null, null, null, null, null, null, null, null, null, null, null, null, null, null)
            ),
            expectedScores(
                0.0,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                0.0f,
                null,
                null,
                null,
                null,
                null,
                0,
                0,
                0,
                0
            )
        ),
        Arguments.of(
            List.of(
                offer(0.1,
                    0.1f,
                    0.1f,
                    0.1f,
                    0.1f,
                    0.1f,
                    0.1f,
                    0.1f,
                    1,
                    1,
                    1,
                    1,
                    1,
                    Set.of("R617", "R665", "I161", "R021")
                )
            ),
            expectedScores(0.1, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 1, 1, 1, 1, 1, 1, 1, 1, 1)
        ),
        Arguments.of(
            List.of(
                offer(
                    0.1,
                    0.6f,
                    0.1f,
                    0.1f,
                    0.6f,
                    0.6f,
                    0.1f,
                    0.1f,
                    1,
                    2,
                    1,
                    2,
                    1,
                    Set.of("R617", "R665", "I161", "R021")
                ),
                offer(0.6,
                    0.1f,
                    0.6f,
                    0.6f,
                    0.1f,
                    0.1f,
                    0.6f,
                    0.6f,
                    2,
                    1,
                    2,
                    1,
                    2,
                    Set.of("R617", "R665", "I161", "R021")
                )
            ),
            expectedScores(0.6, 0.6f, 0.6f, 0.6f, 0.6f, 0.6f, 0.6f, 0.6f, 2, 2, 2, 2, 2, 1, 1, 1, 1)
        )
    );
  }

  private static InternalOffer offer(
      Double sigmaScore,
      Float addressRiskScore,
      Float emailRiskScore,
      Float nameAddressCorrelationScore,
      Float nameEmailCorrelationScore,
      Float phoneRiskScore,
      Float syntheticScore,
      Float namePhoneCorrelationScore,
      Integer flexScoreDecile,
      Integer firstPartySyntheticScore,
      Integer thirdPartySyntheticScore,
      Integer abuseScore,
      Integer idTheftScore,
      Set<String> fraudReasonCodes
  ) {
    return new InternalOffer()
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext()
                .isRootEvaluation(true)
                .socureSigmaScore(sigmaScore)
                .socureAddressRiskScore(addressRiskScore)
                .socureEmailRiskScore(emailRiskScore)
                .socureNameAddressCorrelationScore(nameAddressCorrelationScore)
                .socureNameEmailCorrelationScore(nameEmailCorrelationScore)
                .socurePhoneRiskScore(phoneRiskScore)
                .socureSyntheticScore(syntheticScore)
                .socureNamePhoneCorrelationScore(namePhoneCorrelationScore)
                .flexScoreDecile(flexScoreDecile)
                .sentilinkFirstPartySyntheticScore(firstPartySyntheticScore)
                .sentilinkThirdPartySyntheticScore(thirdPartySyntheticScore)
                .sentilinkAbuseScore(abuseScore)
                .sentilinkIdTheftScore(idTheftScore)
                .socureFraudReasonCodes(fraudReasonCodes)
        );
  }

  private static Map<String, Object> expectedScores(
      Double expectedSigmaScore,
      Float expectedAddressRiskScore,
      Float expectedEmailRiskScore,
      Float expectedNameAddressCorrelationScore,
      Float expectedNameEmailCorrelationScore,
      Float expectedPhoneRiskScore,
      Float expectedSyntheticScore,
      Float expectedNamePhoneCorrelationScore,
      Integer expectedFlexScoreDecile,
      Integer expectedFirstPartySyntheticScore,
      Integer expectedThirdPartySyntheticScore,
      Integer expectedAbuseScore,
      Integer expectedIdTheftScore,
      Integer expectedFraudReasonCodeR617,
      Integer expectedFraudReasonCodeR665,
      Integer expectedFraudReasonCodeI161,
      Integer expectedFraudReasonCodeR021
  ) {
    Map<String, Object> scores = new HashMap<>();
    scores.put("expectedSigmaScore", expectedSigmaScore);
    scores.put("expectedAddressRiskScore", expectedAddressRiskScore);
    scores.put("expectedEmailRiskScore", expectedEmailRiskScore);
    scores.put("expectedNameAddressCorrelationScore", expectedNameAddressCorrelationScore);
    scores.put("expectedNameEmailCorrelationScore", expectedNameEmailCorrelationScore);
    scores.put("expectedPhoneRiskScore", expectedPhoneRiskScore);
    scores.put("expectedSyntheticScore", expectedSyntheticScore);
    scores.put("expectedNamePhoneCorrelationScore", expectedNamePhoneCorrelationScore);
    scores.put("expectedFlexScoreDecile", expectedFlexScoreDecile);
    scores.put("expectedFirstPartySyntheticScore", expectedFirstPartySyntheticScore);
    scores.put("expectedThirdPartySyntheticScore", expectedThirdPartySyntheticScore);
    scores.put("expectedAbuseScore", expectedAbuseScore);
    scores.put("expectedIdTheftScore", expectedIdTheftScore);
    scores.put("expectedFraudReasonCodeR617", expectedFraudReasonCodeR617);
    scores.put("expectedFraudReasonCodeR665", expectedFraudReasonCodeR665);
    scores.put("expectedFraudReasonCodeI161", expectedFraudReasonCodeI161);
    scores.put("expectedFraudReasonCodeR021", expectedFraudReasonCodeR021);
    return scores;

  }

}
