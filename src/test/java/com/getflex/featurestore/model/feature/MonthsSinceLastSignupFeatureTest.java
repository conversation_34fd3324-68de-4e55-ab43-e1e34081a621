package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.billpay.MonthsSinceLastSignupFeature;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Optional;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class MonthsSinceLastSignupFeatureTest {

  @InjectMocks
  MonthsSinceLastSignupFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {"100", "200"})
  public void testGetValue(String offlineFeatureValue) {
    Long customerId = 123L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    Optional<OfflineFeature> offlineFeature = Optional.of(
        OfflineFeature.builder()
            .featureName("months_since_last_signup")
            .primaryKey(customerPublicId)
            .featureValue(offlineFeatureValue)
            .build()
    );
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey("months_since_last_signup", customerPublicId))
        .thenReturn(offlineFeature);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    if (offlineFeatureValue == null || offlineFeatureValue.isEmpty()) {
      assertNull(featureValue.getValue());
    } else {
      assertEquals(Double.valueOf(offlineFeatureValue), featureValue.getValue());
    }
  }
}
