package com.getflex.featurestore.model.feature.base;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.sagemaker.model.base.BaseModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BaseModelFeatureTest {

  @Mock
  FeatureFactory featureFactory;

  @Mock
  BaseFeature feature;

  @ParameterizedTest
  @CsvSource({
      "string, abc",
      "array, []"
  })
  public void fetchValueFromFeature_unsupportedFeatureTypes(String featureType, Object value) {
    when(featureFactory.getFeature(anyString())).thenReturn(feature);
    FeatureValue featureValue = new FeatureValue();
    featureValue.setType(FeatureTypeEnum.fromValue(featureType));
    featureValue.setValue(value);
    when(feature.fetchFeatureValue(any())).thenReturn(featureValue);
    Assertions.assertThrows(IllegalArgumentException.class,
        () -> BaseModelInput.fetchValueFromFeature(featureFactory, new EvalParams(), "TestFeatureName")
    );
  }

}
