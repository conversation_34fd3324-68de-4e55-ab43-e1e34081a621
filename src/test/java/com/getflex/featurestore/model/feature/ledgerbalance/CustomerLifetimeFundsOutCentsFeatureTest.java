package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeFundsOutCentsFeature;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeFundsOutCentsFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record1.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet record2 = new RecordLedgerWallet();
    record2.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    record2.setAmount(3000L);
    record2.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record2.setPaymentCategoryId(MovementCategory.CAPTURE.getValue());

    // This record should be excluded because it's not settled
    RecordLedgerWallet nonSettledRecord = new RecordLedgerWallet();
    nonSettledRecord.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    nonSettledRecord.setAmount(2000L);
    nonSettledRecord.setPaymentStatusId(PaymentState.DECLINED.getValue());
    nonSettledRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    // This record should be excluded because it's not FUNDS_OUT type
    RecordLedgerWallet nonFundsOutRecord = new RecordLedgerWallet();
    nonFundsOutRecord.setMoneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue());
    nonFundsOutRecord.setAmount(1000L);
    nonFundsOutRecord.setPaymentStatusId(PaymentState.SETTLED.getValue());
    nonFundsOutRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    // This record should be excluded because it's canceled, not settled
    RecordLedgerWallet canceledRecord = new RecordLedgerWallet();
    canceledRecord.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    canceledRecord.setAmount(500L);
    canceledRecord.setPaymentStatusId(PaymentState.CANCELED.getValue());
    canceledRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    // This record should be excluded because it's initiated, not settled
    RecordLedgerWallet initiatedRecord = new RecordLedgerWallet();
    initiatedRecord.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    initiatedRecord.setAmount(750L);
    initiatedRecord.setPaymentStatusId(PaymentState.INITIATED.getValue());
    initiatedRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    List<RecordLedgerWallet> records = List.of(record1, record2, nonSettledRecord, nonFundsOutRecord,
        canceledRecord, initiatedRecord);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeFundsOutCentsFeature feature = new CustomerLifetimeFundsOutCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(4000L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeFundsOutCentsFeature feature = new CustomerLifetimeFundsOutCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    // No payment category set - this simulates legacy records

    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeFundsOutCentsFeature feature = new CustomerLifetimeFundsOutCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }
}
