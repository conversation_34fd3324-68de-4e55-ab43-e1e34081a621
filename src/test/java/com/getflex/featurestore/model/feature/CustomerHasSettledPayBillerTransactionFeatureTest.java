package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerHasSettledPayBillerTransactionFeatureTest {

  private final Long customerId = 123L;
  private final String featureName = "CustomerHasSettledPayBillerTransactionFeature";

  @InjectMocks
  CustomerHasSettledPayBillerTransactionFeature customerHasSettledPayBillerTransactionFeature;

  @Mock
  LedgerService ledgerService;

  /** Test case for CustomerHasSettledPayBillerTransactionFeature */
  @Test
  public void testCustomerHasSettledPayBillerTransactionFeature() {
    List<RecordLedger> recordLedgers = new ArrayList<>();
    RecordLedger recordLedger = new RecordLedger();
    recordLedger.setMoneyMovementTypeId(LedgerService.MoneyMovementType.PAY_BILLER.getValue());
    recordLedgers.add(recordLedger);

    Mockito.when(ledgerService.getLedgersByCustomerId(customerId, false))
            .thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue =
            customerHasSettledPayBillerTransactionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /** Test case for CustomerHasSettledPayBillerTransactionFeature return empty */
  @Test
  public void testCustomerHasSettledPayBillerTransactionFeatureReturnEmpty() {
    List<RecordLedger> recordLedgers = new ArrayList<>();
    Mockito.when(ledgerService.getLedgersByCustomerId(customerId, false))
            .thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue =
            customerHasSettledPayBillerTransactionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /** Test case for CustomerHasSettledPayBillerTransactionFeature return differ type */
  @Test
  public void testCustomerEprVantageScoreLastPullDaysFeatureReturnDifferType() {
    List<RecordLedger> recordLedgers = new ArrayList<>();
    RecordLedger recordLedger = new RecordLedger();
    recordLedger.setMoneyMovementTypeId(LedgerService.MoneyMovementType.DOWNPAYMENT.getValue());
    recordLedgers.add(recordLedger);

    Mockito.when(ledgerService.getLedgersByCustomerId(customerId, false))
            .thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue =
            customerHasSettledPayBillerTransactionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }
}