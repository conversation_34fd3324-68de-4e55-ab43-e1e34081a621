package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature.OBJECT_MAPPER;
import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.income.model.underwriting.IsIneligible;
import com.getflex.featurestore.model.feature.income.model.underwriting.OriginalTuFeatures;
import com.getflex.featurestore.model.feature.income.model.underwriting.UnderwritingModel;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.utils.AlloyUtils;
import com.getflex.verification.model.BankAccount;
import com.getflex.verification.model.PayStub;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.ValidationVerifiedBankAccounts;
import com.getflex.verification.model.ValidationVerifiedPayStubs;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IncomeVerificationUnderwritingFeatureTest {

  @Mock
  VerificationService verificationService;

  @Mock
  AlloyUtils alloyUtils;

  @InjectMocks
  IncomeVerificationUnderwritingFeature feature;

  @Test
  public void getUnderwritingFeatures_bank_eligible() throws IOException {
    Validation validation = new Validation();
    validation.setTotalAverageDailyBalanceCent(10000L);
    validation.setGrossMonthlyIncomeCent(325000L);
    ValidationVerifiedBankAccounts bankAccounts = new ValidationVerifiedBankAccounts();
    bankAccounts.setEligibleBankAccounts(List.of(new BankAccount().accountId("accountId")));
    validation.setVerifiedBankAccounts(bankAccounts);
    validation.setGrossAnnualSelfReportedIncomeCent(3900000L);
    validation.setVerifiedGrossMonthlyIncomeCent(350000L);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("BANK_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setAlloyReportUrl("https://fake.com");
    evalParams.setEstimatedRentAmountCent(100000L);
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "03-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(10000L)
        .monthlyGrossIncomeCent(325000L)
        .monthlyGrossVerifiedIncomeCent(350000L)
        .monthlyGrossSelfReportedIncomeCent(325000L)
        .annualTaxAmountCent(420300L)
        .monthlyNetIncomeCent(260775L)
        .totalMonthlyObligationInstallmentTradesCent(24700L)
        .totalMonthlyObligationRevolvingTradesCent(4500L)
        .nonMedicalCollectionBalanceCent(0L)
        .netIncomeToRentRatio(2.60775)
        .grossIncomeToRentRatio(3.25)
        .verifiedGrossIncomeToRentRatio(3.50)
        .estimatedRentAmountCent(100000L)
        .isIneligible(new IsIneligible(false, null))
        .originalTuFeatures(new OriginalTuFeatures(45.0, 247L, null))
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_bank_ineligible() throws IOException {
    Validation validation = new Validation();
    validation.setTotalAverageDailyBalanceCent(10000L);
    ValidationVerifiedBankAccounts bankAccounts = new ValidationVerifiedBankAccounts();
    validation.setVerifiedBankAccounts(bankAccounts);
    validation.setGrossAnnualSelfReportedIncomeCent(3900000L);
    validation.setVerifiedGrossMonthlyIncomeCent(0L);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("BANK_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setAlloyReportUrl("https://fake.com");
    evalParams.setEstimatedRentAmountCent(100000L);
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(10000L)
        .monthlyGrossIncomeCent(0L)
        .monthlyGrossVerifiedIncomeCent(0L)
        .monthlyGrossSelfReportedIncomeCent(325000L)
        .annualTaxAmountCent(0L)
        .monthlyNetIncomeCent(0L)
        .totalMonthlyObligationInstallmentTradesCent(177500L)
        .totalMonthlyObligationRevolvingTradesCent(4500L)
        .nonMedicalCollectionBalanceCent(0L)
        .estimatedRentAmountCent(100000L)
        .netIncomeToRentRatio(0.00)
        .grossIncomeToRentRatio(0.00)
        .verifiedGrossIncomeToRentRatio(0.00)
        .isIneligible(new IsIneligible(true, null))
        .originalTuFeatures(new OriginalTuFeatures(45.0, 1775L, null))
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_pay_stub_eligible() throws IOException {
    Validation validation = new Validation();
    validation.setGrossMonthlyIncomeCent(90000L);
    validation.setGrossAnnualSelfReportedIncomeCent(12000000L);
    validation.setVerifiedGrossMonthlyIncomeCent(90000L);
    validation.setIsSubmissionFraudulent(false);
    ValidationVerifiedPayStubs payStubs = new ValidationVerifiedPayStubs();
    payStubs.setFailGradePayStubs(List.of(new PayStub()));
    payStubs.setPassGradePayStubs(List.of(new PayStub()));
    validation.setVerifiedPayStubs(payStubs);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("PAY_STUB_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setAlloyReportUrl("https://fake.com");
    evalParams.setEstimatedRentAmountCent(100000L);
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "03-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(null)
        .annualTaxAmountCent(108000L)
        .monthlyGrossIncomeCent(90000L)
        .monthlyGrossVerifiedIncomeCent(90000L)
        .monthlyGrossSelfReportedIncomeCent(1000000L)
        .monthlyNetIncomeCent(51800L)
        .totalMonthlyObligationInstallmentTradesCent(24700L)
        .totalMonthlyObligationRevolvingTradesCent(4500L)
        .nonMedicalCollectionBalanceCent(0L)
        .netIncomeToRentRatio(0.518)
        .grossIncomeToRentRatio(0.9)
        .verifiedGrossIncomeToRentRatio(0.9)
        .estimatedRentAmountCent(100000L)
        .isIneligible(new IsIneligible(false, false))
        .originalTuFeatures(new OriginalTuFeatures(45.0, 247L, null))
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_pay_stub_ineligible() throws IOException {
    Validation validation = new Validation();
    validation.setGrossMonthlyIncomeCent(900000L);
    validation.setVerifiedGrossMonthlyIncomeCent(900000L);
    validation.setIsSubmissionFraudulent(true);
    ValidationVerifiedPayStubs payStubs = new ValidationVerifiedPayStubs();
    payStubs.setFailGradePayStubs(List.of(new PayStub()));
    validation.setVerifiedPayStubs(payStubs);
    validation.setGrossAnnualSelfReportedIncomeCent(12000000L);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("PAY_STUB_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setAlloyReportUrl("https://fake.com");
    evalParams.setEstimatedRentAmountCent(100000L);
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(null)
        .annualTaxAmountCent(1358800L)
        .monthlyGrossIncomeCent(900000L)
        .monthlyGrossVerifiedIncomeCent(900000L)
        .monthlyGrossSelfReportedIncomeCent(1000000L)
        .monthlyNetIncomeCent(604766L)
        .totalMonthlyObligationInstallmentTradesCent(177500L)
        .totalMonthlyObligationRevolvingTradesCent(4500L)
        .nonMedicalCollectionBalanceCent(0L)
        .netIncomeToRentRatio(6.04766)
        .grossIncomeToRentRatio(9.00)
        .verifiedGrossIncomeToRentRatio(9.00)
        .estimatedRentAmountCent(100000L)
        .isIneligible(new IsIneligible(true, true))
        .originalTuFeatures(new OriginalTuFeatures(45.0, 1775L, null))
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_required_fields_missing() {
    Validation validation = new Validation();
    validation.setGrossMonthlyIncomeCent(90000L);
    validation.setIsSubmissionFraudulent(false);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");

    Assertions.assertThrows(InternalServiceBadDataException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getUnderwritingFeatures_expired() {
    Verification verification = new Verification();
    verification.setStatus("EXPIRED");
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("PAY_STUB_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(true)
        .averageDailyBalanceCent(null)
        .annualTaxAmountCent(null)
        .monthlyGrossIncomeCent(null)
        .isIneligible(null)
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_overrideValidation() throws IOException {
    Validation validation = new Validation();
    validation.setGrossMonthlyIncomeCent(900000L);
    validation.setIsSubmissionFraudulent(false);
    ValidationVerifiedPayStubs payStubs = new ValidationVerifiedPayStubs();
    payStubs.setFailGradePayStubs(List.of(new PayStub()));
    validation.setVerifiedPayStubs(payStubs);
    validation.setGrossAnnualSelfReportedIncomeCent(12000000L);
    validation.setVerifiedGrossMonthlyIncomeCent(1100000L);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);

    Validation overrideValidation = new Validation();
    overrideValidation.setGrossMonthlyIncomeCent(700000L);
    overrideValidation.setIsSubmissionFraudulent(true);
    ValidationVerifiedPayStubs overridePayStubs = new ValidationVerifiedPayStubs();
    overridePayStubs.setFailGradePayStubs(List.of(new PayStub()));
    overridePayStubs.setPassGradePayStubs(List.of(new PayStub()));
    overrideValidation.setVerifiedPayStubs(overridePayStubs);
    context.setValidationOverride(overrideValidation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("PAY_STUB_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setAlloyReportUrl("https://fake.com");
    evalParams.setEstimatedRentAmountCent(100000L);
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));
    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(null)
        .annualTaxAmountCent(960300L)
        .monthlyGrossIncomeCent(700000L)
        .monthlyGrossVerifiedIncomeCent(1100000L)
        .monthlyGrossSelfReportedIncomeCent(1000000L)
        .monthlyNetIncomeCent(437975L)
        .totalMonthlyObligationInstallmentTradesCent(177500L)
        .totalMonthlyObligationRevolvingTradesCent(4500L)
        .nonMedicalCollectionBalanceCent(0L)
        .netIncomeToRentRatio(4.37975)
        .grossIncomeToRentRatio(7.00)
        .verifiedGrossIncomeToRentRatio(11.00)
        .estimatedRentAmountCent(100000L)
        .isIneligible(new IsIneligible(false, true))
        .originalTuFeatures(new OriginalTuFeatures(45.0, 1775L, null))
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }

  @Test
  public void getUnderwritingFeatures_bank_eligible_noAlloyReport() throws IOException {
    Validation validation = new Validation();
    validation.setTotalAverageDailyBalanceCent(10000L);
    validation.setGrossMonthlyIncomeCent(300000L);
    ValidationVerifiedBankAccounts bankAccounts = new ValidationVerifiedBankAccounts();
    bankAccounts.setEligibleBankAccounts(List.of(new BankAccount().accountId("accountId")));
    validation.setVerifiedBankAccounts(bankAccounts);
    validation.setGrossAnnualSelfReportedIncomeCent(3900000L);
    validation.setVerifiedGrossMonthlyIncomeCent(300000L);
    VerificationContext context = new VerificationContext();
    context.setValidation(validation);
    Verification verification = new Verification();
    verification.setStatus("COMPLETED");
    verification.setContext(context);
    verification.setDtCreated(OffsetDateTime.now().minusDays(10));
    verification.setVerificationType("BANK_INCOME");
    when(verificationService.getVerification("123")).thenReturn(verification);

    EvalParams evalParams = new EvalParams();
    evalParams.setVerificationId("123");
    evalParams.setEstimatedRentAmountCent(100000L);

    FeatureOutput output = feature.getValue(evalParams);

    UnderwritingModel expectedOutput = UnderwritingModel.builder()
        .isExpired(false)
        .averageDailyBalanceCent(10000L)
        .monthlyGrossIncomeCent(300000L)
        .monthlyGrossVerifiedIncomeCent(300000L)
        .monthlyGrossSelfReportedIncomeCent(325000L)
        .annualTaxAmountCent(384300L)
        .monthlyNetIncomeCent(null)
        .totalMonthlyObligationInstallmentTradesCent(null)
        .totalMonthlyObligationRevolvingTradesCent(null)
        .nonMedicalCollectionBalanceCent(null)
        .netIncomeToRentRatio(null)
        .grossIncomeToRentRatio(3.00)
        .verifiedGrossIncomeToRentRatio(3.00)
        .estimatedRentAmountCent(100000L)
        .isIneligible(new IsIneligible(false, null))
        .originalTuFeatures(null)
        .build();

    Assertions.assertNotNull(output);
    Assertions.assertEquals(expectedOutput, output.value());
  }
}
