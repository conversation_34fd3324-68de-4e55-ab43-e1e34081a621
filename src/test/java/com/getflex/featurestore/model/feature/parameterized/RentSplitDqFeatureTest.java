package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.register.AnnotatedFeatureResolver;
import com.getflex.featurestore.service.delinquency.DelinquencyInfo;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import com.getflex.featurestore.service.delinquency.RentSplitDqInfo;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

@ExtendWith(MockitoExtension.class)
public class RentSplitDqFeatureTest {

  private static final Long CUSTOMER_ID = 123L;
  
  @Mock
  private DelinquencyService delinquencyService;

  @Test
  void getType_shouldReturnObject() {
    var feature = new RentSplitDqFeature(delinquencyService, 0, 24);
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
  }

  @Test
  void getDescription_shouldIncludeParameters() {
    var feature = new RentSplitDqFeature(delinquencyService, 30, 12);
    assertEquals("Get rent split delinquency information for DQ30 with 12 months lookback", 
        feature.getDescription());
  }

  @ParameterizedTest
  @MethodSource("provideRentSplitDqFeatureTest")
  void testGetValue_withDifferentParameters(
      int dqDays,
      int lookbackMonths,
      int expectedDqMonths
  ) {
    var feature = new RentSplitDqFeature(delinquencyService, dqDays, lookbackMonths);
    var evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    
    var delinquencyInfo = DelinquencyInfo.builder()
        .isDq(true)
        .firstBpDqDate(null)
        .build();
    
    when(delinquencyService.getDelinquencyInfo(anyLong(), anyInt(), anyInt(), anyBoolean()))
        .thenReturn(delinquencyInfo);
    
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    
    verify(delinquencyService).getDelinquencyInfo(
        eq(CUSTOMER_ID), 
        eq(expectedDqMonths), 
        eq(lookbackMonths), 
        eq(false)  // excludeCb should always be false
    );

    assertNotNull(featureValue);
    assertNotNull(featureValue.getValue());
    RentSplitDqInfo result = (RentSplitDqInfo) featureValue.getValue();
    assertTrue(result.isDq());
    assertNull(result.rentSplitDqBpDate());
  }

  @ParameterizedTest
  @MethodSource("delinquencyTestCases")
  void testGetValue_withDelinquencyData(
      boolean isDq,
      OffsetDateTime firstBpDqDate,
      LocalDate expectedRentSplitDqBpDate
  ) {
    var feature = new RentSplitDqFeature(delinquencyService, 60, 24);
    var evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    
    var delinquencyInfo = DelinquencyInfo.builder()
        .isDq(isDq)
        .firstBpDqDate(firstBpDqDate)
        .build();
    
    when(delinquencyService.getDelinquencyInfo(CUSTOMER_ID, 2, 24, false))
        .thenReturn(delinquencyInfo);
    
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    
    assertNotNull(featureValue);
    assertNotNull(featureValue.getValue());
    RentSplitDqInfo result = (RentSplitDqInfo) featureValue.getValue();
    assertEquals(isDq, result.isDq());
    assertEquals(expectedRentSplitDqBpDate, result.rentSplitDqBpDate());
  }

  private static Stream<Arguments> provideRentSplitDqFeatureTest() {
    return Stream.of(
        // dqDays, lookbackMonths, expectedDqMonths
        Arguments.of(0, 3, 0),      // DQ0 with 3 months lookback
        Arguments.of(30, 24, 1),    // DQ30 with 24 months lookback
        Arguments.of(60, 12, 2),    // DQ60 with 12 months lookback
        Arguments.of(90, 6, 3),     // DQ90 with 6 months lookback
        Arguments.of(120, 24, 4),   // DQ120 with 24 months lookback
        Arguments.of(180, 24, 6),   // DQ180 with 24 months lookback
        Arguments.of(0, 1, 0),      // Edge case: DQ0 with 1 month
        Arguments.of(15, 12, 0),    // Edge case: 15 days -> 0 months
        Arguments.of(45, 12, 1),    // Edge case: 45 days -> 1 month
        Arguments.of(210, 36, 7)    // Edge case: DQ210 with 36 months lookback
    );
  }

  private static Stream<Arguments> delinquencyTestCases() {
    // Create test dates - DelinquencyService now returns normalized dates
    OffsetDateTime normalizedDate = OffsetDateTime.of(2024, 1, 1, 10, 0, 0, 0, ZoneOffset.UTC);
    // The expected LocalDate after timezone conversion
    LocalDate expectedLocalDate = LocalDate.of(2024, 1, 1);
    
    return Stream.of(
        // isDq, firstBpDqDate, expectedRentSplitDqBpDate
        Arguments.of(true, normalizedDate, expectedLocalDate),
        Arguments.of(true, null, null),
        Arguments.of(false, null, null),
        Arguments.of(false, normalizedDate, null)  // When not DQ, date should be null
    );
  }
}
