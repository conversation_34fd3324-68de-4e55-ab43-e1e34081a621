package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerDaysSinceOfferAcceptFeatureTest {

  @InjectMocks
  CustomerDaysSinceOfferAcceptFeature customerDaysSinceOfferAcceptFeature;

  @Mock
  OfferService offerService;

  @Test
  public void testGetValue_success() {
    Long customerId = 1234L;
    int deltaDay = 50;
    OffsetDateTime acceptanceTime = OffsetDateTime.now().minusDays(deltaDay);
    InternalOffer offer =
        new InternalOffer().customerId(customerId).offerId("offer-test").acceptanceTime(acceptanceTime);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(offer);

    FeatureValue featureValue = customerDaysSinceOfferAcceptFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDaysSinceOfferAcceptFeature");
    Assertions.assertEquals(featureValue.getValue(), deltaDay);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValue_failAndReturnDefault() {
    Long customerId = 1234L;
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getFirstEverAcceptedOffer(customerId))
        .thenThrow(new InternalDependencyFailureException("exception"));
    int defaultValue = -1;

    FeatureValue featureValue = customerDaysSinceOfferAcceptFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDaysSinceOfferAcceptFeature");
    Assertions.assertEquals(featureValue.getValue(), defaultValue);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
