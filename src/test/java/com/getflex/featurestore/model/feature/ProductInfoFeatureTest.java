package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.ProductInfoFeature.ProductInfo;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ProductInfoFeatureTest {

  public static final ExtendedBillerAccountData BILLER_ACCOUNT_DATA =
      new ExtendedBillerAccountData().productId(1L).categoryId(2L);

  @InjectMocks
  ProductInfoFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;


  @Test
  public void getValueByBillerAccountId_success() {
    when(identityService.getBillerAccount(123L)).thenReturn(BILLER_ACCOUNT_DATA);

    ProductInfo productInfo = assertInstanceOf(ProductInfo.class,
        feature.getValue(new EvalParams().billerAccountId(123L)).value());
    assertEquals(1L, productInfo.productId());
    assertEquals(2L, productInfo.categoryId());
  }

  @Test
  public void getValueByOfferId_success() {
    when(offerService.getOffer("abc", null)).thenReturn(new InternalOffer().billerAccountId(123L));
    when(identityService.getBillerAccount(123L)).thenReturn(BILLER_ACCOUNT_DATA);

    ProductInfo productInfo = assertInstanceOf(ProductInfo.class,
        feature.getValue(new EvalParams().offerId("abc")).value());
    assertEquals(1L, productInfo.productId());
    assertEquals(2L, productInfo.categoryId());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(Any.of(EvalParamKey.OFFER_ID, EvalParamKey.BILLER_ACCOUNT_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
