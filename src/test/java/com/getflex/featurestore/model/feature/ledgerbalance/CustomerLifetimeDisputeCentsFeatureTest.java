package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeDisputeCentsFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet rp = new RecordLedgerWallet();
    rp.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    rp.setAmount(1000L);
    rp.setPaymentStatusId(PaymentState.SETTLED.getValue());
    rp.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet nonsettledDispute = new RecordLedgerWallet();
    nonsettledDispute.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    nonsettledDispute.setAmount(3000L);
    nonsettledDispute.setPaymentStatusId(PaymentState.DECLINED.getValue());
    nonsettledDispute.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet dispute1 = new RecordLedgerWallet();
    dispute1.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    dispute1.setAmount(5000L);
    dispute1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dispute1.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet dispute2 = new RecordLedgerWallet();
    dispute2.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    dispute2.setAmount(7000L);
    dispute2.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dispute2.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet dp = new RecordLedgerWallet();
    dp.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    dp.setAmount(1000L);
    dp.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dp.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    List<RecordLedgerWallet> records = List.of(rp, nonsettledDispute, dispute2, dispute1, dp);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDisputeCentsFeature feature =
        new CustomerLifetimeDisputeCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(12000L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeDisputeCentsFeature feature =
        new CustomerLifetimeDisputeCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());


    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDisputeCentsFeature feature = new CustomerLifetimeDisputeCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }
}
