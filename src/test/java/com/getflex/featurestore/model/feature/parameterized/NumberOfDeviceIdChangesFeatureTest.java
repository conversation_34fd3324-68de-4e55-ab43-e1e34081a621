package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.DdbEvent;
import com.getflex.featurestore.dao.model.event.eventmetadata.DeviceMetadata;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.service.EventService;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfDeviceIdChangesFeatureTest {

  @Mock
  EventService eventService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(Long customerId, List<DdbEvent> events, String isoDuration, int expectedResult) {
    lenient().when(eventService.getCustomerEventsByCategory(any(), any())).thenReturn(events);
    lenient().when(eventService.getCustomerEventsByCategoryWithTimestamp(any(), any(), any())).thenReturn(events);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    var feature = new NumberOfDeviceIdChangesFeature(eventService, new LookbackDurationFeatureParams(isoDuration));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    ObjectMapper mapper = new ObjectMapper();

    List<DdbEvent> events = new ArrayList<>();

    DeviceMetadata deviceMetadata = new DeviceMetadata();
    deviceMetadata.setDeviceId("deviceId");
    deviceMetadata.setIpAddress("ipAddress");

    events.add(
        DdbEvent.builder()
            .customerId("123")
            .dtCreated(OffsetDateTime.now().toString())
            .metadata(toJsonSafe(deviceMetadata))
            .build()
    );
    events.add(
        DdbEvent.builder()
            .customerId("123")
            .dtCreated(OffsetDateTime.now().minusHours(26).toString())
            .metadata(toJsonSafe(deviceMetadata))
            .build()
    );

    DeviceMetadata deviceMetadata2 = new DeviceMetadata();
    deviceMetadata.setDeviceId("deviceId2");
    deviceMetadata.setIpAddress("ipAddress2");

    events.add(
        DdbEvent.builder()
            .customerId("123")
            .dtCreated(OffsetDateTime.now().minusHours(26).toString())
            .metadata(toJsonSafe(deviceMetadata))
            .build()
    );

    return Stream.of(
        Arguments.of(123L, events, "", 2),
        Arguments.of(123L, events, "PT24H", 2),
        Arguments.of(123L, events, "P7D", 2)
    );
  }

  private static String toJsonSafe(DeviceMetadata deviceMetadata) {
    try {
      return new ObjectMapper().writeValueAsString(deviceMetadata);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

}
