package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionBillAmountFromLedgerFeatureTest {

  @InjectMocks
  BillTransactionBillAmountFromLedgerFeature feature;

  @Mock
  LedgerService ledgerService;

  @Test
  public void testGetValue() {
    String btxId = "abcdeft";
    when(ledgerService.getLedgersByBillTransactionId(btxId, PaymentState.BILL_PAID,
        MoneyMovementType.PAY_BILLER)).thenReturn(
        List.of(new RecordLedger().amount(10000L), new RecordLedger().amount(20000L))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionBillAmountFromLedgerFeature");
    Assertions.assertEquals(featureValue.getValue(), 10000);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoValue() {
    String btxId = "abcdeft";
    when(ledgerService.getLedgersByBillTransactionId(btxId, PaymentState.BILL_PAID,
        MoneyMovementType.PAY_BILLER)).thenReturn(
        List.of()
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionBillAmountFromLedgerFeature");
    Assertions.assertEquals(featureValue.getValue(), 0);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
