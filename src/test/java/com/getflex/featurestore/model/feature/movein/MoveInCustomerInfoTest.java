package com.getflex.featurestore.model.feature.movein;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.offerv2.model.OverallState;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MoveInCustomerInfoTest {

  private ObjectMapper objectMapper;
  
  @BeforeEach
  void setUp() {
    objectMapper = new ObjectMapper();
  }
  
  @Test
  void testNoArgsConstructor() {
    MoveInCustomerInfo info = new MoveInCustomerInfo();
    assertNotNull(info);
    assertFalse(info.isMoveIn());
    assertNull(info.getMoveInType());
    assertNull(info.getMoveInOffers());
  }
  
  @Test
  void testAllArgsConstructor() {
    List<MoveInOfferInfo> offers = Arrays.asList(
        MoveInOfferInfo.builder()
            .offerId("offer1")
            .type(MoveInType.LOAN)
            .build()
    );
    
    MoveInCustomerInfo info = new MoveInCustomerInfo(true, MoveInType.LOAN, offers);
    
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.LOAN, info.getMoveInType());
    assertEquals(offers, info.getMoveInOffers());
    assertEquals(1, info.getMoveInOffers().size());
  }
  
  @Test
  void testBuilder() {
    List<MoveInOfferInfo> offers = Arrays.asList(
        MoveInOfferInfo.builder()
            .offerId("offer1")
            .type(MoveInType.PAY_IN_FULL)
            .build(),
        MoveInOfferInfo.builder()
            .offerId("offer2")
            .type(MoveInType.PAY_IN_FULL)
            .build()
    );
    
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.PAY_IN_FULL)
        .moveInOffers(offers)
        .build();
    
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.PAY_IN_FULL, info.getMoveInType());
    assertEquals(2, info.getMoveInOffers().size());
    assertEquals("offer1", info.getMoveInOffers().get(0).getOfferId());
    assertEquals("offer2", info.getMoveInOffers().get(1).getOfferId());
  }
  
  @Test
  void testBuilderWithEmptyList() {
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(false)
        .moveInOffers(Collections.emptyList())
        .build();
    
    assertFalse(info.isMoveIn());
    assertNull(info.getMoveInType());
    assertNotNull(info.getMoveInOffers());
    assertTrue(info.getMoveInOffers().isEmpty());
  }
  
  @Test
  void testGettersAndSetters() {
    MoveInCustomerInfo info = new MoveInCustomerInfo();
    
    info.setMoveIn(true);
    assertTrue(info.isMoveIn());
    
    info.setMoveInType(MoveInType.LOAN);
    assertEquals(MoveInType.LOAN, info.getMoveInType());
    
    List<MoveInOfferInfo> offers = Arrays.asList(
        MoveInOfferInfo.builder().offerId("test").build()
    );
    info.setMoveInOffers(offers);
    assertEquals(offers, info.getMoveInOffers());
    
    info.setMoveIn(false);
    assertFalse(info.isMoveIn());
  }
  
  @Test
  void testEqualsAndHashCode() {
    List<MoveInOfferInfo> offers1 = Arrays.asList(
        MoveInOfferInfo.builder().offerId("offer1").build()
    );
    List<MoveInOfferInfo> offers2 = Arrays.asList(
        MoveInOfferInfo.builder().offerId("offer1").build()
    );
    
    MoveInCustomerInfo info1 = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.LOAN)
        .moveInOffers(offers1)
        .build();
    
    MoveInCustomerInfo info2 = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.LOAN)
        .moveInOffers(offers2)
        .build();
    
    MoveInCustomerInfo info3 = MoveInCustomerInfo.builder()
        .isMoveIn(false)
        .moveInType(MoveInType.LOAN)
        .moveInOffers(offers1)
        .build();
    
    assertEquals(info1, info2);
    assertEquals(info1.hashCode(), info2.hashCode());
    assertNotEquals(info1, info3);
    assertNotEquals(info1.hashCode(), info3.hashCode());
  }
  
  @Test
  void testEqualsWithNullFields() {
    MoveInCustomerInfo info1 = new MoveInCustomerInfo();
    MoveInCustomerInfo info2 = new MoveInCustomerInfo();
    
    assertEquals(info1, info2);
    assertEquals(info1.hashCode(), info2.hashCode());
  }
  
  @Test
  void testEqualsWithDifferentTypes() {
    MoveInCustomerInfo info = new MoveInCustomerInfo();
    assertNotEquals(info, null);
    assertNotEquals(info, "string");
    assertNotEquals(info, 123);
  }
  
  @Test
  void testToString() {
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.PAY_IN_FULL)
        .moveInOffers(Collections.emptyList())
        .build();
    
    String toString = info.toString();
    assertNotNull(toString);
    assertTrue(toString.contains("isMoveIn=true"));
    assertTrue(toString.contains("moveInType=PAY_IN_FULL"));
    assertTrue(toString.contains("moveInOffers=[]"));
  }
  
  @Test
  void testJsonSerialization() throws Exception {
    List<MoveInOfferInfo> offers = Arrays.asList(
        MoveInOfferInfo.builder()
            .offerId("json-offer")
            .type(MoveInType.LOAN)
            .offerState(OverallState.ACTIVE)
            .build()
    );
    
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.LOAN)
        .moveInOffers(offers)
        .build();
    
    String json = objectMapper.writeValueAsString(info);
    assertNotNull(json);
    assertTrue(json.contains("\"is_move_in\":true"));
    assertTrue(json.contains("\"move_in_type\":\"LOAN\""));
    assertTrue(json.contains("\"move_in_offers\":["));
    assertTrue(json.contains("\"offer_id\":\"json-offer\""));
  }
  
  @Test
  void testJsonDeserialization() throws Exception {
    String json = "{\"is_move_in\":true,\"move_in_type\":\"PAY_IN_FULL\","
        + "\"move_in_offers\":[{\"offer_id\":\"deser-offer\",\"type\":\"PAY_IN_FULL\"}]}";
    
    MoveInCustomerInfo info = objectMapper.readValue(json, MoveInCustomerInfo.class);
    
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.PAY_IN_FULL, info.getMoveInType());
    assertNotNull(info.getMoveInOffers());
    assertEquals(1, info.getMoveInOffers().size());
    assertEquals("deser-offer", info.getMoveInOffers().get(0).getOfferId());
  }
  
  @Test
  void testJsonSerializationWithNulls() throws Exception {
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(false)
        .build();
    
    String json = objectMapper.writeValueAsString(info);
    assertNotNull(json);
    assertTrue(json.contains("\"is_move_in\":false"));
    assertTrue(json.contains("\"move_in_type\":null"));
    assertTrue(json.contains("\"move_in_offers\":null"));
  }
  
  @Test
  void testJsonSerializationWithEmptyList() throws Exception {
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(false)
        .moveInOffers(Collections.emptyList())
        .build();
    
    String json = objectMapper.writeValueAsString(info);
    assertNotNull(json);
    assertTrue(json.contains("\"is_move_in\":false"));
    assertTrue(json.contains("\"move_in_offers\":[]"));
  }
  
  @Test
  void testMultipleOffersScenario() {
    List<MoveInOfferInfo> offers = Arrays.asList(
        MoveInOfferInfo.builder()
            .offerId("offer1")
            .type(MoveInType.LOAN)
            .offerState(OverallState.ACTIVE)
            .loanId(100L)
            .build(),
        MoveInOfferInfo.builder()
            .offerId("offer2")
            .type(MoveInType.PAY_IN_FULL)
            .offerState(OverallState.SUSPENDED)
            .build(),
        MoveInOfferInfo.builder()
            .offerId("offer3")
            .type(MoveInType.LOAN)
            .offerState(OverallState.CANCELED)
            .loanId(200L)
            .build()
    );
    
    MoveInCustomerInfo info = MoveInCustomerInfo.builder()
        .isMoveIn(true)
        .moveInType(MoveInType.LOAN)
        .moveInOffers(offers)
        .build();
    
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.LOAN, info.getMoveInType());
    assertEquals(3, info.getMoveInOffers().size());
    
    assertEquals("offer1", info.getMoveInOffers().get(0).getOfferId());
    assertEquals(MoveInType.LOAN, info.getMoveInOffers().get(0).getType());
    assertEquals(OverallState.ACTIVE, info.getMoveInOffers().get(0).getOfferState());
    
    assertEquals("offer2", info.getMoveInOffers().get(1).getOfferId());
    assertEquals(MoveInType.PAY_IN_FULL, info.getMoveInOffers().get(1).getType());
    assertEquals(OverallState.SUSPENDED, info.getMoveInOffers().get(1).getOfferState());
    
    assertEquals("offer3", info.getMoveInOffers().get(2).getOfferId());
    assertEquals(MoveInType.LOAN, info.getMoveInOffers().get(2).getType());
    assertEquals(OverallState.CANCELED, info.getMoveInOffers().get(2).getOfferState());
  }
}