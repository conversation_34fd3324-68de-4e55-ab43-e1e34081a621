
package com.getflex.featurestore.model.feature.parameterized;

import static com.getflex.featurestore.helper.TestCreationUtils.fundsInRecord;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfLowAmountFundsInFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  LedgerService ledgerService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<RecordLedger> recordLedgers,
      Integer expectedAmount
  ) {
    var numberOfHighAmountFundsIn = new NumberOfLowAmountFundsInFeature(ledgerService,
        new LookbackDurationFeatureParams("P7D"));
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureValue featureValue = numberOfHighAmountFundsIn.fetchFeatureValue(evalParams);
    assertEquals(expectedAmount, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    var validFundsIn = fundsInRecord(
        builder -> builder.paymentStatusId(PaymentState.SETTLED.getValue())
            .amount(99_99L)
    );
    var invalidFundsIn = fundsInRecord(
        builder -> builder.paymentStatusId(PaymentState.SETTLED.getValue())
            .amount(100_00L)
    );
    return Stream.of(
        Arguments.of(null, 0),
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(invalidFundsIn), 0),
        Arguments.of(List.of(validFundsIn), 1),
        Arguments.of(List.of(validFundsIn, invalidFundsIn), 1)
    );
  }
}
