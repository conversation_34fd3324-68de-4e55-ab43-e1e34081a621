package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class HasStripeConnectAccountFeatureTest {

  @InjectMocks
  HasStripeConnectAccountFeature hasStripeConnectAccountFeature;

  @Mock
  IdentityService identityService;

  @Mock
  WalletService walletService;

  private static final Long CUSTOMER_ID = 1L;
  private static final String CUSTOMER_PUBLIC_ID = "2";

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValue(boolean hasStripeConnectAccount) {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    GetCustomerResponse getCustomerResponse = new GetCustomerResponse();
    getCustomerResponse.setCustomerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(getCustomerResponse);
    when(walletService.hasStripeConnectAccount(CUSTOMER_PUBLIC_ID)).thenReturn(hasStripeConnectAccount);
    FeatureValue featureValue = hasStripeConnectAccountFeature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getValue(), hasStripeConnectAccount);
  }

  @Test
  void testGetValueWithIdentityServiceException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    when(identityService.getCustomer(CUSTOMER_ID))
        .thenThrow(new InternalDependencyFailureException("Could not fetch customer"));
    FeatureValue featureValue = hasStripeConnectAccountFeature.fetchFeatureValue(evalParams);
    assertNull(featureValue.getValue());
  }

  @Test
  void testGetValueWithWalletServiceException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    GetCustomerResponse getCustomerResponse = new GetCustomerResponse();
    getCustomerResponse.setCustomerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(getCustomerResponse);
    when(walletService.hasStripeConnectAccount(CUSTOMER_PUBLIC_ID)).thenThrow(
        new InternalDependencyFailureException("No stripe connect account found"));
    FeatureValue featureValue = hasStripeConnectAccountFeature.fetchFeatureValue(evalParams);
    assertNull(featureValue.getValue());
  }
}
