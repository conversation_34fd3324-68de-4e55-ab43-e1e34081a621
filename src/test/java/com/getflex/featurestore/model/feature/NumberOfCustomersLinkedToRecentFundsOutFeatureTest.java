package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfCustomersLinkedToRecentFundsOutFeatureTest {

  @InjectMocks
  NumberOfCustomersLinkedToRecentFundsOutFeature numberOfCustomersLinkedToRecentFundsOutFeature;

  @Mock
  WalletService walletService;

  @Mock
  IdentityService identityService;

  @Test
  public void testGetValue() {
    Long customerId = 1L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse()
            .customerId(customerId)
            .customerPublicId(customerPublicId)
    );
    when(walletService.getDefaultCard(customerPublicId)).thenReturn(
        new GetDefaultCardResponse().card(
            new Card().fingerprint("fingerprint")
        )
    );
    when(walletService.getCardsByFingerprint("fingerprint"))
        .thenReturn(
            List.of(
                new Card().stripeCustomerId("stripeCustomerId1"),
                new Card().stripeCustomerId("stripeCustomerId2")
            )
        );
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = numberOfCustomersLinkedToRecentFundsOutFeature.fetchFeatureValue(evalParams);
    assertEquals(2, featureValue.getValue());
  }
}
