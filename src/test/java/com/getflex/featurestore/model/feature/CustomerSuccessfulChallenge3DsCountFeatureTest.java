package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptEvent;
import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptEventWith3ds;
import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptMetadata;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.helper.TestCreationUtils;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.Model3dsAuthRecord;
import com.getflex.wallet.model.Card;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerSuccessfulChallenge3DsCountFeatureTest {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
  private static final Long CUSTOMER_ID = 123L;
  private static final String CARD_FINGERPRINT = "fingerprint";
  private static final String CUSTOMER_PUBLIC_ID = "customerPublicId";

  @InjectMocks
  CustomerSuccessfulChallenge3DsCountFeature feature;

  @Mock
  EventRepository eventRepository;

  @Mock
  IdentityService identityService;

  @Mock
  PaymentService paymentService;

  @Mock
  WalletService walletService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<Event> events,
      List<Model3dsAuthRecord> authRecords,
      Integer expectedValue
  ) {
    when(eventRepository.findAllByNameAndCustomerIdAndEntityIdOrderByDtArrivedDesc(
            EventName.STRIPE_SETUP_ATTEMPT, CUSTOMER_ID.toString(), CARD_FINGERPRINT
        )
    ).thenReturn(events);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(
        new GetCustomerResponse().customerPublicId(CUSTOMER_PUBLIC_ID)
    );
    when(walletService.getCardsByFingerprint(CARD_FINGERPRINT)).thenReturn(
        List.of(new Card().fingerprint(CARD_FINGERPRINT))
    );
    when(paymentService.get3dsAuthRecords(CUSTOMER_PUBLIC_ID)).thenReturn(authRecords);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    evalParams.setCardFingerprint(CARD_FINGERPRINT);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals("CustomerSuccessfulChallenge3DsCountFeature", featureValue.getName());
    assertEquals(expectedValue, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() throws JsonProcessingException {
    Map<String, Object> flowPreference = new HashMap<>();
    flowPreference.put("type", "challenge");
    Map<String, Object> successMetadata1 = new HashMap<>();
    successMetadata1.put("flowPreference", flowPreference);
    Map<String, Object> successMetadata2 = new HashMap<>();
    successMetadata2.put("flowPreference", "challenge");
    var successAuthRecord = TestCreationUtils.model3dsAuthRecord(
        builder -> builder
            .outcome("AUTHENTICATED")
            .status("SUCCEEDED")
            .metadata(successMetadata1)
    );
    var successAuthRecord2 = TestCreationUtils.model3dsAuthRecord(
        builder -> builder
            .outcome("attempt_acknowledged")
            .status("SUCCEEDED")
            .metadata(successMetadata2)
    );
    var failedAuthRecord = TestCreationUtils.model3dsAuthRecord(
        builder -> builder
            .outcome("rejected")
            .status("SUCCEEDED")
    );
    var duplicateSucceededEvent = createStripeSetupAttemptEvent(
        createStripeSetupAttemptMetadata(
            metadataBuilder -> metadataBuilder.id("123"),
            threeDomainSecureBuilder -> threeDomainSecureBuilder
                .result("succeeded")
                .authenticationFlow("challenge")
        )
    );
    return Stream.of(
        Arguments.of(List.of(), List.of(), 0),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result(null)
                        .authenticationFlow(null)
                )
            ),
            List.of(),
            0
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(builder -> builder.result("failed"))
            ),
            List.of(failedAuthRecord),
            0
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result("succeeded")
                        .authenticationFlow("challenge")
                ),
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result("succeeded")
                        .authenticationFlow("frictionless")
                )
            ),
            List.of(successAuthRecord, successAuthRecord2),
            3
        ),
        Arguments.of(
            List.of(
                duplicateSucceededEvent,
                duplicateSucceededEvent
            ),
            List.of(successAuthRecord),
            2
        )
    );
  }
}
