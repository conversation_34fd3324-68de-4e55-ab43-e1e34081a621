package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StripeZipCodeMatchV2FeatureTest {

  @InjectMocks
  StripeZipCodeMatchV2Feature feature;

  @Mock
  IdentityService identityService;

  private static final long customerId = 123L;
  private static final EvalParams evalParams = new EvalParams();

  @BeforeAll
  public static void setup() {
    evalParams.setCustomerId(customerId);
  }

  @Test
  void getValue_match() {
    evalParams.setStripeZipCode("12345");
    when(identityService.getCustomer(123L)).thenReturn(
        new GetCustomerResponse().zip("12345"));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("StripeZipCodeMatchV2Feature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.BOOLEAN, featureValue.getType());
    Assertions.assertTrue((boolean) featureValue.getValue());
  }

  @Test
  void getValue_mismatch() {
    evalParams.setStripeZipCode("54321");
    when(identityService.getCustomer(123L)).thenReturn(
        new GetCustomerResponse().zip("12345"));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("StripeZipCodeMatchV2Feature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.BOOLEAN, featureValue.getType());
    Assertions.assertFalse((boolean) featureValue.getValue());
  }

  @Test
  void getValue_matchWithHyphen() {
    evalParams.setStripeZipCode("12345-000");
    when(identityService.getCustomer(123L)).thenReturn(
        new GetCustomerResponse().zip("12345"));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("StripeZipCodeMatchV2Feature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.BOOLEAN, featureValue.getType());
    Assertions.assertTrue((boolean) featureValue.getValue());
  }
}
