package com.getflex.featurestore.model.feature.rentsplit;

import static com.getflex.featurestore.constant.ProductConstants.RENTAL_PRODUCT_ID;
import static com.getflex.featurestore.constant.ProductConstants.RENT_SPLIT_CATEGORY_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class RentSplitCustomerFeatureTest {

  @InjectMocks
  RentSplitCustomerFeature feature;

  @Mock
  OfferService offerService;

  private static final Long CUSTOMER_ID = 789L;
  private static final String FEATURE_NAME = "RentSplitCustomerFeature";
  private static final FeatureTypeEnum FEATURE_TYPE = FeatureTypeEnum.OBJECT;

  @ParameterizedTest
  @MethodSource("provideOfferTestCases")
  void testGetValueWithDifferentOfferScenarios(
      List<InternalOffer> offers,
      boolean expectedIsRentSplit,
      boolean expectedHasActiveOrSuspended,
      int expectedOfferCount,
      String scenario) {
    when(offerService.searchOfferByProduct(
        eq(CUSTOMER_ID), eq(Set.of(RENTAL_PRODUCT_ID)), eq(Set.of(RENT_SPLIT_CATEGORY_ID)), eq(false)))
        .thenReturn(offers);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(FEATURE_NAME, featureValue.getName());
    assertEquals(FEATURE_TYPE, featureValue.getType());

    RentSplitCustomerInfo result = (RentSplitCustomerInfo) featureValue.getValue();
    assertEquals(
        expectedIsRentSplit, result.isRentSplit(), "Failed isRentSplit for scenario: " + scenario);
    assertEquals(
        expectedHasActiveOrSuspended,
        result.isHasActiveOrSuspendedOffer(),
        "Failed hasActiveOrSuspendedOffer for scenario: " + scenario);
    assertEquals(
        expectedOfferCount,
        result.getRentSplitOffers().size(),
        "Failed offer count for scenario: " + scenario);
  }

  private static Stream<Arguments> provideOfferTestCases() {
    InternalOffer activeOffer = createOffer(1L, 1L, OverallState.ACTIVE);
    InternalOffer suspendedOffer = createOffer(1L, 1L, OverallState.SUSPENDED);
    InternalOffer pendingOffer = createOffer(1L, 1L, OverallState.PENDING);
    InternalOffer canceledOffer = createOffer(1L, 1L, OverallState.CANCELED);

    return Stream.of(
        // Empty list - no offers
        Arguments.of(Collections.emptyList(), false, false, 0, "No offers exist"),

        // Single offer scenarios
        Arguments.of(List.of(activeOffer), true, true, 1, "Single active offer"),
        Arguments.of(List.of(suspendedOffer), true, true, 1, "Single suspended offer"),
        Arguments.of(List.of(pendingOffer), false, false, 0, "Single pending offer"),
        Arguments.of(List.of(canceledOffer), true, false, 1, "Single canceled offer"),

        // Multiple offers scenarios
        Arguments.of(
            List.of(activeOffer, suspendedOffer),
            true,
            true,
            2,
            "Multiple offers (active + suspended)"),
        Arguments.of(
            List.of(pendingOffer, canceledOffer), true, false, 1, "Multiple offers with canceled"),
        Arguments.of(
            List.of(activeOffer, canceledOffer), true, true, 2, "Mix with at least one active"),
        Arguments.of(
            List.of(suspendedOffer, pendingOffer),
            true,
            true,
            1,
            "Mix with at least one suspended"));
  }

  private static InternalOffer createOffer(Long productId, Long categoryId, OverallState state) {
    InternalOffer offer = new InternalOffer();
    offer.setOfferId("offer-" + System.nanoTime());
    offer.setOfferVersion(1L);
    offer.setProductId(productId);
    offer.setProductCategoryId(categoryId);
    offer.setOverallState(state);
    return offer;
  }

  private static Stream<Arguments> provideEdgeCaseScenarios() {
    List<InternalOffer> listWithNullOffer = new java.util.ArrayList<>();
    listWithNullOffer.add(null);

    return Stream.of(
        Arguments.of(listWithNullOffer, false, "List containing null offer"),
        Arguments.of(
            null, false, "Null offer list (should not happen but testing defensive programming)"));
  }

  @Test
  void testSearchOfferByProductCalledWithCorrectParameters() {
    when(offerService.searchOfferByProduct(anyLong(), anySet(), anySet(), anyBoolean()))
        .thenReturn(Collections.emptyList());

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    feature.fetchFeatureValue(evalParams);

    // Verify the searchOfferByProduct method is called with correct parameters
    org.mockito.Mockito.verify(offerService)
        .searchOfferByProduct(
            eq(CUSTOMER_ID),
            eq(Set.of(RENTAL_PRODUCT_ID)), // RENTAL_PRODUCT_ID
            eq(Set.of(RENT_SPLIT_CATEGORY_ID)), // RENT_SPLIT_CATEGORY_ID
            eq(false) // allVersions
        );
  }

  @Test
  void testGetValueWhenOfferServiceThrowsException() {
    when(offerService.searchOfferByProduct(
        eq(CUSTOMER_ID), eq(Set.of(RENTAL_PRODUCT_ID)), eq(Set.of(RENT_SPLIT_CATEGORY_ID)), eq(false)))
        .thenThrow(
            new InternalDependencyFailureException("Could not fetch offers from customerId 789"));

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(FEATURE_NAME, featureValue.getName());
    assertEquals(null, featureValue.getValue());
    assertEquals(FEATURE_TYPE, featureValue.getType());
    assertEquals("Could not fetch offers from customerId 789", featureValue.getErrorMessage());
  }

  @Test
  void testGetValueDirectlyThrowsInternalDependencyFailureException() {
    when(offerService.searchOfferByProduct(
        eq(CUSTOMER_ID), eq(Set.of(RENTAL_PRODUCT_ID)), eq(Set.of(RENT_SPLIT_CATEGORY_ID)), eq(false)))
        .thenThrow(
            new InternalDependencyFailureException("Could not fetch offers from customerId 789"));

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    Exception exception =
        assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));

    assertEquals("Could not fetch offers from customerId 789", exception.getMessage());
  }

  @Test
  void testOfferInfoMapping() {
    InternalOffer activeOffer = createOffer(1L, 1L, OverallState.ACTIVE);
    activeOffer.setOfferId("test-offer-123");
    activeOffer.setOfferVersion(2L);

    when(offerService.searchOfferByProduct(
        eq(CUSTOMER_ID), eq(Set.of(RENTAL_PRODUCT_ID)), eq(Set.of(RENT_SPLIT_CATEGORY_ID)), eq(false)))
        .thenReturn(List.of(activeOffer));

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    FeatureOutput output = (FeatureOutput) feature.getValue(evalParams);
    RentSplitCustomerInfo result = (RentSplitCustomerInfo) output.value();

    assertEquals(1, result.getRentSplitOffers().size());
    RentSplitOfferInfo offerInfo = result.getRentSplitOffers().get(0);
    assertEquals("test-offer-123", offerInfo.getOfferId());
    assertEquals(2L, offerInfo.getOfferVersion());
    assertEquals(OverallState.ACTIVE, offerInfo.getOfferState());
  }
}
