package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.SettlementService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.settlement.model.DdaDecision;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionHasDuplicatedDdaTransactionFeatureTest {

  @InjectMocks
  BillTransactionHasDuplicatedDdaTransactionFeature feature;

  @Mock
  SettlementService settlementService;

  @Test
  public void testHasDuplicatedTransaction() {
    String btxId = "abcdeft";
    when(settlementService.getDdaDecisionsByBillTransactionId(btxId)).thenReturn(
        List.of(new DdaDecision().amount(5000L).reason("transaction").transactionTypeId(1L),
            new DdaDecision().amount(200L).reason("payment").transactionTypeId(2L))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionHasDuplicatedDdaTransactionFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoTransaction() {
    String btxId = "abcdeft";
    when(settlementService.getDdaDecisionsByBillTransactionId(btxId)).thenReturn(
        List.of()
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionHasDuplicatedDdaTransactionFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoValidTransaction() {
    String btxId = "abcdeft";
    when(settlementService.getDdaDecisionsByBillTransactionId(btxId)).thenReturn(
        List.of(
            new DdaDecision().amount(5000L).reason("Micro_Deposit").transactionTypeId(2L),
            new DdaDecision().amount(200L).reason("payment").transactionTypeId(1L))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionHasDuplicatedDdaTransactionFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
