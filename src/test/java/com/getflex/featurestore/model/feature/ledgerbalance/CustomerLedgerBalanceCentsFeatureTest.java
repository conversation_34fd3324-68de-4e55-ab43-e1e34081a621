package com.getflex.featurestore.model.feature.ledgerbalance;

import static com.getflex.featurestore.utils.ObservabilityConstants.CUSTOMER_ID;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.CustomerLedgerBalanceCentsFeature;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerLedgerBalanceCentsFeatureTest {

  @InjectMocks
  CustomerLedgerBalanceCentsFeature customerLedgerBalanceCentsFeature;
  @Mock
  IdentityService identityService;
  @Mock
  LedgerService ledgerService;
  @Mock
  AllowDenyListRepo allowDenyListRepo;

  public static final String FEATURE_NAME = "CustomerLedgerBalanceCentsFeature";
  public static final FeatureTypeEnum FEATURE_TYPE = FeatureTypeEnum.INT;
  public static final String ALLOW_DENY_USE_CASE = "2023_jan_dq_override";
  public static final String UNBALANCED_AMOUNT_CENTS = "unbalanced_amount_cents";
  public static final String ALLOW_LISTED = "allowlisted";
  public ObjectMapper objectMapper = new ObjectMapper();

  @ParameterizedTest
  @ValueSource(longs = {-3000L, 3000L})
  void testGetValue(Long outstandingBalance) throws JsonProcessingException {
    Long customerId = 1L;
    String customerPublicId = "customer_public_id";

    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerId(customerId).customerPublicId(customerPublicId));
    when(ledgerService.getCustomerOutstandingBalance(customerPublicId)).thenReturn(outstandingBalance);
    if (outstandingBalance > 0) {
      when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(ALLOW_DENY_USE_CASE, String.valueOf(customerId),
          true)).thenReturn(null);
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = customerLedgerBalanceCentsFeature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getName(), FEATURE_NAME);
    assertEquals(featureValue.getType(), FEATURE_TYPE);
    assertEquals(featureValue.getValue(), -outstandingBalance);
    if (outstandingBalance > 0) {
      Map<String, Object> metadata = this.parseMetadata(featureValue.getMetadata());
      assertEquals(metadata.get(CUSTOMER_ID), customerId.intValue());
      assertEquals(metadata.get(UNBALANCED_AMOUNT_CENTS), -outstandingBalance.intValue());
    } else {
      assertNull(featureValue.getMetadata());
    }
  }

  @Test
  void testGetValueAllowList() throws JsonProcessingException {
    Long customerId = 1L;
    String customerPublicId = "customer_public_id";
    Long outstandingBalance = 3000L;

    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerId(customerId).customerPublicId(customerPublicId));
    when(ledgerService.getCustomerOutstandingBalance(customerPublicId)).thenReturn(outstandingBalance);
    when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(ALLOW_DENY_USE_CASE, String.valueOf(customerId),
        true)).thenReturn(new AllowDenyList());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = customerLedgerBalanceCentsFeature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getName(), FEATURE_NAME);
    assertEquals(featureValue.getType(), FEATURE_TYPE);
    assertEquals(featureValue.getValue(), 0L);
    Map<String, Object> metadataMap = this.parseMetadata(featureValue.getMetadata());
    assertEquals(metadataMap.get(CUSTOMER_ID), customerId.intValue());
    assertEquals(metadataMap.get(UNBALANCED_AMOUNT_CENTS), -outstandingBalance.intValue());
    assertEquals(metadataMap.get(ALLOW_LISTED), true);
  }

  private Map<String, Object> parseMetadata(String metadataString) throws JsonProcessingException {
    return objectMapper.readValue(metadataString, new TypeReference<Map<String, Object>>() {
    });
  }
}
