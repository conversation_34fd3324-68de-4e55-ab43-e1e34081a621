package com.getflex.featurestore.model.feature;


import static com.getflex.featurestore.dao.model.event.EventCategory.SELF_REPORTED_INCOME;
import static com.getflex.featurestore.dao.model.event.EventName.SELF_REPORTED_INCOME_VERIFICATION;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EntityType;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import java.time.OffsetDateTime;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SelfReportedAnnualGrossIncomeCentFeatureTest {

  @Mock
  EventRepository eventRepository;

  @InjectMocks
  SelfReportedAnnualGrossIncomeCentFeature feature;

  @Test
  public void getValue_success() throws JsonProcessingException {
    IncomeMetadata metadata = new IncomeMetadata("50000");

    ObjectMapper mapper = new ObjectMapper();

    Event event = new Event(1, SELF_REPORTED_INCOME_VERIFICATION, SELF_REPORTED_INCOME,
        "123", EntityType.INCOME_VERIFICATION_ID, "1", mapper.writeValueAsString(metadata),
        OffsetDateTime.now(), OffsetDateTime.now());
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", SELF_REPORTED_INCOME_VERIFICATION))
        .thenReturn(Optional.of(event));

    EvalParams evalParams = new EvalParams().verificationId("123");
    FeatureValue featureOutput = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(50000, featureOutput.getValue());
  }

  @Test
  public void getValue_failure() {
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", SELF_REPORTED_INCOME_VERIFICATION))
        .thenReturn(Optional.empty());

    EvalParams evalParams = new EvalParams().verificationId("123");
    Assertions.assertThrows(FeatureNotFoundException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getValue_parsingFailure() {
    Event event = new Event(1, SELF_REPORTED_INCOME_VERIFICATION, SELF_REPORTED_INCOME,
        "123", EntityType.INCOME_VERIFICATION_ID, "1", "fake fake fake",
        OffsetDateTime.now(), OffsetDateTime.now());
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", SELF_REPORTED_INCOME_VERIFICATION))
        .thenReturn(Optional.of(event));

    EvalParams evalParams = new EvalParams().verificationId("123");

    Assertions.assertThrows(EventMetadataParsingException.class, () -> feature.getValue(evalParams));
  }

}
