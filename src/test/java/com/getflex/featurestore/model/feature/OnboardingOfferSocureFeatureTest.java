package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.decisionengine.async.model.CheckpointDecisionLog;
import com.getflex.decisionengine.async.model.GetCheckpointDecisionLogsResponse;
import com.getflex.featurestore.integration.flex.DecisionEngineService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext.BillerTypeEnum;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContextFinancialPartner;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContextOnboardingIdentityDocumentVerification;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContextOnboardingIncomeVerification;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.text.StringEscapeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class OnboardingOfferSocureFeatureTest {
  @InjectMocks
  OnboardingOfferSocureFeature feature;

  @Mock
  OfferService offerService;

  @Mock
  DecisionEngineService decisionEngineService;

  private static InternalOffer offer;
  private static InternalOfferAllOfEvaluationContext evaluationContext;

  @BeforeAll
  public static void setup() {
    evaluationContext = new InternalOfferAllOfEvaluationContext()
        .currentEvaluationStep("test-step")
        .evaluationSteps(new ArrayList<>())
        .onboardingIncomeVerification(
            new InternalOfferAllOfEvaluationContextOnboardingIncomeVerification()
                .ivStepUpCheckpointLogId("iv-test-checkpoint-log-id")
                .verificationId("iv-test-verification-id")
                .underwritingCheckpointLogId("underwriting-test-checkpoint-log-id"))
        .onboardingIdentityDocumentVerification(
            new InternalOfferAllOfEvaluationContextOnboardingIdentityDocumentVerification()
                .docvCheckpointLogId("docv-test-checkpoint-log-id")
                .verificationId("docv-test-verification-id")
                .resubmit(false))
        .isVclControlGroup(false)
        .financialPartner(
            new InternalOfferAllOfEvaluationContextFinancialPartner()
                .id(123L)
                .name("Test Partner"))
        .isRootEvaluation(true)
        .billerType(BillerTypeEnum.INNETWORK)
        .sentilinkAbuseScore(50)
        .sentilinkIdTheftScore(60)
        .sentilinkFirstPartySyntheticScore(70)
        .sentilinkThirdPartySyntheticScore(80)
        .socureMobileNumberScore(0.95F)
        .creditScoreSegment("A")
        .slcPricingExperimentGroup("test-group")
        .socureSigmaScore(0.85D)
        .socureAddressRiskScore(0.685F)
        .socureEmailRiskScore(0.628F)
        .socureNameAddressCorrelationScore(0.90F)
        .socureNameEmailCorrelationScore(0.80F)
        .socureNamePhoneCorrelationScore(0.70F)
        .socurePhoneRiskScore(0.446F)
        .socureSyntheticScore(0.271F)
        .socureFraudReasonCodes(new HashSet<>(Arrays.asList("I626", "I602", "I610")))
        .flexScoreDecile(8);

    offer = new InternalOffer();
    offer.setBillerAccountId(1L);
    offer.setOfferId("test-offer-id-1");
    offer.setCustomerId(123L);
    offer.setOfferState(OfferStateEnum.ACCEPTED);
    offer.setOfferVersion(1L);
    offer.setEvaluationContext(evaluationContext);
  }

  private void assertSocureDto(JsonNode actual) {
    assertEquals(0.628, actual.get("emailRiskScore").asDouble(), 1e-6);
    assertEquals(0.446, actual.get("phoneRiskScore").asDouble(), 1e-6);
    assertEquals(0.685, actual.get("addressRiskScore").asDouble(), 1e-6);
    assertEquals(0.271, actual.get("syntheticScore").asDouble(), 1e-6);

    String fr = actual.get("fraudReasonCodes").toString();
    assertTrue(fr.contains("I626"));
    assertTrue(fr.contains("I610"));
    assertTrue(fr.contains("I602"));
  }


  @Test
  public void testGetValueFromDdbGoodFormat() throws IOException {
    Long customerId = 123L;
    String socureJson;
    try (InputStream in = this.getClass()
        .getResourceAsStream("/socure/responses/good_format_socure_feature.json")) {
      assert in != null;
      socureJson = new String(in.readAllBytes(), StandardCharsets.UTF_8);
    }
    ObjectMapper mapper = new ObjectMapper();
    String rawJson = StringEscapeUtils.unescapeJava(socureJson);
    Map<String, Object> socureMap = mapper.readValue(
        rawJson,
        new TypeReference<>() {
        }
    );

    GetCheckpointDecisionLogsResponse response = new GetCheckpointDecisionLogsResponse().checkpointDecisionLogs(
        new ArrayList<>(List.of(
            new CheckpointDecisionLog().id("ddb-id-1").decision("APPROVED").customerId(customerId).featureValues(
                Map.of("SocureFeature", socureMap)
            )
        ))
    );
    when(decisionEngineService.getCheckpointDecisionLogs(customerId, "EvaluateKycCheckpoint", null)).thenReturn(
        CompletableFuture.completedFuture(response)
    );

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("OnboardingOfferSocureFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.OBJECT, featureValue.getType());

    JsonNode actual = (JsonNode) featureValue.getValue();
    assertSocureDto(actual);
  }

  @Test
  public void testGetValueFromDdbBadFormat() throws IOException {
    Long customerId = 123L;
    String socureJson;
    try (InputStream in = this.getClass().getResourceAsStream("/socure/responses/bad_format_socure_feature.json")) {
      assert in != null;
      socureJson = new String(in.readAllBytes(), StandardCharsets.UTF_8);
    }
    GetCheckpointDecisionLogsResponse response = new GetCheckpointDecisionLogsResponse().checkpointDecisionLogs(
        new ArrayList<>(List.of(
            new CheckpointDecisionLog().id("ddb-id-1").decision("APPROVED").customerId(customerId).featureValues(
                Map.of("SocureFeature", socureJson)
            )
        ))
    );
    when(decisionEngineService.getCheckpointDecisionLogs(customerId, "EvaluateKycCheckpoint", null)).thenReturn(
        CompletableFuture.completedFuture(response)
    );

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("OnboardingOfferSocureFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.OBJECT, featureValue.getType());

    JsonNode actual = (JsonNode) featureValue.getValue();
    assertSocureDto(actual);
  }

  @Test
  public void testGetValueFromOffer() {
    Long billerAccountId = 1L;
    when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offer);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setCustomerId(123L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "OnboardingOfferSocureFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.OBJECT);

    JsonNode actual = (JsonNode) featureValue.getValue();
    assertSocureDto(actual);
  }

  @Test
  public void testGetValueFromRootOffer() throws JsonProcessingException {
    String rootOfferId = "root-offer-id";
    ObjectMapper objectMapper = new ObjectMapper();
    // Get a deep copy of the offer
    InternalOffer rootOffer = objectMapper.readValue(objectMapper.writeValueAsString(offer), InternalOffer.class);
    rootOffer.setOfferId(rootOfferId);

    evaluationContext.setIsRootEvaluation(false);
    evaluationContext.setRootOfferId(rootOfferId);
    evaluationContext.setRootOfferVersion(1L);
    offer.setEvaluationContext(evaluationContext);

    Long billerAccountId = 1L;
    when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offer);
    when(offerService.getRootOffer(
        rootOfferId, offer.getEvaluationContext().getRootOfferVersion())).thenReturn(rootOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setCustomerId(123L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "OnboardingOfferSocureFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.OBJECT);

    JsonNode actual = (JsonNode) featureValue.getValue();
    assertSocureDto(actual);
  }
}
