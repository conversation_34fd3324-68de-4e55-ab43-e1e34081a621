package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.utils.TestSupport;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsCardIssuerBlockedFeatureTest {
  @InjectMocks
  IsCardIssuerBlockedFeature isCardIssuerBlockedFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  TestSupport testSupport;

  @ParameterizedTest
  @ValueSource(booleans = { true, false })
  public void testGetValue(boolean featureExists) {
    when(testSupport.isProdEnvironment()).thenReturn(true);
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName(IsCardIssuerBlockedFeature.OFFLINE_FEATURE_NAME)
            .primaryKey("capitalone")
            .featureValue(
                "{\"is_credit_card_blocked\": false, \"is_debit_card_blocked\": true,"
                + " \"card_issuer\": \"Capital One\"}")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("testCardFingerprint");
    evalParams.setCardType("debit");
    evalParams.setCardIssuer("Capital One");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        IsCardIssuerBlockedFeature.OFFLINE_FEATURE_NAME, "capitalone")).thenReturn(feature);

    FeatureValue featureValue = isCardIssuerBlockedFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCardIssuerBlockedFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), Boolean.TRUE);
    } else {
      Assertions.assertEquals(featureValue.getValue(), Boolean.FALSE);
    }
  }

  @Test
  public void testGetValue_DevTestingCreditCard() {
    when(testSupport.isProdEnvironment()).thenReturn(false);
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("8yXB8jhnS2KW92Va");
    evalParams.setCardType("credit");
    evalParams.setCardIssuer("American Express");

    FeatureValue featureValue = isCardIssuerBlockedFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCardIssuerBlockedFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), Boolean.TRUE);
  }

  @Test
  public void testGetValueFromOfflineFeature_jsonParseError() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("testCardFingerprint");
    evalParams.setCardType("debit");
    evalParams.setCardIssuer("Capital One");
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName(IsCardIssuerBlockedFeature.OFFLINE_FEATURE_NAME)
            .primaryKey("capitalone").featureValue("invalidJson").build());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        IsCardIssuerBlockedFeature.OFFLINE_FEATURE_NAME, "capitalone")).thenReturn(feature);

    FeatureValue featureValue = isCardIssuerBlockedFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCardIssuerBlockedFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), null);
  }
}
