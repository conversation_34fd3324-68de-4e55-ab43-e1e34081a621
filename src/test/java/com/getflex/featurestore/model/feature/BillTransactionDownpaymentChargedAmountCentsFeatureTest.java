package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionDownpaymentChargedAmountCentsFeatureTest {

  @InjectMocks
  BillTransactionDownpaymentChargedAmountCentsFeature feature;

  @Mock
  LedgerService ledgerService;

  @Test
  public void testGetValue() {
    String btxId = "abcdeft";
    when(ledgerService.getDownpaymentRecords(btxId, MovementCategory.CHARGE)).thenReturn(
        List.of(new RecordLedger().amount(10000L).paymentStatusId(1L),
            new RecordLedger().amount(20000L).paymentStatusId(2L),
            new RecordLedger().amount(30000L).paymentStatusId(1L))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionDownpaymentChargedAmountCentsFeature");
    Assertions.assertEquals(featureValue.getValue(), 40000);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoValue() {
    String btxId = "abcdeft";
    when(ledgerService.getDownpaymentRecords(btxId, MovementCategory.CHARGE)).thenReturn(
        List.of(new RecordLedger().amount(10000L).paymentStatusId(2L)));
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionDownpaymentChargedAmountCentsFeature");
    Assertions.assertEquals(featureValue.getValue(), 0);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
