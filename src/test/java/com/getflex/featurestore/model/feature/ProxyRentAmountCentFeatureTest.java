package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ProxyRentAmountCentFeatureTest {

  @InjectMocks
  ProxyRentAmountCentFeature proxyRentAmountCentFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 1234L;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void getValue(boolean featureExists) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("proxy_rent_amount_feature")
            .primaryKey("1234").featureValue("1000.06287655")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "proxy_rent_amount_feature", "1234")).thenReturn(feature);
    FeatureValue featureValue = proxyRentAmountCentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "ProxyRentAmountCentFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), 100006);
    } else {
      Assertions.assertEquals(featureValue.getValue(), -1);
    }
  }
}
