package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeRefundCentsFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record1.setPaymentCategoryId(MovementCategory.REFUND.getValue());

    RecordLedgerWallet record2 = new RecordLedgerWallet();
    record2.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    record2.setAmount(1600L);
    record2.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record2.setPaymentCategoryId(MovementCategory.REFUND.getValue());

    RecordLedgerWallet record3 = new RecordLedgerWallet();
    record3.setMoneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue());
    record3.setAmount(800L);
    record3.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record3.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet record4 = new RecordLedgerWallet();
    record4.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    record4.setAmount(500L);
    record4.setPaymentStatusId(PaymentState.SETTLED.getValue());
    record4.setPaymentCategoryId(MovementCategory.REFUND.getValue());

    RecordLedgerWallet nonRefundCategoryRecord = new RecordLedgerWallet();
    nonRefundCategoryRecord.setMoneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue());
    nonRefundCategoryRecord.setAmount(5000L);
    nonRefundCategoryRecord.setPaymentStatusId(PaymentState.SETTLED.getValue());
    nonRefundCategoryRecord.setPaymentCategoryId(MovementCategory.CHARGE.getValue());

    RecordLedgerWallet nonSettledTypeRecord = new RecordLedgerWallet();
    nonSettledTypeRecord.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    nonSettledTypeRecord.setAmount(7000L);
    nonSettledTypeRecord.setPaymentStatusId(PaymentState.DECLINED.getValue());
    nonSettledTypeRecord.setPaymentCategoryId(MovementCategory.REFUND.getValue());

    List<RecordLedgerWallet> records = List.of(record1, record2, record3, record4,
        nonSettledTypeRecord, nonRefundCategoryRecord);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeRefundCentsFeature feature = new CustomerLifetimeRefundCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(3900L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeRefundCentsFeature feature = new CustomerLifetimeRefundCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());


    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeRefundCentsFeature feature = new CustomerLifetimeRefundCentsFeature(ledgerService);

    assertEquals(new FeatureOutput(0L), feature.getValue(new EvalParams().customerId(1L)));
  }
}
