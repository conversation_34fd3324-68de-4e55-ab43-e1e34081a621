package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.helper.TestCreationUtils.fundsInRecord;
import static com.getflex.featurestore.helper.TestCreationUtils.fundsOutRecord;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.featurestore.model.feature.parameterized.FundsOutFundsInSameCardCountFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FundsOutFundsInSameCardCountFeatureTest {

  private static final Long CUSTOMER_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customerPublicId";

  @Mock
  WalletService walletService;

  @Mock
  IdentityService identityService;

  @Mock
  LedgerUtils ledgerUtils;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<Card> cards,
      List<RecordLedger> fundsOutRecords,
      List<RecordLedger> fundsInRecords,
      Integer expectedAmount
  ) {
    when(walletService.getCards(any())).thenReturn(cards);
    when(identityService.getCustomer(any()))
        .thenReturn(new GetCustomerResponse().customerPublicId(CUSTOMER_PUBLIC_ID));
    when(ledgerUtils.getFundsOutRecords(any(), any())).thenReturn(fundsOutRecords);
    when(ledgerUtils.getFundsInRecords(any(), any())).thenReturn(fundsInRecords);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FundsOutFundsInSameCardCountFeature fundsOutFundsInSameCardCountFeature = new FundsOutFundsInSameCardCountFeature(
        walletService,
        identityService,
        ledgerUtils,
        new LookbackDurationFeatureParams("P30D")
    );
    FeatureValue featureValue = fundsOutFundsInSameCardCountFeature.fetchFeatureValue(evalParams);
    assertEquals(expectedAmount, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    Long paymentMethodId1 = 1L;
    Long paymentMethodId2 = 2L;
    Long paymentMethodId3 = 3L;
    Long paymentMethodId4 = 4L;
    OffsetDateTime dtCreated = OffsetDateTime.now();

    Card card1 = new Card().paymentMethodId(paymentMethodId1).fingerprint("fingerprint1");
    Card card2 = new Card().paymentMethodId(paymentMethodId2).fingerprint("fingerprint1");
    Card card3 = new Card().paymentMethodId(paymentMethodId3).fingerprint("fingerprint2");
    Card card4 = new Card().paymentMethodId(paymentMethodId4).fingerprint("fingerprint3");

    RecordLedger fundsOutRecord1 = fundsOutRecord(
        builder -> builder.toPaymentMethodId(paymentMethodId1).dtCreated(dtCreated.minusDays(1))
    );
    RecordLedger fundsOutRecord2 = fundsOutRecord(
        builder -> builder.toPaymentMethodId(paymentMethodId2).dtCreated(dtCreated.minusDays(1))
    );
    RecordLedger fundsOutRecord3 = fundsOutRecord(
        builder -> builder.toPaymentMethodId(paymentMethodId3).dtCreated(dtCreated.minusDays(5))
    );
    RecordLedger fundsOutRecord4 = fundsOutRecord(
        builder -> builder.toPaymentMethodId(paymentMethodId4).dtCreated(dtCreated.minusHours(12))
    );

    RecordLedger fundsInRecord1 = fundsInRecord(
        builder -> builder.fromPaymentMethodId(paymentMethodId1).dtCreated(dtCreated.minusDays(2))
    );
    RecordLedger fundsInRecord2 = fundsInRecord(
        builder -> builder.fromPaymentMethodId(paymentMethodId2).dtCreated(dtCreated.minusDays(3))
    );
    RecordLedger fundsInRecord3 = fundsInRecord(
        builder -> builder.fromPaymentMethodId(paymentMethodId3).dtCreated(dtCreated.minusDays(10))
    );
    RecordLedger fundsInRecord4 = fundsInRecord(
        builder -> builder.fromPaymentMethodId(paymentMethodId4).dtCreated(dtCreated.minusHours(20))
    );

    return Stream.of(
        Arguments.of(List.of(card1), List.of(fundsOutRecord1), List.of(fundsInRecord1), 1),
        Arguments.of(List.of(card1, card2), List.of(fundsOutRecord1, fundsOutRecord2), List.of(fundsInRecord1), 2),
        Arguments.of(List.of(card4), List.of(fundsOutRecord4), List.of(fundsInRecord4), 1),
        Arguments.of(List.of(card1, card3), List.of(fundsOutRecord1), List.of(fundsInRecord1, fundsInRecord3), 1),
        Arguments.of(
            List.of(card1, card3),
            List.of(fundsOutRecord1, fundsOutRecord3),
            List.of(fundsInRecord1, fundsInRecord3),
            2
        ),
        Arguments.of(
            List.of(card1, card2, card3, card4),
            List.of(fundsOutRecord1, fundsOutRecord2, fundsOutRecord3, fundsOutRecord4),
            List.of(fundsInRecord1, fundsInRecord2, fundsInRecord3, fundsInRecord4),
            4
        )
    );
  }
}
