package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerHasTagFeatureTest {

  @Mock
  TaggingService taggingService;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValue(boolean hasTag) {
    Long customerId = 1L;
    EvalParams evalParams = new EvalParams();
    CustomerHasTagFeature feature = new CustomerHasTagFeature(taggingService, "tagName");
    evalParams.setCustomerId(customerId);
    if (hasTag) {
      when(taggingService.customerHasTag(customerId, "tagName")).thenReturn(true);
    } else {
      when(taggingService.customerHasTag(customerId, "tagName")).thenReturn(false);
    }
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getValue(), hasTag);
  }

  @Test
  void testGetValueException() {
    Long customerId = 1L;
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    CustomerHasTagFeature feature = new CustomerHasTagFeature(taggingService, "tagName");
    when(taggingService.customerHasTag(customerId, "tagName")).thenThrow(
        new RuntimeException());
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getValue(), false);
  }
}
