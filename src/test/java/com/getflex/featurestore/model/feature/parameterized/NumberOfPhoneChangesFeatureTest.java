package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.integration.flex.UserAccountService.UserAccountUpdateType;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.CustomerAccountUpdateHistory;
import com.getflex.useraccount.model.UpdateHistoryRecord;
import java.time.OffsetDateTime;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfPhoneChangesFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  UserAccountService userAccountService;
  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(CustomerAccountUpdateHistory history, String isoDuration, int expectedResult) {
    when(userAccountService.getUserAccountUpdateHistory(any())).thenReturn(history);
    when(identityService.getCustomer(any())).thenReturn(new GetCustomerResponse().customerId(CUSTOMER_ID));
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    var numOfEmailChangesFeature = new NumberOfPhoneChangesFeature(identityService, userAccountService,
        new LookbackDurationFeatureParams(isoDuration));
    FeatureValue featureValue = numOfEmailChangesFeature.fetchFeatureValue(evalParams);
    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    CustomerAccountUpdateHistory history = new CustomerAccountUpdateHistory();
    history.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPSERT_CREATE.toString())
        .dateCreated(OffsetDateTime.now().minusHours(23))
    );
    history.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+***********")
        .type(UserAccountUpdateType.UPDATE.toString())
        .dateCreated(OffsetDateTime.now().minusHours(25))
    );
    history.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPDATE.toString())
        .dateCreated(OffsetDateTime.now().minusHours(26))
    );

    CustomerAccountUpdateHistory history2 = new CustomerAccountUpdateHistory();
    history2.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPSERT_CREATE.toString())
    );

    CustomerAccountUpdateHistory history3 = new CustomerAccountUpdateHistory();
    history3.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPSERT_CREATE.toString())
    );
    history3.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPDATE.toString())
    );
    history3.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPDATE.toString())
    );

    CustomerAccountUpdateHistory history4 = new CustomerAccountUpdateHistory();
    history4.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPSERT_CREATE.toString())
    );
    history4.addHistoriesItem(new UpdateHistoryRecord()
        .phone("+1**************")
        .type(UserAccountUpdateType.UPDATE.toString())
    );

    return Stream.of(
        Arguments.of(history, "", 1),
        Arguments.of(history, "PT24H", 0),
        Arguments.of(history2, "", 0),
        Arguments.of(history3, "", 1),
        Arguments.of(history4, "", 0)
    );
  }

}
