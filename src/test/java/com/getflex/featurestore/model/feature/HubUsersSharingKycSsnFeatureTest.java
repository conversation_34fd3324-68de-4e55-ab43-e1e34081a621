package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.PartnerHubService;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.partnerhub.model.User;
import com.getflex.verification.model.Kyc;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class HubUsersSharingKycSsnFeatureTest {

  @InjectMocks
  HubUsersSharingKycSsnFeature feature;

  @Mock
  VerificationService verificationService;

  @Mock
  PartnerHubService partnerHubService;

  Kyc kyc = new Kyc().entityName("hubuser:1234").verifiedSsnHmac512("verifiedSsnHmac512");

  @Test
  public void getValue_success() {
    String kycId = UUID.randomUUID().toString();
    when(verificationService.getKyc(kycId)).thenReturn(kyc);
    when(verificationService.findKycSharingSameSsn(kyc)).thenReturn(List.of(new Kyc().entityName("hubuser:1345")));
    User otherUser = new User().id(1345L);
    when(partnerHubService.getHubUser(1345L)).thenReturn(otherUser);

    assertEquals(List.of(otherUser), feature.getValue(new EvalParams().kycId(kycId)).value());
  }

  @Test
  public void getValue_dedup() {
    String kycId = UUID.randomUUID().toString();
    when(verificationService.getKyc(kycId)).thenReturn(kyc);
    when(verificationService.findKycSharingSameSsn(kyc)).thenReturn(
        List.of(new Kyc().id("kyc1").entityName("hubuser:1345"), new Kyc().id("kyc2").entityName("hubuser:1345")));
    User otherUser = new User().id(1345L);
    when(partnerHubService.getHubUser(1345L)).thenReturn(otherUser);

    assertEquals(List.of(otherUser), feature.getValue(new EvalParams().kycId(kycId)).value());
    verify(partnerHubService).getHubUser(1345L);
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.ARRAY, feature.getType());
    assertEquals(All.of(EvalParamKey.KYC_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
