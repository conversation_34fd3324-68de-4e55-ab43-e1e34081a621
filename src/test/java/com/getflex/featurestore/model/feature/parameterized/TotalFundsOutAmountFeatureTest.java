package com.getflex.featurestore.model.feature.parameterized;

import static com.getflex.featurestore.helper.TestCreationUtils.fundsOutRecord;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class TotalFundsOutAmountFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  LedgerUtils ledgerUtils;

  @Test
  void getType_shouldReturnBoolean() {
    var feature = new TotalFundsOutAmountFeature(ledgerUtils, new LookbackDurationFeatureParams("P1D"));
    assertEquals(FeatureTypeEnum.INT, feature.getType());
  }

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<RecordLedger> recordLedgers,
      String lookbackWindow,
      Integer expectedAmount
  ) {
    var totalFundsOutAmountFeature = new TotalFundsOutAmountFeature(ledgerUtils,
        new LookbackDurationFeatureParams(lookbackWindow));
    when(ledgerUtils.getFundsOutRecords(any(), any())).thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureValue featureValue = totalFundsOutAmountFeature.fetchFeatureValue(evalParams);
    assertEquals(expectedAmount, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    var fundsOutAmount500 = fundsOutRecord(builder -> builder.amount(500L));
    var fundsOutAmount50 = fundsOutRecord(builder -> builder.amount(50L));
    var octFundsOutAmount50 = fundsOutRecord(
        builder -> builder.amount(50L)
            .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
            .paymentCategoryId(MovementCategory.CHARGE.getValue())
    );
    return Stream.of(
        Arguments.of(null, "P7D", 0),
        Arguments.of(List.of(), "P7D", 0),
        Arguments.of(List.of(fundsOutAmount500), "P7D", 500),
        Arguments.of(List.of(fundsOutAmount500, fundsOutAmount50), "P7D", 550),
        Arguments.of(List.of(fundsOutAmount500), "P7D", 500),
        Arguments.of(List.of(fundsOutAmount500, fundsOutAmount50, octFundsOutAmount50), "P7D", 600)
    );
  }
}
