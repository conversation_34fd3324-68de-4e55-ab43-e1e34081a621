package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FundsInPast30DaysFeatureTest {

  @InjectMocks
  FundsInPast30DaysFeature fundsInPast30DaysFeature;

  @Mock
  LedgerService ledgerService;

  @Test
  public void testGetValue() {
    Long customerId = 1L;
    RecordLedger fundsInRecordLedger = new RecordLedger()
        .customerId(customerId)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now().minusDays(1));
    List<RecordLedger> fundsInRecordLedgers = List.of(fundsInRecordLedger);
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(fundsInRecordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = fundsInPast30DaysFeature.fetchFeatureValue(evalParams);
    assertEquals(1, featureValue.getValue());
    assertEquals(FeatureTypeEnum.INT, featureValue.getType());
  }

  @Test
  public void testGetValueEmptyList() {
    Long customerId = 1L;
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = fundsInPast30DaysFeature.fetchFeatureValue(evalParams);
    assertEquals(0, featureValue.getValue());
  }
}
