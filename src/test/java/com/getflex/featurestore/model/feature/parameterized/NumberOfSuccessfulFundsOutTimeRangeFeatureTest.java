package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfSuccessfulFundsOutTimeRangeFeatureTest {
  @Mock
  LedgerService ledgerService;

  @ParameterizedTest
  @MethodSource("provideTestValues")
  public void testFeature(List<RecordLedger> ledgerResponse, String isoDuration, int expected) {
    NumberOfSuccessfulFundsOutTimeRangeFeature numberOfSuccessfulFundsOutTimeRangeFeature =
        new NumberOfSuccessfulFundsOutTimeRangeFeature(ledgerService, new LookbackDurationFeatureParams(isoDuration));
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(ledgerResponse);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    FeatureValue featureValue = numberOfSuccessfulFundsOutTimeRangeFeature.fetchFeatureValue(evalParams);
    assertEquals(FeatureTypeEnum.INT, featureValue.getType());
    assertEquals(expected, featureValue.getValue());
  }

  private static Stream<Arguments> provideTestValues() {
    RecordLedger ledgerRecord1 = new RecordLedger()
        .customerId(1L)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .fromPaymentMethodId(123L)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now().minusHours(1));

    RecordLedger ledgerRecord2 = new RecordLedger()
        .customerId(2L)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .fromPaymentMethodId(123L)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now().minusDays(1).minusHours(2));

    RecordLedger ledgerRecord3 = new RecordLedger()
        .customerId(3L)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .fromPaymentMethodId(123L)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now().minusDays(1).minusHours(2));

    List<RecordLedger> ledgerResponse1 = new ArrayList<>();
    ledgerResponse1.add(ledgerRecord1);
    ledgerResponse1.add(ledgerRecord2);
    ledgerResponse1.add(ledgerRecord3);

    RecordLedger ledgerRecord4 = new RecordLedger()
        .customerId(3L)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .fromPaymentMethodId(123L)
        .paymentStatusId(PaymentState.SETTLED.getValue())
        .paymentCategoryId(MovementCategory.AUTH.getValue())
        .dtCreated(OffsetDateTime.now().minusDays(1).minusHours(2));

    List<RecordLedger> ledgerResponse2 = new ArrayList<>();
    ledgerResponse2.add(ledgerRecord1);
    ledgerResponse2.add(ledgerRecord2);
    ledgerResponse2.add(ledgerRecord4);

    return Stream.of(
        Arguments.of(ledgerResponse1, "PT2H", 3),
        Arguments.of(ledgerResponse2, "P1DT3H", 2),
        Arguments.of(List.of(), "P3D", 0)
    );
  }
}
