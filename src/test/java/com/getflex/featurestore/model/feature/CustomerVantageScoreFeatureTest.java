package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.VantageScoreMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerVantageScoreFeatureTest {

  String customerId = "123";

  @InjectMocks
  CustomerVantageScoreFeature customerVantageScoreFeature;

  @Mock
  EventRepository eventRepository;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Test
  public void testGetValue() {
    OffsetDateTime dateTime = OffsetDateTime.now();

    Event event1 = Event.builder()
        .customerId(customerId)
        .dtArrived(dateTime)
        .metadata("""
            {"vantage_score": 720,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "alloy_token": "23456",
            "s3_url": "https:fake.com",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """)
        .build();
    Event event2 = Event.builder()
        .customerId(customerId)
        .dtArrived(dateTime)
        .metadata("""
            {"vantage_score": 500,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """).build();

    OfflineFeature feature = OfflineFeature.builder()
        .featureName("customer_epr_vantage_score")
        .featureValue("""
            {"vantage_score": 500,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """)
        .primaryKey(customerId + "_" + dateTime)
        .dtCreated(dateTime).build();
    
    Mockito.when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId))
        .thenReturn(List.of(event1, event2));
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        "customer_epr_vantage_score", customerId)).thenReturn(List.of(feature));

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));

    FeatureValue featureValue = customerVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerVantageScoreFeature");

    List<VantageScoreMetadata> featureValues = (List) featureValue.getValue();
    Assertions.assertEquals(featureValues.size(), 3);
    Assertions.assertEquals(featureValues.get(0).getVantageScore(), 500);
    Assertions.assertEquals(featureValues.get(1).getVantageScore(), 720);
    Assertions.assertEquals(featureValues.get(2).getVantageScore(), 500);
    Assertions.assertEquals(featureValues.get(0).getUpdatedAt(), dateTime);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.ARRAY);
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueEventOnly() {
    OffsetDateTime dateTime = OffsetDateTime.now();

    Event event = Event.builder()
        .customerId(customerId)
        .dtArrived(dateTime)
        .metadata("""
            {"vantage_score": 720,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "alloy_token": "23456",
            "s3_url": "https:fake.com",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """)
        .build();

    Mockito.when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId))
        .thenReturn(List.of(event));
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        "customer_epr_vantage_score", customerId)).thenReturn(List.of());

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));

    FeatureValue featureValue = customerVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerVantageScoreFeature");

    List<VantageScoreMetadata> featureValues = (List) featureValue.getValue();
    Assertions.assertEquals(featureValues.size(), 1);
    Assertions.assertEquals(featureValues.get(0).getVantageScore(), 720);
    Assertions.assertEquals(featureValues.get(0).getUpdatedAt(), dateTime);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.ARRAY);
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueOfflineFeatureOnly() {
    OffsetDateTime dateTime = OffsetDateTime.now();

    OfflineFeature feature = OfflineFeature.builder()
        .featureName("customer_epr_vantage_score")
        .featureValue("""
            {"vantage_score": 500,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """)
        .primaryKey(customerId + "_" + dateTime)
        .dtCreated(dateTime).build();

    Mockito.when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId))
        .thenReturn(List.of());
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        "customer_epr_vantage_score", customerId)).thenReturn(List.of(feature));

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));

    FeatureValue featureValue = customerVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerVantageScoreFeature");

    List<VantageScoreMetadata> featureValues = (List) featureValue.getValue();
    Assertions.assertEquals(featureValues.size(), 1);
    Assertions.assertEquals(featureValues.get(0).getVantageScore(), 500);
    Assertions.assertEquals(featureValues.get(0).getUpdatedAt(), dateTime);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.ARRAY);
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoFeatureFailure() {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));

    Mockito.when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
        EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId)).thenReturn(List.of());
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        "customer_epr_vantage_score", customerId)).thenReturn(List.of());

    FeatureValue featureValue = customerVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getErrorMessage(), "No vantage scores found for customer 123");
    Assertions.assertNull(featureValue.getValue());
  }

  @Test
  public void testParsingError() {
    OffsetDateTime dateTime = OffsetDateTime.now();

    Event event = Event.builder()
        .customerId(customerId)
        .dtCreated(dateTime)
        .metadata("I am not valid metadata")
        .build();
    Mockito.when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
        EventName.EVALUATE_OFFER_VANTAGE_SCORE, customerId)).thenReturn(List.of(event));

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));

    FeatureValue featureValue = customerVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getErrorMessage(),
        "Failed to parse vantage scores metadata for customer 123");
    Assertions.assertNull(featureValue.getValue());
  }
}
