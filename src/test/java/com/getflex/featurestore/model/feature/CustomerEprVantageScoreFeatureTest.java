package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.All;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerEprVantageScoreFeatureTest {

  @InjectMocks
  CustomerEprVantageScoreFeature customerEprVantageScoreFeature;
  @Mock
  OfflineFeatureRepo offlineFeatureRepo;
  public static final String FEATURE_NAME = "customer_epr_vantage_score";

  @Test
  public void testCustomerEprVantageScoreFeature() {
    String customerId = "123";
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));
    OffsetDateTime dateTime = OffsetDateTime.now();
    OfflineFeature feature = OfflineFeature.builder()
        .featureName("customer_epr_vantage_score")
        .featureValue("""
            {"vantage_score": 500,
            "reason_code1": "23",
            "reason_code2": "25",
            "reason_code3": "26",
            "reason_code4": "27",
            "deceased_flag": false,
            "frozen_credit": "FROZEN",
            "no_score_reason": "CREDIT_PROFILE_TOO_THIN"}
            """)
        .primaryKey(customerId + "_" + dateTime)
        .dtCreated(dateTime).build();
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            FEATURE_NAME, customerId + "_"))
        .thenReturn(List.of(feature));
    FeatureValue featureValue = customerEprVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(500, featureValue.getValue());
    Assertions.assertEquals("CustomerEprVantageScoreFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.INT, featureValue.getType());
    Assertions.assertEquals("Vantage score of customer from EPR",
        customerEprVantageScoreFeature.getDescription());
    Assertions.assertEquals(All.of(EvalParamKey.CUSTOMER_ID),
        customerEprVantageScoreFeature.getRequiredEvalParamKeys());
    Assertions.assertEquals(FEATURE_NAME,
        customerEprVantageScoreFeature.getOfflineFeatureName());
    Assertions.assertEquals(customerId + "_", customerEprVantageScoreFeature.getPrimaryKey(evalParams));
  }

  @Test
  public void testCustomerEprVantageScoreFeature_returnEmpty() {
    String customerId = "123";
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            FEATURE_NAME, customerId + "_"))
        .thenReturn(List.of());
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));
    FeatureValue featureValue = customerEprVantageScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerEprVantageScoreFeature");
    Assertions.assertEquals(0, featureValue.getValue());
  }
}
