package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.client.ApiException;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseBillPayRestrictionResult;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.BillerDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class IsPastDueRentFeatureTest {

  @Mock
  private IdentityService identityService;
  @Mock
  private BillingService billingService;

  @InjectMocks
  private IsPastDueRentFeature isPastDueRentFeature;

  @Test
  public void testGetValue() {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(1L);
    evalParams.billerId(123L);
    evalParams.billerAccountId(456L);
    when(billingService.getBillPayRestrictionStatusWithBillerId(123L, 456L)).thenReturn(
        new ComGetflexBillingDtoResponseBillPayRestrictionResult().pastDueRent(true));
    FeatureValue featureValue = isPastDueRentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsPastDueRentFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueWithoutBillerId() {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(1L);
    evalParams.billerAccountId(456L);
    when(identityService.getBillerByBillerAccountId(456L)).thenReturn(new BillerDto().billerId(123L));
    when(billingService.getBillPayRestrictionStatusWithBillerId(123L, 456L)).thenReturn(
        new ComGetflexBillingDtoResponseBillPayRestrictionResult().pastDueRent(true));
    FeatureValue featureValue = isPastDueRentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsPastDueRentFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueWithApiException501() {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(1L);
    evalParams.billerId(123L);
    evalParams.billerAccountId(456L);

    // Mock the ApiException with a 501 status code
    ApiException apiException = new ApiException(501, "501 NOT_IMPLEMENTED", null, null);

    when(billingService.getBillPayRestrictionStatusWithBillerId(123L, 456L))
        .thenThrow(new InternalDependencyFailureException(apiException));

    FeatureValue featureValue = isPastDueRentFeature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "IsPastDueRentFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
