package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.utils.TestSupport;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsCapitalOneCreditCardFeatureTest {
  @InjectMocks
  IsCapitalOneCreditCardFeature feature;

  @Mock
  TestSupport testSupport;

  @Test
  public void getValue_WithCapitalOneCreditCard() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("X");
    evalParams.setCardType("credit");
    evalParams.setCardIssuer("CapitalOne");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCapitalOneCreditCardFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), true);
  }

  @Test
  public void getValue_WithoutCapitalOneCreditCard() {
    when(testSupport.isProdEnvironment()).thenReturn(true);
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint("X");
    evalParams.setCardType("credit");
    evalParams.setCardIssuer("Bank1");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCapitalOneCreditCardFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), false);
  }

  @Test
  public void getValue_WithCardFingerPrintTestCard() {
    when(testSupport.isProdEnvironment()).thenReturn(false);
    EvalParams evalParams = new EvalParams();
    evalParams.setCardFingerprint(IsCapitalOneCreditCardFeature.CARD_FINGERPRINT_DENYLIST.get(0));
    evalParams.setCardType("credit");
    evalParams.setCardIssuer("Bank1");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsCapitalOneCreditCardFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), true);
  }
}
