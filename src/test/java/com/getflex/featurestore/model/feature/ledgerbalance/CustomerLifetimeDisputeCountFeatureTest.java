package com.getflex.featurestore.model.feature.ledgerbalance;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerLifetimeDisputeCountFeatureTest {

  @Mock
  LedgerService ledgerService;

  @Test
  void getValue() {
    RecordLedgerWallet dpFeeDispute = new RecordLedgerWallet();
    dpFeeDispute.setMoneyMovementTypeId(MoneyMovementType.PROCESSING_FEE_DOWNPAYMENT.getValue());
    dpFeeDispute.setAmount(3000L);
    dpFeeDispute.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dpFeeDispute.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet rpFeeDispute = new RecordLedgerWallet();
    rpFeeDispute.setMoneyMovementTypeId(MoneyMovementType.PROCESSING_FEE_REPAYMENT.getValue());
    rpFeeDispute.setAmount(5000L);
    rpFeeDispute.setPaymentStatusId(PaymentState.SETTLED.getValue());
    rpFeeDispute.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet dispute1 = new RecordLedgerWallet();
    dispute1.setMoneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue());
    dispute1.setAmount(7000L);
    dispute1.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dispute1.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    RecordLedgerWallet dispute2 = new RecordLedgerWallet();
    dispute2.setMoneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue());
    dispute2.setAmount(1000L);
    dispute2.setPaymentStatusId(PaymentState.SETTLED.getValue());
    dispute2.setPaymentCategoryId(MovementCategory.DISPUTE.getValue());

    List<RecordLedgerWallet> records = List.of(dpFeeDispute, dispute1, rpFeeDispute, dispute2);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDisputeCountFeature feature = new CustomerLifetimeDisputeCountFeature(ledgerService);

    assertEquals(new FeatureOutput(2), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void getValueWithNoResults() {
    LedgerService ledgerService = mock(LedgerService.class);
    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(List.of());
    CustomerLifetimeDisputeCountFeature feature = new CustomerLifetimeDisputeCountFeature(ledgerService);

    assertEquals(new FeatureOutput(0), feature.getValue(new EvalParams().customerId(1L)));
  }

  @Test
  void ignoresLegacyWalletLedgerRecordWithoutCategory() {
    RecordLedgerWallet record1 = new RecordLedgerWallet();
    record1.setMoneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue());
    record1.setAmount(1000L);
    record1.setPaymentStatusId(PaymentState.SETTLED.getValue());

    List<RecordLedgerWallet> records = List.of(record1);

    when(ledgerService.getWalletRecordLedgerByCustomerId(1L)).thenReturn(records);

    CustomerLifetimeDisputeCountFeature feature = new CustomerLifetimeDisputeCountFeature(ledgerService);

    assertEquals(new FeatureOutput(0), feature.getValue(new EvalParams().customerId(1L)));
  }
}
