package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.fulfillment.model.FulfillmentInfo;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerHasActiveFulfillmentFeatureTest {

  private final Long customerId = 123L;
  long fulfillmentId = 1L;
  String billTransactionId = "2";
  private final String featureName = "CustomerHasActiveFulfillmentFeature";

  @InjectMocks CustomerHasActiveFulfillmentFeature customerHasActiveFulfillmentFeature;

  @Mock
  FulfillmentService fulfillmentService;

  /** Test case for CustomerHasActiveFulfillmentFeature return true when return type PAUSED */
  @Test
  public void testCustomerHasActiveFulfillmentFeatureWhenStatusPaused() {
    FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
    fulfillmentInfo.setFulfillmentId(fulfillmentId);
    fulfillmentInfo.setBillTransactionId(billTransactionId);
    Mockito.when(fulfillmentService.getFulfillmentByCustomerInBp(customerId))
            .thenReturn(fulfillmentInfo);

    GetStatusResponseV2 getStatusResponseV2 = new GetStatusResponseV2();
    getStatusResponseV2.setStatus(GetStatusResponseV2.StatusEnum.PAUSED);
    Mockito.when(
                    fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
                            billTransactionId, fulfillmentId))
            .thenReturn(getStatusResponseV2);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = customerHasActiveFulfillmentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /** Test case for CustomerHasActiveFulfillmentFeature return true when return type WAITING */
  @Test
  public void testCustomerHasActiveFulfillmentFeatureWhenStatusWaiting() {
    FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
    fulfillmentInfo.setFulfillmentId(fulfillmentId);
    fulfillmentInfo.setBillTransactionId(billTransactionId);
    Mockito.when(fulfillmentService.getFulfillmentByCustomerInBp(customerId))
            .thenReturn(fulfillmentInfo);

    GetStatusResponseV2 getStatusResponseV2 = new GetStatusResponseV2();
    getStatusResponseV2.setStatus(GetStatusResponseV2.StatusEnum.WAITING);
    Mockito.when(
                    fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
                            billTransactionId, fulfillmentId))
            .thenReturn(getStatusResponseV2);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = customerHasActiveFulfillmentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /**
   * Test case for CustomerHasActiveFulfillmentFeature return true when return type is not WAITING
   * or PAUSED
   */
  @Test
  public void testCustomerHasActiveFulfillmentFeatureWhenStatusNotInValidList() {
    FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
    fulfillmentInfo.setFulfillmentId(fulfillmentId);
    fulfillmentInfo.setBillTransactionId(billTransactionId);
    Mockito.when(fulfillmentService.getFulfillmentByCustomerInBp(customerId))
            .thenReturn(fulfillmentInfo);

    GetStatusResponseV2 getStatusResponseV2 = new GetStatusResponseV2();
    getStatusResponseV2.setStatus(GetStatusResponseV2.StatusEnum.SUCCESS);
    Mockito.when(
                    fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
                            billTransactionId, fulfillmentId))
            .thenReturn(getStatusResponseV2);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = customerHasActiveFulfillmentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /**
   * Test case for CustomerHasActiveFulfillmentFeature return true get FulfillmentInfo return null
   */
  @Test
  public void testCustomerHasActiveFulfillmentFeatureWhenFulfillmentReturnNull() {
    Mockito.when(fulfillmentService.getFulfillmentByCustomerInBp(customerId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = customerHasActiveFulfillmentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /** Test case for CustomerHasActiveFulfillmentFeature return true when get Status return null */
  @Test
  public void testCustomerHasActiveFulfillmentFeatureWhenGetStatusReturnNull() {
    FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
    fulfillmentInfo.setFulfillmentId(fulfillmentId);
    fulfillmentInfo.setBillTransactionId(billTransactionId);
    Mockito.when(fulfillmentService.getFulfillmentByCustomerInBp(customerId))
            .thenReturn(fulfillmentInfo);

    Mockito.when(
                    fulfillmentService.getFulfillmentStatusByBillTransactionIdV2(
                            billTransactionId, fulfillmentId))
            .thenReturn(null);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = customerHasActiveFulfillmentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }
}