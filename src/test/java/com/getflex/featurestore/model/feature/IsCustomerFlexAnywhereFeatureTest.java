package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.BillerDto;
import com.getflex.identity.model.BillerTypeEnumDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsCustomerFlexAnywhereFeatureTest {

  @InjectMocks
  IsCustomerFlexAnywhereFeature feature;

  @Mock
  BillingService billingService;

  @Mock
  IdentityService identityService;

  @Test
  public void testGetValueWithFlexAnywhereBillerType() {
    Long billerAccountId = 1L;
    BillerDto billerAccount = new BillerDto().billerType(BillerTypeEnumDto.FLEX_ANYWHERE)
        .billerId(1L);
    when(identityService.getBillerByBillerAccountId(billerAccountId)).thenReturn(billerAccount);

    ComGetflexBillingControllerV2PropertyControllerPropertyResponse biller =
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse()
            .integrationType(IntegrationTypeEnum.FLEX_ANYWHERE);
    when(billingService.getPropertyById(1L)).thenReturn(biller);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "IsCustomerFlexAnywhereFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), true);
  }

  @Test
  public void testGetValueWithoutFlexAnywhereBillerType() {
    Long billerAccountId = 1L;
    BillerDto billerAccount = new BillerDto().billerType(BillerTypeEnumDto.PORTAL).billerId(1L);
    when(identityService.getBillerByBillerAccountId(billerAccountId)).thenReturn(billerAccount);

    ComGetflexBillingControllerV2PropertyControllerPropertyResponse biller =
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse()
            .integrationType(IntegrationTypeEnum.PORTAL);
    when(billingService.getPropertyById(1L)).thenReturn(biller);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "IsCustomerFlexAnywhereFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), false);
  }

}
