package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.verification.model.Kyc;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class KycFeatureTest {

  @InjectMocks
  KycFeature feature;

  @Mock
  VerificationService verificationService;

  @Mock
  Kyc kyc;

  @Test
  public void getValue_success() {
    String kycId = UUID.randomUUID().toString();
    when(verificationService.getKyc(kycId)).thenReturn(kyc);

    assertSame(kyc, feature.getValue(new EvalParams().kycId(kycId)).value());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(All.of(EvalParamKey.KYC_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
