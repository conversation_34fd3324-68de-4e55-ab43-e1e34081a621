package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class EditDistanceToStripePaymentFullNameFeatureTest {
  @InjectMocks
  EditDistanceToStripePaymentFullNameFeature editDistanceToStripePaymentFullNameFeature;

  @Mock
  IdentityService identityService;

  Long customerId = 3L;
  GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId).firstName("John").lastName("Smith");

  @Test
  public void testGetValue() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("john smith");

    FeatureValue featureValue = editDistanceToStripePaymentFullNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFullNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 0);
  }

  @Test
  public void testGetValue_hasLength() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = editDistanceToStripePaymentFullNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(-1, featureValue.getValue());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValue_emptyString() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("");

    FeatureValue featureValue = editDistanceToStripePaymentFullNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(-1, featureValue.getValue());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValue_noLastName() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("John");

    FeatureValue featureValue = editDistanceToStripePaymentFullNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(5, featureValue.getValue());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void getValue_default() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = editDistanceToStripePaymentFullNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFullNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), -1);
  }
}
