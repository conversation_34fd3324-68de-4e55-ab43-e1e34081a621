package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.parameterized.CurrentCardSameAsDownpaymentCardFeature;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for {@link CurrentCardSameAsDownpaymentCardFeature}.
 */
@ExtendWith(MockitoExtension.class)
class CurrentCardSameAsDownpaymentCardFeatureTest {
  private static final Long CUSTOMER_ID = 123L;
  private static final String BILL_TRANSACTION_ID = "txn-123";
  private static final String CUSTOMER_PUBLIC_ID = "cust-pub-123";
  private static final Long CARD_PAYMENT_METHOD_ID = 123L;
  private static final Long DIFFERENT_PAYMENT_METHOD_ID = 456L;

  @Mock
  private WalletService walletService;

  @Mock
  private LedgerService ledgerService;

  @Mock
  private IdentityService identityService;

  @InjectMocks
  private CurrentCardSameAsDownpaymentCardFeature feature;

  private EvalParams evalParams;

  @BeforeEach
  void setUp() {
    evalParams = new EvalParams()
        .customerId(CUSTOMER_ID)
        .billTransactionId(BILL_TRANSACTION_ID);
  }

  /**
   * Tests that the feature type is BOOLEAN.
   */
  @Test
  void getType_shouldReturnBoolean() {
    assertEquals(FeatureTypeEnum.BOOLEAN, feature.getType());
  }

  /**
   * Tests that false is returned when there are no downpayment records.
   */
  @Test
  void getValue_whenNoDownpaymentRecords_shouldReturnFalse() {
    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Collections.emptyList());

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
    assertEquals(FeatureTypeEnum.BOOLEAN, result.getType());
  }

  /**
   * Tests that false is returned when there is no default card.
   */
  @Test
  void getValue_whenNoDefaultCard_shouldReturnFalse() {
    mockCustomer();
    setupDownpaymentRecord(CARD_PAYMENT_METHOD_ID);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(null);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that false is returned when the card has no payment method ID.
   */
  @Test
  void getValue_whenCardHasNoPaymentMethodId_shouldReturnFalse() {
    mockCustomer();
    setupDownpaymentRecord(CARD_PAYMENT_METHOD_ID);
    
    // Setup a card with null payment method ID
    Card card = new Card()
        .paymentMethodId(null);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID))
        .thenReturn(new GetDefaultCardResponse().card(card));

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that true is returned when the card matches the downpayment card.
   */
  @Test
  void getValue_whenCardMatchesDownpayment_shouldReturnTrue() {
    mockCustomer();
    setupDownpaymentRecord(CARD_PAYMENT_METHOD_ID);
    setupDefaultCard(CARD_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertTrue((Boolean) result.getValue());
  }

  /**
   * Tests that false is returned when the card doesn't match the downpayment card.
   */
  @Test
  void getValue_whenCardDoesNotMatchDownpayment_shouldReturnFalse() {
    mockCustomer();
    setupDownpaymentRecord(CARD_PAYMENT_METHOD_ID);
    setupDefaultCard(DIFFERENT_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that all downpayment records are checked when multiple exist.
   */
  @Test
  void getValue_whenMultipleDownpaymentRecords_shouldCheckAll() {
    mockCustomer();
    // Setup two downpayment records, one with matching payment method, one without
    RecordLedger matchingRecord = new RecordLedger()
        .fromPaymentMethodId(CARD_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());
    RecordLedger nonMatchingRecord = new RecordLedger()
        .fromPaymentMethodId(DIFFERENT_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());

    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Arrays.asList(nonMatchingRecord, matchingRecord));

    setupDefaultCard(CARD_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that downpayment records are filtered by category when multiple exist.
   */
  @Test
  void getValue_whenMultipleDownpaymentRecords_shouldFilterByCategory() {
    mockCustomer();
    // Setup three downpayment records: one with charge category, one with transfer, and one with null category
    RecordLedger chargeRecord = new RecordLedger()
        .fromPaymentMethodId(CARD_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());
    RecordLedger transferRecord = new RecordLedger()
        .fromPaymentMethodId(CARD_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.INTRA_BANK_TRANSFER.getValue());
    RecordLedger nullCategoryRecord = new RecordLedger()
        .fromPaymentMethodId(CARD_PAYMENT_METHOD_ID)
        .paymentCategoryId(null);

    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Arrays.asList(chargeRecord, transferRecord, nullCategoryRecord));

    setupDefaultCard(CARD_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);
    
    // Should only match the record with CHARGE category
    assertTrue((Boolean) result.getValue());
  }

  /**
   * Tests that false is returned when a downpayment record has null payment method ID.
   */
  @Test
  void getValue_whenDownpaymentRecordHasNullPaymentMethodId_shouldReturnFalse() {
    mockCustomer();
    // Setup a downpayment record with null payment method ID
    RecordLedger recordWithNullPaymentMethod = new RecordLedger()
        .fromPaymentMethodId(null)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());

    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Collections.singletonList(recordWithNullPaymentMethod));

    setupDefaultCard(CARD_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that false is returned when the default card has null payment method ID.
   */
  @Test
  void getValue_whenDefaultCardHasNullPaymentMethodId_shouldReturnFalse() {
    mockCustomer();
    setupDownpaymentRecord(CARD_PAYMENT_METHOD_ID);
    
    // Setup default card with null payment method ID
    Card card = new Card()
        .paymentMethodId(null);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID))
        .thenReturn(new GetDefaultCardResponse().card(card));

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Tests that false is returned when there are multiple downpayment records with some having null payment method IDs.
   */
  @Test
  void getValue_whenMultipleDownpaymentRecordsWithSomeNullPaymentMethods_shouldReturnFalse() {
    mockCustomer();
    // Setup three downpayment records: one with matching payment method, one with different, one with null
    RecordLedger matchingRecord = new RecordLedger()
        .fromPaymentMethodId(CARD_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());
    RecordLedger nonMatchingRecord = new RecordLedger()
        .fromPaymentMethodId(DIFFERENT_PAYMENT_METHOD_ID)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());
    RecordLedger nullPaymentMethodRecord = new RecordLedger()
        .fromPaymentMethodId(null)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());

    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Arrays.asList(matchingRecord, nonMatchingRecord, nullPaymentMethodRecord));

    setupDefaultCard(CARD_PAYMENT_METHOD_ID);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertFalse((Boolean) result.getValue());
  }

  /**
   * Sets up a mock downpayment record with the given payment method ID.
   *
   * @param paymentMethodId The payment method ID to use in the mock record
   */
  private void setupDownpaymentRecord(Long paymentMethodId) {
    RecordLedger record = new RecordLedger()
        .fromPaymentMethodId(paymentMethodId)
        .paymentCategoryId(MovementCategory.CHARGE.getValue());

    when(ledgerService.getLedgersByBillTransactionId(
        BILL_TRANSACTION_ID,
        LedgerService.PaymentState.SETTLED,
        LedgerService.MoneyMovementType.DOWNPAYMENT))
        .thenReturn(Collections.singletonList(record));
  }

  /**
   * Mocks the customer service to return a test customer.
   */
  private void mockCustomer() {
    when(identityService.getCustomer(CUSTOMER_ID))
        .thenReturn(new GetCustomerResponse()
            .customerId(CUSTOMER_ID)
            .customerPublicId(CUSTOMER_PUBLIC_ID));
  }

  /**
   * Sets up a mock default card with the given payment method ID.
   *
   * @param paymentMethodId The payment method ID to use for the mock card
   */
  private void setupDefaultCard(Long paymentMethodId) {
    Card card = new Card()
        .paymentMethodId(paymentMethodId);

    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID))
        .thenReturn(new GetDefaultCardResponse().card(card));
  }
}
