package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsDeviceIdBlockedFeatureTest {

  @InjectMocks
  IsDeviceIdBlockedFeature isDeviceIdBlockedFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  String deviceId = "test-device-id";

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean featureExists) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("denylist_device_id")
            .primaryKey(deviceId).featureValue("1")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setDeviceId(deviceId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "denylist_device_id", deviceId)).thenReturn(feature);
    FeatureValue featureValue = isDeviceIdBlockedFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsDeviceIdBlockedFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), featureExists);
  }
}
