package com.getflex.featurestore.model.feature.parameterized;

import static com.getflex.featurestore.helper.TestCreationUtils.recordLedger;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfFundsOutAttemptsFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  LedgerService ledgerService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<RecordLedger> recordLedgers,
      Integer expectedAmount
  ) {
    var numberOfFundsOutAttempts = new NumberOfFundsOutAttemptsFeature(ledgerService,
        new LookbackDurationFeatureParams("P7D"));
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureValue featureValue = numberOfFundsOutAttempts.fetchFeatureValue(evalParams);
    assertEquals(expectedAmount, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    var settledFundsOut = recordLedger(
        builder -> builder
            .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
            .paymentCategoryId(MovementCategory.REFUND.getValue())
    );
    var settledOctFundsOut = recordLedger(
        builder -> builder
            .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
            .paymentCategoryId(MovementCategory.CHARGE.getValue())
    );
    var declinedFundsOut = recordLedger(
        builder -> builder
            .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
            .paymentCategoryId(MovementCategory.REFUND.getValue())
            .paymentStatusId(PaymentState.DECLINED.getValue())
    );
    var declinedOctFundsOut = recordLedger(
        builder -> builder
            .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
            .paymentCategoryId(MovementCategory.CHARGE.getValue())
            .paymentStatusId(PaymentState.DECLINED.getValue())
    );
    var initiatedOctFundsOut = recordLedger(
        builder -> builder
            .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
            .paymentCategoryId(MovementCategory.CHARGE.getValue())
            .paymentStatusId(PaymentState.INITIATED.getValue())
    );
    return Stream.of(
        Arguments.of(null, 0),
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(initiatedOctFundsOut), 0),
        Arguments.of(List.of(settledFundsOut), 1),
        Arguments.of(List.of(settledOctFundsOut), 1),
        Arguments.of(List.of(declinedFundsOut), 1),
        Arguments.of(List.of(declinedOctFundsOut), 1),
        Arguments.of(
            List.of(
                initiatedOctFundsOut,
                settledFundsOut,
                settledOctFundsOut,
                declinedFundsOut,
                declinedOctFundsOut
            ),
            4
        )
    );
  }
}
