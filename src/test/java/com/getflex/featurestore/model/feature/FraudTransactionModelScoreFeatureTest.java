package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.StripeAvsStreetCheckEnum;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelScoreFeatureTest {

  @InjectMocks
  FraudTransactionModelScoreFeature fraudTransactionModelScoreFeature;

  @Mock
  SageMakerRuntimeClient sageMaker;

  @Mock
  ServiceConfig serviceConfig;

  @Mock
  FeatureFactory featureFactory;

  @Mock
  ExecutorService executorService;

  @Mock
  BaseFeature baseFeature;

  private static final Long customerId = 123L;
  private static final Long billerAccountId = 123L;

  @Test
  public void getValue() throws InterruptedException {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setBillerAccountId(billerAccountId);
    evalParams.setStripeAvsStreetCheck(StripeAvsStreetCheckEnum.PASS);
    evalParams.setCardType("credit");
    FeatureValue featureValue = new FeatureValue();
    featureValue.setName("Test-Mock-Feature");
    featureValue.setType(FeatureTypeEnum.INT);
    Number value = 100;
    featureValue.setValue(value);
    when(baseFeature.fetchFeatureValue(evalParams)).thenReturn(featureValue);
    when(featureFactory.getFeature(ArgumentMatchers.anyString())).thenReturn(baseFeature);
    Double expectedValue = 0.*****************;
    InvokeEndpointResponse response = InvokeEndpointResponse.builder()
        .contentType("text/csv; charset=utf-8")
        .body(SdkBytes.fromUtf8String(expectedValue.toString()))
        .build();
    when(sageMaker.invokeEndpoint(any(InvokeEndpointRequest.class))).thenReturn(response);
    List<Future<Object>> futures = fraudTransactionModelScoreFeature.getModelFeaturesList().stream()
        .map(f -> {
          Number val = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          val = val != null ? val : -********;
          Future<Object> future = CompletableFuture.completedFuture(
              Map.entry(f, val));
          return future;
        })
        .toList();
    when(executorService.invokeAll(anyCollection())).thenReturn(futures);

    FeatureValue actualValue = fraudTransactionModelScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(expectedValue, actualValue.getValue());

    Map<FraudTransactionModelInput, Number> expectedValues = new ConcurrentHashMap<>();
    fraudTransactionModelScoreFeature.getModelFeaturesList().forEach(f -> {
      if (f.equals(FraudTransactionModelInput.POSTAL_MATCH_FLAG)) {
        expectedValues.put(f, 2);
      } else if (f.equals(FraudTransactionModelInput.ADDRESS_CHECK_FLAG)) {
        expectedValues.put(f, 1);
      } else if (f.equals(FraudTransactionModelInput.CARD_FUNDING_FLAG)) {
        expectedValues.put(f, 1);
      } else {
        expectedValues.put(f, 100);
      }
    });
    Assertions.assertEquals(new JSONObject(expectedValues).toString(), actualValue.getMetadata());
  }

  @Test
  public void getValue_allNull() throws InterruptedException {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setBillerAccountId(billerAccountId);
    FeatureValue featureValue = new FeatureValue();
    featureValue.setName("Test-Mock-Feature");
    featureValue.setValue(null);
    when(baseFeature.fetchFeatureValue(evalParams)).thenReturn(featureValue);
    when(featureFactory.getFeature(ArgumentMatchers.anyString())).thenReturn(baseFeature);
    Double expectedValue = 0.*****************;
    InvokeEndpointResponse response = InvokeEndpointResponse.builder()
        .contentType("text/csv; charset=utf-8")
        .body(SdkBytes.fromUtf8String(expectedValue.toString()))
        .build();
    when(sageMaker.invokeEndpoint(any(InvokeEndpointRequest.class))).thenReturn(response);
    List<Future<Object>> futures = fraudTransactionModelScoreFeature.getModelFeaturesList().stream()
        .map(f -> {
          Number val = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          val = val != null ? val : -********;
          Future<Object> future = CompletableFuture.completedFuture(Map.entry(f, val));
          return future;
        })
        .toList();
    when(executorService.invokeAll(anyCollection())).thenReturn(futures);

    FeatureValue actual = fraudTransactionModelScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(expectedValue, actual.getValue());

    Map<FraudTransactionModelInput, Number> expectedValues = new ConcurrentHashMap<>();
    fraudTransactionModelScoreFeature.getModelFeaturesList().forEach(f -> {
      if (f.equals(FraudTransactionModelInput.POSTAL_MATCH_FLAG)) {
        expectedValues.put(f, 3);
      } else if (f.equals(FraudTransactionModelInput.ADDRESS_CHECK_FLAG)) {
        expectedValues.put(f, 5);
      } else if (f.equals(FraudTransactionModelInput.CARD_FUNDING_FLAG)) {
        expectedValues.put(f, 0);
      } else {
        expectedValues.put(f, -********);
      }
    });
    Assertions.assertEquals(new JSONObject(expectedValues).toString(), actual.getMetadata());
  }

  @Test
  public void featureOrderPreserved() {
    List<FraudTransactionModelInput> modelInputFeatures =
        fraudTransactionModelScoreFeature.getModelFeaturesList();

    Assertions.assertEquals(FraudTransactionModelInput.ABUSE_SCORE, modelInputFeatures.get(0));
    Assertions.assertEquals(FraudTransactionModelInput.IDENTITY_THEFT_SCORE, modelInputFeatures.get(1));
    Assertions.assertEquals(FraudTransactionModelInput.NAME_MATCH_FLAG, modelInputFeatures.get(2));
    Assertions.assertEquals(FraudTransactionModelInput.FLEX_CUST_SCORE, modelInputFeatures.get(3));
    Assertions.assertEquals(FraudTransactionModelInput.TOTAL_INSUFFICIENT_DECL, modelInputFeatures.get(4));
    Assertions.assertEquals(FraudTransactionModelInput.CARD_DECLINE_COUNT, modelInputFeatures.get(5));
    Assertions.assertEquals(FraudTransactionModelInput.CARD_SUPPORT_FAIL_COUNT, modelInputFeatures.get(6));
    Assertions.assertEquals(FraudTransactionModelInput.INVALID_ACCOUNT_COUNT, modelInputFeatures.get(7));
    Assertions.assertEquals(FraudTransactionModelInput.TOT_DIST_CARD, modelInputFeatures.get(8));
    Assertions.assertEquals(FraudTransactionModelInput.TOTAL_SUCCESSFUL_PAYMENT, modelInputFeatures.get(9));
    Assertions.assertEquals(FraudTransactionModelInput.CARD_FUNDING_FLAG, modelInputFeatures.get(10));
    Assertions.assertEquals(FraudTransactionModelInput.ADDRESS_CHECK_FLAG, modelInputFeatures.get(11));
    Assertions.assertEquals(FraudTransactionModelInput.POSTAL_MATCH_FLAG, modelInputFeatures.get(12));
    Assertions.assertEquals(FraudTransactionModelInput.MOBILE_VERIFY, modelInputFeatures.get(13));
    Assertions.assertEquals(FraudTransactionModelInput.FA_IND, modelInputFeatures.get(14));
    Assertions.assertEquals(FraudTransactionModelInput.TOTAL_DISPUTE_PRE, modelInputFeatures.get(15));
    Assertions.assertEquals(FraudTransactionModelInput.FNAME_EDIT_DISTANCE, modelInputFeatures.get(16));
    Assertions.assertEquals(FraudTransactionModelInput.LNAME_EDIT_DISTANCE, modelInputFeatures.get(17));
    Assertions.assertEquals(FraudTransactionModelInput.SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE,
        modelInputFeatures.get(18));
    Assertions.assertEquals(FraudTransactionModelInput.SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE,
        modelInputFeatures.get(19));
    Assertions.assertEquals(FraudTransactionModelInput.RISKY_CARRIER_IND, modelInputFeatures.get(20));
  }

}
