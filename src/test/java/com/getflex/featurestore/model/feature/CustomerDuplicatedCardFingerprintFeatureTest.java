package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDuplicatedCardFingerprintFeatureTest {

  @InjectMocks
  CustomerDuplicatedCardFingerprintFeature customerDuplicatedCardFingerprintFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @ParameterizedTest
  @ValueSource(longs = {3L, 4L})
  public void testGetValueOneCustomerId(Long customerId) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("card_fingerprint_to_customer_id_list")
            .primaryKey("test_fingerprint").featureValue("3")
            .build());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setCardFingerprint("test_fingerprint");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "card_fingerprint_to_customer_id_list", "test_fingerprint")).thenReturn(feature);
    FeatureValue featureValue = customerDuplicatedCardFingerprintFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDuplicatedCardFingerprintFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    if (customerId == 3L) {
      Assertions.assertEquals(featureValue.getValue(), Boolean.FALSE);
    } else {
      Assertions.assertEquals(featureValue.getValue(), Boolean.TRUE);
    }
  }


  @ParameterizedTest
  @ValueSource(longs = {3L, 4L})
  public void testGetValueNoCustomerId(Long customerId) {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setCardFingerprint("test_fingerprint");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "card_fingerprint_to_customer_id_list", "test_fingerprint")).thenReturn(Optional.empty());
    FeatureValue featureValue = customerDuplicatedCardFingerprintFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDuplicatedCardFingerprintFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), Boolean.FALSE);
  }

  @ParameterizedTest
  @ValueSource(longs = {3L, 4L})
  public void testGetValueMultipleCustomerId(Long customerId) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("card_fingerprint_to_customer_id_list")
            .primaryKey("test_fingerprint").featureValue("3,8")
            .build());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setCardFingerprint("test_fingerprint");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "card_fingerprint_to_customer_id_list", "test_fingerprint")).thenReturn(feature);
    FeatureValue featureValue = customerDuplicatedCardFingerprintFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDuplicatedCardFingerprintFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), Boolean.TRUE);
  }
}
