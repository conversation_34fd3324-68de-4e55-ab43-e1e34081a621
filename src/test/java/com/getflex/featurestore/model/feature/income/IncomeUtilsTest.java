package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.dao.model.event.EventName.BANK_INCOME_VERIFICATION;
import static com.getflex.featurestore.dao.model.event.EventName.SELF_REPORTED_INCOME_VERIFICATION;
import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.eventmetadata.BankIncomeVerificationMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.IncomeMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.identity.model.GetCustomerResponse;
import com.plaid.client.model.BaseReport;
import com.plaid.client.model.CraCheckReportBaseReportGetResponse;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IncomeUtilsTest {

  @Mock
  IdentityService identityService;
  @Mock
  EventRepository eventRepository;
  @Mock
  S3Client s3Client;

  @InjectMocks
  IncomeUtils incomeUtils;

  @Test
  public void getSelfReportedAnnualGrossIncomeCents() throws JsonProcessingException {
    IncomeMetadata incomeMetadata = new IncomeMetadata("50000");
    String metadata = OBJECT_MAPPER.writeValueAsString(incomeMetadata);
    Event event = Event.builder().customerId("1").name(SELF_REPORTED_INCOME_VERIFICATION)
        .category(EventCategory.SELF_REPORTED_INCOME).entityId("123").metadata(metadata).build();
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", SELF_REPORTED_INCOME_VERIFICATION))
        .thenReturn(Optional.of(event));

    Double response = incomeUtils.getSelfReportedAnnualGrossIncomeCents("123");

    Assertions.assertEquals(50000.00, response);
  }

  @Test
  public void getCustomerFullName_success() {
    when(identityService.getCustomer(1L)).thenReturn(
        new GetCustomerResponse().customerId(1L).firstName("John").lastName("Smith"));
    String result = incomeUtils.getCustomerFullName(1L);
    Assertions.assertEquals("John Smith", result);
  }

  @Test
  public void getCustomerFullName_failure() {
    when(identityService.getCustomer(1L)).thenReturn(null);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> incomeUtils.getCustomerFullName(1L));
  }

  @Test
  public void getEvent_deserializeMetadata_success() throws JsonProcessingException {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);

    CraCheckReportBaseReportGetResponse mockedResponse = new CraCheckReportBaseReportGetResponse()
        .report(new BaseReport().items(List.of()));

    String metadata = OBJECT_MAPPER.writeValueAsString(mockedResponse);
    Event event = Event.builder().customerId("1").name(BANK_INCOME_VERIFICATION)
        .category(EventCategory.BANK_INCOME).metadata(metadata).build();
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", BANK_INCOME_VERIFICATION))
        .thenReturn(Optional.of(event));

    Event testEvent = incomeUtils.getEvent(evalParams.getVerificationId(), BANK_INCOME_VERIFICATION);
    Assertions.assertDoesNotThrow(() ->
        incomeUtils.getEvent(evalParams.getVerificationId(), BANK_INCOME_VERIFICATION));
    Assertions.assertDoesNotThrow(() -> incomeUtils.deserializeMetadata(testEvent.getMetadata(),
        BankIncomeVerificationMetadata.class));
  }

  @Test
  public void getEvent_notFound() {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);
    when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc("123", BANK_INCOME_VERIFICATION))
        .thenReturn(Optional.empty());

    Assertions.assertThrows(FeatureNotFoundException.class, () ->
        incomeUtils.getEvent(evalParams.getVerificationId(), BANK_INCOME_VERIFICATION));
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      John Smith, jon smiith, true
      John Smith, John Smith, true
      Taylor Swift, Taylor Alison Swift, true
      taylor swif, TAYLOR ALISON SWIFT, true
      TAYLOR SWTF, Taylor A. Swift, true
      Taylor ALISON switf!, Taylor Swift, true
      John Smith, Jack Sparrow, false
      JOHN SMITH, JACKSON SMITH, true
      Nick Haines, Nicholas Haines, true
      Henry Jimenez-Johnson, Henry Jimenez, true
      Swift Taylor, Taylor Swift, true
      Taylor Swift, Sabrina Carpenter, false
      Taaaaaaaaaaaaylor Swiiift!, Taylor Fast, false
      , Taylor Swift, false
      Taylor Swift, , true
      """)
  public void fuzzyNameMatchResult(String name1, String name2, boolean result) {
    assertEquals(result, incomeUtils.namesFuzzyMatch(name1, name2),
        "%s does not match expected result for %s".formatted(name1, name2));
  }

  @Test
  public void downloadReports_success() throws URISyntaxException {
    when(s3Client.getObjectAsBytes(GetObjectRequest.builder().bucket("path").key("to/file.json").build()))
        .thenReturn(ResponseBytes.fromByteArray(GetObjectResponse.builder().build(), "bytestring".getBytes()));

    assertEquals("bytestring", incomeUtils.downloadReports("s3://path/to/file.json"));
  }

  @Test
  public void downloadReports_failure() {
    assertThrows(URISyntaxException.class, () -> incomeUtils.downloadReports("malformed\n\nuri"));
  }

  @Test
  public void doubleFormat() {
    Assertions.assertEquals(10.00, incomeUtils.formatDouble(10.00000003));
  }

}
