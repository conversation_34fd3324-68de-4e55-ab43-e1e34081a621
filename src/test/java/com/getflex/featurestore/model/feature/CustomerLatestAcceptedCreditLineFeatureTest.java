package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerLatestAcceptedCreditLineFeatureTest {

  @InjectMocks
  CustomerLatestAcceptedCreditLineFeature customerLatestAcceptedCreditLineFeature;

  @Mock
  OfferService offerService;

  @Test
  public void customerLatestAcceptedCreditLineFeature_success() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1")
            .offerState(OfferStateEnum.ACCEPTED).creditLimitAmountCent(50000L)
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .acceptanceTime(OffsetDateTime.of(LocalDate.of(2021, 10, 2), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer2 =
        new InternalOffer().customerId(customerId).offerId("offer-2")
            .offerState(OfferStateEnum.CLOSED).creditLimitAmountCent(60000L)
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 11, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .acceptanceTime(OffsetDateTime.of(LocalDate.of(2021, 11, 2), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 11, 3), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer3 =
        new InternalOffer().customerId(customerId).offerId("offer-3")
            .offerState(OfferStateEnum.ACCEPTED).creditLimitAmountCent(70000L)
            .initiationTime(OffsetDateTime.of(LocalDate.of(2020, 11, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .acceptanceTime(OffsetDateTime.of(LocalDate.of(2020, 11, 2), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1, offer2, offer3));

    FeatureValue featureValue = customerLatestAcceptedCreditLineFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerLatestAcceptedCreditLineFeature");
    Assertions.assertEquals(featureValue.getValue(), 50000L);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void customerLatestAcceptedCreditLineFeature_featureNotFound() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1")
            .offerState(OfferStateEnum.CLOSED).creditLimitAmountCent(60000L)
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 11, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .acceptanceTime(OffsetDateTime.of(LocalDate.of(2021, 11, 2), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 11, 3), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1));

    FeatureValue featureValue = customerLatestAcceptedCreditLineFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerLatestAcceptedCreditLineFeature");
    Assertions.assertEquals(featureValue.getValue(), null);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
    Assertions.assertEquals(featureValue.getErrorMessage(),
        "CustomerLatestAcceptedCreditLineFeature value not found. customerId=1234");
    Assertions.assertNull(featureValue.getMetadata());
  }
}
