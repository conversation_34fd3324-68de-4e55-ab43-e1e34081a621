package com.getflex.featurestore.model.feature.movein;

import static com.getflex.featurestore.constant.ProductConstants.MOVE_IN_CATEGORY_ID;
import static com.getflex.featurestore.constant.ProductConstants.RENTAL_PRODUCT_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.credit.client.model.Loan;
import com.getflex.credit.client.model.Loan.StateEnum;
import com.getflex.featurestore.exception.UnexpectedFeatureStateException;
import com.getflex.featurestore.integration.flex.CreditManagementService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MoveInCustomerFeatureTest {

  @Mock
  private OfferService offerService;

  @Mock
  private CreditManagementService creditManagementService;

  private MoveInCustomerFeature feature;

  private static final Long CUSTOMER_ID = 12345L;

  @BeforeEach
  void setUp() {
    feature = new MoveInCustomerFeature(offerService, creditManagementService);
  }

  @Test
  void testGetType() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
  }

  @Test
  void testGetDescription() {
    assertNotNull(feature.getDescription());
    assertTrue(feature.getDescription().contains("move-in"));
  }

  @Test
  void testNonMoveInCustomer_NoOffers() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.emptyList());

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    assertNotNull(result);
    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertFalse(info.isMoveIn());
    assertNull(info.getMoveInType());
    assertTrue(info.getMoveInOffers().isEmpty());
  }

  @Test
  void testNonMoveInCustomer_OnlyPendingOffers() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    InternalOffer pendingOffer1 = createOffer("offer1", 1L, null, OverallState.PENDING);
    InternalOffer pendingOffer2 = createOffer("offer2", 1L, null, OverallState.PENDING);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Arrays.asList(pendingOffer1, pendingOffer2));

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertFalse(info.isMoveIn());
    assertNull(info.getMoveInType());
    assertTrue(info.getMoveInOffers().isEmpty());
  }

  @Test
  void testMoveInCustomer_CanceledOfferWithoutTerminationTime_ThrowsException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    // Create a canceled offer without termination time
    InternalOffer canceledOffer = createOffer("offer1", 1L, null, OverallState.CANCELED);
    // Don't set termination time

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(canceledOffer));

    // BaseFeature catches UnexpectedFeatureStateException and converts to error message
    FeatureValue result = feature.fetchFeatureValue(evalParams);
    
    assertNotNull(result);
    assertNotNull(result.getErrorMessage());
    assertTrue(result.getErrorMessage().contains("Invalid state: Unable to determine move in type for customerId"));
    assertNull(result.getValue()); // Default value should be null
  }

  @Test
  void testMoveInCustomer_PayInFull() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    InternalOffer payInFullOffer = createOffer("offer1", 1L, null, OverallState.ACTIVE);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(payInFullOffer));

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.PAY_IN_FULL, info.getMoveInType());
    assertEquals(1, info.getMoveInOffers().size());
    
    MoveInOfferInfo offerInfo = info.getMoveInOffers().get(0);
    assertEquals("offer1", offerInfo.getOfferId());
    assertEquals(1L, offerInfo.getOfferVersion());
    assertNull(offerInfo.getLoanId());
    assertEquals(MoveInType.PAY_IN_FULL, offerInfo.getType());
    assertEquals(OverallState.ACTIVE, offerInfo.getOfferState());
    assertNull(offerInfo.getLoan());

    verify(creditManagementService, never()).getLoanById(anyLong());
  }

  @Test
  void testMoveInCustomer_WithLoan() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    Long loanId = 999L;
    InternalOffer loanOffer = createOffer("offer1", 1L, loanId, OverallState.ACTIVE);

    Loan loan = createLoan(loanId);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(loanOffer));
    
    when(creditManagementService.getLoanById(loanId)).thenReturn(loan);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.LOAN, info.getMoveInType());
    assertEquals(1, info.getMoveInOffers().size());
    
    MoveInOfferInfo offerInfo = info.getMoveInOffers().get(0);
    assertEquals("offer1", offerInfo.getOfferId());
    assertEquals(1L, offerInfo.getOfferVersion());
    assertEquals(loanId, offerInfo.getLoanId());
    assertEquals(MoveInType.LOAN, offerInfo.getType());
    assertEquals(OverallState.ACTIVE, offerInfo.getOfferState());
    
    assertNotNull(offerInfo.getLoan());
    assertEquals(loanId, offerInfo.getLoan().getLoanId());
  }

  @Test
  void testMoveInCustomer_MultipleOffers() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    InternalOffer payInFullOffer = createOffer("offer1", 1L, null, OverallState.ACTIVE);
    InternalOffer loanOffer = createOffer("offer2", 2L, 1000L, OverallState.SUSPENDED);
    InternalOffer inactiveOffer = createOffer("offer3", 1L, null, OverallState.CANCELED);

    Loan loan = createLoan(1000L);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Arrays.asList(payInFullOffer, loanOffer, inactiveOffer));
    
    when(creditManagementService.getLoanById(1000L)).thenReturn(loan);

    FeatureValue result = feature.fetchFeatureValue(evalParams);

    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.PAY_IN_FULL, info.getMoveInType()); // ACTIVE offer takes precedence
    assertEquals(3, info.getMoveInOffers().size()); // Now includes canceled offers

    // Verify first offer (pay-in-full)
    MoveInOfferInfo firstOffer = info.getMoveInOffers().get(0);
    assertEquals(MoveInType.PAY_IN_FULL, firstOffer.getType());
    assertNull(firstOffer.getLoan());

    // Verify second offer (loan)
    MoveInOfferInfo secondOffer = info.getMoveInOffers().get(1);
    assertEquals(MoveInType.LOAN, secondOffer.getType());
    assertNotNull(secondOffer.getLoan());
    assertEquals(OverallState.SUSPENDED, secondOffer.getOfferState());
  }

  @Test
  void testMoveInCustomer_LoanFetchFailure() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    Long loanId = 999L;
    InternalOffer loanOffer = createOffer("offer1", 1L, loanId, OverallState.ACTIVE);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(loanOffer));
    
    when(creditManagementService.getLoanById(loanId))
        .thenThrow(new RuntimeException("Failed to fetch loan"));

    // BaseFeature catches exceptions and converts them to error messages
    FeatureValue result = feature.fetchFeatureValue(evalParams);
    
    assertNotNull(result);
    assertNotNull(result.getErrorMessage());
    assertTrue(result.getErrorMessage().contains("Failed to fetch loan"));
    assertNull(result.getValue()); // Default value should be null
  }

  @Test
  void testMoveInCustomer_CanceledOfferWithOpenLoan_ThrowsException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    Long loanId = 999L;
    InternalOffer canceledOffer = createOffer("offer1", 1L, loanId, OverallState.CANCELED);

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(canceledOffer));
    
    // Create an open loan - any state other than CLOSED means open
    Loan openLoan = createLoan(loanId, StateEnum.ORIGINATED);
    when(creditManagementService.getLoanById(loanId)).thenReturn(openLoan);

    // BaseFeature catches UnexpectedFeatureStateException and converts to error message
    FeatureValue result = feature.fetchFeatureValue(evalParams);
    
    assertNotNull(result);
    assertNotNull(result.getErrorMessage());
    assertTrue(result.getErrorMessage().contains("Invalid state: Offer offer1 is closed but loan 999 is still open"));
    assertNull(result.getValue()); // Default value should be null
  }
  
  @Test
  void testMoveInCustomer_CanceledOfferWithClosedLoan_NoException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);

    Long loanId = 999L;
    InternalOffer canceledOffer = createOffer("offer1", 1L, loanId, OverallState.CANCELED);
    canceledOffer.setTerminationTime(OffsetDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT, ZoneOffset.UTC));

    when(offerService.searchOfferByProduct(CUSTOMER_ID, Set.of(RENTAL_PRODUCT_ID), 
        Set.of(MOVE_IN_CATEGORY_ID), false))
        .thenReturn(Collections.singletonList(canceledOffer));
    
    Loan closedLoan = createLoan(loanId, StateEnum.CLOSED);
    when(creditManagementService.getLoanById(loanId)).thenReturn(closedLoan);

    FeatureValue result = feature.fetchFeatureValue(evalParams);
    
    MoveInCustomerInfo info = (MoveInCustomerInfo) result.getValue();
    assertTrue(info.isMoveIn());
    assertEquals(MoveInType.LOAN, info.getMoveInType());
    assertEquals(1, info.getMoveInOffers().size());
    
    MoveInOfferInfo offerInfo = info.getMoveInOffers().get(0);
    assertEquals(MoveInType.LOAN, offerInfo.getType());
    assertEquals(loanId, offerInfo.getLoanId());
    assertEquals(closedLoan, offerInfo.getLoan());
  }

  private InternalOffer createOffer(String offerId, Long version, Long loanId, OverallState state) {
    InternalOffer offer = new InternalOffer();
    offer.setOfferId(offerId);
    offer.setOfferVersion(version);
    offer.setLoanId(loanId);
    offer.setOverallState(state);
    return offer;
  }

  private Loan createLoan(Long loanId) {
    return createLoan(loanId, null); // Default to open loan (not closed)
  }
  
  private Loan createLoan(Long loanId, StateEnum state) {
    Loan loan = new Loan();
    loan.setLoanId(loanId);
    loan.setState(state);
    return loan;
  }
}
