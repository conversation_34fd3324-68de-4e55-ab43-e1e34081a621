package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfAddCardSuccessFeatureTest {

  private static final Long CUSTOMER_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customerPublicId";

  @Mock
  IdentityService identityService;

  @Mock
  WalletService walletService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<Card> cards,
      Integer expectedNumberOfAddCardSuccess
  ) {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(CUSTOMER_ID);
    Mockito.when(identityService.getCustomer(CUSTOMER_ID))
        .thenReturn(new GetCustomerResponse().customerPublicId(CUSTOMER_PUBLIC_ID));
    Mockito.when(walletService.getCards(CUSTOMER_PUBLIC_ID)).thenReturn(cards);
    var featureValue = new NumberOfAddCardSuccessFeature(
        identityService, walletService, new LookbackDurationFeatureParams("P60D")
    ).fetchFeatureValue(evalParams);
    assertEquals(expectedNumberOfAddCardSuccess, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(card("P1D")), 1),
        Arguments.of(List.of(card("P61D")), 0)
    );
  }

  private static Card card(String lookbackWindow) {
    return new Card()
        .dtCreated(OffsetDateTime.now().minus(Duration.parse(lookbackWindow)));
  }
}
