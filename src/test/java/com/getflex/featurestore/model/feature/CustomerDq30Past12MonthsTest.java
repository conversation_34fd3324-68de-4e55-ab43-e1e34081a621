package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.integration.flex.LedgerService.PARENT_ID_FLEX;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.time.Clock;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDq30Past12MonthsTest {

  private CustomerDq30Past12Months customerDq30Past12Months;
  private DelinquencyService delinquencyService;

  @Mock
  private LedgerService ledgerService;

  @Mock
  private Clock etClock;

  @BeforeEach
  public void setUp() {
    // Set up clock mocks
    Instant fixedInstant = Instant.parse("2024-08-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);
    
    // Create real DelinquencyService with mocked dependencies
    delinquencyService = new DelinquencyService(ledgerService, etClock);
    
    // Create feature instance
    customerDq30Past12Months = new CustomerDq30Past12Months(ledgerService, etClock, delinquencyService);
  }

  @Test
  public void testDq30Past12MonthsDueDateNewUserDueDateNotReached() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1000L)
        .dtCreated(OffsetDateTime.of(2024, 8, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayInTime() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 20, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayInTimeEarlyBp() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }


  @Test
  public void testDq30Past12MonthsPayLate() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateBpEarly() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 5, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateTwoBp() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 9, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    txList.add(new RecordLedgerWallet().billTransactionId("btx3").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2023, 8, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx3").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2023, 8, 1, 9, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx3").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2023, 10, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateButOutOfCutOfDate() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2023, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2023, 7, 1, 9, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2023, 9, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateWithCredit() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 9, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(800L)
        .dtCreated(OffsetDateTime.of(2024, 8, 15, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.CUSTOMER_CREDIT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(700L)
        .dtCreated(OffsetDateTime.of(2024, 8, 18, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.CHECK_REVERSAL.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateWithCreditRefund() {
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 28, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 5, 1, 9, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(800L)
        .dtCreated(OffsetDateTime.of(2024, 8, 15, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.CUSTOMER_CREDIT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(400L)
        .dtCreated(OffsetDateTime.of(2024, 8, 15, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.CUSTOMER_CREDIT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()).toParentIdentityId(PARENT_ID_FLEX));
    txList.add(new RecordLedgerWallet().billTransactionId("btx2").customerId(customerId).amount(700L)
        .dtCreated(OffsetDateTime.of(2024, 8, 18, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.CHECK_REVERSAL.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12Months.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12Months");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }
}
