package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.event.eventmetadata.BankIncomeVerificationMetadata;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.income.model.bank.BankModel;
import com.getflex.featurestore.model.feature.income.model.bank.VerifiedBankAccount;
import com.getflex.featurestore.model.feature.income.model.bank.VerifiedBankAccountOwner;
import com.getflex.featurestore.model.feature.income.model.bank.VerifiedBankAccounts;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import com.plaid.client.model.AccountType;
import com.plaid.client.model.BaseReport;
import com.plaid.client.model.BaseReportAccount;
import com.plaid.client.model.BaseReportAccountBalances;
import com.plaid.client.model.BaseReportAccountInsights;
import com.plaid.client.model.BaseReportAverageMonthlyBalances;
import com.plaid.client.model.BaseReportItem;
import com.plaid.client.model.BaseReportNumberFlowInsights;
import com.plaid.client.model.CraBankIncomeItem;
import com.plaid.client.model.CraBankIncomeSource;
import com.plaid.client.model.CraBankIncomeSummary;
import com.plaid.client.model.CraCheckReportBaseReportGetResponse;
import com.plaid.client.model.CraCheckReportIncomeInsightsGetResponse;
import com.plaid.client.model.CraIncomeInsights;
import com.plaid.client.model.CreditAmountWithCurrency;
import com.plaid.client.model.Owner;
import java.net.URISyntaxException;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BankIncomeVerificationFeatureTest {
  @Mock
  IncomeUtils incomeUtils;

  @Mock
  static Clock etClock;

  @Mock
  VerificationService verificationService;

  @InjectMocks
  BankIncomeVerificationFeature feature;

  @Test
  public void getBankFeatures_success() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").customerId(1L);

    Instant fixedInstant = Instant.now();
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);

    BaseReportAverageMonthlyBalances balance1 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(500.00)).endDate(LocalDate.now().minusMonths(4).toString());
    BaseReportAverageMonthlyBalances balance2 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(400.00)).endDate(LocalDate.now().minusMonths(2).toString());
    BaseReportAverageMonthlyBalances balance3 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(500.00)).endDate(LocalDate.now().minusMonths(1).toString());
    BaseReportAccountBalances balances = new BaseReportAccountBalances()
        .averageMonthlyBalances(List.of(balance1, balance2, balance3));

    BaseReportAccountInsights insights = new BaseReportAccountInsights()
        .numberOfOutflows(List.of(new BaseReportNumberFlowInsights().count(4).startDate(LocalDate.now())
            .endDate(LocalDate.now().minusDays(30))));
    BaseReportAccount account = new BaseReportAccount().type(AccountType.DEPOSITORY).name("My Account")
        .accountId("my-account-id")
        .officialName("My official account").owners(List.of(new Owner()
        .names(List.of("john smith", "Jane Smith")))).balances(balances).accountInsights(insights);
    BaseReportAccount account2 = new BaseReportAccount().type(AccountType.DEPOSITORY).name("My Account")
        .accountId("my-account-id-2")
        .officialName("My official account").owners(List.of(new Owner()
            .names(List.of("john smith", "Jane Smith")))).balances(balances).accountInsights(insights);
    BaseReportItem baseReportItem = new BaseReportItem().accounts(List.of(account, account2));
    VerifiedBankAccount verifiedBankAccount = VerifiedBankAccount.builder()
        .type(AccountType.DEPOSITORY).name("My Account").accountId("my-account-id")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john smith", "Jane Smith")).build())).build();
    VerifiedBankAccount verifiedBankAccount2 = VerifiedBankAccount.builder()
        .type(AccountType.DEPOSITORY).name("My Account").accountId("my-account-id-2")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john smith", "Jane Smith")).build())).build();
    VerifiedBankAccounts verifiedBankAccounts = VerifiedBankAccounts.builder()
        .needManualReviewBankAccounts(List.of())
        .nonPersonalDepositoryBankAccounts(List.of())
        .eligibleBankAccounts(List.of(verifiedBankAccount, verifiedBankAccount2))
        .build();
    CraCheckReportBaseReportGetResponse mockedResponse1 = new CraCheckReportBaseReportGetResponse()
        .report(new BaseReport().items(List.of(baseReportItem)));

    CraCheckReportIncomeInsightsGetResponse mockedResponse2 = new CraCheckReportIncomeInsightsGetResponse()
        .report(new CraIncomeInsights().bankIncomeSummary(new CraBankIncomeSummary().historicalAnnualGrossIncome(
            List.of(new CreditAmountWithCurrency().amount(120000d)))).items(
                List.of(new CraBankIncomeItem().bankIncomeSources(List.of(new CraBankIncomeSource()
                    .accountId("my-account-id").startDate(LocalDate.now().minusDays(10))
                    .endDate(LocalDate.now()).historicalAverageMonthlyGrossIncome(9000d))))));

    BankIncomeVerificationReports metadata = new BankIncomeVerificationReports(mockedResponse2, mockedResponse1);
    when(incomeUtils.getSelfReportedAnnualGrossIncomeCents(evalParams.getVerificationId())).thenReturn(6000000d);
    when(incomeUtils.getCustomerFullName(1L)).thenReturn("John Smith");
    when(incomeUtils.namesFuzzyMatch(any(), any())).thenReturn(true);
    when(incomeUtils.formatDouble(900000.00)).thenReturn(900000.00);

    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString(metadata));
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
            .context(new VerificationContext().plaidReportS3Uri("s3://path/to/file.json"))
        .id(evalParams.getVerificationId()));

    BankModel expectedFeatures = BankModel.builder()
        .totalAverageDailyBalanceCent(90000.00)
        .grossMonthlyIncomeCent(500000.00)
        .verifiedBankAccounts(verifiedBankAccounts)
        .verifiedGrossMonthlyIncomeCent(900000.00)
        .grossAnnualSelfReportedIncomeCent(6000000.00)
        .build();

    FeatureOutput output = feature.getValue(evalParams);

    Assertions.assertEquals(expectedFeatures, output.value());
  }

  @Test
  public void getBankFeatures_no_eligible_accounts() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").customerId(1L);

    Instant fixedInstant = Instant.now();
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);

    BaseReportAverageMonthlyBalances balance1 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(500.00)).endDate(LocalDate.now().minusMonths(4).toString());
    BaseReportAverageMonthlyBalances balance2 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(400.00)).endDate(LocalDate.now().minusMonths(2).toString());
    BaseReportAverageMonthlyBalances balance3 = new BaseReportAverageMonthlyBalances().averageBalance(
        new CreditAmountWithCurrency().amount(500.00)).endDate(LocalDate.now().minusMonths(1).toString());
    BaseReportAccountBalances balances = new BaseReportAccountBalances()
        .averageMonthlyBalances(List.of(balance1, balance2, balance3));

    BaseReportAccountInsights insights = new BaseReportAccountInsights()
        .numberOfOutflows(List.of(new BaseReportNumberFlowInsights().count(4).startDate(LocalDate.now())
            .endDate(LocalDate.now().minusDays(30))));
    BaseReportAccount account = new BaseReportAccount().type(AccountType.DEPOSITORY).name("My Account")
        .accountId("my-account-id")
        .officialName("My official account").owners(List.of(new Owner()
            .names(List.of("john smith", "Jane Doe")))).balances(balances).accountInsights(insights);
    BaseReportAccount account2 = new BaseReportAccount().type(AccountType.CREDIT).name("My Account")
        .accountId("my-account-id-2")
        .officialName("My official account").owners(List.of(new Owner()
            .names(List.of("john smith", "Jane Smith")))).balances(balances).accountInsights(insights);
    BaseReportAccount account3 = new BaseReportAccount().type(AccountType.DEPOSITORY).name("My Account")
        .accountId("my-account-id-3")
        .officialName("My official account").owners(List.of(new Owner()
            .names(List.of("john's business", "Jane's LLC")))).balances(balances).accountInsights(insights);
    BaseReportItem baseReportItem = new BaseReportItem().accounts(List.of(account, account2, account3));
    VerifiedBankAccount verifiedBankAccount = VerifiedBankAccount.builder()
        .type(AccountType.DEPOSITORY).name("My Account").accountId("my-account-id")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john smith", "Jane Doe")).build())).build();
    VerifiedBankAccount verifiedBankAccount2 = VerifiedBankAccount.builder()
        .type(AccountType.CREDIT).name("My Account").accountId("my-account-id-2")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john smith", "Jane Smith")).build())).build();
    VerifiedBankAccount verifiedBankAccount3 = VerifiedBankAccount.builder()
        .type(AccountType.DEPOSITORY).name("My Account").accountId("my-account-id-3")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john's business", "Jane's LLC")).build())).build();
    VerifiedBankAccounts verifiedBankAccounts = VerifiedBankAccounts.builder()
        .needManualReviewBankAccounts(List.of())
        .nonPersonalDepositoryBankAccounts(List.of(verifiedBankAccount, verifiedBankAccount2, verifiedBankAccount3))
        .eligibleBankAccounts(List.of())
        .build();
    CraCheckReportBaseReportGetResponse mockedResponse1 = new CraCheckReportBaseReportGetResponse()
        .report(new BaseReport().items(List.of(baseReportItem)));

    CraCheckReportIncomeInsightsGetResponse mockedResponse2 = new CraCheckReportIncomeInsightsGetResponse()
        .report(new CraIncomeInsights().bankIncomeSummary(new CraBankIncomeSummary().historicalAnnualGrossIncome(
            List.of(new CreditAmountWithCurrency().amount(120000d)))).items(
            List.of(new CraBankIncomeItem().bankIncomeSources(List.of(new CraBankIncomeSource()
                .accountId("my-account-id").startDate(LocalDate.now().minusDays(10))
                .endDate(LocalDate.now()).historicalAverageMonthlyGrossIncome(9000d))))));

    BankIncomeVerificationReports metadata = new BankIncomeVerificationReports(mockedResponse2, mockedResponse1);
    when(incomeUtils.getSelfReportedAnnualGrossIncomeCents(evalParams.getVerificationId())).thenReturn(6000000d);
    when(incomeUtils.getCustomerFullName(1L)).thenReturn("John Smith");
    when(incomeUtils.namesFuzzyMatch(any(), any())).thenReturn(false);
    when(incomeUtils.formatDouble(0.00)).thenReturn(0.00);

    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString(metadata));
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().plaidReportS3Uri("s3://path/to/file.json"))
        .id(evalParams.getVerificationId()));

    BankModel expectedFeatures = BankModel.builder()
        .totalAverageDailyBalanceCent(0.00)
        .grossMonthlyIncomeCent(0.00)
        .verifiedBankAccounts(verifiedBankAccounts)
        .verifiedGrossMonthlyIncomeCent(0.00)
        .grossAnnualSelfReportedIncomeCent(6000000.00)
        .build();

    FeatureOutput output = feature.getValue(evalParams);

    Assertions.assertEquals(expectedFeatures, output.value());
  }

  @Test
  public void getBankFeatures_alreadyVerified_success() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").customerId(1L);

    BaseReportAccountBalances balances = new BaseReportAccountBalances();
    BaseReportAccountInsights insights = new BaseReportAccountInsights();
    BaseReportAccount account = new BaseReportAccount().type(AccountType.DEPOSITORY).name("My Account")
        .accountId("my-account-id")
        .officialName("My official account").owners(List.of(new Owner()
            .names(List.of("john smith", "Jane Smith")))).balances(balances).accountInsights(insights);
    BaseReportItem baseReportItem = new BaseReportItem().accounts(List.of(account));
    VerifiedBankAccount verifiedBankAccount = VerifiedBankAccount.builder()
        .type(AccountType.DEPOSITORY).name("My Account").accountId("my-account-id")
        .officialName("My official account").owners(List.of(VerifiedBankAccountOwner.builder()
            .names(List.of("john smith", "Jane Smith")).build())).build();
    VerifiedBankAccounts verifiedBankAccounts = VerifiedBankAccounts.builder()
        .needManualReviewBankAccounts(List.of())
        .nonPersonalDepositoryBankAccounts(List.of())
        .eligibleBankAccounts(List.of(verifiedBankAccount))
        .build();
    CraCheckReportBaseReportGetResponse mockedResponse1 = new CraCheckReportBaseReportGetResponse()
        .report(new BaseReport().items(List.of(baseReportItem)));

    CraCheckReportIncomeInsightsGetResponse mockedResponse2 = new CraCheckReportIncomeInsightsGetResponse()
        .report(new CraIncomeInsights().bankIncomeSummary(new CraBankIncomeSummary().historicalAnnualGrossIncome(
            List.of(new CreditAmountWithCurrency().amount(120000d)))).items(
            List.of(new CraBankIncomeItem().bankIncomeSources(List.of(new CraBankIncomeSource()
                .accountId("my-account-id").startDate(LocalDate.now().minusDays(10))
                .endDate(LocalDate.now()).historicalAverageMonthlyGrossIncome(9000d))))));

    BankIncomeVerificationReports metadata = new BankIncomeVerificationReports(mockedResponse2, mockedResponse1);
    when(incomeUtils.getSelfReportedAnnualGrossIncomeCents(evalParams.getVerificationId())).thenReturn(2400000d);
    when(incomeUtils.getCustomerFullName(1L)).thenReturn("John Smith");
    when(incomeUtils.namesFuzzyMatch(any(), any())).thenReturn(true);

    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString(metadata));
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().validationOverride(new Validation().grossMonthlyIncomeCent(3000000L)
            .totalAverageDailyBalanceCent(600000L)).plaidReportS3Uri("s3://path/to/file.json"))
        .status("COMPLETED").id(evalParams.getVerificationId()));

    BankModel expectedFeatures = BankModel.builder()
        .totalAverageDailyBalanceCent(600000.00)
        .grossMonthlyIncomeCent(200000.00)
        .verifiedBankAccounts(verifiedBankAccounts)
        .verifiedGrossMonthlyIncomeCent(3000000.00)
        .grossAnnualSelfReportedIncomeCent(2400000.00).build();

    FeatureOutput output = feature.getValue(evalParams);

    Assertions.assertEquals(expectedFeatures, output.value());
  }

  @Test
  public void getBankFeatures_notFound() {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().validationOverride(new Validation().grossMonthlyIncomeCent(3000000L)
            .totalAverageDailyBalanceCent(600000L)).plaidReportS3Uri(null))
        .status("COMPLETED").id(evalParams.getVerificationId()));

    Assertions.assertThrows(FeatureNotFoundException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getBankFeatures_failure() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);

    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString("fake fake fake"));

    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().validationOverride(new Validation().grossMonthlyIncomeCent(3000000L)
            .totalAverageDailyBalanceCent(600000L)).plaidReportS3Uri("s3://path/to/file.json"))
        .status("COMPLETED").id(evalParams.getVerificationId()));

    Assertions.assertThrows(EventMetadataParsingException.class, () -> feature.getValue(evalParams));
  }
}
