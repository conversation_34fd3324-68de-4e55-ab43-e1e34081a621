package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.RestingPeriodMetadata;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.EvalParams.OnboardingDomainEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class RestingPeriodFeatureTest {

  @Mock
  EventRepository eventRepository;

  @InjectMocks
  RestingPeriodFeature feature;

  @ParameterizedTest
  @CsvSource("""
      CREDIT, ONBOARDING_CREDIT_RESTING
      FRAUD, ONBOARDING_FRAUD_RESTING
      """)
  public void getValue_success(OnboardingDomainEnum onboardingDomain, EventName eventName)
      throws JsonProcessingException {
    RestingPeriodMetadata metadata1 = new RestingPeriodMetadata();
    metadata1.setFailureTimestamp(OffsetDateTime.now().minusDays(59));
    Event event1 = new Event();
    event1.setMetadata(OBJECT_MAPPER.writeValueAsString(metadata1));

    RestingPeriodMetadata metadata2 = new RestingPeriodMetadata();
    metadata2.setFailureTimestamp(OffsetDateTime.now().minusDays(60));
    Event event2 = new Event();
    event2.setMetadata(OBJECT_MAPPER.writeValueAsString(metadata2));

    EvalParams params = new EvalParams();
    params.setOnboardingDomain(onboardingDomain);
    params.setCustomerId(1L);
    params.setSsnHmac("123");
    when(eventRepository.findAllByNameAndEntityIdOrderByDtArrivedDesc(
        eventName, "123")).thenReturn(List.of(event1, event2));

    FeatureOutput output = feature.getValue(params);
    Assertions.assertEquals(2L, output.value());
  }

  @Test
  public void getValue_failure() {
    when(eventRepository.findAllByNameAndEntityIdOrderByDtArrivedDesc(any(), any()))
        .thenThrow(new RuntimeException());
    EvalParams params = new EvalParams();
    params.setOnboardingDomain(OnboardingDomainEnum.CREDIT);
    params.setCustomerId(1L);
    params.setSsnHmac("123");
    FeatureOutput output = feature.getValue(params);
    Assertions.assertEquals(output.value(), 0L);
  }
}
