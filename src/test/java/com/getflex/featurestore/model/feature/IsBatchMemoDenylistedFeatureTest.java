package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsBatchMemoDenylistedFeatureTest {

  @InjectMocks
  IsBatchMemoDenylistedFeature feature;

  @Mock
  AllowDenyListRepo allowDenyListRepo;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValue(boolean exists) {
    String batchMemo = "memo";
    AllowDenyList entity = new AllowDenyList(IsBatchMemoDenylistedFeature.BatchMemoDenylistUseCase,
        batchMemo, false);
    if (exists) {
      when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(
          IsBatchMemoDenylistedFeature.BatchMemoDenylistUseCase,
          batchMemo, false)).thenReturn(entity);
    } else {
      when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(
          IsBatchMemoDenylistedFeature.BatchMemoDenylistUseCase,
          batchMemo, false)).thenReturn(null);
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setBatchMemo(batchMemo);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getName(), "IsBatchMemoDenylistedFeature");
    assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(featureValue.getValue(), exists);
  }
}
