package com.getflex.featurestore.model.feature;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.BillerAccountRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class IsCustomerFlexAnywhereByCustomerIdFeatureTest {

  private final Long customerId = 123L;
  private long billerId = 1L;
  private final String featureName = "IsCustomerFlexAnywhereByCustomerIdFeature";

  @InjectMocks
  IsCustomerFlexAnywhereByCustomerIdFeature featureInstance;

  @Mock
  BillingService billingService;

  @Mock
  IdentityService identityService;

  /**
   * Test case for IsCustomerFlexAnywhereByCustomerIdFeatureTest when IntegrationType is FLEX_ANYWHERE
   */
  @Test
  public void isCustomerFlexAnywhereByCustomerIdFeatureHappyPathTest() {
    // mock data for billing service
    ComGetflexBillingControllerV2PropertyControllerPropertyResponse mockPropertyResponse =
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse();
    mockPropertyResponse.setIntegrationType(
        ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum.FLEX_ANYWHERE);

    // mock data for identity service
    BillerAccountRecord billerAccountRecord = new BillerAccountRecord();
    billerAccountRecord.setBillerId(billerId);

    Mockito.when(identityService.getActiveBillerAccountByCustomerId(customerId)).thenReturn(billerAccountRecord);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /**
   * Test case for IsCustomerFlexAnywhereByCustomerIdFeatureTest when IntegrationType is not FLEX_ANYWHERE
   */
  @Test
  public void isCustomerFlexAnywhereByCustomerIdFeatureNegativeTest() {
    // mock data for billing service
    ComGetflexBillingControllerV2PropertyControllerPropertyResponse mockPropertyResponse =
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse();
    mockPropertyResponse.setIntegrationType(
        ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum.DIRECT_INTEGRATION);

    // mock data for identity service
    BillerAccountRecord billerAccountRecord = new BillerAccountRecord();
    billerAccountRecord.setBillerId(billerId);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    Mockito.when(identityService.getActiveBillerAccountByCustomerId(evalParams.getCustomerId()))
            .thenReturn(billerAccountRecord);
    Mockito.when(billingService.getBillerInfoByBillerId(billerId)).thenReturn(mockPropertyResponse);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /**
   * Test case for IsCustomerFlexAnywhereByCustomerIdFeatureTest when identity Service returns null
   */
  @Test
  public void isCustomerFlexAnywhereByCustomerIdFeatureNullIdentityTest() {
    // mock data for identity service
    Mockito.when(identityService.getActiveBillerAccountByCustomerId(customerId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /**
   * Test case for IsCustomerFlexAnywhereByCustomerIdFeatureTest when billing service return null
   */
  @Test
  public void isCustomerFlexAnywhereByCustomerIdFeatureNullBillingTest() {
    // mock data for identity service
    BillerAccountRecord billerAccountRecord = new BillerAccountRecord();
    billerAccountRecord.setBillerId(billerId);

    Mockito.when(identityService.getActiveBillerAccountByCustomerId(customerId)).thenReturn(billerAccountRecord);
    Mockito.when(billingService.getBillerInfoByBillerId(billerId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }
}
