package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.similarity.AddressLevelBatchMemoNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.BillerLevelBatchMemoNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.BillerPmcNameBatchMemoNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.BillerPropertyNameBatchMemoNlpSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.CustomerLevelBatchMemoNlpSimilarityScoreFeature;
import com.getflex.identity.model.GetCustomerResponse;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NlpSimilarityScoreFeatureTest {

  @InjectMocks
  AddressLevelBatchMemoNlpSimilarityScoreFeature addressLevelBatchMemoNlpSimilarityScoreFeature;

  @InjectMocks
  BillerLevelBatchMemoNlpSimilarityScoreFeature billerLevelBatchMemoNlpSimilarityScoreFeature;

  @InjectMocks
  CustomerLevelBatchMemoNlpSimilarityScoreFeature customerLevelBatchMemoNlpSimilarityScoreFeature;

  @InjectMocks
  BillerPmcNameBatchMemoNlpSimilarityScoreFeature billerPmcNameBatchMemoNlpSimilarityScoreFeature;

  @InjectMocks
  BillerPropertyNameBatchMemoNlpSimilarityScoreFeature billerPropertyNameBatchMemoNlpSimilarityScoreFeature;

  @InjectMocks
  BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature
      billerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  IdentityService identityService;

  @Mock
  BillingService billingService;


  @Mock
  SageMakerRuntimeClient sagemaker;

  @Test
  public void testAddressLevelNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    when(identityService.getCustomer(123L)).thenReturn(
        new GetCustomerResponse().customerId(123L).addressLine1("715thave").city("New York")
            .state("NY").zip("10001")
    );
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("address_level_batch_memo_history")
            .primaryKey("715thave&newyork&ny&10001").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "address_level_batch_memo_history", "newyork&ny&10001")).thenReturn(feature);
    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = addressLevelBatchMemoNlpSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "AddressLevelBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }

  @Test
  public void testBillerLevelNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("biller_level_batch_memo_history")
            .primaryKey("123L").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "biller_level_batch_memo_history", "123")).thenReturn(feature);
    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = billerLevelBatchMemoNlpSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillerLevelBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }

  @Test
  public void testCustomerLevelNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("customer_level_batch_memo_history")
            .primaryKey("123").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "customer_level_batch_memo_history", "123")).thenReturn(feature);
    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = customerLevelBatchMemoNlpSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "CustomerLevelBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }

  @Test
  public void testPropertyNameNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    when(identityService.getPropertyNameByBillerAccountId(123L)).thenReturn("PropertyName");

    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = billerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }

  @Test
  public void testBillerPropertyNameNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");
    evalParams.setBillerId(234L);

    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().name("71 5th Avenue")
    );

    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = billerPropertyNameBatchMemoNlpSimilarityScoreFeature
        .fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillerPropertyNameBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }

  @Test
  public void testBillerPmcNameNlpSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");
    evalParams.setBillerId(234L);

    when(billingService.getPropertyByBillerId(234L)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse()
            .pmcName("71 5th Avenue")
    );

    InvokeEndpointResponse res = InvokeEndpointResponse.builder()
        .body(SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();
    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    FeatureValue featureValue = billerPmcNameBatchMemoNlpSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillerPmcNameBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.7);
  }
}
