package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.offerv2.model.OverallState;
import com.getflex.wallet.model.Card;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDistinctCardZipCodeCountFeatureTest {

  @InjectMocks
  CustomerDistinctCardZipCodesCountFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  WalletService walletService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(List<Card> cards, Integer expectedResult) {
    Long customerId = 123L;
    String customerPublicId = UUID.randomUUID().toString();
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    when(walletService.getCards(customerPublicId)).thenReturn(cards);
    FeatureValue featureValue = feature.fetchFeatureValue(new EvalParams().customerId(customerId));
    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(new Card().addressZipcode("12345")), 1),
        Arguments.of(
            List.of(
                new Card().addressZipcode("12345"),
                new Card().addressZipcode("12345")
            ),
            1
        ),
        Arguments.of(
            List.of(
                new Card().addressZipcode("12345"),
                new Card().addressZipcode("67891")
            ),
            2
        )
    );
  }
}
