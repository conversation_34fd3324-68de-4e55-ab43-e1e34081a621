package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.GetCustomerBillResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionBillAmountCentsFromPaymentFeatureTest {

  @InjectMocks
  BillTransactionBillAmountCentsFromPaymentFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  PaymentService paymentService;

  @Test
  public void testBillWithOverwriteAmount() {
    String btxId = "abcdeft";
    Long customerId = 1L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    when(paymentService.getCustomerBill(customerPublicId, btxId)).thenReturn(
        new GetCustomerBillResponse().amountOverwrite(1000L).amount(2200L)
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId).customerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionBillAmountCentsFromPaymentFeature");
    Assertions.assertEquals(featureValue.getValue(), 1000);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetBillWithoutOverwriteAmount() {
    String btxId = "abcdeft";
    Long customerId = 1L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    when(paymentService.getCustomerBill(customerPublicId, btxId)).thenReturn(
        new GetCustomerBillResponse().amount(2200L)
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId).customerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionBillAmountCentsFromPaymentFeature");
    Assertions.assertEquals(featureValue.getValue(), 2200);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetBillNoBill() {
    String btxId = "abcdeft";
    Long customerId = 1L;
    String customerPublicId = "customerPublicId";
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    when(paymentService.getCustomerBill(customerPublicId, btxId)).thenReturn(
        null
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId).customerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "BillTransactionBillAmountCentsFromPaymentFeature");
    Assertions.assertEquals(featureValue.getValue(), 0);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
