package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class HasPendingWithdrawalLast24HoursFeatureTest {

  @InjectMocks
  HasPendingWithdrawalLast24HoursFeature hasPendingWithdrawalLast24HoursFeature;

  @Mock
  LedgerService ledgerService;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean hasPendingWithdrawal) {
    Long customerId = 1L;
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    RecordLedger fundsInRecordLedger = new RecordLedger()
        .customerId(customerId)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .paymentStatusId(PaymentState.INITIATED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now().minusMinutes(1));
    List<RecordLedger> fundsInRecordLedgers = List.of(fundsInRecordLedger);
    if (hasPendingWithdrawal) {
      when(ledgerService.retrieveLedgerByCustomerId(any()))
          .thenReturn(fundsInRecordLedgers);
    } else {
      when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());
    }
    FeatureValue featureValue = hasPendingWithdrawalLast24HoursFeature.fetchFeatureValue(evalParams);
    assertEquals(hasPendingWithdrawal, featureValue.getValue());
  }


}