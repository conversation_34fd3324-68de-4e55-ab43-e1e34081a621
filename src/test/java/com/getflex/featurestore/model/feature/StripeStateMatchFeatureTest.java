package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.wallet.model.Card;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StripeStateMatchFeatureTest {

  @InjectMocks
  StripeStateMatchFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  WalletService walletService;


  @Test
  public void testGetValue() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    evalParams.setCardFingerprint("aaa");
    when(identityService.getCustomer(123L)).thenReturn(new GetCustomerResponse().state("NY"));
    when(walletService.getCardsByFingerprint("aaa")).thenReturn(List.of(new Card().addressState("NY")));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals(true, featureValue.getValue());
  }
}
