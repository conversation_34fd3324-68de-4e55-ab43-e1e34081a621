package com.getflex.featurestore.model.feature.base.param;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.getflex.featurestore.model.EvalParamKey;
import java.util.Set;
import org.junit.jupiter.api.Test;

public class ParamRequiredTest {
  @Test
  void any_atLeastOneParam() {
    assertThrows(IllegalArgumentException.class, () -> Any.of());
  }
  
  @Test
  void all_nothingRequired() {
    All.of().validateEvaluationParams(Set.of());
    All.of().validateEvaluationParams(Set.of(EvalParamKey.BILLER_ACCOUNT_ID));
    All.of().validateEvaluationParams(Set.of(EvalParamKey.KYC_ID));
  }

  @Test
  void complexLogic() {
    ParamRequired requirement = new All(Set.of(All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BATCH_MEMO),
        Any.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.OFFER_ID)));

    assertThrows(IllegalArgumentException.class, () -> requirement.validateEvaluationParams(Set.of()));
    assertThrows(IllegalArgumentException.class,
        () -> requirement.validateEvaluationParams(Set.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BILLER_ACCOUNT_ID)));
    assertThrows(IllegalArgumentException.class,
        () -> requirement.validateEvaluationParams(Set.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BATCH_MEMO)));
    assertThrows(IllegalArgumentException.class,
        () -> requirement.validateEvaluationParams(Set.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.OFFER_ID)));
    requirement.validateEvaluationParams(
        Set.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BATCH_MEMO, EvalParamKey.BILLER_ACCOUNT_ID));
    requirement.validateEvaluationParams(
        Set.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.BATCH_MEMO, EvalParamKey.OFFER_ID));
  }
}
