package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelV2Input;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import java.util.List;
import java.util.concurrent.ExecutorService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelV2ScoreFeatureTest {

  @InjectMocks
  FraudTransactionModelV2ScoreFeature feature;

  @Mock
  SageMakerRuntimeClient sageMaker;

  @Mock
  ServiceConfig serviceConfig;

  @Mock
  FeatureFactory featureFactory;

  @Mock
  ExecutorService executorService;

  @Test
  public void modelFeatureOrderList() {
    List<FraudTransactionModelV2Input> modelFeatures = feature.getModelFeaturesList();
    Assertions.assertEquals(33, modelFeatures.size());

    Assertions.assertEquals(FraudTransactionModelV2Input.IDENTITY_THEFT_SCORE, modelFeatures.get(0));
    Assertions.assertEquals(FraudTransactionModelV2Input.ABUSE_SCORE, modelFeatures.get(1));
    Assertions.assertEquals(FraudTransactionModelV2Input.NAME_MATCH_FLAG, modelFeatures.get(2));
    Assertions.assertEquals(FraudTransactionModelV2Input.CMM2_SCORE, modelFeatures.get(3));
    Assertions.assertEquals(FraudTransactionModelV2Input.TOTAL_INSUFFICIENT_DECL, modelFeatures.get(4));
    Assertions.assertEquals(FraudTransactionModelV2Input.CARD_DECLINE_COUNT, modelFeatures.get(5));
    Assertions.assertEquals(FraudTransactionModelV2Input.CARD_SUPPORT_FAIL_COUNT_V2, modelFeatures.get(6));
    Assertions.assertEquals(FraudTransactionModelV2Input.INVALID_ACCOUNT_COUNT, modelFeatures.get(7));
    Assertions.assertEquals(FraudTransactionModelV2Input.TOT_DIST_CARD, modelFeatures.get(8));
    Assertions.assertEquals(FraudTransactionModelV2Input.TOTAL_SUCESSFUL_PAYMENT, modelFeatures.get(9));
    Assertions.assertEquals(FraudTransactionModelV2Input.CARD_FUNDING_FLAG, modelFeatures.get(10));
    Assertions.assertEquals(FraudTransactionModelV2Input.ADDRESS_CHECK_FLAG, modelFeatures.get(11));
    Assertions.assertEquals(FraudTransactionModelV2Input.POSTAL_MATCH_FLAG_V2, modelFeatures.get(12));
    Assertions.assertEquals(FraudTransactionModelV2Input.MOBILE_VERIFY, modelFeatures.get(13));
    Assertions.assertEquals(FraudTransactionModelV2Input.FA_IND, modelFeatures.get(14));
    Assertions.assertEquals(FraudTransactionModelV2Input.TOTAL_DISPUTE_PRE, modelFeatures.get(15));
    Assertions.assertEquals(FraudTransactionModelV2Input.FNAME_EDIT_DISTANCE, modelFeatures.get(16));
    Assertions.assertEquals(FraudTransactionModelV2Input.LNAME_EDITDISTANCE, modelFeatures.get(17));
    Assertions.assertEquals(FraudTransactionModelV2Input.SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE, modelFeatures.get(18));
    Assertions.assertEquals(FraudTransactionModelV2Input.SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE, modelFeatures.get(19));
    Assertions.assertEquals(FraudTransactionModelV2Input.RISKY_CARRIER_IND, modelFeatures.get(20));
    Assertions.assertEquals(FraudTransactionModelV2Input.SIGMA_SCORE, modelFeatures.get(21));
    Assertions.assertEquals(FraudTransactionModelV2Input.ADDRESSRISK_SCORE, modelFeatures.get(22));
    Assertions.assertEquals(FraudTransactionModelV2Input.EMAILRISK_SCORE, modelFeatures.get(23));
    Assertions.assertEquals(FraudTransactionModelV2Input.NAMEADDRESSCORRELATION, modelFeatures.get(24));
    Assertions.assertEquals(FraudTransactionModelV2Input.NAMEEMAILCORRELATION, modelFeatures.get(25));
    Assertions.assertEquals(FraudTransactionModelV2Input.NAMEPHONECORRELATION, modelFeatures.get(26));
    Assertions.assertEquals(FraudTransactionModelV2Input.PHONERISK_SCORE, modelFeatures.get(27));
    Assertions.assertEquals(FraudTransactionModelV2Input.SYNTHETIC_SCORE, modelFeatures.get(28));
    Assertions.assertEquals(FraudTransactionModelV2Input.PAYMENT_SEG, modelFeatures.get(29));
    Assertions.assertEquals(FraudTransactionModelV2Input.LNAME_JAROW, modelFeatures.get(30));
    Assertions.assertEquals(FraudTransactionModelV2Input.FNAME_JAROW, modelFeatures.get(31));
    Assertions.assertEquals(FraudTransactionModelV2Input.ADDRESS1_JAROW, modelFeatures.get(32));
  }
}
