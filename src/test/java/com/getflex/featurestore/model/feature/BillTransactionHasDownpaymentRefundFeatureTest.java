package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.ledger.model.RecordLedger;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionHasDownpaymentRefundFeatureTest {
  @InjectMocks
  BillTransactionHasDownpaymentRefundFeature feature;

  @Mock
  LedgerService ledgerService;

  @Test
  public void testGetValue() {
    String btxId = "abcdeft";
    when(ledgerService.getDownpaymentRecords(btxId, MovementCategory.REFUND)).thenReturn(
        List.of(new RecordLedger())
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "BillTransactionHasDownpaymentRefundFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testNoValue() {
    String btxId = "abcdeft";
    when(ledgerService.getDownpaymentRecords(btxId, MovementCategory.REFUND)).thenReturn(null);
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "BillTransactionHasDownpaymentRefundFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
