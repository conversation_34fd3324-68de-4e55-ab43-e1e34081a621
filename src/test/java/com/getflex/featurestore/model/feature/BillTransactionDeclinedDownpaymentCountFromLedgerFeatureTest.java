package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.BillTransactionDeclinedDownpaymentCountFromLedgerFeature.DownpaymentDeclineInfo;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class BillTransactionDeclinedDownpaymentCountFromLedgerFeatureTest {

  private static final long AMOUNT = 123321L;
  private static final OffsetDateTime MOST_RECENT_DECLINE_DT = OffsetDateTime.of(2024, 8, 28, 10, 15, 0, 0,
      ZoneOffset.UTC);

  @InjectMocks
  BillTransactionDeclinedDownpaymentCountFromLedgerFeature feature;

  @Mock
  LedgerService ledgerService;

  @Test
  void testGetValue() {
    List<RecordLedger> records = new ArrayList<>();
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue())
        .paymentStatusId(PaymentState.DECLINED.getValue())
        .amount(AMOUNT)
        .dtCreated(OffsetDateTime.of(2024, 6, 28, 10, 15, 0, 0, ZoneOffset.UTC))
    );
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue())
        .paymentStatusId(PaymentState.DECLINED.getValue())
        .amount(AMOUNT)
        .dtCreated(MOST_RECENT_DECLINE_DT)
    );
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue())
        .amount(AMOUNT)
    );
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue())
        .amount(AMOUNT)
    );
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue())
        .amount(AMOUNT)
    );
    // this last record shouldn't happen, but including to check against money movement category.
    records.add(new RecordLedger()
        .paymentCategoryId(MovementCategory.CAPTURE.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue())
        .amount(AMOUNT)
        .dtCreated(OffsetDateTime.of(2025, 1, 28, 10, 15, 0, 0, ZoneOffset.UTC))
    );

    when(ledgerService.getLedgersByBillTransactionId(any(), any(), any())).thenReturn(records);

    FeatureOutput result = feature.getValue(new EvalParams());

    assertEquals(new DownpaymentDeclineInfo(2, MOST_RECENT_DECLINE_DT),
        result.value());
  }


  @Test
  void testGetValueEmpty() {
    when(ledgerService.getLedgersByBillTransactionId(any(), any(), any())).thenReturn(List.of());

    assertEquals(new DownpaymentDeclineInfo(0, null), feature.getValue(new EvalParams()).value());
  }
}
