package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.FulfillmentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.fulfillment.model.FulfillmentCode;
import com.getflex.fulfillment.model.GetStatusResponseV2;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionHasSuspiciousFulfillmentCodeFeatureTest {
  @InjectMocks
  BillTransactionHasSuspiciousFulfillmentCodeFeature feature;

  @Mock
  FulfillmentService fulfillmentService;

  @Test
  public void testNoSuspiciousCode() {
    String btxId = "abcdeft";
    when(fulfillmentService.getStatusCode(btxId)).thenReturn(new GetStatusResponseV2().codes(List.of(
        FulfillmentCode.SELFPAY_DDA_OFFERED.toString()
        ))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "BillTransactionHasSuspiciousFulfillmentCodeFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testHasSuspiciousCode() {
    String btxId = "abcdeft";
    when(fulfillmentService.getStatusCode(btxId)).thenReturn(new GetStatusResponseV2().codes(List.of(
            FulfillmentCode.SELFPAY_VC_OFFERED.toString(), FulfillmentCode.SELFPAY_DDA_OFFERED.toString()
        ))
    );
    EvalParams evalParams = new EvalParams().billTransactionId(btxId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "BillTransactionHasSuspiciousFulfillmentCodeFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void getStatusFail() {
    when(fulfillmentService.getStatusCode(any()))
        .thenThrow(new InternalDependencyFailureException("404 FF status not found"));
    EvalParams evalParams = new EvalParams().billTransactionId("testBtxId");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(true, featureValue.getValue());
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
