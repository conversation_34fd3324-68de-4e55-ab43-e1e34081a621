package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfAddCardAttemptsFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  EventRepository eventRepository;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<Event> events,
      Integer expectedNumberOfCardAttempts
  ) {
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(CUSTOMER_ID);
    Mockito.when(
        eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.STRIPE_SETUP_ATTEMPT,
            CUSTOMER_ID.toString()
        )
    ).thenReturn(
            events
    );
    var featureValue = new NumberOfAddCardAttemptsFeature(
        eventRepository, new LookbackDurationFeatureParams("P7D")
    ).fetchFeatureValue(evalParams);
    assertEquals(expectedNumberOfCardAttempts, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    var filteredEvent = event("id");
    filteredEvent.setDtArrived(OffsetDateTime.now().minusDays(8));
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(event("id")), 1),
        Arguments.of(List.of(event("sameId"), event("sameId")), 1),
        Arguments.of(List.of(event("id"), event("id2")), 2),
        Arguments.of(List.of(filteredEvent), 0)
    );
  }

  private static Event event(String id) {
    return Event.builder()
        .name(EventName.STRIPE_SETUP_ATTEMPT)
        .customerId(CUSTOMER_ID.toString())
        .metadata("{\"id\": \"%s\"}".formatted(id))
        .dtArrived(OffsetDateTime.now())
        .build();
  }
}
