package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptEventWith3ds;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerTriggered3DsCountFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @InjectMocks
  CustomerTriggered3DsCountFeature feature;

  @Mock
  EventRepository eventRepository;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(List<Event> events, Integer expectedValue) {
    when(eventRepository.findAllByNameAndCustomerIdOrderByDtArrivedDesc(
            eq(EventName.STRIPE_SETUP_ATTEMPT), eq(CUSTOMER_ID.toString())
        )
    ).thenReturn(events);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals("CustomerTriggered3DsCountFeature", featureValue.getName());
    assertEquals(expectedValue, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() throws JsonProcessingException {
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result(null)
                        .authenticationFlow(null)
                )
            ),
            0
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result("success")
                        .authenticationFlow("challenge")
                )
            ),
            1
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .authenticationFlow("frictionless")
                )
            ),
            1
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .authenticationFlow("challenge")
                )
            ),
            1
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result("succeeded")
                        .authenticationFlow("frictionless")
                ),
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result("failed")
                        .authenticationFlow("challenge")
                )
            ),
            2
        )
    );
  }
}
