package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.model.feature.base.AbstractFlexScoreFeature.OBJECT_MAPPER;
import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.flexscore.alloy.AlloyReport;
import com.getflex.featurestore.utils.AlloyUtils;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CreditReportFeatureTest {
  @InjectMocks
  CreditReportFeature feature;

  @Mock
  S3Client s3Client;

  @Mock
  AlloyUtils alloyUtils;

  @Captor
  ArgumentCaptor<GetObjectRequest> getObjectRequest;

  @ParameterizedTest
  @ValueSource(strings = {
      "https://bucket/folder/key.json?a=b",
      "http://bucket.s3.amazonaws.com:8080/folder/key.json"
  })
  void fetchFeatureValue_success(String alloyReportUri) throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));

    FeatureOutput result = feature.getValue(
        new EvalParams().alloyReportUrl(alloyReportUri));

    assertNotNull(result);
    assertNull(result.metadata());

    assertInstanceOf(Map.class, result.value());
    Map<String, Object> dataMap = (Map<String, Object>) result.value();
    assertFalse(dataMap.isEmpty());
    assertEquals(1775L, dataMap.get("TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT"));
    assertEquals(45.0D, dataMap.get("EADS11_CV23"));
    assertTrue(dataMap.containsKey("COLLECTIONS_BALANCE_NO_MEDICAL"));
    assertNull(dataMap.get("COLLECTIONS_BALANCE_NO_MEDICAL"));
    assertEquals(false, dataMap.get("IS_THIN_CREDIT_FILE"));
    assertEquals(false, dataMap.get("IS_CREDIT_REPORT_SUBJECT_DECEASED"));
    assertEquals(true, dataMap.get("IS_INITIAL_FRAUD_ALERT_REPORT"));
    assertEquals(true, dataMap.get("IS_FRAUD_ALERT_REPORT"));
    assertEquals(false, dataMap.get("IS_CREDIT_REPORT_SUPPRESSED"));
    assertEquals(false, dataMap.get("IS_CREDIT_REPORT_FROZEN"));
    assertEquals(-1, dataMap.get("MONTHS_SINCE_LAST_BANKRUPTCY_RECORD"));
  }

  @Test
  void fetchFeatureValue_thinFile_success() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "06-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));

    FeatureOutput result = feature.getValue(
        new EvalParams().alloyReportUrl("s3://test-s3-bucket/key.json"));

    assertNotNull(result);
    assertNull(result.metadata());

    assertInstanceOf(Map.class, result.value());
    Map<String, Object> dataMap = (Map<String, Object>) result.value();
    assertFalse(dataMap.isEmpty());
    assertEquals(true, dataMap.get("IS_THIN_CREDIT_FILE"));
    assertEquals(false, dataMap.get("IS_MISSING_CREDIT_FILE"));
  }

  @Test
  void fetchFeatureValue_missingFile_success() throws IOException {
    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "07-input.json").readAllBytes();
    when(alloyUtils.downloadAlloyReport(any())).thenReturn(OBJECT_MAPPER.readValue(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent).asInputStream(), AlloyReport.class));

    FeatureOutput result = feature.getValue(
      new EvalParams().alloyReportUrl("s3://test-s3-bucket/key.json"));

    assertNotNull(result);
    assertNull(result.metadata());

    assertInstanceOf(Map.class, result.value());
    Map<String, Object> dataMap = (Map<String, Object>) result.value();
    assertFalse(dataMap.isEmpty());
    assertEquals(false, dataMap.get("IS_THIN_CREDIT_FILE"));
    assertEquals(true, dataMap.get("IS_MISSING_CREDIT_FILE"));
  }

  @Test
  void getType_mustBeObject() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
  }

  @Test
  void getDescription_mustBeNonEmpty() {
    assertFalse(feature.getDescription().isEmpty());
  }

  @Test
  void getRequiredEvalParamKeys_idAndIp() {
    assertEquals(All.of(EvalParamKey.ALLOY_REPORT_URL), feature.getRequiredEvalParamKeys());
  }
}
