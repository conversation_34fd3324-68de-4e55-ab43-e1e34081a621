package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PropertyInfoFeatureTest {

  public static final ExtendedBillerAccountData BILLER_ACCOUNT_DATA = new ExtendedBillerAccountData().billerId(123L);
  public static final PropertyInfo PROPERTY_INFO = new PropertyInfo(BillingIntegrationTypeEnum.P2P, false, null);

  @InjectMocks
  PropertyInfoFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;

  @Mock
  BillingService billingService;

  @Test
  public void getValueByBillerId_success() {
    when(billingService.getPropertyByIdentityBillerId(123L)).thenReturn(PROPERTY_INFO);

    PropertyInfo productInfo = assertInstanceOf(PropertyInfo.class,
        feature.getValue(new EvalParams().billerId(123L)).value());
    assertEquals(BillingIntegrationTypeEnum.P2P, productInfo.integrationType());
    assertFalse(productInfo.isOutOfNetwork());
  }

  @Test
  public void getValueByBillerAccountId_success() {
    when(identityService.getBillerAccount(456L)).thenReturn(BILLER_ACCOUNT_DATA);
    when(billingService.getPropertyByIdentityBillerId(123L)).thenReturn(PROPERTY_INFO);

    PropertyInfo productInfo = assertInstanceOf(PropertyInfo.class,
        feature.getValue(new EvalParams().billerAccountId(456L)).value());
    assertEquals(BillingIntegrationTypeEnum.P2P, productInfo.integrationType());
    assertFalse(productInfo.isOutOfNetwork());
  }

  @Test
  public void getValueByOfferId_success() {
    when(offerService.getOffer("abc", null)).thenReturn(new InternalOffer().billerAccountId(456L));
    when(identityService.getBillerAccount(456L)).thenReturn(BILLER_ACCOUNT_DATA);
    when(billingService.getPropertyByIdentityBillerId(123L)).thenReturn(PROPERTY_INFO);

    PropertyInfo productInfo = assertInstanceOf(PropertyInfo.class,
        feature.getValue(new EvalParams().offerId("abc")).value());
    assertEquals(BillingIntegrationTypeEnum.P2P, productInfo.integrationType());
    assertFalse(productInfo.isOutOfNetwork());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(Any.of(EvalParamKey.OFFER_ID, EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.BILLER_ID),
        feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
