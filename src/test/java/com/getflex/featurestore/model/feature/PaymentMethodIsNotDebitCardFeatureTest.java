package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PaymentMethodIsNotDebitCardFeatureTest {

  @InjectMocks
  PaymentMethodIsNotDebitCardFeature paymentMethodIsNotDebitCardFeature;

  @Mock
  WalletService walletService;

  @Mock
  IdentityService identityService;

  private static final Long PAYMENT_METHOD_ID = 1L;
  private static final Long CUSTOMER_ID = 1L;
  private static final long BILLER_ACCOUNT_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customer_public_id";

  @ParameterizedTest
  @MethodSource("cardTestCases")
  void testGetValue(boolean paymentMethodIdActive, Card card, boolean paymentMethodIsNotDebitCard) {
    EvalParams evalParams = new EvalParams();
    if (paymentMethodIdActive) {
      evalParams.setPaymentMethodId(PAYMENT_METHOD_ID);
      when(walletService.getCard(PAYMENT_METHOD_ID)).thenReturn(card);
    } else {
      evalParams.setCustomerId(CUSTOMER_ID);
      when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(
          new GetCustomerResponse()
              .customerId(CUSTOMER_ID)
              .customerPublicId(CUSTOMER_PUBLIC_ID)
      );
      when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(
          new GetDefaultCardResponse()
              .card(card)
      );
    }
    FeatureValue featureValue = paymentMethodIsNotDebitCardFeature.fetchFeatureValue(evalParams);
    assertEquals(paymentMethodIsNotDebitCard, featureValue.getValue());
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValueWithBillerAccountId(Boolean paymentMethodIsNotDebitCard) {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(BILLER_ACCOUNT_ID);

    when(identityService.getBillerAccount(BILLER_ACCOUNT_ID)).thenReturn(
        new ExtendedBillerAccountData().customerId(CUSTOMER_ID));
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(
        new GetCustomerResponse()
            .customerId(CUSTOMER_ID)
            .customerPublicId(CUSTOMER_PUBLIC_ID)
    );
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(
        new GetDefaultCardResponse()
            .card(new Card().cardType(paymentMethodIsNotDebitCard ? "credit" : "debit"))
    );

    FeatureValue featureValue = paymentMethodIsNotDebitCardFeature.fetchFeatureValue(evalParams);
    assertEquals(paymentMethodIsNotDebitCard, featureValue.getValue());
  }

  @Test
  void testGetValueWithWalletServiceException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setPaymentMethodId(PAYMENT_METHOD_ID);
    when(walletService.getCard(PAYMENT_METHOD_ID)).thenThrow(
        new InternalDependencyFailureException("No card found")
    );
    FeatureValue featureValue = paymentMethodIsNotDebitCardFeature.fetchFeatureValue(evalParams);
    assertNull(featureValue.getValue());
  }

  static Stream<Arguments> cardTestCases() {
    return Stream.of(
        Arguments.of(true, null, true),
        Arguments.of(true, new Card(), true),
        Arguments.of(true, new Card().cardType("credit"), true),
        Arguments.of(true, new Card().cardType("debit"), false),
        Arguments.of(false, null, true),
        Arguments.of(false, new Card(), true),
        Arguments.of(false, new Card().cardType("credit"), true),
        Arguments.of(false, new Card().cardType("debit"), false)
    );
  }
}
