package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import com.getflex.wallet.model.GetDefaultCardResponse;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CurrentCardSameAsFundsInCardInTimeRangeFeatureTest {

  @Mock
  WalletService walletService;

  @Mock
  LedgerService ledgerService;

  @Mock
  IdentityService identityService;

  private static final Long CUSTOMER_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customer_public_123";
  private static final Long PAYMENT_METHOD_ID = 456L;
  private static final String TIME_RANGE_30_DAYS = "P30D";
  private static final String TIME_RANGE_7_DAYS = "P7D";

  @Test
  void getValue_AllFundsInRecordsMatchCurrentCard_ReturnsTrue() {
    // Setup
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock default card response
    Card card = new Card().paymentMethodId(PAYMENT_METHOD_ID);
    GetDefaultCardResponse defaultCardResponse = new GetDefaultCardResponse().card(card);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(defaultCardResponse);

    // Mock funds in records - all with same payment method ID
    RecordLedger record1 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(PAYMENT_METHOD_ID)
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P10D")));

    RecordLedger record2 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(PAYMENT_METHOD_ID)
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P20D")));

    List<RecordLedger> fundsInRecords = List.of(record1, record2);
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(fundsInRecords);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput(true), result);
    assertEquals(FeatureTypeEnum.BOOLEAN, feature.getType());
  }

  @Test
  void getValue_SomeFundsInRecordsDifferentFromCurrentCard_ReturnsFalse() {
    // Setup
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock default card response
    Card card = new Card().paymentMethodId(PAYMENT_METHOD_ID);
    GetDefaultCardResponse defaultCardResponse = new GetDefaultCardResponse().card(card);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(defaultCardResponse);

    // Mock funds in records - some with different payment method ID
    RecordLedger record1 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(PAYMENT_METHOD_ID)
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P10D")));

    RecordLedger record2 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(789L) // Different payment method ID
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P20D")));

    List<RecordLedger> fundsInRecords = List.of(record1, record2);
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(fundsInRecords);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput(false), result);
  }

  @Test
  void getValue_NoFundsInRecords_ReturnsTrue() {
    // Setup - when there are no funds in records, allMatch returns true
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock default card response
    Card card = new Card().paymentMethodId(PAYMENT_METHOD_ID);
    GetDefaultCardResponse defaultCardResponse = new GetDefaultCardResponse().card(card);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(defaultCardResponse);

    // Mock empty funds in records
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput(true), result);
  }

  @Test
  void getValue_DifferentTimeRange_WorksCorrectly() {
    // Setup with 7 days time range
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_7_DAYS);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock default card response
    Card card = new Card().paymentMethodId(PAYMENT_METHOD_ID);
    GetDefaultCardResponse defaultCardResponse = new GetDefaultCardResponse().card(card);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(defaultCardResponse);

    // Mock funds in records
    RecordLedger record1 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(PAYMENT_METHOD_ID)
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P3D")));

    List<RecordLedger> fundsInRecords = List.of(record1);
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(fundsInRecords);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput(true), result);
    assertEquals("P7D", feature.timeRange);
  }

  @Test
  void getValue_AllFundsInRecordsDifferentFromCurrentCard_ReturnsFalse() {
    // Setup
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock default card response
    Card card = new Card().paymentMethodId(PAYMENT_METHOD_ID);
    GetDefaultCardResponse defaultCardResponse = new GetDefaultCardResponse().card(card);
    when(walletService.getDefaultCard(CUSTOMER_PUBLIC_ID)).thenReturn(defaultCardResponse);

    // Mock funds in records - all with different payment method IDs
    RecordLedger record1 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(789L) // Different payment method ID
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P10D")));

    RecordLedger record2 = new RecordLedger()
        .customerId(CUSTOMER_ID)
        .fromPaymentMethodId(999L) // Different payment method ID
        .dtCreated(OffsetDateTime.now().minus(Period.parse("P20D")));

    List<RecordLedger> fundsInRecords = List.of(record1, record2);
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(fundsInRecords);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput(false), result);
  }

  @Test
  void getDescription_ReturnsCorrectDescription() {
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    String description = feature.getDescription();
    
    assertEquals("whether customers current card is the same as the funds in card in the past x time range, "
        + "i.e.CurrentCardSameAsFundsInCardInTimeRangeFeature_P30D means checking the funds in card within "
        + "the past 30 days", description);
  }

  @Test
  void getType_ReturnsBooleanType() {
    CurrentCardSameAsFundsInCardInTimeRangeFeature feature =
        new CurrentCardSameAsFundsInCardInTimeRangeFeature(
            walletService, ledgerService, identityService, TIME_RANGE_30_DAYS);

    assertEquals(FeatureTypeEnum.BOOLEAN, feature.getType());
  }
}
