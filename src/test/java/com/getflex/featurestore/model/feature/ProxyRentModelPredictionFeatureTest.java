package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.COLLECTIONS_BALANCE_NO_MEDICAL;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.EADS11_CV23;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.MONTHS_ON_FILE;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.TOTAL_OUTSTANDING_BALANCE_OPEN_GOOD_REVOLVING;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING;
import static com.getflex.featurestore.model.flexscore.alloy.AlloyExtractedFeatureNumber.VANTAGE30_SCORE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.config.ServiceConfig;
import com.getflex.featurestore.integration.sagemaker.model.ProxyRentModelInput;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.proxyrent.ProxyRentModelPredictionFeature;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ProxyRentModelPredictionFeatureTest {

  @InjectMocks
  ProxyRentModelPredictionFeature proxyRentModelPredictionFeature;

  @Mock
  SageMakerRuntimeClient sageMaker;

  @Mock
  ServiceConfig serviceConfig;

  @Mock
  FeatureFactory featureFactory;

  @Mock
  ExecutorService executorService;

  @Mock
  BaseFeature baseFeature;

  @Mock
  BaseFeature creditBaseFeature;

  @Mock
  BaseFeature inNetworkBaseFeature;


  @Test
  public void modelFeatureOrderList() {
    List<ProxyRentModelInput> inputs = proxyRentModelPredictionFeature.getModelFeaturesList();
    Assertions.assertEquals(17, inputs.size());
    Assertions.assertEquals(inputs.get(0), ProxyRentModelInput.VANTAGE_SCORE);
    Assertions.assertEquals(inputs.get(1), ProxyRentModelInput.UW_MONTH);
    Assertions.assertEquals(inputs.get(2), ProxyRentModelInput.ACQUISITION_CHANNEL);
    Assertions.assertEquals(inputs.get(3), ProxyRentModelInput.CREDIT_HISTORY_DURATION);
    Assertions.assertEquals(inputs.get(4), ProxyRentModelInput.COLLECTIONS_BALANCE_NO_MEDICAL);
    Assertions.assertEquals(inputs.get(5), ProxyRentModelInput.TU_DEBT);
    Assertions.assertEquals(inputs.get(6), ProxyRentModelInput.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING);
    Assertions.assertEquals(inputs.get(7), ProxyRentModelInput.PROPERTY_MEAN_RENT);
    Assertions.assertEquals(inputs.get(8), ProxyRentModelInput.STREET_MEAN_RENT);
    Assertions.assertEquals(inputs.get(9), ProxyRentModelInput.ZIP5_MEAN_RENT);
    Assertions.assertEquals(inputs.get(10), ProxyRentModelInput.CITY_MEAN_RENT);
    Assertions.assertEquals(inputs.get(11), ProxyRentModelInput.STATE_MEAN_RENT);
    Assertions.assertEquals(inputs.get(12), ProxyRentModelInput.PROPERTY_MEAN_BP);
    Assertions.assertEquals(inputs.get(13), ProxyRentModelInput.STREET_MEAN_BP);
    Assertions.assertEquals(inputs.get(14), ProxyRentModelInput.ZIP5_MEAN_BP);
    Assertions.assertEquals(inputs.get(15), ProxyRentModelInput.CITY_MEAN_BP);
    Assertions.assertEquals(inputs.get(16), ProxyRentModelInput.STATE_MEAN_BP);
  }

  @ParameterizedTest
  @CsvSource({
      "10, 10000", // min case
      "50000, 1000000", // max case
      "300, 30000"
  })
  public void getValue(double modelOutput, Long expected) throws InterruptedException {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    evalParams.billerId(1L);
    evalParams.setAlloyReportUrl("url");

    FeatureValue featureValue = new FeatureValue().name("Test-Mock-Feature").type(FeatureTypeEnum.DOUBLE).value(100D);
    when(baseFeature.fetchFeatureValue(evalParams)).thenReturn(featureValue);
    when(featureFactory.getFeature(argThat(s -> !s.equals("CreditReportFeature")
        && !s.equals("IsInNetworkFeature")))).thenReturn(baseFeature);

    FeatureValue creditFeature = new FeatureValue().name("Test-Credit-Feature").type(FeatureTypeEnum.OBJECT).value(
        Map.of(VANTAGE30_SCORE.name(), 500,
            MONTHS_ON_FILE.name(), 10,
            COLLECTIONS_BALANCE_NO_MEDICAL.name(), 100,
            EADS11_CV23.name(), 100,
            TOTAL_MONTHLY_PMT_OPEN_INSTALLMENT.name(), 100,
            TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING.name(), 100
        )
    );
    when(creditBaseFeature.fetchFeatureValue(evalParams)).thenReturn(creditFeature);
    when(featureFactory.getFeature("CreditReportFeature")).thenReturn(creditBaseFeature);

    FeatureValue inNetworkFeature = new FeatureValue().name("Test-In-Network-Feature").type(FeatureTypeEnum.BOOLEAN)
        .value(false);
    when(inNetworkBaseFeature.fetchFeatureValue(evalParams)).thenReturn(inNetworkFeature);
    when(featureFactory.getFeature("IsInNetworkFeature")).thenReturn(inNetworkBaseFeature);

    InvokeEndpointResponse response = InvokeEndpointResponse.builder()
        .contentType("text/csv; charset=utf-8")
        .body(SdkBytes.fromUtf8String(String.valueOf(modelOutput)))
        .build();
    when(sageMaker.invokeEndpoint(any(InvokeEndpointRequest.class))).thenReturn(response);
    List<Future<Object>> futures = proxyRentModelPredictionFeature.getModelFeaturesList().stream()
        .map(f -> {
          Number val = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          Future<Object> future = CompletableFuture.completedFuture(Pair.of(f, val));
          return future;
        })
        .toList();
    when(executorService.invokeAll(anyCollection())).thenReturn(futures);

    FeatureValue actualValue = proxyRentModelPredictionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(expected, actualValue.getValue());

    Map<ProxyRentModelInput, Number> expectedValues = new TreeMap<>();
    proxyRentModelPredictionFeature.getModelFeaturesList().forEach(f -> {
      if (ProxyRentModelInput.VANTAGE_SCORE.equals(f)) {
        expectedValues.put(f, 500);
      } else if (ProxyRentModelInput.UW_MONTH.equals(f)) {
        expectedValues.put(f, LocalDate.now().getMonthValue());
      } else if (ProxyRentModelInput.ACQUISITION_CHANNEL.equals(f)) {
        expectedValues.put(f, 0);
      } else if (ProxyRentModelInput.CREDIT_HISTORY_DURATION.equals(f)) {
        expectedValues.put(f, 10);
      } else if (ProxyRentModelInput.COLLECTIONS_BALANCE_NO_MEDICAL.equals(f)) {
        expectedValues.put(f, 100);
      } else if (ProxyRentModelInput.TU_DEBT.equals(f)) {
        expectedValues.put(f, 200.0);
      } else if (ProxyRentModelInput.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING.equals(f)) {
        expectedValues.put(f, 100);
      } else {
        expectedValues.put(f, 100.00);
      }
    });
    Assertions.assertEquals(new JSONObject(expectedValues).toString(), actualValue.getMetadata());
  }

  @Test
  public void getValue_sanitizedMetadata() throws InterruptedException {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    evalParams.billerId(1L);
    evalParams.setAlloyReportUrl("url");

    FeatureValue featureValue = new FeatureValue().name("Test-Mock-Feature").type(FeatureTypeEnum.DOUBLE).value(null);
    when(baseFeature.fetchFeatureValue(evalParams)).thenReturn(featureValue);
    when(featureFactory.getFeature(argThat(s -> !s.equals("CreditReportFeature")
        && !s.equals("IsInNetworkFeature")))).thenReturn(baseFeature);

    FeatureValue creditFeature = new FeatureValue().name("Test-Credit-Feature").type(FeatureTypeEnum.OBJECT).value(
        Map.of()
    );
    when(creditBaseFeature.fetchFeatureValue(evalParams)).thenReturn(creditFeature);
    when(featureFactory.getFeature("CreditReportFeature")).thenReturn(creditBaseFeature);

    FeatureValue inNetworkFeature = new FeatureValue().name("Test-In-Network-Feature").type(FeatureTypeEnum.BOOLEAN)
        .value(false);
    when(inNetworkBaseFeature.fetchFeatureValue(evalParams)).thenReturn(inNetworkFeature);
    when(featureFactory.getFeature("IsInNetworkFeature")).thenReturn(inNetworkBaseFeature);

    InvokeEndpointResponse response = InvokeEndpointResponse.builder()
        .contentType("text/csv; charset=utf-8")
        .body(SdkBytes.fromUtf8String(String.valueOf(500)))
        .build();
    when(sageMaker.invokeEndpoint(any(InvokeEndpointRequest.class))).thenReturn(response);
    List<Future<Object>> futures = proxyRentModelPredictionFeature.getModelFeaturesList().stream()
        .map(f -> {
          Number val = f.getModelInputValueFunction().apply(featureFactory, evalParams);
          Future<Object> future = CompletableFuture.completedFuture(Pair.of(f, val));
          return future;
        }).toList();
    when(executorService.invokeAll(anyCollection())).thenReturn(futures);

    FeatureValue actualValue = proxyRentModelPredictionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(50000L, actualValue.getValue());

    Map<ProxyRentModelInput, Number> expectedValues = new TreeMap<>();
    proxyRentModelPredictionFeature.getModelFeaturesList().forEach(f -> {
      if (ProxyRentModelInput.VANTAGE_SCORE.equals(f)) {
        expectedValues.put(f, 0);
      } else if (ProxyRentModelInput.UW_MONTH.equals(f)) {
        expectedValues.put(f, LocalDate.now().getMonthValue());
      } else if (ProxyRentModelInput.ACQUISITION_CHANNEL.equals(f)) {
        expectedValues.put(f, 0);
      } else if (ProxyRentModelInput.CREDIT_HISTORY_DURATION.equals(f)) {
        expectedValues.put(f, 0);
      } else if (ProxyRentModelInput.COLLECTIONS_BALANCE_NO_MEDICAL.equals(f)) {
        expectedValues.put(f, 0);
      } else if (ProxyRentModelInput.TU_DEBT.equals(f)) {
        expectedValues.put(f, 0.0);
      } else if (ProxyRentModelInput.TOTAL_OUTSTANDING_BALANCE_OPEN_REVOLVING.equals(f)) {
        expectedValues.put(f, 0);
      } else {
        expectedValues.put(f, null);
      }
    });
    Assertions.assertEquals(new JSONObject(expectedValues).toString(), actualValue.getMetadata());
  }

  @Test
  public void getValue_defaultWithException() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    evalParams.billerId(1L);
    evalParams.setAlloyReportUrl("url");
    when(sageMaker.invokeEndpoint(any(InvokeEndpointRequest.class))).thenThrow(new UnsupportedOperationException());
    FeatureValue actualValue = proxyRentModelPredictionFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(0, actualValue.getValue());
    Assertions.assertNull(actualValue.getMetadata());
  }
}

