package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalServiceBadDataException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDaysSinceLastSuspensionFeatureTest {
  @InjectMocks
  CustomerDaysSinceLastSuspensionFeature feature;

  @Mock
  OfferService offerService;

  @ParameterizedTest
  @ValueSource(longs = {50L, 70L})
  public void testGetValue(Long deltaDay) {
    Long customerId = 1234L;
    OffsetDateTime suspensionTime = OffsetDateTime.now().minusDays(deltaDay);
    InternalOffer offer =
        new InternalOffer().customerId(customerId).offerId("offer-test")
            .offerState(InternalOffer.OfferStateEnum.CLOSED)
            .terminationTime(suspensionTime);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getLastSuspension(customerId)).thenReturn(offer);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDaysSinceLastSuspensionFeature");
    Assertions.assertEquals(featureValue.getValue(), Math.toIntExact(deltaDay));
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueNoOffer() {
    Long customerId = 1234L;
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getLastSuspension(customerId)).thenReturn(null);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerDaysSinceLastSuspensionFeature");
    Assertions.assertEquals(featureValue.getValue(), Integer.MAX_VALUE);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetInvalidOfferTime() {
    Long customerId = 1234L;
    Long delayDays = Integer.MAX_VALUE + 10L;
    OffsetDateTime suspensionTime = OffsetDateTime.now().minusDays(delayDays);
    InternalOffer offer =
        new InternalOffer().customerId(customerId).offerId("offer-test")
            .offerState(InternalOffer.OfferStateEnum.CLOSED)
            .terminationTime(suspensionTime);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.getLastSuspension(customerId)).thenReturn(offer);
    try {
      feature.fetchFeatureValue(evalParams);
    } catch (Exception e) {
      assertTrue(e.getMessage().contains("Bad terminate received. customerId=1234"));
    }
  }
}
