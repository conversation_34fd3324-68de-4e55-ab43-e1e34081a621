package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsTrustedIpAddressFeatureTest {
  @InjectMocks
  IsTrustedIpAddressFeature isTrustedIpAddressFeature;

  @Mock
  IdentityService identityService;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 3L;
  GetCustomerResponse customer = new GetCustomerResponse().customerId(3L).email("<EMAIL>").customerPublicId(
      "publicId3");

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean featureExists) {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("is_trusted_ip_address_to_customer_id")
            .primaryKey("*******").featureValue("publicId3")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress("*******");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "trusted_ip_address_to_customer_id", "*******")).thenReturn(feature);
    FeatureValue featureValue = isTrustedIpAddressFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsTrustedIpAddressFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), Boolean.TRUE);
    } else {
      Assertions.assertEquals(featureValue.getValue(), Boolean.FALSE);
    }
  }

  @Test
  public void testGetValueIncorrectCustomerId() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("is_trusted_ip_address_to_customer_id")
            .primaryKey("*******").featureValue("anotherPublicId")
            .build());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setIpAddress("*******");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "trusted_ip_address_to_customer_id", "*******")).thenReturn(feature);
    FeatureValue featureValue = isTrustedIpAddressFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsTrustedIpAddressFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertEquals(featureValue.getValue(), Boolean.FALSE);
  }
}
