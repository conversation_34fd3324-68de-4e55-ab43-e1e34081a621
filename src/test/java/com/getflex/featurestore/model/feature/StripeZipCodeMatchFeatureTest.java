package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StripeZipCodeMatchFeatureTest {
  @InjectMocks
  StripeZipCodeMatchFeature feature;

  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @CsvSource({
    "10, 12345, true",
    "10, 00000, false"
  })
  void getValue_MatchAndNoMatch(Long customerId, String stripeZipCode, boolean expectedValue) {
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().zip(expectedValue ? stripeZipCode : "54321"));
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeZipCode(stripeZipCode);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("StripeZipCodeMatchFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.BOOLEAN, featureValue.getType());
    Assertions.assertEquals(expectedValue, featureValue.getValue());
  }

}
