package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.utils.FlexConstant.FLEX_TIMEZONE;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.time.Clock;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class IsBpWindowFeatureTest {

  @InjectMocks
  private IsBpWindowFeature feature;

  @Mock
  private Clock etClock;

  @ParameterizedTest
  @CsvSource({
    "26, 3, false",
    "27, 3, false",
    "28, 3, true",
    "29, 3, true",
    "30, 3, true",
    "31, 3, true",
    "1, 4, true",
    "2, 4, true",
    "3, 4, true",
    "4, 4, true",
    "5, 4, true",
    "6, 4, false",
    "7, 4, false",
    "8, 4, false",
    "9, 4, false",
    "10, 4, false",
    "11, 4, false",
    "12, 4, false",
    "26, 4, false",
    "27, 4, true",
    "28, 4, true",
    "29, 4, true",
    "30, 4, true"
  })
  public void testGetValue(int dayOfMonth, int month, boolean expectedValue) {
    ZonedDateTime fixedDateTime = ZonedDateTime.of(2024, month, dayOfMonth, 8, 8, 8, 0, FLEX_TIMEZONE);
    when(etClock.instant()).thenReturn(fixedDateTime.toInstant());
    when(etClock.getZone()).thenReturn(FLEX_TIMEZONE);

    EvalParams evalParams = new EvalParams();
    evalParams.customerId(1234L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "IsBpWindowFeature");
    Assertions.assertEquals(featureValue.getValue(), expectedValue);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
