package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.similarity.AddressLevelBatchMemoSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.BillerLevelBatchMemoSimilarityScoreFeature;
import com.getflex.featurestore.model.feature.similarity.CustomerLevelBatchMemoSimilarityScoreFeature;
import com.getflex.identity.model.GetCustomerResponse;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SimilarityScoreFeatureTest {

  @InjectMocks
  AddressLevelBatchMemoSimilarityScoreFeature addressLevelBatchMemoSimilarityScoreFeature;

  @InjectMocks
  BillerLevelBatchMemoSimilarityScoreFeature billerLevelBatchMemoSimilarityScoreFeature;

  @InjectMocks
  CustomerLevelBatchMemoSimilarityScoreFeature customerLevelBatchMemoSimilarityScoreFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Mock
  IdentityService identityService;

  @Test
  public void testAddressLevelSimilarityScore() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    when(identityService.getCustomer(123L)).thenReturn(
        new GetCustomerResponse().customerId(123L).addressLine1("715thave").city("New York")
            .state("NY").zip("10001")
    );
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("address_level_batch_memo_history")
            .primaryKey("newyork&ny&10001").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "address_level_batch_memo_history", "newyork&ny&10001")).thenReturn(feature);

    FeatureValue featureValue = addressLevelBatchMemoSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "AddressLevelBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 80);
  }

  @Test
  public void testBillerLevelSimilarityScore() {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("address_level_batch_memo_history")
            .primaryKey("715thave&newyork&ny&10001").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "biller_level_batch_memo_history", "123")).thenReturn(feature);
    FeatureValue featureValue = billerLevelBatchMemoSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "BillerLevelBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 80);
  }

  @Test
  public void testCustomerLevelSimilarityScore() {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("address_level_batch_memo_history")
            .primaryKey("715thave&newyork&ny&10001").featureValue(
                "71 5th Avenue13889634|71 5th Avenue40692473|71 5th Avenue39991637|"
                    + "71 5th Avenue42050772|71 5th Avenue40274865|71 5th Avenue40715534|"
                    + "71 5th Avenue41697096")
            .build());

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    evalParams.setBatchMemo("71 5th Avenue13888888");

    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "customer_level_batch_memo_history", "123")).thenReturn(feature);
    FeatureValue featureValue = customerLevelBatchMemoSimilarityScoreFeature.fetchFeatureValue(
        evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerLevelBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 80);
  }


}
