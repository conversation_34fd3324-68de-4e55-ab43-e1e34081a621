package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerSuccessfulBpsExcludingMostRecentCountFeatureTest {

  @InjectMocks
  CustomerSuccessfulBpsExcludingMostRecentCountFeature feature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  private static final String FEATURE_NAME = "customer_successful_bps";
  private static final long CUSTOMER_ID = 123L;


  @Test
  public void getValue_success() {
    int expected = 3;
    OfflineFeature offlineFeature = OfflineFeature.builder()
        .featureName(FEATURE_NAME)
        .featureValue(Integer.toString(expected))
        .build();
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(eq(FEATURE_NAME), eq(Long.toString(CUSTOMER_ID))))
        .thenReturn(Optional.of(offlineFeature));

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }

  @Test
  public void getValue_NoValue() {
    int expected = 0;

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }
}
