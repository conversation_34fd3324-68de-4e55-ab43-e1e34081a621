package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerIsReapplyIneligibleSelfPayAbuseFeatureTest {

  private long billerAccountId = 1L;
  private final String featureName = "CustomerIsReapplyIneligibleSelfPayAbuseFeature";

  @InjectMocks
  CustomerIsReapplyIneligibleSelfPayAbuseFeature featureInstance;

  @Mock
  OfferService offerService;


  /**
   * Test case for isReapplyIneligibleSelfPayAbuseFeatureTest
   * return not null and offer is closed due to SelfPayAbuse
   */
  @Test
  public void isReapplyIneligibleSelfPayAbuseFeatureTest() {
    // mock data for identity service
    InternalOffer offer = new InternalOffer();
    offer.setOfferState(InternalOffer.OfferStateEnum.CLOSED);
    offer.setDeactivationReasonDetail("SelfPayAbuse");

    Mockito.when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);

    // check results
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /**
   * Test case for isReapplyIneligibleSelfPayAbuseFeatureTest
   * return null
   */
  @Test
  public void isReapplyIneligibleSelfPayAbuseFeatureReturnNullTest() {
    Mockito.when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);

    // check results
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /**
   * Test case for isReapplyIneligibleSelfPayAbuseFeatureTest
   * return not null
   * offer is not closed
   */
  @Test
  public void isReapplyIneligibleSelfPayAbuseFeatureNotClosedTest() {
    // mock data for identity service
    InternalOffer offer = new InternalOffer();
    offer.setOfferState(InternalOffer.OfferStateEnum.ACCEPTED);
    offer.setDeactivationReasonDetail("SelfPayAbuse");

    Mockito.when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);

    // check results
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /**
   * Test case for isReapplyIneligibleSelfPayAbuseFeatureTest
   * return not null
   * offer is closed
   * reason is not SelfPayAbuse
   */
  @Test
  public void isReapplyIneligibleSelfPayAbuseFeatureReasonNotMatchTest() {
    // mock data for identity service
    InternalOffer offer = new InternalOffer();
    offer.setOfferState(InternalOffer.OfferStateEnum.CLOSED);
    offer.setDeactivationReasonDetail("NotSelfPayAbuse");

    Mockito.when(offerService.getOfferByBillerAccountId(billerAccountId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = featureInstance.fetchFeatureValue(evalParams);

    // check results
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  @Test
  void getDescription() {
    Assertions.assertEquals("Check if the offer is closed due to SelfPayAbuse",
        featureInstance.getDescription());
  }
}
