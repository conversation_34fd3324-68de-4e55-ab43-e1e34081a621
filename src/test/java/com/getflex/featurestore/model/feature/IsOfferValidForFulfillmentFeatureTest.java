package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.exception.fulfillment.FulfillmentNonVclOfferException;
import com.getflex.featurestore.exception.fulfillment.FulfillmentOfferNotFoundException;
import com.getflex.featurestore.exception.fulfillment.FulfillmentOfferStateNotEligibleException;
import com.getflex.featurestore.exception.fulfillment.FulfillmentOutdatedBillAccountException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsOfferValidForFulfillmentFeatureTest {

  @InjectMocks
  IsOfferValidForFulfillmentFeature isOfferValidForFulfillmentFeature;

  @Mock
  OfferService offerService;

  @Test
  void testGetValueOfferServiceFailure() {
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenThrow(
        InternalDependencyFailureException.class);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setOfferVersion(1L);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertEquals(result.value(), false);
    String metadata = "{"
        + "\"offer_id\":null,\"estimated_bill_amount_cents\":null,\"offer_version\":null,"
        + "\"exception_type\":\"FulfillmentOfferNotFoundException\","
        + "\"exception_message\":\"Offer or specified version not found (biller_account_id=123, version=1\"}";
    assertEquals(result.metadata(), metadata);
  }

  @Test
  void testGetValueOfferNotFound() {
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setOfferVersion(1L);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertEquals(result.value(), false);
    String metadata = "{"
        + "\"offer_id\":null,\"estimated_bill_amount_cents\":null,\"offer_version\":null,"
        + "\"exception_type\":\"FulfillmentOfferNotFoundException\","
        + "\"exception_message\":\"Offer or specified version not found (biller_account_id=123, version=1\"}";
    assertEquals(result.metadata(), metadata);
  }

  @Test
  void testGetValueOfferStateNotLegitimate() {
    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(123L);
    evalParams.setOfferVersion(1L);
    InternalOffer offer = new InternalOffer();
    offer.offerId("offerId");
    offer.estimatedBillAmountCent(5000L);
    offer.offerVersion(1L);
    offer.offerState(OfferStateEnum.REJECTED);
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenReturn(offer);
    when(offerService.getOfferByCustomerId(any())).thenReturn(null);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertEquals(result.value(), false);
    String metadata = "{"
        + "\"offer_id\":\"offerId\",\"estimated_bill_amount_cents\":5000,\"offer_version\":1,"
        + "\"exception_type\":\"FulfillmentOfferStateNotEligibleException\","
        + "\"exception_message\":\"Offer state is not eligible for fulfillment (offerId=offerId, state=Rejected)\"}";
    assertEquals(result.metadata(), metadata);
  }

  @Test
  void testGetValueOutdatedBillAccount() {
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferVersion(1L);
    evalParams.setBillerAccountId(123L);
    InternalOffer offer = new InternalOffer();
    offer.offerState(OfferStateEnum.REJECTED);
    offer.offerId("offerId");
    offer.billerAccountId(1L);
    offer.offerVersion(1L);
    offer.estimatedBillAmountCent(5000L);
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenReturn(offer);
    InternalOffer customerOffer = new InternalOffer();
    customerOffer.offerState(OfferStateEnum.ACCEPTED);
    customerOffer.billerAccountId(1L);
    customerOffer.deactivationReason("SuspendCreditLine");
    when(offerService.getOfferByCustomerId(any())).thenReturn(customerOffer);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertEquals(result.value(), false);
    String metadata = "{"
        + "\"offer_id\":\"offerId\",\"estimated_bill_amount_cents\":5000,\"offer_version\":1,"
        + "\"exception_type\":\"FulfillmentOutdatedBillAccountException\","
        + "\"exception_message\":\"Outdated biller account provided (biller_account_id=123)\"}";
    assertEquals(result.metadata(), metadata);
  }

  @Test
  void testGetValueNonVlcOffer() {
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferVersion(1L);
    evalParams.setBillerAccountId(123L);
    InternalOffer offer = new InternalOffer();
    offer.offerState(OfferStateEnum.CLOSED);
    offer.offerId("offerId");
    offer.billerAccountId(123L);
    offer.offerVersion(1L);
    offer.estimatedBillAmountCent(5000L);
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenReturn(offer);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertEquals(result.value(), false);
    String metadata = "{"
        + "\"offer_id\":\"offerId\",\"estimated_bill_amount_cents\":5000,\"offer_version\":1,"
        + "\"exception_type\":\"FulfillmentNonVclOfferException\","
        + "\"exception_message\":\"NonVCL offer not allowed for biller_account_id=123 and version=1\"}";
    assertEquals(result.metadata(), metadata);
  }

  @Test
  void testGetValue_Success() throws Exception {
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferVersion(1L);
    InternalOffer offer = new InternalOffer();
    offer.offerState(OfferStateEnum.ACCEPTED).billerAccountId(1L).creditUtilizationCent(1L).offerId("offerId")
        .estimatedBillAmountCent(5000L).offerVersion(1L);
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(any(), any())).thenReturn(offer);
    FeatureOutput result = isOfferValidForFulfillmentFeature.getValue(evalParams);
    assertNotNull(result);
    assertEquals(result.value(), true);
    String metadata = "{"
        + "\"offer_id\":\"offerId\",\"estimated_bill_amount_cents\":5000,\"offer_version\":1,"
        + "\"exception_type\":null,"
        + "\"exception_message\":null}";
    assertEquals(result.metadata(), metadata);
  }
}
