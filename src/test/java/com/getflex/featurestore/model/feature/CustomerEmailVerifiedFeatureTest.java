package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.UserAccountService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.useraccount.model.UserAccount;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerEmailVerifiedFeatureTest {

  @InjectMocks
  CustomerEmailVerifiedFeature customerEmailVerifiedFeature;
  @Mock
  UserAccountService userAccountService;
  @Mock
  IdentityService identityService;

  GetCustomerResponse customer = new GetCustomerResponse().customerId(1L).customerPublicId("customer_public_id");
  UserAccount userAccount = new UserAccount().customerPublicId("customer_public_id").email("<EMAIL>")
      .firstName("firstName").lastName(
          "LastName").emailVerified(true);

  @Test
  public void testGetValue() {
    Long customerId = 1L;
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    when(userAccountService.getUserAccountByPublicId("customer_public_id")).thenReturn(userAccount);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = customerEmailVerifiedFeature.fetchFeatureValue(evalParams);

    assertEquals(featureValue.getName(), "CustomerEmailVerifiedFeature");
    assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(featureValue.getValue(), true);
  }
}
