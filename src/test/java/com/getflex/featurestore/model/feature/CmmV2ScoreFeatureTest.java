package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CmmV2ScoreFeatureTest {
  @InjectMocks
  CmmV2ScoreFeature cmmV2ScoreFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 1234L;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean featureExists) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("cmm_v2_score")
            .primaryKey("1234").featureValue("{\"score\": 0.05}")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "cmm_v2_score", "1234")).thenReturn(feature);
    FeatureValue featureValue = cmmV2ScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CmmV2ScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), 0.05);
    } else {
      Assertions.assertEquals(featureValue.getValue(), -1.0);
    }
  }
}
