package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class IsSuspendedBeforeCancelledFeatureTest {

  @InjectMocks
  IsSuspendedBeforeCancelledFeature isSuspendedBeforeCancelledFeature;

  @Mock
  OfferService offerService;

  @Test
  public void testGetValueSuspendedBeforeCancelled() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("CancelCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer2 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("SuspendCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 5, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 7, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer3 =
        new InternalOffer().customerId(customerId).offerId("offer-3")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2022, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1, offer2, offer3));

    FeatureValue featureValue = isSuspendedBeforeCancelledFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsSuspendedBeforeCancelledFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueSuspendedBeforeCancelledWithReactivated() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("CancelCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 11, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer2 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("SuspendCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 5, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC))
            .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 7, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer3 =
        new InternalOffer().customerId(customerId).offerId("offer-3")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2022, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1, offer2, offer3));

    FeatureValue featureValue = isSuspendedBeforeCancelledFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsSuspendedBeforeCancelledFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueSuspendedNotBeforeCancelled() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("CancelCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer2 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("SuspendCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2023, 5, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer3 =
        new InternalOffer().customerId(customerId).offerId("offer-3")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2022, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1, offer2, offer3));

    FeatureValue featureValue = isSuspendedBeforeCancelledFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsSuspendedBeforeCancelledFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueNoSuspended() {
    Long customerId = 1234L;
    InternalOffer offer1 =
        new InternalOffer().customerId(customerId).offerId("offer-1").deactivationReason("SuspendCreditLine")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2023, 5, 6), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    InternalOffer offer2 =
        new InternalOffer().customerId(customerId).offerId("offer-2")
            .initiationTime(OffsetDateTime.of(LocalDate.of(2022, 10, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    when(offerService.searchOffer(customerId, Boolean.TRUE)).thenReturn(List.of(offer1, offer2));

    FeatureValue featureValue = isSuspendedBeforeCancelledFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "IsSuspendedBeforeCancelledFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
