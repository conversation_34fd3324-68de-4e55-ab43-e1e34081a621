package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.model.feature.CustomerCprReportFeature.DEFAULT_VANTAGE_SCORE_40;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.output.CustomerCprReportOutput;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerCprReportFeatureTest {

  private static final Long CUSTOMER_ID = 1234L;
  private static final String OFFLINE_FEATURE_NAME = "customer_cpr_report";

  @Mock
  private OfflineFeatureRepo offlineFeatureRepo;

  @InjectMocks
  private CustomerCprReportFeature feature;

  @Test
  public void getValue_success() {
    OfflineFeature offlineFeature = OfflineFeature.builder()
        .featureName("customer_cpr_report")
        .featureValue("""
            {
              "eads142_s207s": -4,
              "vantage_40_adverse_reason_code_1": 68,
              "vantage_40_adverse_reason_code_2": 22,
              "vantage_40_adverse_reason_code_3": 5,
              "vantage_40_adverse_reason_code_4": 7,
              "vantage_40_adverse_reason_code_5": 84,
              "vantage_score_30": 508,
              "vantage_score_40": 520
            }""")
        .build();
    when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        OFFLINE_FEATURE_NAME, CUSTOMER_ID + "_"))
        .thenReturn(List.of(offlineFeature));

    FeatureOutput output = feature.getValue(new EvalParams().customerId(CUSTOMER_ID));
    assertNotNull(output);
    assertNotNull(output.value());
    CustomerCprReportOutput featureValue = (CustomerCprReportOutput) output.value();
    assertEquals(featureValue.getVantageScore40(), Integer.valueOf(520));
    assertEquals(featureValue.getVantage40AdverseReasonCode1(), Integer.valueOf(68));
    assertEquals(featureValue.getVantage40AdverseReasonCode2(), Integer.valueOf(22));
    assertEquals(featureValue.getVantage40AdverseReasonCode3(), Integer.valueOf(5));
    assertEquals(featureValue.getVantage40AdverseReasonCode4(), Integer.valueOf(7));
    assertEquals(featureValue.getVantage40AdverseReasonCode5(), Integer.valueOf(84));
    assertEquals(featureValue.getVantageScore30(), Integer.valueOf(508));
    assertEquals(featureValue.getEads142S207s(), Integer.valueOf(-4));
  }

  @Test
  public void getValue_missingScore() {
    when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
        OFFLINE_FEATURE_NAME, CUSTOMER_ID + "_"))
        .thenReturn(Collections.emptyList());

    FeatureOutput output = feature.getValue(new EvalParams().customerId(CUSTOMER_ID));
    assertNotNull(output);
    assertNotNull(output.value());
    CustomerCprReportOutput featureValue = (CustomerCprReportOutput) output.value();
    assertEquals(featureValue.getVantageScore40(), Integer.valueOf(DEFAULT_VANTAGE_SCORE_40));
  }
}
