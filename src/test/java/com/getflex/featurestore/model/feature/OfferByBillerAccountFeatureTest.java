package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class OfferByBillerAccountFeatureTest {

  @InjectMocks
  OfferByBillerAccountFeature feature;

  @Mock
  OfferService offerService;

  private static final Long BILLER_ACCOUNT_ID = 123456L;
  private static final Long OFFER_VERSION = 1L;
  private static final String FEATURE_NAME = "OfferByBillerAccountFeature";
  private static final FeatureTypeEnum FEATURE_TYPE = FeatureTypeEnum.OBJECT;

  @Test
  void testGetValueWithExistingOffer() {
    InternalOffer mockOffer = new InternalOffer();
    mockOffer.setOfferId("999");
    mockOffer.setBillerAccountId(BILLER_ACCOUNT_ID);
    mockOffer.setOfferVersion(OFFER_VERSION);
    
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(BILLER_ACCOUNT_ID, OFFER_VERSION))
        .thenReturn(mockOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(BILLER_ACCOUNT_ID);
    evalParams.setOfferVersion(OFFER_VERSION);

    FeatureOutput result = feature.getValue(evalParams);

    assertNotNull(result);
    assertNull(result.metadata());
    assertEquals(mockOffer, result.value());
  }

  @Test
  void testGetValueWithNullOfferVersion() {
    InternalOffer mockOffer = new InternalOffer();
    mockOffer.setOfferId("999");
    mockOffer.setBillerAccountId(BILLER_ACCOUNT_ID);
    
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(BILLER_ACCOUNT_ID, null))
        .thenReturn(mockOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(BILLER_ACCOUNT_ID);
    evalParams.setOfferVersion(null);

    FeatureOutput result = feature.getValue(evalParams);

    assertNotNull(result);
    assertNull(result.metadata());
    assertEquals(mockOffer, result.value());
  }

  @Test
  void testGetValueWithNonExistentOffer() {
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(anyLong(), anyLong()))
        .thenReturn(null);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(BILLER_ACCOUNT_ID);
    evalParams.setOfferVersion(OFFER_VERSION);

    FeatureOutput result = feature.getValue(evalParams);

    assertNotNull(result);
    assertNull(result.metadata());
    assertEquals("Offer not found", result.value());
  }

  @Test
  void testFetchFeatureValue() {
    InternalOffer mockOffer = new InternalOffer();
    mockOffer.setOfferId("999");
    mockOffer.setBillerAccountId(BILLER_ACCOUNT_ID);
    mockOffer.setOfferVersion(OFFER_VERSION);
    
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(BILLER_ACCOUNT_ID, OFFER_VERSION))
        .thenReturn(mockOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(BILLER_ACCOUNT_ID);
    evalParams.setOfferVersion(OFFER_VERSION);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(FEATURE_NAME, featureValue.getName());
    assertEquals(mockOffer, featureValue.getValue());
    assertEquals(FEATURE_TYPE, featureValue.getType());
    assertNull(featureValue.getErrorMessage());
  }

  @Test
  void testGetType() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
  }

  @Test
  void testGetDescription() {
    assertEquals("This feature returns the offer from GetOfferByBillerAccount", feature.getDescription());
  }
}
