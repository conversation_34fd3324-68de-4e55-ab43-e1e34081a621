package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.GeoLocation;
import com.getflex.featurestore.dao.repo.GeoIpLocationRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.output.IpToGeoLocationOutput;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IpToGeoLocationFeatureTest {

  @InjectMocks
  IpToGeoLocationFeature ipToGeoLocationFeature;

  @Mock
  GeoIpLocationRepo geoIpLocationRepo;

  @Mock
  GeoLocation mockProjection;

  @Test
  public void testGetValueReturnsCompleteGeoLocationData() {
    String ipAddress = "***********";

    // Mock the projection interface methods
    when(mockProjection.getNetwork()).thenReturn("*************/24");
    when(mockProjection.getLatitude()).thenReturn(37.7749);
    when(mockProjection.getLongitude()).thenReturn(-122.4194);
    when(mockProjection.getCityName()).thenReturn("San Francisco");
    when(mockProjection.getCountryName()).thenReturn("United States");
    when(mockProjection.getCountryIsoCode()).thenReturn("US");
    when(mockProjection.getContinentCode()).thenReturn("NA");
    when(mockProjection.getSubdivision1IsoCode()).thenReturn("CA");
    when(mockProjection.getSubdivision1Name()).thenReturn("California");
    when(mockProjection.getSubdivision2IsoCode()).thenReturn("SF");
    when(mockProjection.getSubdivision2Name()).thenReturn("San Francisco County");

    when(geoIpLocationRepo.findGeoLocationDataByIpAddress(ipAddress)).thenReturn(mockProjection);

    EvalParams evalParams = new EvalParams().ipAddress(ipAddress);
    FeatureOutput featureOutput = ipToGeoLocationFeature.getValue(evalParams);

    assertNotNull(featureOutput.value());
    assertEquals(FeatureTypeEnum.OBJECT, ipToGeoLocationFeature.getType());

    IpToGeoLocationOutput geoLocationOutput = (IpToGeoLocationOutput) featureOutput.value();

    assertEquals("*************/24", geoLocationOutput.getNetwork());
    assertEquals(Double.valueOf(37.7749), geoLocationOutput.getLatitude());
    assertEquals(Double.valueOf(-122.4194), geoLocationOutput.getLongitude());
    assertEquals("San Francisco", geoLocationOutput.getCityName());
    assertEquals("United States", geoLocationOutput.getCountryName());
    assertEquals("US", geoLocationOutput.getCountryIsoCode());
    assertEquals("NA", geoLocationOutput.getContinentCode());
    assertEquals("CA", geoLocationOutput.getSubdivision1IsoCode());
    assertEquals("California", geoLocationOutput.getSubdivision1Name());
    assertEquals("SF", geoLocationOutput.getSubdivision2IsoCode());
    assertEquals("San Francisco County", geoLocationOutput.getSubdivision2Name());
  }

  @Test
  public void testGetValueNoGeoDataFound_ReturnsEmptyOutput() {
    String ipAddress = "********";
    when(geoIpLocationRepo.findGeoLocationDataByIpAddress(ipAddress)).thenReturn(null);

    EvalParams evalParams = new EvalParams().ipAddress(ipAddress);
    FeatureOutput featureOutput = ipToGeoLocationFeature.getValue(evalParams);

    assertNotNull(featureOutput.value());
    assertEquals(FeatureTypeEnum.OBJECT, ipToGeoLocationFeature.getType());

    // Verify metadata is set correctly
    assertEquals("No geo location data found for the IP address: " + ipAddress, featureOutput.metadata());

    IpToGeoLocationOutput geoLocationOutput = (IpToGeoLocationOutput) featureOutput.value();

    // Verify empty strings for string fields
    assertEquals("", geoLocationOutput.getNetwork());
    assertEquals("", geoLocationOutput.getCityName());
    assertEquals("", geoLocationOutput.getCountryName());
    assertEquals("", geoLocationOutput.getCountryIsoCode());
    assertEquals("", geoLocationOutput.getContinentCode());
    assertEquals("", geoLocationOutput.getSubdivision1IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision1Name());
    assertEquals("", geoLocationOutput.getSubdivision2IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision2Name());

    // Verify special values for coordinates
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLatitude());
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLongitude());
  }

  @Test
  public void testGetValueWithSomeNullValuesHandlesNullsCorrectly() {
    String ipAddress = "*******";

    // Mock projection with some null values
    when(mockProjection.getNetwork()).thenReturn("*******/24");
    when(mockProjection.getLatitude()).thenReturn(37.4056);
    when(mockProjection.getLongitude()).thenReturn(-122.0775);
    when(mockProjection.getCityName()).thenReturn(null); // null city
    when(mockProjection.getCountryName()).thenReturn("United States");
    when(mockProjection.getCountryIsoCode()).thenReturn("US");
    when(mockProjection.getContinentCode()).thenReturn("NA");
    when(mockProjection.getSubdivision1IsoCode()).thenReturn("CA");
    when(mockProjection.getSubdivision1Name()).thenReturn("California");
    when(mockProjection.getSubdivision2IsoCode()).thenReturn(null); // null subdivision2
    when(mockProjection.getSubdivision2Name()).thenReturn(null); // null subdivision2

    when(geoIpLocationRepo.findGeoLocationDataByIpAddress(ipAddress)).thenReturn(mockProjection);

    EvalParams evalParams = new EvalParams().ipAddress(ipAddress);
    FeatureOutput featureOutput = ipToGeoLocationFeature.getValue(evalParams);

    assertNotNull(featureOutput.value());
    IpToGeoLocationOutput geoLocationOutput = (IpToGeoLocationOutput) featureOutput.value();

    // Non-null values should be set correctly
    assertEquals("*******/24", geoLocationOutput.getNetwork());
    assertEquals(Double.valueOf(37.4056), geoLocationOutput.getLatitude());
    assertEquals(Double.valueOf(-122.0775), geoLocationOutput.getLongitude());
    assertEquals("United States", geoLocationOutput.getCountryName());
    assertEquals("US", geoLocationOutput.getCountryIsoCode());
    assertEquals("NA", geoLocationOutput.getContinentCode());
    assertEquals("CA", geoLocationOutput.getSubdivision1IsoCode());
    assertEquals("California", geoLocationOutput.getSubdivision1Name());

    // Null values from database should remain null
    assertNull(geoLocationOutput.getCityName());
    assertNull(geoLocationOutput.getSubdivision2IsoCode());
    assertNull(geoLocationOutput.getSubdivision2Name());
  }

  @Test
  public void testIpToGeoLocationOutputBuilderAndSetters() {
    // Create an instance using the builder
    IpToGeoLocationOutput output = IpToGeoLocationOutput.builder()
        .network("***********/24")
        .latitude(37.7749)
        .longitude(-122.4194)
        .build();

    // Test initial values
    assertEquals("***********/24", output.getNetwork());
    assertEquals(Double.valueOf(37.7749), output.getLatitude());
    assertEquals(Double.valueOf(-122.4194), output.getLongitude());
    assertNull(output.getCityName());

    // Test setters
    output.setCityName("San Francisco");
    output.setCountryName("United States");
    output.setCountryIsoCode("US");
    output.setContinentCode("NA");

    assertEquals("San Francisco", output.getCityName());
    assertEquals("United States", output.getCountryName());
    assertEquals("US", output.getCountryIsoCode());
    assertEquals("NA", output.getContinentCode());
  }

  @Test
  public void testIpToGeoLocationOutputToBuilder() {
    // Create initial object
    IpToGeoLocationOutput original = IpToGeoLocationOutput.builder()
        .network("***********/24")
        .latitude(37.7749)
        .longitude(-122.4194)
        .cityName("San Francisco")
        .build();

    // Use toBuilder to create modified copy
    IpToGeoLocationOutput modified = original.toBuilder()
        .countryName("United States")
        .countryIsoCode("US")
        .build();

    // Verify original values are preserved
    assertEquals("***********/24", modified.getNetwork());
    assertEquals(Double.valueOf(37.7749), modified.getLatitude());
    assertEquals(Double.valueOf(-122.4194), modified.getLongitude());
    assertEquals("San Francisco", modified.getCityName());

    // Verify new values are set
    assertEquals("United States", modified.getCountryName());
    assertEquals("US", modified.getCountryIsoCode());
  }

  @Test
  public void testGetValueWithNullIpAddress_ReturnsEmptyOutput() {
    // Create EvalParams with null IP address
    EvalParams evalParams = new EvalParams().ipAddress(null);

    // Call the feature method
    FeatureOutput featureOutput = ipToGeoLocationFeature.getValue(evalParams);

    // Verify output is not null
    assertNotNull(featureOutput.value());
    assertEquals(FeatureTypeEnum.OBJECT, ipToGeoLocationFeature.getType());

    // Verify metadata is set correctly
    assertEquals("IP address is null or empty, returning empty object.", featureOutput.metadata());

    // Verify output is an empty IpToGeoLocationOutput object with empty strings
    IpToGeoLocationOutput geoLocationOutput = (IpToGeoLocationOutput) featureOutput.value();

    // All string properties should be empty strings
    assertEquals("", geoLocationOutput.getNetwork());
    assertEquals("", geoLocationOutput.getCityName());
    assertEquals("", geoLocationOutput.getCountryName());
    assertEquals("", geoLocationOutput.getCountryIsoCode());
    assertEquals("", geoLocationOutput.getContinentCode());
    assertEquals("", geoLocationOutput.getSubdivision1IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision1Name());
    assertEquals("", geoLocationOutput.getSubdivision2IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision2Name());

    // Coordinates should be set to special values
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLatitude());
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLongitude());

    // Verify no interactions with repository
    verifyNoInteractions(geoIpLocationRepo);
  }

  @Test
  public void testGetValueWithEmptyIpAddress_ReturnsEmptyOutput() {
    // Create EvalParams with empty IP address
    EvalParams evalParams = new EvalParams().ipAddress("");

    // Call the feature method
    FeatureOutput featureOutput = ipToGeoLocationFeature.getValue(evalParams);

    // Verify output is not null
    assertNotNull(featureOutput.value());
    assertEquals(FeatureTypeEnum.OBJECT, ipToGeoLocationFeature.getType());

    // Verify metadata is set correctly
    assertEquals("IP address is null or empty, returning empty object.", featureOutput.metadata());

    // Verify output is an empty IpToGeoLocationOutput object with empty strings
    IpToGeoLocationOutput geoLocationOutput = (IpToGeoLocationOutput) featureOutput.value();

    // All string properties should be empty strings
    assertEquals("", geoLocationOutput.getNetwork());
    assertEquals("", geoLocationOutput.getCityName());
    assertEquals("", geoLocationOutput.getCountryName());
    assertEquals("", geoLocationOutput.getCountryIsoCode());
    assertEquals("", geoLocationOutput.getContinentCode());
    assertEquals("", geoLocationOutput.getSubdivision1IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision1Name());
    assertEquals("", geoLocationOutput.getSubdivision2IsoCode());
    assertEquals("", geoLocationOutput.getSubdivision2Name());

    // Coordinates should be set to special values
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLatitude());
    assertEquals(Double.valueOf(999.9999), geoLocationOutput.getLongitude());

    // Verify no interactions with repository
    verifyNoInteractions(geoIpLocationRepo);
  }
}