package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.wallet.model.Card;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class MaxNumberOfCustomersLinkedToFundsInTimeWindowFeatureTest {
  @Mock private LedgerService ledgerService;
  @Mock private WalletService walletService;

  RecordLedger ledgerRecord1 =
      new RecordLedger()
          .customerId(1L)
          .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
          .fromPaymentMethodId(123L)
          .paymentStatusId(PaymentState.SETTLED.getValue())
          .paymentCategoryId(MovementCategory.CHARGE.getValue())
          .dtCreated(OffsetDateTime.now().minusHours(1));

  @Test
  public void maxNumberOfCustomersLinkedToFundsInTimeWindow() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of(ledgerRecord1));
    Card card1 = new Card().stripeCustomerId("111");
    Card card2 = new Card().stripeCustomerId("222");
    when(walletService.getCard(any())).thenReturn(card1);
    when(walletService.getCardsByFingerprint(any())).thenReturn(List.of(card1, card2));

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature feature =
        new MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature(
            ledgerService, walletService, new LookbackDurationFeatureParams("P30D"));
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(FeatureTypeEnum.INT, featureValue.getType());
    assertEquals(2, featureValue.getValue());
  }

  @Test
  public void testNoLedgerRecords() {
    MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature feature =
        new MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature(
            ledgerService, walletService, new LookbackDurationFeatureParams("P30D"));

    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    assertEquals(FeatureTypeEnum.INT, featureValue.getType());
    assertEquals(0, featureValue.getValue());
  }
}
