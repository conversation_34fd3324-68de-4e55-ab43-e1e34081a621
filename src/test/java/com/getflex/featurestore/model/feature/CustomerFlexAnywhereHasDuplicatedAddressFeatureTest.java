package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerFlexAnywhereHasDuplicatedAddressFeatureTest {

  @InjectMocks
  CustomerFlexAnywhereHasDuplicatedAddressFeature feature;

  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @ValueSource(ints = {0, 1, 2})
  public void testGetValue(Integer numDuplicatedAddr) {
    Long customerId = 1L;
    when(identityService.getCustomerFlexAnywhereDuplicatedAddress(customerId)).thenReturn(
        numDuplicatedAddr);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(),
        "CustomerFlexAnywhereHasDuplicatedAddressFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.BOOLEAN);
    if (numDuplicatedAddr == 0 || numDuplicatedAddr == 1) {
      Assertions.assertEquals(featureValue.getValue(), false);
    } else {
      Assertions.assertEquals(featureValue.getValue(), true);
    }
  }
}
