package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfDeclinedFundsInFeatureTest {

  private static final Long CUSTOMER_ID = 123L;

  @Mock
  LedgerService ledgerService;

  @Test
  void getType_shouldReturnBoolean() {
    var feature = new NumberOfDeclinedFundsInFeature(ledgerService, new LookbackDurationFeatureParams("P1D"));
    assertEquals(FeatureTypeEnum.INT, feature.getType());
  }

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<RecordLedger> recordLedgers,
      String lookbackWindow,
      Integer expectedAmount
  ) {
    var numberOfDeclinedFundsInFeature = new NumberOfDeclinedFundsInFeature(ledgerService,
        new LookbackDurationFeatureParams(lookbackWindow));
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(recordLedgers);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureValue featureValue = numberOfDeclinedFundsInFeature.fetchFeatureValue(evalParams);
    assertEquals(expectedAmount, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    var failedFundsIn = declinedFundsInRecordLedger();
    var failedFundsIn2 = declinedFundsInRecordLedger();
    var failedFundsIn3 = declinedFundsInRecordLedger();
    var failedFundsInRefund = declinedFundsInRecordLedger()
        .paymentCategoryId(MovementCategory.REFUND.getValue());
    return Stream.of(
        Arguments.of(null, "P7D", 0),
        Arguments.of(List.of(), "P7D", 0),
        Arguments.of(List.of(failedFundsIn), "P7D", 1),
        Arguments.of(List.of(failedFundsIn, failedFundsIn2), "P7D", 2),
        Arguments.of(List.of(failedFundsIn, failedFundsIn2, failedFundsIn3), "P7D", 3),
        Arguments.of(List.of(failedFundsIn, failedFundsIn2, failedFundsIn3, failedFundsInRefund), "P7D", 3)
    );
  }

  private static RecordLedger declinedFundsInRecordLedger() {
    return new RecordLedger()
        .customerId(CUSTOMER_ID)
        .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
        .paymentStatusId(PaymentState.DECLINED.getValue())
        .paymentCategoryId(MovementCategory.CHARGE.getValue())
        .dtCreated(OffsetDateTime.now());
  }
}
