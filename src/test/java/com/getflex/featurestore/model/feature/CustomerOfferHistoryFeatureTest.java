package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.CustomerOfferHistoryFeature.CustomerOfferHistory;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OfferState;
import com.getflex.offerv2.model.ProductType;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerOfferHistoryFeatureTest {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

  private static long customerId = 1L;
  private static String ssnHmac = "124";

  @Mock
  OfferService offerService;

  @Mock
  Clock etClock;

  @InjectMocks
  CustomerOfferHistoryFeature customerOfferHistoryFeature;

  @BeforeEach
  public void setUpFixedClock() {
    Instant fixedInstant = Instant.parse("2024-01-01T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);
  }

  @ParameterizedTest
  @CsvSource("""
      2022, 2023, 730, 365, false
      2021, 2022, 1095, 730, false
      2023,, 365, -1, false
      2022, 2023, -1, -1, true
      2021, 2022, -1, -1, true
      2023,, -1, -1, true
      """)
  public void getValue_success(Integer acceptYear, Integer closeYear, Long daysSinceAccept,
      Long daysSinceClose, boolean isCb) {
    ZoneOffset offset = ZonedDateTime.now(ZoneId.of("America/New_York")).getOffset();
    InternalOffer offer1 = new InternalOffer().customerId(customerId).offerId("offer-1")
        .productType(isCb ? ProductType.CREDITBUILDER : ProductType.REGULAR)
        .state(OfferState.ACCEPTED.getValue()).initiationTime(OffsetDateTime.of(acceptYear, 1, 1, 0, 0, 0, 0, offset))
        .acceptanceTime(OffsetDateTime.of(acceptYear, 1, 1, 0, 0, 0, 0, offset));
    InternalOffer offer2 = new InternalOffer().customerId(customerId).offerId("offer-1")
        .productType(isCb ? ProductType.CREDITBUILDER : ProductType.REGULAR)
        .state(OfferState.CLOSED.getValue()).initiationTime(OffsetDateTime.of(acceptYear, 1, 1, 0, 0, 0, 0, offset))
        .acceptanceTime(OffsetDateTime.of(acceptYear, 1, 1, 0, 0, 0, 0, offset));
    if (closeYear != null) {
      offer2.terminationTime(OffsetDateTime.of(closeYear, 1, 1, 0, 0, 0, 0, offset))
          .deactivationReason("CancelCreditLine");
    }
    InternalOffer offer3 = new InternalOffer().customerId(customerId).offerId("offer-2")
        .productType(isCb ? ProductType.CREDITBUILDER : ProductType.REGULAR)
        .state(OfferState.ACCEPTED.getValue()).initiationTime(OffsetDateTime.of(
            LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset)).acceptanceTime(
                OffsetDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset));
    InternalOffer offer4 = new InternalOffer().customerId(customerId).offerId("offer-2")
        .productType(isCb ? ProductType.CREDITBUILDER : ProductType.REGULAR)
        .state(OfferState.CLOSED.getValue()).deactivationReason("CancelCreditLine")
        .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset))
        .acceptanceTime(OffsetDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset))
        .terminationTime(OffsetDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset));
    EvalParams evalParams = new EvalParams().customerId(customerId).ssnHmac(ssnHmac);
    when(offerService.getOfferBySsnHmac(customerId, ssnHmac)).thenReturn(List.of(offer1, offer2, offer3, offer4));

    FeatureOutput output = customerOfferHistoryFeature.getValue(evalParams);
    CustomerOfferHistory history = OBJECT_MAPPER.convertValue(output.value(), CustomerOfferHistory.class);
    CustomerOfferHistory expectedHistory = new CustomerOfferHistory(daysSinceAccept, daysSinceClose);
    Assertions.assertEquals(expectedHistory, history);
  }

  @Test
  public void getValue_noRecords() {
    ZoneOffset offset = ZonedDateTime.now(ZoneId.of("America/New_York")).getOffset();
    InternalOffer offer =
        new InternalOffer().customerId(customerId).offerId("offer-2").state(OfferState.DENIED.getValue())
            .initiationTime(OffsetDateTime.of(LocalDate.of(2021, 1, 1), LocalTime.MIDNIGHT, offset));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    evalParams.ssnHmac(ssnHmac);
    when(offerService.getOfferBySsnHmac(customerId, ssnHmac)).thenReturn(List.of(offer));

    FeatureOutput output = customerOfferHistoryFeature.getValue(evalParams);
    CustomerOfferHistory history = OBJECT_MAPPER.convertValue(output.value(), CustomerOfferHistory.class);
    CustomerOfferHistory expectedHistory = new CustomerOfferHistory(-1L, -1L);
    Assertions.assertEquals(expectedHistory, history);
  }

  @Test
  public void getValue_failure() {
    when(offerService.getOfferBySsnHmac(customerId, ssnHmac)).thenThrow(
        new InternalDependencyFailureException("failure!"));
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    evalParams.ssnHmac(ssnHmac);
    FeatureOutput output = customerOfferHistoryFeature.getValue(evalParams);
    CustomerOfferHistory history = OBJECT_MAPPER.convertValue(output.value(), CustomerOfferHistory.class);
    CustomerOfferHistory expectedHistory = new CustomerOfferHistory(-1L, -1L);
    Assertions.assertEquals(expectedHistory, history);
  }

}
