package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerBillerIntegrationTypeFeatureTest {

  public static final Long BILLER_ID = 123L;
  public static final ExtendedBillerAccountData BILLER_ACCOUNT_DATA =
      new ExtendedBillerAccountData().billerId(BILLER_ID);

  @InjectMocks
  CustomerBillerIntegrationTypeFeature feature;

  @Mock
  BillingService billingService;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;

  private static Stream<Arguments> integrationTypeTestCases() {
    return Stream.of(
        Arguments.of(1L, BillingIntegrationTypeEnum.FLEX_ANYWHERE, "FLEX_ANYWHERE"),
        Arguments.of(2L, BillingIntegrationTypeEnum.PORTAL, "PORTAL"),
        Arguments.of(3L, BillingIntegrationTypeEnum.DIRECT_INTEGRATION, "DIRECT_INTEGRATION"),
        Arguments.of(4L, BillingIntegrationTypeEnum.YARDI, "YARDI"),
        Arguments.of(5L, BillingIntegrationTypeEnum.P2P, "P2P"),
        Arguments.of(100L, null, null)
    );
  }

  @ParameterizedTest
  @MethodSource("integrationTypeTestCases")
  public void testIntegrationTypeValues(Long billerAccountId, BillingIntegrationTypeEnum integrationType,
      String expectedValue) {

    when(identityService.getBillerAccount(billerAccountId)).thenReturn(BILLER_ACCOUNT_DATA);

    when(billingService.getPropertyByIdentityBillerId(BILLER_ID))
        .thenReturn(new PropertyInfo(integrationType, false, null));

    EvalParams evalParams = new EvalParams();
    evalParams.setBillerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals("CustomerBillerIntegrationTypeFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.STRING, featureValue.getType());
    Assertions.assertEquals(expectedValue, featureValue.getValue());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetType() {
    Assertions.assertEquals(FeatureTypeEnum.STRING, feature.getType());
  }

  @Test
  public void testGetDescription() {
    Assertions.assertEquals("Get Customer Biller Integration Type", feature.getDescription());
  }
}
