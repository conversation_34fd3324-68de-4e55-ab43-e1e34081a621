package com.getflex.featurestore.model.feature.fraud;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.tagging.TaggingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.utils.FraudConstant.FraudTagName;
import com.getflex.tagging.model.CustomerTagDataItem;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class FraudTaggedFeatureTests {

  private static final Long FRAUDSTER_ID = 555L;
  private static final Long CUSTOMER_ID = 123L;

  @Mock
  TaggingService taggingService;

  @ParameterizedTest
  @MethodSource
  public void testFraudIsTaggedFeature(FraudTagName fraudTagName,
      Class<? extends BaseFraudTaggedFeature> fraudFeatureClass)
      throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
    when(taggingService.getFraudTags(FRAUDSTER_ID)).thenReturn(
        List.of(createCustomerTagDataItem(fraudTagName.name())
        ));
    when(taggingService.getFraudTags(CUSTOMER_ID)).thenReturn(
        List.of(createCustomerTagDataItem("some_other_tag")
        ));

    BaseFraudTaggedFeature feature = fraudFeatureClass.getConstructor(TaggingService.class)
        .newInstance(taggingService);
    FeatureValue fv1 = feature.fetchFeatureValue(
        new EvalParams().customerId(FRAUDSTER_ID));
    assertEquals(fv1.getValue(), true);

    FeatureValue fv2 = feature.fetchFeatureValue(
        new EvalParams().customerId(CUSTOMER_ID));
    assertEquals(fv2.getValue(), false);

    assertEquals(fraudTagName, feature.getFraudTagName());
  }

  private static Stream<Arguments> testFraudIsTaggedFeature() {
    return Stream.of(
        Arguments.of(FraudTagName.IDENTITY_THEFT, IsFraudTaggedIdentityTheftFeature.class),
        Arguments.of(FraudTagName.STOLEN_FINANCIALS, IsFraudTaggedStolenFinancialsFeature.class),
        Arguments.of(FraudTagName.ACCOUNT_TAKEOVER, IsFraudTaggedAccountTakeoverFeature.class),
        Arguments.of(FraudTagName.REPETITIVE_DISPUTE, IsFraudTaggedRepetitiveDisputeFeature.class),
        Arguments.of(FraudTagName.SUBSCRIPTION_CANCELED_DISPUTE,
            IsFraudTaggedSubscriptionCanceledDisputeFeature.class),
        Arguments.of(FraudTagName.FRAUD_INVESTIGATION, IsFraudTaggedFraudInvestigationFeature.class),
        Arguments.of(FraudTagName.BLACKLISTED, IsFraudTaggedBlacklistedFeature.class),
        Arguments.of(FraudTagName.FRAUDULENT_DISPUTE, IsFraudTaggedFraudulentDisputeFeature.class),
        Arguments.of(FraudTagName.ABUSE, IsFraudTaggedAbuseFeature.class)
    );
  }

  private CustomerTagDataItem createCustomerTagDataItem(String tagName) {
    return new CustomerTagDataItem().tagName(tagName)
        .tagCreatedBy("<EMAIL>")
        .customerTagEffectiveBeginDate(LocalDate.now());
  }
}
