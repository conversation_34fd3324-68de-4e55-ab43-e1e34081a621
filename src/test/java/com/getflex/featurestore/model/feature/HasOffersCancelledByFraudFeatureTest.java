package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.offerv2.model.InternalOffer;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class HasOffersCancelledByFraudFeatureTest {

  @Mock
  OfferService offerService;

  @InjectMocks
  HasOffersCancelledByFraudFeature feature;

  @ParameterizedTest
  @CsvSource(textBlock = """
      Other, Fraud, false
      CancelCreditLine, Fraud, true,
      ,,false
      """)
  public void getValue_success(String reason, String detail, boolean expected) {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);
    InternalOffer offer1 = new InternalOffer();
    offer1.setDeactivationReason(reason);
    offer1.setDeactivationReasonDetail(detail);
    InternalOffer offer2 = new InternalOffer();
    offer2.setDeactivationReason(reason);
    offer2.setDeactivationReasonDetail(detail);

    when(offerService.searchOffer(evalParams.getCustomerId(), true)).thenReturn(List.of(offer1, offer2));
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }

  @Test
  public void getValue_failure() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);

    when(offerService.searchOffer(evalParams.getCustomerId(), true))
        .thenThrow(new InternalDependencyFailureException("error!"));
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertFalse(Boolean.parseBoolean(output.value().toString()));
  }

}
