package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.integration.socure.SocureService.SOCURE_BEST_MATCHED_ENTITY_PATH;
import static com.getflex.featurestore.integration.socure.SocureService.SOCURE_DOB_FIELD;
import static com.getflex.featurestore.integration.socure.SocureService.SOCURE_SSN_FIELD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.getflex.cipher.util.CipherUtil;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.socure.SocureService;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class SocureFeatureTest {
  @InjectMocks
  SocureFeature socureFeature;

  @Mock
  IdentityService identityService;

  @Mock
  SocureService socureService;

  @Mock
  CipherUtil cipherUtil;

  @Test
  void getType_mustBeObject() {
    assertEquals(FeatureTypeEnum.OBJECT, socureFeature.getType());
  }

  @Test
  void getDescription_mustBeNonEmpty() {
    assertFalse(socureFeature.getDescription().isEmpty());
  }

  @Test
  void getRequiredEvalParamKeys_idAndIp() {
    assertEquals(
        All.of(EvalParamKey.CUSTOMER_ID, EvalParamKey.IP_ADDRESS, EvalParamKey.SSN_CIPHER, EvalParamKey.DOB_CIPHER),
        socureFeature.getRequiredEvalParamKeys());
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      123-456-7890, 2000-01-01
      1234567890, 2000/01/01
      """)
  void getValue_success(String matchedSsn, String matchedDob) {
    CustomerInfo customerInfo = CustomerInfo.builder().customerId(3L).state("WA").build();
    when(identityService.getCompleteCustomerInfo(any())).thenReturn(customerInfo);
    ObjectNode socureResult = new ObjectMapper().createObjectNode();
    socureResult.withObject(SOCURE_BEST_MATCHED_ENTITY_PATH).put(SOCURE_SSN_FIELD, matchedSsn)
        .put(SOCURE_DOB_FIELD, matchedDob).put("otherPii", "pii");
    when(socureService.runKyc(any())).thenReturn(socureResult);
    when(cipherUtil.encryptWithCache(matchedSsn)).thenReturn("newSsnCipher");
    when(cipherUtil.encryptWithCache(matchedDob)).thenReturn("newDobCipher");

    var result = socureFeature.getValue(new EvalParams().customerId(3L).ipAddress("***********"));

    assertNotNull(result.value());
    assertInstanceOf(JsonNode.class, result.value());
    JsonNode featureValue = (JsonNode) result.value();
    assertEquals("newSsnCipher", featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get(SOCURE_SSN_FIELD).textValue());
    assertEquals("newDobCipher", featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get(SOCURE_DOB_FIELD).textValue());
    assertNull(featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get("otherPii"));
  }

  @ParameterizedTest
  @CsvSource(textBlock = """
      The source used in verification does not return underlying data values.,A2000/01/01
      The source used in verification does not return underlying data values.,2000/01/01B
      A1234567890,The source used in verification does not return underlying data values.
      1234567890B,The source used in verification does not return underlying data values.
      """)
  void getValue_filterOutInvalidPii(String matchedSsn, String matchedDob) {
    CustomerInfo customerInfo = CustomerInfo.builder().customerId(3L).state("WA").build();
    when(identityService.getCompleteCustomerInfo(any())).thenReturn(customerInfo);
    ObjectNode socureResult = new ObjectMapper().createObjectNode();
    socureResult.withObject(SOCURE_BEST_MATCHED_ENTITY_PATH).put(SOCURE_SSN_FIELD, matchedSsn)
        .put(SOCURE_DOB_FIELD, matchedDob).put("otherPii", "pii");
    when(socureService.runKyc(any())).thenReturn(socureResult);

    var result = socureFeature.getValue(new EvalParams().customerId(3L).ipAddress("***********"));

    assertNotNull(result.value());
    assertInstanceOf(JsonNode.class, result.value());
    JsonNode featureValue = (JsonNode) result.value();
    assertNull(featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get(SOCURE_SSN_FIELD));
    assertNull(featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get(SOCURE_DOB_FIELD));
    assertNull(featureValue.at(SOCURE_BEST_MATCHED_ENTITY_PATH).get("otherPii"));
  }
}
