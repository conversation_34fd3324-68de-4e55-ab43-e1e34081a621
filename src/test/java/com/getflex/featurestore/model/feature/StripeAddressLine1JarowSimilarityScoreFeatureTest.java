package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StripeAddressLine1JarowSimilarityScoreFeatureTest {
  @InjectMocks
  private StripeAddressLine1JarowSimilarityScoreFeature feature;

  @Mock
  private IdentityService identityService;

  private final Long customerId = 123L;
  private final GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId)
      .addressLine1("1250 Minot Dr");

  @Test
  public void getValue_sameAddress() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeAddressLine1(" 1250 Minot Dr ");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "StripeAddressLine1JarowSimilarityScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(100.0, featureValue.getValue());
  }

  @Test
  public void getValue_differentAddress() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeAddressLine1("88 University Place");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(48.133, (Double) featureValue.getValue(), 0.001);
  }

  @Test
  public void getValue_default() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeAddressLine1("abc");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(-1.0, featureValue.getValue());
  }

  @Test
  public void getValue_defaultNullIdentityRecord() {
    GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId);
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeAddressLine1("abc");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(-1.0, featureValue.getValue());
  }

  @Test
  public void getValue_defaultNoName() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripeAddressLine1("");

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(-1.0, featureValue.getValue());
  }
}
