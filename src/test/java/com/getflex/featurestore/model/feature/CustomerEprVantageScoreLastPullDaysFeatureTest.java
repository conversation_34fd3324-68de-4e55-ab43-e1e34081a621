package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.All;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CustomerEprVantageScoreLastPullDaysFeatureTest {
  String customerId = "123";
  @InjectMocks
  CustomerEprVantageScoreLastPullDaysFeature customerEprVantageScoreLastPullDaysFeature;
  @Mock
  OfflineFeatureRepo offlineFeatureRepo;
  public static final String FEATURE_NAME = "customer_epr_vantage_score";

  /**
   *
   */
  @Test
  public void testCustomerEprVantageScoreLastPullDaysFeature() {
    long days = 10L;
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            FEATURE_NAME, customerId))
            .thenReturn(List.of(OfflineFeature.builder().dtCreated(OffsetDateTime.now().minusDays(days)).build()));
    FeatureValue featureValue = customerEprVantageScoreLastPullDaysFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(days, featureValue.getValue());
    Assertions.assertEquals("CustomerEprVantageScoreLastPullDaysFeature", featureValue.getName());
    Assertions.assertEquals(FeatureTypeEnum.INT, featureValue.getType());
    Assertions.assertEquals("Number of days since the last Vantage Score pull for the customer",
            customerEprVantageScoreLastPullDaysFeature.getDescription());
    Assertions.assertEquals(All.of(EvalParamKey.CUSTOMER_ID),
        customerEprVantageScoreLastPullDaysFeature.getRequiredEvalParamKeys());
    Assertions.assertEquals(FEATURE_NAME,
            customerEprVantageScoreLastPullDaysFeature.getOfflineFeatureName());
    Assertions.assertEquals(customerId, customerEprVantageScoreLastPullDaysFeature.getPrimaryKey(evalParams));
  }

  @Test
  public void testCustomerEprVantageScoreLastPullDaysFeatureReturnEmpty() {
    Mockito.when(offlineFeatureRepo.findAllByFeatureNameAndPrimaryKeyStartingWithOrderByDtCreatedDesc(
            FEATURE_NAME, customerId))
            .thenReturn(List.of());
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(Long.valueOf(customerId));
    FeatureValue featureValue = customerEprVantageScoreLastPullDaysFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerEprVantageScoreLastPullDaysFeature");
    Assertions.assertEquals(-1L, featureValue.getValue());
  }
}