package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.ProductType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class IsCustomerSlcFeatureTest {

  private final Long customerId = 123L;
  private final String featureName = "IsCustomerSlcFeature";

  @InjectMocks
  IsCustomerSlcFeature isCustomerSlcFeature;

  @Mock OfferService offerService;

  /** Test case for IsCustomerSlcFeature return true when the offerService return SLC type */
  @Test
  public void testIsSlcFeatureReturnTrueWhenProductTypeSlc() {
    InternalOffer offer = new InternalOffer();
    offer.setProductType(ProductType.SLC);
    Mockito.when(offerService.getOfferByCustomerId(customerId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue = isCustomerSlcFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /** Test case for IsCustomerSlcFeature return true when the offerService return CREDITBUILDER */
  @Test
  public void testIsSlcFeatureReturnTrueWhenProductTypeCreditBuilder() {
    InternalOffer offer = new InternalOffer();
    offer.setProductType(ProductType.CREDITBUILDER);
    Mockito.when(offerService.getOfferByCustomerId(customerId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue = isCustomerSlcFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertTrue((Boolean) featureValue.getValue());
  }

  /**
   * Test case for IsCustomerSlcFeature return true when the offerService not return CREDITBUILDER or SLC
   */
  @Test
  public void testIsSlcFeatureReturnWhenProductTypeNotInSelectedList() {
    InternalOffer offer = new InternalOffer();
    offer.setProductType(ProductType.LITE);
    Mockito.when(offerService.getOfferByCustomerId(customerId)).thenReturn(offer);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue = isCustomerSlcFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }

  /** Test case for IsCustomerSlcFeature return true when the offerService not return null */
  @Test
  public void testIsSlcFeatureReturnFalseWhenReturnNull() {
    Mockito.when(offerService.getOfferByCustomerId(customerId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    FeatureValue featureValue = isCustomerSlcFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), featureName);
    Assertions.assertFalse((Boolean) featureValue.getValue());
  }
}