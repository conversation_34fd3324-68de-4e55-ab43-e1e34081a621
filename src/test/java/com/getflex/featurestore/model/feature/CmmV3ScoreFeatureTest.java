package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.CmmMetadata;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.cmmv3.CmmV3Mrc;
import com.getflex.featurestore.model.feature.cmmv3.CmmV3OfflineFeatureValue;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CmmV3ScoreFeatureTest {

  private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

  @InjectMocks
  CmmV3ScoreFeature cmmV3ScoreFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 1234L;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void getValue(boolean featureExists) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("cmm_v3_score")
            .primaryKey("1234").featureValue("{\"score\": 0.05}")
            .build());
    if (!featureExists) {
      feature = Optional.empty();
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "cmm_v3_score", "1234")).thenReturn(feature);
    FeatureValue featureValue = cmmV3ScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CmmV3ScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), 0.05);
    } else {
      Assertions.assertEquals(featureValue.getValue(), -1.0);
    }
  }

  @SneakyThrows
  @Test
  public void getValue_MrcValues() {
    String rawFeatureValue;
    try (InputStream resource =
        this.getClass().getResourceAsStream("/offline_feature/cmm_v3_feature_value.json")) {
      rawFeatureValue = new String(resource.readAllBytes(), StandardCharsets.UTF_8);
    }
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("cmm_v3_score")
            .primaryKey("1234").featureValue(rawFeatureValue)
            .build());
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "cmm_v3_score", "1234")).thenReturn(feature);

    FeatureValue featureValue = cmmV3ScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CmmV3ScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertEquals(featureValue.getValue(), 0.07);
    Assertions.assertNotNull(featureValue.getMetadata());
    CmmMetadata metadata = OBJECT_MAPPER.readValue(featureValue.getMetadata(), CmmMetadata.class);
    Assertions.assertEquals(metadata.getModelReasonCodes().size(), 4);
    Assertions.assertTrue(metadata.getModelReasonCodes().contains(CmmV3Mrc.MF01.name()));
    Assertions.assertTrue(metadata.getModelReasonCodes().contains(CmmV3Mrc.MF03.name()));
    Assertions.assertTrue(metadata.getModelReasonCodes().contains(CmmV3Mrc.MF11.name()));
    Assertions.assertTrue(metadata.getModelReasonCodes().contains(CmmV3Mrc.MF10.name()));
  }

  @ParameterizedTest
  @MethodSource("provideAttributeToModelReasonCodeMappings")
  public void getRejectionCodes_AttributeMappings(String attributeName, CmmV3Mrc expectedMrc, boolean shouldBeMapped) {
    // Create a feature value with a single attribute having a high SHAP value
    Map<String, Double> shapValues = new HashMap<>();
    shapValues.put(attributeName, 0.5); // High SHAP value to ensure it's selected
    
    CmmV3OfflineFeatureValue featureValue = new CmmV3OfflineFeatureValue();
    featureValue.setScore(0.1);
    featureValue.setShapValues(shapValues);
    
    Set<CmmV3Mrc> rejectionCodes = cmmV3ScoreFeature.getRejectionCodes(featureValue);
    
    if (shouldBeMapped) {
      Assertions.assertEquals(1, rejectionCodes.size(), 
          "Expected exactly one rejection code for mapped attribute: " + attributeName);
      Assertions.assertTrue(rejectionCodes.contains(expectedMrc), 
          "Expected rejection code " + expectedMrc + " for attribute: " + attributeName);
    } else {
      Assertions.assertEquals(0, rejectionCodes.size(), 
          "Expected no rejection codes for unmapped attribute: " + attributeName);
    }
  }

  private static Stream<Arguments> provideAttributeToModelReasonCodeMappings() {
    return Stream.of(
        Arguments.of("PAY_SUCCESS_RATE_1MON", CmmV3Mrc.MF01, true),
        Arguments.of("PAY_SUCCESS_RATE_24MON", CmmV3Mrc.MF01, true),
        Arguments.of("PAY_SUCCESS_RATE_6MON", CmmV3Mrc.MF01, true),
        Arguments.of("DQ0_FREQ_LAST_12MONTHS", CmmV3Mrc.MF03, true),
        Arguments.of("PAYMENT_RESCHULED_1MON", CmmV3Mrc.MF11, true),
        Arguments.of("BP_RATE_LAST_6MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("PAY_SUCCESS_RATE_3MON", CmmV3Mrc.MF01, true),
        Arguments.of("CPR_VANTAGE40_SCORE", CmmV3Mrc.MM04, true),
        Arguments.of("PAY_SUCCESS_RATE_1MON_GREATERTHAN50", CmmV3Mrc.MF01, true),
        Arguments.of("CDQ0_FREQ_1MON", CmmV3Mrc.MF02, true),
        Arguments.of("AVG_DAYS_REPAYMENT_LEFT_1MON", CmmV3Mrc.MF11, true),
        Arguments.of("BP_RATE_LAST_12MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("CDQ_UP_TO_30_FREQ_1MON", CmmV3Mrc.MF02, true),
        Arguments.of("NSF_RATE_LAST_6MONTHS", CmmV3Mrc.MF02, true),
        Arguments.of("NUMBER_OF_BILL_PAID_LAST_6MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("PAY_SUCCESS_RATE_DOWN_0MON", CmmV3Mrc.MF01, true),
        Arguments.of("NUMBER_OF_BILL_PAID_LAST_12MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("NSF_RATE_LAST_24MONTHS", CmmV3Mrc.MF02, true),
        Arguments.of("AVG_TRANS_24MON", CmmV3Mrc.MF02, true),
        Arguments.of("NUMBER_OF_BILL_PAID_LAST_24MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("CDQ0_FREQ_6MON", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_CFFE02_COLTOT", CmmV3Mrc.MM05, true),
        Arguments.of("AVG_DAYS_REPAYMENT_LEFT_24MON", CmmV3Mrc.MF11, true),
        Arguments.of("MAX_DAYS_DOWN_PAID_0MON", CmmV3Mrc.MF02, true),
        Arguments.of("AVG_DAYS_REPAYMENT_LEFT_3MON", CmmV3Mrc.MF11, true),
        Arguments.of("CPR_CFFE02_BALGRE", CmmV3Mrc.MM07, true),
        Arguments.of("CPR_EADS142_G411S", CmmV3Mrc.MM09, true),
        Arguments.of("MONTHS_IN_SUSPENSION_LAST_24MONTHS", CmmV3Mrc.MF02, true),
        Arguments.of("MONTHS_ACTIVE_LAST_6MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("AVG_DAYS_REPAYMENT_LEFT_6MON", CmmV3Mrc.MF11, true),
        Arguments.of("AVERAGE_CREDIT_UTILIZATION_RATIO_LAST_6MONTHS", CmmV3Mrc.MM06, true),
        Arguments.of("AVG_TRANS_3MON", CmmV3Mrc.MF02, true),
        Arguments.of("NUMBER_OF_BP_INITIATION_LAST_12MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("CPR_EADS142_G224C", CmmV3Mrc.MM05, true),
        Arguments.of("CDQ0_FREQ_24MON", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_CFFE02_REVUT", CmmV3Mrc.MM06, true),
        Arguments.of("PAY_SUCCESS_RATE_6MON_GREATERTHAN50", CmmV3Mrc.MF01, true),
        Arguments.of("CPR_CFFE02_MONDE", CmmV3Mrc.MM12, true),
        Arguments.of("CPR_CFFE02_COLNMED", CmmV3Mrc.MM05, true),
        Arguments.of("CPR_CFFE02_IACCT24", CmmV3Mrc.MM05, true),
        Arguments.of("AVG_TRANS_0MON", CmmV3Mrc.MF02, true),
        Arguments.of("AVG_DECLINE_INSUFF_FUNDS_0MON", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_EADS142_BR02S", CmmV3Mrc.MM07, true),
        Arguments.of("CPR_CFFE02_ACCTDE", CmmV3Mrc.MF05, true),
        Arguments.of("CPR_CFFE02_RETOP", CmmV3Mrc.MF07, true),
        Arguments.of("CPR_CFFE02_MONOREV", CmmV3Mrc.MM08, true),
        Arguments.of("MONTHS_SINCE_LAST_SIGNUP", CmmV3Mrc.MM10, true),
        Arguments.of("CPR_EADS142_S061S", CmmV3Mrc.MM05, true),
        Arguments.of("CPR_CFFE02_ACCTREG", CmmV3Mrc.MM07, true),
        Arguments.of("AVG_DECLINE_INSUFF_FUNDS_3MON", CmmV3Mrc.MF02, true),
        Arguments.of("AVG_DECLINE_INSUFF_FUNDS_6MON_GREATERTHAN50", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_CFFE02_REV24M", CmmV3Mrc.MM05, true),
        Arguments.of("NUMBER_OF_BP_INITIATION_LAST_6MONTHS", CmmV3Mrc.MF10, true),
        Arguments.of("AVG_DECLINE_INSUFF_FUNDS_1MON_GREATERTHAN50", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_CFFE02_HCLGRE", CmmV3Mrc.MM07, true),
        Arguments.of("CPR_EADS142_AT24S", CmmV3Mrc.MM07, true),
        Arguments.of("CPR_CFFE02_REV100UT", CmmV3Mrc.MM06, true),
        Arguments.of("MONTHS_INACTIVE_L24", CmmV3Mrc.MF10, true),
        Arguments.of("AVG_TRANS_1MON_GREATERTHAN50", CmmV3Mrc.MF02, true),
        Arguments.of("MAX_DAYS_DOWN_PAID_1MON", CmmV3Mrc.MF02, true),
        Arguments.of("CPR_CFFE02_ACT24M", CmmV3Mrc.MM05, true)
    );
  }
}
