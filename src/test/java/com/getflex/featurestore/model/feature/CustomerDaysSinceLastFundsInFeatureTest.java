package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.utils.FlexConstant.UTC_TIMEZONE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDaysSinceLastFundsInFeatureTest {

  @InjectMocks
  CustomerDaysSinceLastFundsInFeature feature;

  @Mock
  LedgerService ledgerService;

  private static final long CUSTOMER_ID = 123L;

  @Test
  public void getValue_success() {
    long expected = 4L;
    OffsetDateTime date = OffsetDateTime.now(UTC_TIMEZONE).minusDays(expected);
    RecordLedger record1 = createLedgerRecord(MoneyMovementType.DOWNPAYMENT, MovementCategory.CHARGE, date);
    RecordLedger record2 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CHARGE, date);
    RecordLedger record3 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CAPTURE, date);
    List<RecordLedger> response = List.of(record1, record2, record3);
    when(ledgerService.getLedgersByCustomerId(any(), anyBoolean())).thenReturn(response);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }

  @Test
  public void getValue_successMultipleDates() {
    long expected = 1L;
    OffsetDateTime date1 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(expected);
    OffsetDateTime date2 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(expected + 1);
    OffsetDateTime date3 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(expected + 10);
    RecordLedger record1 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CHARGE, date1);
    RecordLedger record2 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CHARGE, date2);
    RecordLedger record3 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CHARGE, date3);
    List<RecordLedger> response = List.of(record1, record2, record3);
    when(ledgerService.getLedgersByCustomerId(any(), anyBoolean())).thenReturn(response);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }

  @Test
  public void getValue_success25hours() {
    long expected = 1L;
    OffsetDateTime date1 = OffsetDateTime.now(UTC_TIMEZONE).minusHours(25);
    RecordLedger record1 = createLedgerRecord(MoneyMovementType.FUNDS_IN, MovementCategory.CHARGE, date1);
    List<RecordLedger> response = List.of(record1);
    when(ledgerService.getLedgersByCustomerId(any(), anyBoolean())).thenReturn(response);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput output = feature.getValue(evalParams);
    Assertions.assertEquals(expected, output.value());
  }

  @Test
  public void getValue_NoRecords() {
    List<RecordLedger> response = Collections.emptyList();
    when(ledgerService.getLedgersByCustomerId(any(), anyBoolean())).thenReturn(response);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getValue_NoFundsInRecords() {
    OffsetDateTime date1 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(1);
    OffsetDateTime date2 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(2);
    OffsetDateTime date3 = OffsetDateTime.now(UTC_TIMEZONE).minusDays(3);
    RecordLedger record1 = createLedgerRecord(MoneyMovementType.CUSTOMER_CREDIT, MovementCategory.CHARGE, date1);
    RecordLedger record2 = createLedgerRecord(MoneyMovementType.CUSTOMER_CREDIT, MovementCategory.CHARGE, date2);
    RecordLedger record3 = createLedgerRecord(MoneyMovementType.CUSTOMER_CREDIT, MovementCategory.CHARGE, date3);
    List<RecordLedger> response = List.of(record1, record2, record3);
    when(ledgerService.getLedgersByCustomerId(any(), anyBoolean())).thenReturn(response);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));
  }

  private RecordLedger createLedgerRecord(MoneyMovementType type, MovementCategory category,
      OffsetDateTime dtCreated) {
    RecordLedger record = new RecordLedger();
    record.setMoneyMovementTypeId(type.getValue());
    record.setPaymentCategoryId(category.getValue());
    record.setDtCreated(dtCreated);
    return record;
  }

}
