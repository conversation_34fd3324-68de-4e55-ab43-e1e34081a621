package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class EditDistanceToStripePaymentFirstNameFeatureTest {

  @InjectMocks
  private EditDistanceToStripePaymentFirstNameFeature editDistanceToStripePaymentFirstNameFeature;

  @Mock
  private IdentityService identityService;

  private final Long customerId = 123L;
  private final GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId).firstName("John")
      .lastName("Smith");

  @Test
  public void getValue_sameName() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("John Smith");

    FeatureValue featureValue = editDistanceToStripePaymentFirstNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFirstNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 0);
  }

  @Test
  public void getValue_differentName() {
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("Jack Smith");

    FeatureValue featureValue = editDistanceToStripePaymentFirstNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFirstNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 3);
  }

  @Test
  public void getValue_default() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = editDistanceToStripePaymentFirstNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFirstNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), -1);
  }

  @Test
  public void getValue_defaultNullIdentityRecord() {
    GetCustomerResponse customer = new GetCustomerResponse().customerId(customerId);
    when(identityService.getCustomer(customerId)).thenReturn(customer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("John Smith");

    FeatureValue featureValue = editDistanceToStripePaymentFirstNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFirstNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), -1);
  }

  @Test
  public void getValue_defaultNoName() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    evalParams.setStripePaymentFullName("");

    FeatureValue featureValue = editDistanceToStripePaymentFirstNameFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "EditDistanceToStripePaymentFirstNameFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), -1);
  }
}
