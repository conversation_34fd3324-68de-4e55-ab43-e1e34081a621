package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.integration.sagemaker.model.FraudTransactionModelV3Input;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FraudTransactionModelV3ScoreFeatureTest {

  @InjectMocks
  FraudTransactionModelV3ScoreFeature feature;

  @Test
  public void modelFeatureOrderList() {
    List<FraudTransactionModelV3Input> modelFeatures = feature.getModelFeaturesList();
    Assertions.assertEquals(71, modelFeatures.size());

    Assertions.assertEquals(FraudTransactionModelV3Input.TOTAL_SUCESSFUL_PAYMENT, modelFeatures.get(0));
    Assertions.assertEquals(FraudTransactionModelV3Input.BP_RATE_LAST_6MONTHS, modelFeatures.get(1));
    Assertions.assertEquals(FraudTransactionModelV3Input.BP_RATE_LAST_12MONTHS, modelFeatures.get(2));
    Assertions.assertEquals(FraudTransactionModelV3Input.PAY_SUCCESS_RATE_6MON, modelFeatures.get(3));
    Assertions.assertEquals(FraudTransactionModelV3Input.AVG_DECLINE_INSUFF_FUNDS_6MON, modelFeatures.get(4));
    Assertions.assertEquals(FraudTransactionModelV3Input.MONTHS_SINCE_FIRST_SIGNUP, modelFeatures.get(5));
    Assertions.assertEquals(FraudTransactionModelV3Input.MONTHS_SINCE_LAST_SIGNUP, modelFeatures.get(6));
    Assertions.assertEquals(FraudTransactionModelV3Input.AVG_DECLINED_CARD_6MON, modelFeatures.get(7));
    Assertions.assertEquals(FraudTransactionModelV3Input.AVG_INVALID_ACCT_6MON, modelFeatures.get(8));
    Assertions.assertEquals(FraudTransactionModelV3Input.MONTHS_INACTIVE_6MON, modelFeatures.get(9));
    Assertions.assertEquals(FraudTransactionModelV3Input.MONTHS_INACTIVE_12MON, modelFeatures.get(10));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SOCURE_SIGMA_SCORE, modelFeatures.get(11));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SYNTHETIC_SCORE, modelFeatures.get(12));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_PHONERISK_SCORE, modelFeatures.get(13));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SENTILINK_ID_THEFT_SCORE, modelFeatures.get(14));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SENTILINK_ABUSE_SCORE, modelFeatures.get(15));
    Assertions.assertEquals(
        FraudTransactionModelV3Input.MAX_SENTILINK_FIRST_PARTY_SYNTHETIC_SCORE,
        modelFeatures.get(16)
    );
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_EMAILRISK_SCORE, modelFeatures.get(17));
    Assertions.assertEquals(
        FraudTransactionModelV3Input.MAX_SENTILINK_THIRD_PARTY_SYNTHETIC_SCORE,
        modelFeatures.get(18)
    );
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_NAMEPHONECORRELATION, modelFeatures.get(19));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SOCURE_R617, modelFeatures.get(20));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_RISKY_CARRIER_IND, modelFeatures.get(21));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SOCURE_R665, modelFeatures.get(22));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_NAMEEMAILCORRELATION, modelFeatures.get(23));
    Assertions.assertEquals(FraudTransactionModelV3Input.MAX_SOCURE_I161, modelFeatures.get(24));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOTAL_DISPUTE_PRE, modelFeatures.get(25));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_ADDCARDATTEMPT_60D, modelFeatures.get(26));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_ADDCARD_SUCCESS_60D, modelFeatures.get(27));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_ADDCARDATTEMPT_7D, modelFeatures.get(28));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_ADDCARDATTEMPT_24H, modelFeatures.get(29));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_ADDCARDATTEMPT_6H, modelFeatures.get(30));
    Assertions.assertEquals(FraudTransactionModelV3Input.POSTAL_MATCH_FLAG_V2, modelFeatures.get(31));
    Assertions.assertEquals(FraudTransactionModelV3Input.STATEMATCHFLAG, modelFeatures.get(32));
    Assertions.assertEquals(FraudTransactionModelV3Input.NAME_MATCH_FLAG, modelFeatures.get(33));
    Assertions.assertEquals(FraudTransactionModelV3Input.ADDRESS1_JAROW, modelFeatures.get(34));
    Assertions.assertEquals(FraudTransactionModelV3Input.FNAME_EDITDISTANCE, modelFeatures.get(35));
    Assertions.assertEquals(FraudTransactionModelV3Input.FNAME_JAROW, modelFeatures.get(36));
    Assertions.assertEquals(FraudTransactionModelV3Input.LNAME_EDITDISTANCE, modelFeatures.get(37));
    Assertions.assertEquals(FraudTransactionModelV3Input.LNAME_JAROW, modelFeatures.get(38));
    Assertions.assertEquals(FraudTransactionModelV3Input.ZIPCHECK_FAIL, modelFeatures.get(39));
    Assertions.assertEquals(FraudTransactionModelV3Input.CARD_FUNDING_FLAG, modelFeatures.get(40));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_BANKDECLINE_7D, modelFeatures.get(41));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_RISKYTRANS_DECLINE_7D, modelFeatures.get(42));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_RISKYTRANS_DECLINE_24H, modelFeatures.get(43));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_VELOCITY_DECLINE_7D, modelFeatures.get(44));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_BANKDECLINE_24H, modelFeatures.get(45));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSIN_ATTEMPTS_7D, modelFeatures.get(46));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSIN_ATTEMPTS_24H, modelFeatures.get(47));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSIN_FAILED_7D, modelFeatures.get(48));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOT_FUNDSIN_AMT_7D, modelFeatures.get(49));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOT_FUNDSIN_AMT_24H, modelFeatures.get(50));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSIN_FAILED_24H, modelFeatures.get(51));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_HIGHAMT_FUNDSIN_30D, modelFeatures.get(52));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSOUT_ATTEMPTS_7D, modelFeatures.get(53));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOT_FUNDSOUT_AMT_7D, modelFeatures.get(54));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSINOUT_DIFFERENTCARD_30D, modelFeatures.get(55));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSINOUT_SAMECARD_30D, modelFeatures.get(56));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_FUNDSOUT_ATTEMPTS_24H, modelFeatures.get(57));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOT_FUNDSOUT_AMT_24H, modelFeatures.get(58));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_PHONE_CHANGE, modelFeatures.get(59));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_EMAIL_CHANGE, modelFeatures.get(60));
    Assertions.assertEquals(FraudTransactionModelV3Input.TOT_DIST_CARD, modelFeatures.get(61));
    Assertions.assertEquals(FraudTransactionModelV3Input.CARD_DECLINE_COUNT, modelFeatures.get(62));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUM_3DS_FAILED_ALL, modelFeatures.get(63));
    Assertions.assertEquals(FraudTransactionModelV3Input.RATIO_BILL_RENT, modelFeatures.get(64));
    Assertions.assertEquals(FraudTransactionModelV3Input.CARD_SUPPORT_FAIL_COUNT_V2, modelFeatures.get(65));
    Assertions.assertEquals(FraudTransactionModelV3Input.INVALID_ACCOUNT_COUNT, modelFeatures.get(66));
    Assertions.assertEquals(FraudTransactionModelV3Input.CPR_EADS142_AT24S, modelFeatures.get(67));
    Assertions.assertEquals(FraudTransactionModelV3Input.CPR_VANTAGE40_SCORE, modelFeatures.get(68));
    Assertions.assertEquals(FraudTransactionModelV3Input.NUMCARDSLINK, modelFeatures.get(69));
    Assertions.assertEquals(FraudTransactionModelV3Input.FUNDSIN_FLAG, modelFeatures.get(70));
  }
}