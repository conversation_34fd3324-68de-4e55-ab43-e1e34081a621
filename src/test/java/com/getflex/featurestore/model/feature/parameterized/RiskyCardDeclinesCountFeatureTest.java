package com.getflex.featurestore.model.feature.parameterized;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.base.param.LookbackDurationFeatureParams;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class RiskyCardDeclinesCountFeatureTest {

  @Mock
  PaymentService paymentService;

  @Test
  public void testGetValue() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);
    Mockito.when(paymentService.getTotalUserPaymentsDeclinedV2(any(), any(), any()))
        .thenReturn(1);
    FeatureValue featureValue = new RiskyCardDeclinesCountFeature(
        paymentService, new LookbackDurationFeatureParams("P7D")
    ).fetchFeatureValue(evalParams);
    assertEquals(1, featureValue.getValue());
  }
}
