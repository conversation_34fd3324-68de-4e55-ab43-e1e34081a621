package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.BillingIntegrationTypeEnum;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel;
import com.getflex.featurestore.integration.flex.flexscore.FlexScoreModel.Version;
import com.getflex.featurestore.integration.flex.flexscore.model.ModelOutput;
import com.getflex.featurestore.integration.flex.model.PropertyInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.FlexScoreMetadata;
import com.getflex.featurestore.model.feature.base.FeatureFactory;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests;
import com.getflex.featurestore.model.flexscore.feature.FlexScoreModelExtractedFeature;
import com.getflex.identity.model.BillerDto;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FlexScoreSevenFeatureTests {

  static final Set<String> REASON_CODES = Set.of("MR001", "MR002", "MR003");

  @InjectMocks
  FlexScoreSevenFeature feature;

  @Mock
  FlexScoreModel flexScoreModel;

  @Mock
  BillingService billingService;

  @Mock
  IdentityService identityService;

  @Mock
  Function<String, String> alloyReportDownloader;

  @Mock
  FeatureFactory featureFactory;

  @Test
  void getType() {
    assertEquals(FeatureTypeEnum.DOUBLE, feature.getType());
  }

  @Test
  void getDescription() {
    assertEquals("flexscore-7", feature.getDescription());
  }

  @Test
  void getRequiredEvalParamKeys() {
    assertEquals(All.of(EvalParamKey.BILLER_ACCOUNT_ID, EvalParamKey.ALLOY_REPORT_URL),
        feature.getRequiredEvalParamKeys());
  }

  /**
   * This only tests high level flow. flexscore feature extraction is tested in {@link FlexScoreInputFeatureTests}.
   *
   * @throws IOException
   */
  @Test
  void fetchFeatureValue_success() throws IOException {
    when(alloyReportDownloader.apply("alloyReport")).thenReturn(
        new String(getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "08-input.json").readAllBytes(),
            StandardCharsets.UTF_8));

    when(identityService.getBillerByBillerAccountId(123L)).thenReturn(new BillerDto().billerId(456L));
    PropertyInfo propertyInfo = new PropertyInfo(BillingIntegrationTypeEnum.DIRECT_INTEGRATION, false, null);
    when(billingService.getPropertyByIdentityBillerId(456L)).thenReturn(propertyInfo);
    when(flexScoreModel.execute(eq(Version.V7), any())).thenReturn(new ModelOutput(Map.of(
        FlexScoreModelExtractedFeature.VANTAGE40_SCORE, 578,
        FlexScoreModelExtractedFeature.EADS05_AGG602, -2,
        FlexScoreModelExtractedFeature.IS_RETURNING_CUSTOMER, 1), 578, 0.042833, 6, REASON_CODES));

    FeatureValue result = feature.fetchFeatureValue(
        new EvalParams().alloyReportUrl("alloyReport").billerAccountId(123L));

    assertEquals(0.042833, result.getValue());
    FlexScoreMetadata metadata = new ObjectMapper().readValue(result.getMetadata(), FlexScoreMetadata.class);
    assertEquals(BigDecimal.valueOf(-2L), metadata.getInputFeatures().get("EADS05_AGG602"));
    assertEquals(BigDecimal.valueOf(578L), metadata.getInputFeatures().get("VANTAGE40_SCORE"));
    assertEquals(BigDecimal.valueOf(1L), metadata.getInputFeatures().get("IS_RETURNING_CUSTOMER"));
    assertEquals(578, metadata.getVantageScore());
    assertEquals(6, metadata.getDecile());
    assertEquals(REASON_CODES, Set.copyOf(metadata.getReasonCodes()));
  }

  @Test
  void fetchFeatureValue_propertyInfoFailure() throws IOException {
    when(alloyReportDownloader.apply("alloyReport")).thenReturn(
        new String(getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "08-input.json").readAllBytes(),
            StandardCharsets.UTF_8));

    when(identityService.getBillerByBillerAccountId(123L)).thenThrow(new InternalDependencyFailureException("oops"));
    when(flexScoreModel.execute(eq(Version.V7), any())).thenReturn(new ModelOutput(Map.of(
        FlexScoreModelExtractedFeature.VANTAGE40_SCORE, 578), 578, 0.042833, 6, REASON_CODES));

    FeatureValue result = feature.fetchFeatureValue(
        new EvalParams().alloyReportUrl("alloyReport").billerAccountId(123L));

    assertEquals(0.042833, result.getValue());
  }

  @Test
  void fetchFeatureValue_ioException() {
    when(alloyReportDownloader.apply(any())).thenReturn("{");
    FeatureValue result = feature.fetchFeatureValue(
        new EvalParams().billerAccountId(123L).alloyReportUrl("alloyReport"));
    Assertions.assertNotNull(result.getErrorMessage());
    Assertions.assertFalse(result.getErrorMessage().isEmpty());
  }
} 