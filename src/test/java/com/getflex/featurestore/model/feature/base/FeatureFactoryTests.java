package com.getflex.featurestore.model.feature.base;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.getflex.featurestore.helper.MockedApplicationContextTest;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.feature.KycFeature;
import com.getflex.featurestore.model.feature.parameterized.CustomerHasTagFeature;
import java.lang.reflect.Modifier;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.reflections.Reflections;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FeatureFactoryTests extends MockedApplicationContextTest {

  // static because it only needs to be created once for this entire suite.
  // tests simply are asserting what features are registered and that they can be retrieved
  private static final FeatureFactory FEATURE_FACTORY = new FeatureFactory(
      MOCKED_APPLICATION_CONTEXT.getAutowireCapableBeanFactory());

  @BeforeEach
  public void setup() {
    FEATURE_FACTORY.onApplicationEvent(null);
  }

  /*
   * Can add new features to the all_features.csv file to ensure they are registered correctly
   */
  @ParameterizedTest
  @CsvFileSource(resources = "/all_features.csv")
  public void getFeature_allFeatures_csv(String expected, String featureName) throws ClassNotFoundException {
    BaseFeature feature = FEATURE_FACTORY.getFeature(featureName);
    Assertions.assertEquals(Class.forName(expected), feature.getClass());
  }

  @Test
  public void getFeatures_allFeatures_have_registration() {
    Reflections reflections = new Reflections(FeatureFactory.FEATURE_PACKAGE_NAME);
    List<Class<? extends BaseFeature>> expectedClasses = reflections.getSubTypesOf(BaseFeature.class).stream()
        .filter(f -> !f.isInterface() && !Modifier.isAbstract(f.getModifiers()))
        .distinct().toList();

    List<BaseFeature> features = this.FEATURE_FACTORY.getFeatures(
            Set.of(EvalParamKey.class.getEnumConstants()))
        .stream()
        .toList();

    List<? extends Class<? extends BaseFeature>> registeredClasses = features.stream()
        .map(BaseFeature::getClass)
        .distinct()
        .toList();

    Set<?> registeredButNotExpected = new HashSet<>(registeredClasses);
    expectedClasses.forEach(registeredButNotExpected::remove);

    Set<?> expectedButNotRegistered = new HashSet<>(expectedClasses);
    registeredClasses.forEach(expectedButNotRegistered::remove);

    if (!expectedButNotRegistered.isEmpty()) {
      Assertions.fail("The following features are not registered in the FeatureFactory: "
          + expectedButNotRegistered);
    }

    if (!registeredButNotExpected.isEmpty()) {
      Assertions.fail("The following features are registered in the FeatureFactory but do not exist: "
          + registeredButNotExpected);
    }

    for (BaseFeature feature : features) {
      Assertions.assertNotNull(feature.getDescription());
      Assertions.assertNotNull(feature.getDefaultValue());
      Assertions.assertNotNull(feature.getName());
      Assertions.assertNotNull(feature.getType());
      Assertions.assertNotNull(feature.getRequiredEvalParamKeys());
    }
  }

  @Test
  public void getFeatures_kycId() {
    List<BaseFeature> features = FEATURE_FACTORY.getFeatures(Set.of(EvalParamKey.KYC_ID));
    assertTrue(features.stream().anyMatch(f -> f instanceof KycFeature));
  }

  @Test
  public void getFeature_kycId() {
    BaseFeature kycFeature = this.FEATURE_FACTORY.getFeature("KycFeature");
    Assertions.assertInstanceOf(KycFeature.class, kycFeature);
  }

  @Test
  public void getFeature_customerTag() {
    BaseFeature customerTagFeature = this.FEATURE_FACTORY.getFeature("CustomerHasTagFeature_TagName");
    Assertions.assertInstanceOf(CustomerHasTagFeature.class, customerTagFeature);
  }

  @Test
  public void getFeatures_ProductInfo() {
    List<BaseFeature> features = this.FEATURE_FACTORY.getFeatures(Set.of(EvalParamKey.BILLER_ACCOUNT_ID));
    assertTrue(features.stream().anyMatch(f -> "ProductInfoFeature".equals(f.getClass().getSimpleName())));
    features = this.FEATURE_FACTORY.getFeatures(Set.of(EvalParamKey.OFFER_ID));
    assertTrue(features.stream().anyMatch(f -> "ProductInfoFeature".equals(f.getClass().getSimpleName())));
  }
}
