package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class TotalSuccessfulPaymentsFeatureTest {
  @InjectMocks
  TotalSuccessfulPaymentsFeature feature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 3L;

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean featureExists) {
    Optional<OfflineFeature> offlineFeature = Optional.of(
        OfflineFeature.builder().featureName("total_successful_payments")
            .primaryKey("3").featureValue("3").build());
    if (!featureExists) {
      offlineFeature = Optional.empty();
    }
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "total_successful_payments", "3")).thenReturn(offlineFeature);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "TotalSuccessfulPaymentsFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    if (featureExists) {
      Assertions.assertEquals(featureValue.getValue(), 3);
    } else {
      Assertions.assertEquals(featureValue.getValue(), 0);
    }
  }
}
