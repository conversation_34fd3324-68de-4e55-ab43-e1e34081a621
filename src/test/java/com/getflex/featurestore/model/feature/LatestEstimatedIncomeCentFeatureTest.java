package com.getflex.featurestore.model.feature;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LatestEstimatedIncomeCentFeatureTest {

  @InjectMocks
  LatestEstimatedIncomeCentFeature latestEstimatedIncomeCentFeature;

  @Mock
  EventRepository eventRepository;

  @Test
  public void testGetValue() {
    Long customerId = 1234L;
    Event e = Event.builder()
        .customerId(customerId.toString())
        .metadata("{\"estimated_gross_annual_income_cents\": \"12345600\"}")
        .build();
    EvalParams evalParams = new EvalParams();
    evalParams.customerId(customerId);
    Mockito.when(eventRepository.findFirstByNameAndCustomerIdOrderByDtArrivedDesc(
            EventName.EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME, customerId.toString()))
        .thenReturn(Optional.of(e));

    FeatureValue featureValue = latestEstimatedIncomeCentFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "LatestEstimatedIncomeCentFeature");
    Assertions.assertEquals(featureValue.getValue(), 12345600);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
