package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class EstimatedBillAmountCentFromFirstAcceptedOfferFeatureTest {

  @InjectMocks
  EstimatedBillAmountCentFromFirstAcceptedOfferFeature feature;

  @Mock
  OfferService offerService;

  private final Long customerId = 123L;

  @Test
  public void getValue() {
    InternalOffer offer = new InternalOffer();
    offer.setEstimatedBillAmountCent(100000L);
    when(offerService.getFirstEverAcceptedOffer(eq(customerId))).thenReturn(offer);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue actual = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(offer.getEstimatedBillAmountCent(), actual.getValue());
    Assertions.assertEquals(EstimatedBillAmountCentFromFirstAcceptedOfferFeature.class.getSimpleName(),
        actual.getName());
    Assertions.assertEquals(FeatureTypeEnum.LONG, actual.getType());
    Assertions.assertNull(actual.getMetadata());
    Assertions.assertNull(actual.getErrorMessage());
  }

  @Test
  public void getValue_noOffer() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getValue_nullEstimatedBillAmountCent() {
    InternalOffer offer = new InternalOffer();
    when(offerService.getFirstEverAcceptedOffer(eq(customerId))).thenReturn(offer);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));
  }
}
