package com.getflex.featurestore.model.feature;

import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptEvent;
import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptEventWith3ds;
import static com.getflex.featurestore.helper.TestCreationUtils.createStripeSetupAttemptMetadata;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerSuccessful3DsCountFeatureTest {

  private static final Long CUSTOMER_ID = 123L;
  private static final String CARD_FINGERPRINT = "fingerprint";

  @InjectMocks
  CustomerSuccessful3DsCountFeature feature;

  @Mock
  EventRepository eventRepository;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(List<Event> events, Integer expectedValue) {
    when(eventRepository.findAllByNameAndCustomerIdAndEntityIdOrderByDtArrivedDesc(
            EventName.STRIPE_SETUP_ATTEMPT, CUSTOMER_ID.toString(), CARD_FINGERPRINT
        )
    ).thenReturn(events);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    evalParams.setCardFingerprint(CARD_FINGERPRINT);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    assertEquals("CustomerSuccessful3DsCountFeature", featureValue.getName());
    assertEquals(expectedValue, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() throws JsonProcessingException {
    var duplicateSucceededEvent = createStripeSetupAttemptEvent(
        createStripeSetupAttemptMetadata(
            metadataBuilder -> metadataBuilder.id("123"),
            threeDomainSecureBuilder -> threeDomainSecureBuilder.result("succeeded")
        )
    );
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(
                    builder -> builder
                        .result(null)
                        .authenticationFlow(null)
                )
            ),
            0
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(builder -> builder.result("failed"))
            ),
            0
        ),
        Arguments.of(
            List.of(
                createStripeSetupAttemptEventWith3ds(builder -> builder.result("succeeded"))
            ),
            1
        ),
        Arguments.of(
            List.of(
                duplicateSucceededEvent,
                duplicateSucceededEvent
            ),
            1
        )
    );
  }
}
