package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.payment.model.DeclineCodeEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerTotalCardDeclinesTransactionNotAllowedFeatureTest {

  @InjectMocks
  CustomerTotalCardDeclinesTransactionNotAllowedFeature feature;

  @Mock
  PaymentService paymentService;

  @Test
  public void testGetValue() {
    Long customerId = 10L;
    when(paymentService.getTotalUserPaymentsDeclined(customerId,
        DeclineCodeEnum.TRANSACTION_NOT_ALLOWED)).thenReturn(3);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "CustomerTotalCardDeclinesTransactionNotAllowedFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 3);
  }

}
