package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfSsnLinkFeatureTest {

  @Mock OfferService offerService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(InternalOffer offer, List<InternalOffer> offerList, int expectedResult) {
    when(offerService.getOfferByCustomerId(any(Long.class))).thenReturn(offer);
    when(offerService.getOfferBySsnHmac(any(Long.class), any(String.class))).thenReturn(offerList);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);

    var numberOfSsnLinkFeature = new NumberOfSsnLinkFeature(offerService);
    FeatureValue featureValue = numberOfSsnLinkFeature.fetchFeatureValue(evalParams);

    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    InternalOffer customerOffer = new InternalOffer().customerId(1L).ssnHmac512("ssn-1");
    InternalOffer linkedOffer1 = new InternalOffer().customerId(2L).ssnHmac512("ssn-1");
    InternalOffer linkedOffer2 = new InternalOffer().customerId(3L).ssnHmac512("ssn-1");

    List<InternalOffer> selfOnly = List.of(customerOffer);
    List<InternalOffer> oneLink = List.of(customerOffer, linkedOffer1);
    List<InternalOffer> twoLinks = List.of(customerOffer, linkedOffer1, linkedOffer2);
    List<InternalOffer> duplicates = List.of(customerOffer, linkedOffer1, linkedOffer1);


    return Stream.of(
        Arguments.of(customerOffer, selfOnly, 1),
        Arguments.of(customerOffer, oneLink, 2),
        Arguments.of(customerOffer, twoLinks, 3),
        Arguments.of(customerOffer, Collections.emptyList(), 0),
        Arguments.of(customerOffer, duplicates, 2)
    );
  }
}