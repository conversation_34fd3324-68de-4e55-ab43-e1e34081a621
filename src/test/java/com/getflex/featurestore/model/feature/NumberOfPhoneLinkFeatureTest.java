package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.v2.CustomerDataV2;
import com.getflex.identity.model.v2.SearchCustomerRequest;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class NumberOfPhoneLinkFeatureTest {

  @Mock IdentityService identityService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      CustomerDataV2 customer, List<CustomerDataV2> customerList, int expectedResult) {
    when(identityService.getCustomerById(any(Long.class))).thenReturn(customer);
    when(identityService.searchCustomer(any(SearchCustomerRequest.class))).thenReturn(customerList);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(123L);

    var numberOfPhoneLinkFeature = new NumberOfPhoneLinkFeature(identityService);
    FeatureValue featureValue = numberOfPhoneLinkFeature.fetchFeatureValue(evalParams);

    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    CustomerDataV2 customer1 = new CustomerDataV2().id(1L).phone("testPhone").email("testEmail");
    CustomerDataV2 customer2 = new CustomerDataV2().id(2L).phone("testPhone2").email("testEmail2");
    CustomerDataV2 customer3 = new CustomerDataV2().id(3L).phone("testPhone").email("testEmail");
    List<CustomerDataV2> customerList1 = List.of(customer1, customer2);
    List<CustomerDataV2> customerList2 = List.of(customer1, customer2, customer3);

    return Stream.of(
        Arguments.of(customer1, customerList1, 2), Arguments.of(customer1, customerList2, 3));
  }
}
