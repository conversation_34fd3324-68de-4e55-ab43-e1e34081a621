package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.utils.FlexConstant;
import com.getflex.offerv2.model.DelinquencyStatusResponse;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDqStatusTest {
  @InjectMocks
  private CustomerDqStatus customerDqStatus;

  @Mock
  OfferService offerService;

  @Mock
  Clock etClock;

  @Test
  void testNotDq() {
    when(offerService.getDqStatus(123L)).thenReturn(new DelinquencyStatusResponse().isDelinquent(false));

    var result = customerDqStatus.getValue(new EvalParams().customerId(123L));
    assertInstanceOf(CustomerDqStatus.DqStatus.class, result.value());
    CustomerDqStatus.DqStatus status = (CustomerDqStatus.DqStatus) result.value();
    assertFalse(status.isDq());
    assertNull(status.dqBpDate());
  }

  @Test
  void testDq0() {
    when(etClock.getZone()).thenReturn(FlexConstant.FLEX_TIMEZONE);
    when(etClock.instant()).thenReturn(Instant.parse("2025-07-01T04:00:00Z"));
    when(offerService.getDqStatus(123L)).thenReturn(
        new DelinquencyStatusResponse().isDelinquent(true).delinquentForDays(0L));

    var result = customerDqStatus.getValue(new EvalParams().customerId(123L));
    assertInstanceOf(CustomerDqStatus.DqStatus.class, result.value());
    CustomerDqStatus.DqStatus status = (CustomerDqStatus.DqStatus) result.value();
    assertTrue(status.isDq());
    assertEquals(LocalDate.of(2025, 6, 1), status.dqBpDate());
    assertEquals(0L, status.dqDays());
  }

  @Test
  void testDq31() {
    when(etClock.getZone()).thenReturn(FlexConstant.FLEX_TIMEZONE);
    when(etClock.instant()).thenReturn(Instant.parse("2025-08-01T04:00:00Z"));
    when(offerService.getDqStatus(123L)).thenReturn(
        new DelinquencyStatusResponse().isDelinquent(true).delinquentForDays(31L));

    var result = customerDqStatus.getValue(new EvalParams().customerId(123L));
    assertInstanceOf(CustomerDqStatus.DqStatus.class, result.value());
    CustomerDqStatus.DqStatus status = (CustomerDqStatus.DqStatus) result.value();
    assertTrue(status.isDq());
    assertEquals(LocalDate.of(2025, 6, 1), status.dqBpDate());
    assertEquals(31L, status.dqDays());
  }
}
