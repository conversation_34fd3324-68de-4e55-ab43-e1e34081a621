package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.autopay.model.EpisodicToggleResponse;
import com.getflex.autopay.model.EpisodicToggleStatus;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class CustomerIsAutopayOnFeatureTest {

  @Mock
  private AutopayService autopayService;

  @InjectMocks
  private CustomerIsAutopayOnFeature feature;

  @ParameterizedTest
  @MethodSource("provideCustomerIsAutopayOnFeatureTestValues")
  void shouldReturnValueBasedOnAutopayServiceToggle(EpisodicToggleStatus status, boolean expected) {
    // given
    when(autopayService.getCustomerEpisodicToggle(any())).thenReturn(new EpisodicToggleResponse().optInStatus(status));

    // when
    FeatureOutput result = feature.getValue(new EvalParams());

    // then
    assertEquals(expected, result.value());
  }

  private static Stream<Arguments> provideCustomerIsAutopayOnFeatureTestValues() {
    return Stream.of(
        Arguments.of(EpisodicToggleStatus.AUTOPAY_ON, true),
        Arguments.of(EpisodicToggleStatus.EPISODIC_ON, false)
    );
  }
}
