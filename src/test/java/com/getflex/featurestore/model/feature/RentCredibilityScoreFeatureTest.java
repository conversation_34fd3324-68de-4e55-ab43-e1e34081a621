package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.DsScoreService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class RentCredibilityScoreFeatureTest {
  @InjectMocks
  RentCredibilityScoreFeature feature;

  @Mock
  DsScoreService service;

  @Test
  public void testScoreFeature() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    when(service.getBatchMemoRentCredibilityScore(batchMemo)).thenReturn(75);

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(75, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
  }
}
