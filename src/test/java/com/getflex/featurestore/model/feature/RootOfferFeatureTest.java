package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.featurestore.model.feature.base.param.Any;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.OverallState;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RootOfferFeatureTest {

  @InjectMocks
  RootOfferFeature feature;

  @Mock
  OfferService offerService;

  @Mock
  InternalOffer offer;

  @Test
  public void getValue_success_byOfferId() {
    String offerId = UUID.randomUUID().toString();
    Long offerVersion = 3L;
    when(offerService.getOffer(offerId, offerVersion)).thenReturn(
        new InternalOffer().offerId(offerId).offerVersion(offerVersion).overallState(OverallState.ACTIVE));
    when(offerService.getRootOffer(offerId, offerVersion)).thenReturn(offer);

    assertSame(offer, feature.getValue(new EvalParams().offerId(offerId).offerVersion(offerVersion)).value());
  }

  @Test
  public void getValue_currentOfferNotFound_byOfferId() {
    String offerId = UUID.randomUUID().toString();
    Long offerVersion = 3L;
    when(offerService.getOffer(offerId, offerVersion)).thenReturn(null);

    assertEquals("Specified offer not found",
        feature.getValue(new EvalParams().offerId(offerId).offerVersion(offerVersion)).value());
  }

  @Test
  public void getValue_success_byBillerAccountId() {
    Long billerAccountId = 134L;
    Long offerVersion = 3L;
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(billerAccountId, offerVersion)).thenReturn(
        new InternalOffer().offerId("offerId").offerVersion(1L).overallState(OverallState.SUSPENDED));
    when(offerService.getRootOffer("offerId", 1L)).thenReturn(offer);

    assertSame(offer,
        feature.getValue(new EvalParams().billerAccountId(billerAccountId).offerVersion(offerVersion)).value());
  }

  @Test
  public void getValue_badCurrentOfferState_byBillerAccountId() {
    Long billerAccountId = 134L;
    Long offerVersion = 3L;
    when(offerService.getOfferByBillerAccountIdAndOfferVersion(billerAccountId, offerVersion)).thenReturn(
        new InternalOffer().offerId("offerId").offerVersion(1L).overallState(OverallState.PENDING));

    assertEquals("Root offer not available",
        feature.getValue(new EvalParams().billerAccountId(billerAccountId).offerVersion(offerVersion)).value());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(Any.of(EvalParamKey.OFFER_ID, EvalParamKey.BILLER_ACCOUNT_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
