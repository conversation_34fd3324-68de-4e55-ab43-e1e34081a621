package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.identity.model.BillerAccountStatusEnumDto;
import com.getflex.identity.model.BillerAccountStatusRecord;
import com.getflex.identity.model.BillerAccountStatusRecord.StatusEnum;
import com.getflex.identity.model.v2.ExtendedBillerAccountData;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class IsAccountStatusFraudCancelledFeatureTest {

  @Mock private IdentityService identityService;

  @InjectMocks private IsAccountStatusFraudCancelledFeature feature;

  @ParameterizedTest
  @MethodSource("provideIsAccountStatusFraudCancelledFeatureTestValues")
  void shouldReturnWhetherAccountIsFraudCancelled(
      List<ExtendedBillerAccountData> mockBillerAccts,
      List<BillerAccountStatusRecord> mockBillerAcctHist,
      boolean expected) {
    when(identityService.getBillerAccountsByCustomerId(any())).thenReturn(mockBillerAccts);
    lenient().when(identityService.getBillerAccountStatusHistory(any())).thenReturn(mockBillerAcctHist);

    FeatureOutput result = feature.getValue(new EvalParams());

    assertEquals(expected, result.value());
  }

  private static Stream<Arguments> provideIsAccountStatusFraudCancelledFeatureTestValues() {
    ExtendedBillerAccountData record1 =
        new ExtendedBillerAccountData().customerId(1L).status(BillerAccountStatusEnumDto.PENDING.name());

    ExtendedBillerAccountData record2 =
        new ExtendedBillerAccountData().customerId(1L).status(BillerAccountStatusEnumDto.ACTIVE.name());

    ExtendedBillerAccountData record3 =
        new ExtendedBillerAccountData().customerId(1L).status(BillerAccountStatusEnumDto.CLOSED.name());

    List<ExtendedBillerAccountData> response1 = new ArrayList<>();
    response1.add(record1);
    response1.add(record2);

    List<ExtendedBillerAccountData> response2 = new ArrayList<>();
    response2.add(record3);

    List<ExtendedBillerAccountData> response3 = new ArrayList<>();

    return Stream.of(
        Arguments.of(response1, List.of(), false),
        Arguments.of(
            response2,
            List.of(
                new BillerAccountStatusRecord()
                    .customerId(1L)
                    .status(StatusEnum.CLOSED)
                    .reason("fraud")),
            true),
        Arguments.of(response3, List.of(), false));
  }
}
