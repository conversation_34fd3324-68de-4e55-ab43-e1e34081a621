package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;

import com.getflex.featurestore.integration.flex.WalletService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.wallet.model.Card;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class RealtimeCardLinkageCountFeatureTest {

  private static final String FINGERPRINT = "fingerprint";

  @Mock
  WalletService walletService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(
      List<Card> cards,
      Integer expectedNumberOfCardAttempts
  ) {
    EvalParams evalParams = new EvalParams();
    evalParams.cardFingerprint(FINGERPRINT);
    Mockito.when(walletService.getCardsByFingerprint(FINGERPRINT)).thenReturn(cards);
    FeatureValue featureValue = new RealtimeCardLinkageCountFeature(walletService).fetchFeatureValue(evalParams);
    assertEquals(expectedNumberOfCardAttempts, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    return Stream.of(
        Arguments.of(List.of(), 0),
        Arguments.of(List.of(card("customerId1")), 1),
        Arguments.of(List.of(card("customerId1"), card("customerId1")), 1),
        Arguments.of(List.of(card("customerId1"), card("customerId2")), 2)
    );
  }

  private static Card card(String stripeCustomerId) {
    return new Card()
        .stripeCustomerId(stripeCustomerId);
  }
}
