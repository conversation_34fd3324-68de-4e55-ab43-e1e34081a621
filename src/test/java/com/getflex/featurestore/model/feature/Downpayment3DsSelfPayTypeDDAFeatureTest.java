package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.billPaymentMethod.model.MethodType;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.BillPaymentService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.identity.model.GetCustomerResponse;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class Downpayment3DsSelfPayTypeDDAFeatureTest {

  @Mock
  AutopayService autopayService;

  @Mock
  IdentityService identityService;

  @Mock
  BillPaymentService billPaymentService;

  private static final Long CUSTOMER_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customer_public_123";
  private static final String BILL_TRANSACTION_ID = "btx_456";

  @Test
  void getValue_WithAutopayTaskAndVCPaymentType_ReturnsVC() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with valid task
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return VC
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.VC);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("vc"), result);
  }

  @Test
  void getValue_WithAutopayTaskAndDDAPaymentType_ReturnsDDA() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with valid task
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return DDA
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.DDA);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullAutopayResponse_ReturnsDefaultDDA() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay service to return null
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(null);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullAutopayTasks_ReturnsDefaultDDA() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with null tasks
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(null);
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithEmptyAutopayTasks_ReturnsDefaultDDA() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with empty tasks
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of());
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullBillTransactionId_ReturnsDefaultDDA() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with task but null bill transaction ID
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(null);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithMultipleAutopayTasks_UsesFirstTask() {
    // Setup
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with multiple tasks
    GetAutopayTaskResponse firstTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    GetAutopayTaskResponse secondTask = new GetAutopayTaskResponse()
        .billTransactionId("btx_789");
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(firstTask, secondTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return VC for first task
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.VC);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify - should use first task and return VC
    assertEquals(new FeatureOutput("vc"), result);
  }

  @Test
  void getType_ReturnsStringType() {
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    assertEquals(FeatureTypeEnum.STRING, feature.getType());
  }

  @Test
  void getDescription_ReturnsCorrectDescription() {
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    String description = feature.getDescription();
    assertEquals("Get Customer Selfpay DDA type, either 'DDA' or 'VC'", description);
  }

  @Test
  void getRequiredEvalParamKeys_ReturnsCustomerId() {
    Downpayment3DsSelfPayTypeDDAFeature feature = new Downpayment3DsSelfPayTypeDDAFeature(
        autopayService, identityService, billPaymentService);

    assertEquals(All.of(EvalParamKey.CUSTOMER_ID), feature.getRequiredEvalParamKeys());
  }
}
