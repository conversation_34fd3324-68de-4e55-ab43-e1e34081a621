package com.getflex.featurestore.model.feature.docv;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class OfferHasOnboardingKycReferEventFeatureTest {
  @Mock
  EventRepository eventRepository;

  @InjectMocks
  OfferHasOnboardingKycReferEventFeature feature;

  @Test
  public void testGetValueEventExists() {
    String offerId = "abcd";
    Long offerVersion = 1L;

    Event e = Event.builder()
        .entityId(offerId + "-" + offerVersion)
        .metadata("{\"kyc_result\": \"refer\"")
        .build();
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferId(offerId);
    evalParams.setOfferVersion(offerVersion);
    Mockito.when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
            offerId + "-" + offerVersion, EventName.ONBOARDING_KYC_REFER))
        .thenReturn(Optional.of(e));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "OfferHasOnboardingKycReferEventFeature");
    Assertions.assertEquals(featureValue.getValue(), true);
  }

  @Test
  public void testGetValueEventNotExists() {
    String offerId = "abcd";
    Long offerVersion = 1L;

    EvalParams evalParams = new EvalParams();
    evalParams.setOfferId(offerId);
    evalParams.setOfferVersion(offerVersion);
    Mockito.when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
            offerId + "-" + offerVersion, EventName.ONBOARDING_KYC_REFER))
        .thenReturn(Optional.empty());

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "OfferHasOnboardingKycReferEventFeature");
    Assertions.assertEquals(featureValue.getValue(), false);
  }
}
