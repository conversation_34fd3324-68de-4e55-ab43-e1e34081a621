package com.getflex.featurestore.model.feature.income;

import static com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata.OBJECT_MAPPER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.exception.EventMetadataParsingException;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStub;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubEmployee;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubEmployer;
import com.getflex.featurestore.model.feature.income.model.paystub.PayStubModel;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStub;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStubMetadata;
import com.getflex.featurestore.model.feature.income.model.paystub.VerifiedPayStubs;
import com.getflex.verification.model.Validation;
import com.getflex.verification.model.Verification;
import com.getflex.verification.model.VerificationContext;
import com.plaid.client.model.CreditPayStub;
import com.plaid.client.model.CreditPayStubEmployee;
import com.plaid.client.model.CreditPayStubEmployer;
import com.plaid.client.model.CreditPayrollIncomeGetResponse;
import com.plaid.client.model.CreditPayrollIncomeRiskSignalsGetResponse;
import com.plaid.client.model.DocumentRiskSignalsObject;
import com.plaid.client.model.DocumentRiskSummary;
import com.plaid.client.model.PayStubPayPeriodDetails;
import com.plaid.client.model.PayrollIncomeObject;
import com.plaid.client.model.PayrollItem;
import com.plaid.client.model.PayrollItemStatus;
import com.plaid.client.model.PayrollRiskSignalsItem;
import com.plaid.client.model.RiskSignalDocumentReference;
import com.plaid.client.model.SingleDocumentRiskSignal;
import java.net.URISyntaxException;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PayStubIncomeVerificationFeatureTest {

  @Mock
  IncomeUtils incomeUtils;
  @Mock
  static Clock etClock;
  @Mock
  VerificationService verificationService;

  @InjectMocks
  PayStubIncomeVerificationFeature feature;

  @Test
  public void getPayrollFeatures_success() throws JsonProcessingException, URISyntaxException {
    Instant fixedInstant = Instant.parse("2024-08-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);

    EvalParams evalParams = new EvalParams().verificationId("123").customerId(1L);

    CreditPayStub creditPayStub = new CreditPayStub()
        .documentId("document-id-1")
        .employer(new CreditPayStubEmployer().name("Employer"))
        .employee(new CreditPayStubEmployee().name("John Smith"))
        .payPeriodDetails(new PayStubPayPeriodDetails().startDate(LocalDate.now(etClock).minusDays(30))
            .endDate(LocalDate.now(etClock))
            .grossEarnings(3000.00));
    PayStub payStub = PayStub.builder()
        .documentId("document-id-1")
        .employee(PayStubEmployee.builder().name("John Smith").build())
        .employer(PayStubEmployer.builder().name("Employer").build())
        .payPeriodDetails(com.getflex.featurestore.model.feature.income.model.paystub.PayStubPayPeriodDetails
            .builder().startDate(LocalDate.now(etClock).minusDays(30))
            .endDate(LocalDate.now(etClock)).grossEarnings(3000.00).build()).build();
    VerifiedPayStub verifiedPayStub = VerifiedPayStub.builder().payStub(payStub)
        .metadata(VerifiedPayStubMetadata.builder()
            .employeeNamePresent(true).doesEmployeeNameMatch(true).payDatePresent(false).isRecent(false)
            .riskScore(20.0).build()).build();

    CreditPayStub creditPayStub1 = new CreditPayStub()
        .documentId("document-id-1")
        .employer(new CreditPayStubEmployer().name("Employer"))
        .employee(new CreditPayStubEmployee().name("John Smith"))
        .payPeriodDetails(new PayStubPayPeriodDetails().payDate(LocalDate.now(etClock).minusDays(50))
            .startDate(LocalDate.now(etClock).minusDays(50))
            .endDate(LocalDate.now(etClock).minusDays(20))
            .grossEarnings(3000.00));
    PayStub payStub1 = PayStub.builder()
        .documentId("document-id-1")
        .employee(PayStubEmployee.builder().name("John Smith").build())
        .employer(PayStubEmployer.builder().name("Employer").build())
        .payPeriodDetails(com.getflex.featurestore.model.feature.income.model.paystub.PayStubPayPeriodDetails
            .builder().payDate(LocalDate.now(etClock).minusDays(50))
            .startDate(LocalDate.now(etClock).minusDays(50))
            .endDate(LocalDate.now(etClock).minusDays(20))
            .grossEarnings(3000.00).build()).build();
    VerifiedPayStub verifiedPayStub1 = VerifiedPayStub.builder().payStub(payStub1)
        .metadata(VerifiedPayStubMetadata.builder()
            .employeeNamePresent(true).doesEmployeeNameMatch(true).payDatePresent(true).isRecent(false)
            .riskScore(20.0).build()).build();

    // Duplicated, should have only one at the end.
    CreditPayStub creditPayStubDuplicated = new CreditPayStub()
        .documentId("document-id-2")
        .employer(new CreditPayStubEmployer().name("Employer"))
        .employee(new CreditPayStubEmployee().name("John Smith"))
        .payPeriodDetails(new PayStubPayPeriodDetails().startDate(LocalDate.now(etClock).minusDays(30))
            .endDate(LocalDate.now(etClock))
            .grossEarnings(3000.00));

    PayrollIncomeObject payrollIncome = new PayrollIncomeObject().payStubs(List.of(creditPayStub, creditPayStub1,
        creditPayStubDuplicated));
    PayrollItem payrollItem = new PayrollItem().status(new PayrollItemStatus().processingStatus("PROCESSING_COMPLETE"))
        .institutionName("bank name").payrollIncome(List.of(payrollIncome));
    CreditPayrollIncomeGetResponse mockedResponse1 = new CreditPayrollIncomeGetResponse().items(List.of(payrollItem));

    SingleDocumentRiskSignal singleDocument1 = new SingleDocumentRiskSignal()
        .riskSummary(new DocumentRiskSummary().riskScore(20.00))
        .documentReference(new RiskSignalDocumentReference().documentId("document-id-1"));
    SingleDocumentRiskSignal singleDocument2 = new SingleDocumentRiskSignal()
        .riskSummary(new DocumentRiskSummary().riskScore(80.00));

    DocumentRiskSignalsObject document = new DocumentRiskSignalsObject()
        .singleDocumentRiskSignals(List.of(singleDocument1, singleDocument2));
    PayrollRiskSignalsItem item = new PayrollRiskSignalsItem().verificationRiskSignals(List.of(document));
    CreditPayrollIncomeRiskSignalsGetResponse mockedResponse2 = new CreditPayrollIncomeRiskSignalsGetResponse()
        .items(List.of(item));

    PayStubIncomeVerificationReports payStubMetadata = new PayStubIncomeVerificationReports(mockedResponse1,
        mockedResponse2);
    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString(payStubMetadata));
    when(incomeUtils.getSelfReportedAnnualGrossIncomeCents(evalParams.getVerificationId())).thenReturn(3000000d);
    when(incomeUtils.getCustomerFullName(1L)).thenReturn("John Smith");
    when(incomeUtils.namesFuzzyMatch(any(), any())).thenReturn(true);
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().plaidReportS3Uri("s3://path/to/file.json"))
        .id(evalParams.getVerificationId()));

    PayStubModel expectedFeatures = PayStubModel.builder()
        .isSubmissionFraudulent(true)
        .maxRiskScore(80.0)
        .grossMonthlyIncomeCent(0.00)
        .verifiedGrossMonthlyIncomeCent(0.00)
        .grossAnnualSelfReportedIncomeCent(3000000d)
        .verifiedPayStubs(VerifiedPayStubs.builder()
            .passGradePayStubs(List.of())
            .failGradePayStubs(List.of(verifiedPayStub1))
            .uncertainGradePayStubs(List.of(verifiedPayStub)).build())
        .build();

    FeatureOutput output = feature.getValue(evalParams);

    Assertions.assertEquals(expectedFeatures, output.value());
  }

  @Test
  public void getPayrollFeatures_alreadyValidated_success() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").customerId(1L);

    CreditPayStub payStub = new CreditPayStub()
        .documentId("document-id-1")
        .employer(new CreditPayStubEmployer().name("Employer"))
        .employee(new CreditPayStubEmployee().name("John Smith"))
        .payPeriodDetails(new PayStubPayPeriodDetails().startDate(LocalDate.now().minusDays(30))
            .endDate(LocalDate.now())
            .grossEarnings(3000.00));

    PayStub payStub1 = PayStub.builder()
        .documentId("document-id-1")
        .employee(PayStubEmployee.builder().name("John Smith").build())
        .employer(PayStubEmployer.builder().name("Employer").build())
        .payPeriodDetails(com.getflex.featurestore.model.feature.income.model.paystub.PayStubPayPeriodDetails
            .builder().startDate(LocalDate.now().minusDays(30))
            .endDate(LocalDate.now()).grossEarnings(3000.00).build()).build();
    VerifiedPayStub verifiedPayStub = VerifiedPayStub.builder().payStub(payStub1)
        .metadata(VerifiedPayStubMetadata.builder()
            .employeeNamePresent(true).doesEmployeeNameMatch(true).payDatePresent(false).isRecent(false)
            .riskScore(20.0).build()).build();

    // Duplicated, should have only one at the end.
    CreditPayStub payStubDuplicated = new CreditPayStub()
        .documentId("document-id-2")
        .employer(new CreditPayStubEmployer().name("Employer"))
        .employee(new CreditPayStubEmployee().name("John Smith"))
        .payPeriodDetails(new PayStubPayPeriodDetails().startDate(LocalDate.now().minusDays(30))
            .endDate(LocalDate.now())
            .grossEarnings(3000.00));

    PayrollIncomeObject payrollIncome = new PayrollIncomeObject().payStubs(List.of(payStub, payStubDuplicated));
    PayrollItem payrollItem = new PayrollItem().status(new PayrollItemStatus().processingStatus("PROCESSING_COMPLETE"))
        .institutionName("bank name").payrollIncome(List.of(payrollIncome));
    CreditPayrollIncomeGetResponse mockedResponse1 = new CreditPayrollIncomeGetResponse().items(List.of(payrollItem));

    SingleDocumentRiskSignal singleDocument1 = new SingleDocumentRiskSignal()
        .riskSummary(new DocumentRiskSummary().riskScore(20.00))
        .documentReference(new RiskSignalDocumentReference().documentId("document-id-1"));
    SingleDocumentRiskSignal singleDocument2 = new SingleDocumentRiskSignal()
        .riskSummary(new DocumentRiskSummary().riskScore(70.00));

    DocumentRiskSignalsObject document = new DocumentRiskSignalsObject()
        .singleDocumentRiskSignals(List.of(singleDocument1, singleDocument2));
    PayrollRiskSignalsItem item = new PayrollRiskSignalsItem().verificationRiskSignals(List.of(document));
    CreditPayrollIncomeRiskSignalsGetResponse mockedResponse2 = new CreditPayrollIncomeRiskSignalsGetResponse()
        .items(List.of(item));

    PayStubIncomeVerificationReports payStubMetadata = new PayStubIncomeVerificationReports(mockedResponse1,
        mockedResponse2);
    when(incomeUtils.getSelfReportedAnnualGrossIncomeCents(evalParams.getVerificationId())).thenReturn(3600000d);
    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString(payStubMetadata));
    when(incomeUtils.getCustomerFullName(1L)).thenReturn("John Smith");
    when(incomeUtils.namesFuzzyMatch(any(), any())).thenReturn(true);
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
            .status("COMPLETED").context(new VerificationContext().plaidReportS3Uri("s3://path/to/file.json")
            .validation(new Validation().grossMonthlyIncomeCent(100L)).validationOverride(new Validation()
                .isSubmissionFraudulent(true).grossMonthlyIncomeCent(200L)))
        .id(evalParams.getVerificationId()));

    Instant fixedInstant = Instant.parse("2024-08-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);

    PayStubModel expectedFeatures = PayStubModel.builder()
        .isSubmissionFraudulent(true)
        .maxRiskScore(70.0)
        .grossMonthlyIncomeCent(200.00)
        .verifiedGrossMonthlyIncomeCent(200.00)
        .grossAnnualSelfReportedIncomeCent(3600000.0)
        .verifiedPayStubs(VerifiedPayStubs.builder()
            .passGradePayStubs(List.of())
            .failGradePayStubs(List.of())
            .uncertainGradePayStubs(List.of(verifiedPayStub)).build())
        .build();

    FeatureOutput output = feature.getValue(evalParams);

    Assertions.assertEquals(expectedFeatures, output.value());
  }

  @Test
  public void getPayrollFeatures_notFound() {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);
    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().validationOverride(new Validation().grossMonthlyIncomeCent(3000000L)
            .totalAverageDailyBalanceCent(600000L)).plaidReportS3Uri(null))
        .status("COMPLETED").id(evalParams.getVerificationId()));

    Assertions.assertThrows(FeatureNotFoundException.class, () -> feature.getValue(evalParams));
  }

  @Test
  public void getPayrollFeatures_failure() throws JsonProcessingException, URISyntaxException {
    EvalParams evalParams = new EvalParams().verificationId("123").billerAccountId(1L);

    when(incomeUtils.downloadReports("s3://path/to/file.json"))
        .thenReturn(OBJECT_MAPPER.writeValueAsString("fake fake fake"));

    when(verificationService.getVerification(evalParams.getVerificationId())).thenReturn(new Verification()
        .context(new VerificationContext().validationOverride(new Validation().grossMonthlyIncomeCent(3000000L)
            .totalAverageDailyBalanceCent(600000L)).plaidReportS3Uri("s3://path/to/file.json"))
        .status("COMPLETED").id(evalParams.getVerificationId()));

    Assertions.assertThrows(EventMetadataParsingException.class, () -> feature.getValue(evalParams));
  }

}
