package com.getflex.featurestore.model.feature.movein;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.getflex.credit.client.model.Loan;
import com.getflex.offerv2.model.OverallState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MoveInOfferInfoTest {

  private ObjectMapper objectMapper;
  
  @BeforeEach
  void setUp() {
    objectMapper = new ObjectMapper();
  }
  
  @Test
  void testNoArgsConstructor() {
    MoveInOfferInfo info = new MoveInOfferInfo();
    assertNotNull(info);
    assertNull(info.getOfferId());
    assertNull(info.getOfferVersion());
    assertNull(info.getLoanId());
    assertNull(info.getType());
    assertNull(info.getOfferState());
    assertNull(info.getLoan());
  }
  
  @Test
  void testAllArgsConstructor() {
    Loan loan = new Loan();
    MoveInOfferInfo info = new MoveInOfferInfo(
        "offer123",
        1L,
        100L,
        MoveInType.LOAN,
        OverallState.ACTIVE,
        loan
    );
    
    assertEquals("offer123", info.getOfferId());
    assertEquals(1L, info.getOfferVersion());
    assertEquals(100L, info.getLoanId());
    assertEquals(MoveInType.LOAN, info.getType());
    assertEquals(OverallState.ACTIVE, info.getOfferState());
    assertEquals(loan, info.getLoan());
  }
  
  @Test
  void testBuilder() {
    Loan loan = new Loan();
    MoveInOfferInfo info = MoveInOfferInfo.builder()
        .offerId("offer456")
        .offerVersion(2L)
        .loanId(200L)
        .type(MoveInType.LOAN)
        .offerState(OverallState.SUSPENDED)
        .loan(loan)
        .build();
    
    assertEquals("offer456", info.getOfferId());
    assertEquals(2L, info.getOfferVersion());
    assertEquals(200L, info.getLoanId());
    assertEquals(MoveInType.LOAN, info.getType());
    assertEquals(OverallState.SUSPENDED, info.getOfferState());
    assertEquals(loan, info.getLoan());
  }
  
  @Test
  void testBuilderWithPartialFields() {
    MoveInOfferInfo info = MoveInOfferInfo.builder()
        .offerId("offer789")
        .type(MoveInType.PAY_IN_FULL)
        .offerState(OverallState.ACTIVE)
        .build();
    
    assertEquals("offer789", info.getOfferId());
    assertNull(info.getOfferVersion());
    assertNull(info.getLoanId());
    assertEquals(MoveInType.PAY_IN_FULL, info.getType());
    assertEquals(OverallState.ACTIVE, info.getOfferState());
    assertNull(info.getLoan());
  }
  
  @Test
  void testGettersAndSetters() {
    MoveInOfferInfo info = new MoveInOfferInfo();
    
    info.setOfferId("test-offer");
    assertEquals("test-offer", info.getOfferId());
    
    info.setOfferVersion(3L);
    assertEquals(3L, info.getOfferVersion());
    
    info.setLoanId(300L);
    assertEquals(300L, info.getLoanId());
    
    info.setType(MoveInType.LOAN);
    assertEquals(MoveInType.LOAN, info.getType());
    
    info.setOfferState(OverallState.CANCELED);
    assertEquals(OverallState.CANCELED, info.getOfferState());
    
    Loan loan = new Loan();
    info.setLoan(loan);
    assertEquals(loan, info.getLoan());
  }
  
  @Test
  void testEqualsAndHashCode() {
    MoveInOfferInfo info1 = MoveInOfferInfo.builder()
        .offerId("offer1")
        .offerVersion(1L)
        .loanId(100L)
        .type(MoveInType.LOAN)
        .offerState(OverallState.ACTIVE)
        .build();
    
    MoveInOfferInfo info2 = MoveInOfferInfo.builder()
        .offerId("offer1")
        .offerVersion(1L)
        .loanId(100L)
        .type(MoveInType.LOAN)
        .offerState(OverallState.ACTIVE)
        .build();
    
    MoveInOfferInfo info3 = MoveInOfferInfo.builder()
        .offerId("offer2")
        .offerVersion(1L)
        .loanId(100L)
        .type(MoveInType.LOAN)
        .offerState(OverallState.ACTIVE)
        .build();
    
    assertEquals(info1, info2);
    assertEquals(info1.hashCode(), info2.hashCode());
    assertNotEquals(info1, info3);
    assertNotEquals(info1.hashCode(), info3.hashCode());
  }
  
  @Test
  void testEqualsWithNullFields() {
    MoveInOfferInfo info1 = new MoveInOfferInfo();
    MoveInOfferInfo info2 = new MoveInOfferInfo();
    
    assertEquals(info1, info2);
    assertEquals(info1.hashCode(), info2.hashCode());
  }
  
  @Test
  void testEqualsWithDifferentTypes() {
    MoveInOfferInfo info = new MoveInOfferInfo();
    assertNotEquals(info, null);
    assertNotEquals(info, "string");
    assertNotEquals(info, 123);
  }
  
  @Test
  void testToString() {
    MoveInOfferInfo info = MoveInOfferInfo.builder()
        .offerId("offer123")
        .offerVersion(1L)
        .type(MoveInType.PAY_IN_FULL)
        .build();
    
    String toString = info.toString();
    assertNotNull(toString);
    assertTrue(toString.contains("offerId=offer123"));
    assertTrue(toString.contains("offerVersion=1"));
    assertTrue(toString.contains("type=PAY_IN_FULL"));
  }
  
  @Test
  void testJsonSerialization() throws Exception {
    Loan loan = new Loan();
    
    MoveInOfferInfo info = MoveInOfferInfo.builder()
        .offerId("json-offer")
        .offerVersion(5L)
        .loanId(500L)
        .type(MoveInType.LOAN)
        .offerState(OverallState.ACTIVE)
        .loan(loan)
        .build();
    
    String json = objectMapper.writeValueAsString(info);
    assertNotNull(json);
    assertTrue(json.contains("\"offer_id\":\"json-offer\""));
    assertTrue(json.contains("\"offer_version\":5"));
    assertTrue(json.contains("\"loan_id\":500"));
    assertTrue(json.contains("\"type\":\"LOAN\""));
    assertTrue(json.contains("\"offer_state\":\"Active\""));
  }
  
  @Test
  void testJsonDeserialization() throws Exception {
    String json = "{\"offer_id\":\"deser-offer\",\"offer_version\":10,\"loan_id\":1000,"
        + "\"type\":\"LOAN\",\"offer_state\":\"Suspended\"}";
    
    MoveInOfferInfo info = objectMapper.readValue(json, MoveInOfferInfo.class);
    
    assertEquals("deser-offer", info.getOfferId());
    assertEquals(10L, info.getOfferVersion());
    assertEquals(1000L, info.getLoanId());
    assertEquals(MoveInType.LOAN, info.getType());
    assertEquals(OverallState.SUSPENDED, info.getOfferState());
    assertNull(info.getLoan());
  }
  
  @Test
  void testJsonSerializationWithNulls() throws Exception {
    MoveInOfferInfo info = MoveInOfferInfo.builder()
        .offerId("null-test")
        .type(MoveInType.PAY_IN_FULL)
        .build();
    
    String json = objectMapper.writeValueAsString(info);
    assertNotNull(json);
    assertTrue(json.contains("\"offer_id\":\"null-test\""));
    assertTrue(json.contains("\"type\":\"PAY_IN_FULL\""));
    assertTrue(json.contains("\"offer_version\":null"));
    assertTrue(json.contains("\"loan_id\":null"));
    assertTrue(json.contains("\"offer_state\":null"));
    assertTrue(json.contains("\"loan\":null"));
  }
}