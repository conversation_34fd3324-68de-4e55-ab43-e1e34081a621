package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.payment.model.GetCustomerBillResponse;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerCurrentBillAmountFeatureTest {

  @InjectMocks
  CustomerCurrentBillAmountFeature feature;

  @Mock
  AutopayService autopayService;

  @Mock
  PaymentService paymentService;

  @Mock
  IdentityService identityService;

  @Mock
  OfferService offerService;

  @Test
  public void testGetValue() {
    Long customerId = 1234L;
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId("customerPublicId").customerId(customerId)
    );
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(new InternalOffer().billerAccountId(5678L));
    when(autopayService.getCurrentAutoPay(eq("customerPublicId"), any()))
        .thenReturn(new SearchAutopayTasksResponse().autopayTasks(List.of(
            new GetAutopayTaskResponse().billTransactionId("btxId1"),
            new GetAutopayTaskResponse().billTransactionId("btxId2"))));
    when(paymentService.getCustomerBill("customerPublicId", "btxId1"))
        .thenReturn(new GetCustomerBillResponse().amount(10000L));

    when(paymentService.getCustomerBill("customerPublicId", "btxId2"))
        .thenReturn(new GetCustomerBillResponse().amount(20000L));

    EvalParams evalParams = new EvalParams().customerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerCurrentBillAmountFeature");
    Assertions.assertEquals(featureValue.getValue(), 20000L);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testDefaultValueWhenEmptyAutoPayTasks() {
    Long customerId = 1234L;
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId("customerPublicId").customerId(customerId)
    );
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(new InternalOffer().billerAccountId(5678L));
    when(autopayService.getCurrentAutoPay(eq("customerPublicId"), any()))
        .thenReturn(new SearchAutopayTasksResponse().autopayTasks(List.of()));
    verifyNoInteractions(paymentService);

    EvalParams evalParams = new EvalParams().customerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerCurrentBillAmountFeature");
    Assertions.assertEquals(featureValue.getValue(), 0L);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testDefaultValueWhenNoAutoPayTasks() {
    Long customerId = 1234L;
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId("customerPublicId").customerId(customerId)
    );
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(new InternalOffer().billerAccountId(5678L));
    when(autopayService.getCurrentAutoPay(eq("customerPublicId"), any()))
        .thenThrow(new InternalDependencyFailureException(
            "Could not fetch autopay for customerPublicId %s".formatted("customerPublicId")));
    verifyNoInteractions(paymentService);

    EvalParams evalParams = new EvalParams().customerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerCurrentBillAmountFeature");
    Assertions.assertEquals(featureValue.getValue(), 0L);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

}
