package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.billPaymentMethod.model.MethodType;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.BillPaymentService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.identity.model.GetCustomerResponse;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class Downpayment3DsSelfPayTypeDdaFeatureTest {

  @Mock
  AutopayService autopayService;

  @Mock
  IdentityService identityService;

  @Mock
  BillPaymentService billPaymentService;

  private static final Long CUSTOMER_ID = 123L;
  private static final String CUSTOMER_PUBLIC_ID = "customer_public_123";
  private static final String BILL_TRANSACTION_ID = "btx_456";

  private Downpayment3DsSelfPayTypeDdaFeature feature;

  @BeforeEach
  void setUp() {
    feature = new Downpayment3DsSelfPayTypeDdaFeature(
        autopayService, identityService, billPaymentService);
  }

  @Test
  void getValue_WithAutopayTaskAndVcPaymentType_ReturnsVc() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with valid task
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return VC
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.VC);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("vc"), result);
  }

  @Test
  void getValue_WithAutopayTaskAndDdaPaymentType_ReturnsDda() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with valid task
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return DDA
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.DDA);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullAutopayResponse_ReturnsDefaultDda() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay service to return null
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(null);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullAutopayTasks_ReturnsDefaultDda() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with null tasks
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(null);
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithEmptyAutopayTasks_ReturnsDefaultDda() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with empty tasks
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of());
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithNullBillTransactionId_ReturnsDefaultDda() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with task but null bill transaction ID
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(null);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_WithMultipleAutopayTasks_UsesFirstTask() {
    // Setup

    // Mock customer response
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with multiple tasks
    GetAutopayTaskResponse firstTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    GetAutopayTaskResponse secondTask = new GetAutopayTaskResponse()
        .billTransactionId("btx_789");
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(firstTask, secondTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Mock bill payment service to return VC for first task
    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.VC);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify - should use first task and return VC
    assertEquals(new FeatureOutput("vc"), result);
  }

  @Test
  void getType_ReturnsStringType() {

    assertEquals(FeatureTypeEnum.STRING, feature.getType());
  }

  @Test
  void getDescription_ReturnsCorrectDescription() {

    String description = feature.getDescription();
    assertEquals("Get Customer Selfpay DDA type, either 'DDA' or 'VC'", description);
  }

  @Test
  void getRequiredEvalParamKeys_ReturnsCustomerId() {

    assertEquals(All.of(EvalParamKey.CUSTOMER_ID), feature.getRequiredEvalParamKeys());
  }

  @Test
  void getValue_WithEmptyBillTransactionId_ReturnsDefaultDda() {
    // Setup
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with task but empty bill transaction ID
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId("");
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify - empty string is considered valid, so should call bill payment service
    // But since we didn't mock it, it will return null and default to DDA
    assertEquals(new FeatureOutput("dda"), result);
  }

  @Test
  void getValue_VerifyServiceCallOrder() {
    // Setup
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId(BILL_TRANSACTION_ID);
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID))
        .thenReturn(MethodType.VC);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    feature.getValue(evalParams);

    // Verify service call order and interactions
    verify(identityService, times(1)).getCustomer(CUSTOMER_ID);
    verify(autopayService, times(1)).getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class));
    verify(billPaymentService, times(1)).getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, BILL_TRANSACTION_ID);
  }

  @Test
  void getValue_WhenBillPaymentServiceNotCalled_VerifyNoInteraction() {
    // Setup - scenario where bill payment service should not be called
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay service to return null
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(null);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    feature.getValue(evalParams);

    // Verify bill payment service was never called
    verify(billPaymentService, never()).getFlexAnywhereBillPaymentType(any(), any());
  }

  @Test
  void getValue_WithAutopayTaskButEmptyStringBillTransactionId_CallsBillPaymentService() {
    // Setup
    GetCustomerResponse customerResponse = new GetCustomerResponse()
        .customerId(CUSTOMER_ID)
        .customerPublicId(CUSTOMER_PUBLIC_ID);
    when(identityService.getCustomer(CUSTOMER_ID)).thenReturn(customerResponse);

    // Mock autopay response with task having empty string bill transaction ID
    GetAutopayTaskResponse autopayTask = new GetAutopayTaskResponse()
        .billTransactionId("");
    SearchAutopayTasksResponse autopayResponse = new SearchAutopayTasksResponse()
        .autopayTasks(List.of(autopayTask));
    when(autopayService.getCurrentAutoPay(eq(CUSTOMER_PUBLIC_ID), any(LocalDate.class)))
        .thenReturn(autopayResponse);

    when(billPaymentService.getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, ""))
        .thenReturn(MethodType.DDA);

    // Execute
    EvalParams evalParams = new EvalParams().customerId(CUSTOMER_ID);
    FeatureOutput result = feature.getValue(evalParams);

    // Verify - empty string is not null, so should call bill payment service
    assertEquals(new FeatureOutput("dda"), result);
    verify(billPaymentService, times(1)).getFlexAnywhereBillPaymentType(CUSTOMER_PUBLIC_ID, "");
  }

  @Test
  void constructor_InitializesAllFields() {
    // Verify constructor properly initializes all dependencies
    Downpayment3DsSelfPayTypeDdaFeature testFeature = new Downpayment3DsSelfPayTypeDdaFeature(
        autopayService, identityService, billPaymentService);

    // Test that the feature can be used (indirect verification of proper initialization)
    assertEquals(FeatureTypeEnum.STRING, testFeature.getType());
    assertEquals("Get Customer Selfpay DDA type, either 'DDA' or 'VC'", testFeature.getDescription());
    assertEquals(All.of(EvalParamKey.CUSTOMER_ID), testFeature.getRequiredEvalParamKeys());
  }
}
