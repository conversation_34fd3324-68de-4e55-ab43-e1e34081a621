package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.dispute.model.Dispute;
import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class BillTransactionDisputedDaysWithMinAmountFeatureTest {

  @InjectMocks
  BillTransactionDisputedDaysWithMinAmountFeature billTransactionDisputedDaysWithMinAmountFeature;

  @Mock
  DisputeService disputeService;

  @Mock
  AllowDenyListRepo allowDenyListRepo;

  public static final String BYPASS_DISPUTE_DAYS_FEATURE = "bypass_dispute_days_feature";

  @Test
  void testGetValueAllowList() {
    Long customerId = 1L;
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(BYPASS_DISPUTE_DAYS_FEATURE,
        String.valueOf(customerId),
        true)).thenReturn(new AllowDenyList());
    FeatureValue featureValue = billTransactionDisputedDaysWithMinAmountFeature.fetchFeatureValue(evalParams);
    assertEquals(featureValue.getValue(), -1L);
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValueNotInAllowList(boolean featureExists) {
    Long customerId = 1L;
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(BYPASS_DISPUTE_DAYS_FEATURE,
        String.valueOf(customerId),
        true)).thenReturn(null);
    if (featureExists) {
      OffsetDateTime disputeEventCreated1 = OffsetDateTime.now(ZoneOffset.UTC).minusDays(2L);
      OffsetDateTime disputeEventCreated2 = OffsetDateTime.now(ZoneOffset.UTC).minusDays(3L);
      OffsetDateTime disputeEventCreated3 = OffsetDateTime.now(ZoneOffset.UTC).minusDays(4L);
      when(disputeService.getAllDisputesByCustomerId(customerId)).thenReturn(
          List.of(
              new Dispute().disputeAmount(1000L).dtCreated(disputeEventCreated1),
              new Dispute().disputeAmount(5000L).dtCreated(disputeEventCreated2),
              new Dispute().disputeAmount(6000L).dtCreated(disputeEventCreated3)));
    } else {
      when(disputeService.getAllDisputesByCustomerId(customerId)).thenReturn(null);
    }
    FeatureValue featureValue = billTransactionDisputedDaysWithMinAmountFeature.fetchFeatureValue(evalParams);
    if (featureExists) {
      assertEquals(featureValue.getValue(), 3L);
    } else {
      assertEquals(featureValue.getValue(), -1L);
    }
  }
}
