package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerSentilinkIdentityTheftScoreFeatureTest {

  @InjectMocks
  CustomerSentilinkIdentityTheftScoreFeature sentilinkIdentityTheftScoreFeature;

  @Mock
  OfferService offerService;

  private final Long customerId = 123L;

  @Test
  public void getValue() {
    InternalOffer internalOffer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    Integer expected = 90;
    evalContext.setSentilinkIdTheftScore(expected);
    internalOffer.setEvaluationContext(evalContext);
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(internalOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue actual = sentilinkIdentityTheftScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(expected, actual.getValue());
  }

  @Test
  public void getValue_nullScore() {
    InternalOffer internalOffer = new InternalOffer();
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    internalOffer.setEvaluationContext(evalContext);
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(internalOffer);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue actual = sentilinkIdentityTheftScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(0, actual.getValue());
  }

  @Test
  public void getValue_noOffer() {
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue actual = sentilinkIdentityTheftScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(0, actual.getValue());
  }

  @Test
  public void getValue_onboardingCustomer() {
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(null);
    InternalOffer internalOffer = new InternalOffer();
    internalOffer.setOfferState(OfferStateEnum.PENDINGACCEPT);
    InternalOfferAllOfEvaluationContext evalContext = new InternalOfferAllOfEvaluationContext();
    Integer expected = 90;
    evalContext.setSentilinkIdTheftScore(expected);
    when(offerService.getOfferByCustomerId(customerId)).thenReturn(internalOffer);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue actual = sentilinkIdentityTheftScoreFeature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(0, actual.getValue());
  }
}
