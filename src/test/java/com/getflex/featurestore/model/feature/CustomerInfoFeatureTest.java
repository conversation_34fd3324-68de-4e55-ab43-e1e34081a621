package com.getflex.featurestore.model.feature;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.CustomerInfo;
import com.getflex.featurestore.model.EvalParamKey;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.param.All;
import com.getflex.verification.model.Kyc;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerInfoFeatureTest {

  @InjectMocks
  CustomerInfoFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  CustomerInfo customerInfo;

  @Test
  public void getValue_success() {
    EvalParams evalParams = new EvalParams().customerId(123L);
    when(identityService.getCustomerInfo(evalParams)).thenReturn(customerInfo);

    assertSame(customerInfo, feature.getValue(evalParams).value());
  }

  @Test
  public void testFeatureSupportingFunctions() {
    assertEquals(FeatureTypeEnum.OBJECT, feature.getType());
    assertEquals(All.of(EvalParamKey.CUSTOMER_ID), feature.getRequiredEvalParamKeys());
    assertNotNull(feature.getDescription());
  }
}
