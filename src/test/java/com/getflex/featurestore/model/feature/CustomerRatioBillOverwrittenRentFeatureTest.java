package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.autopay.model.GetAutopayTaskResponse;
import com.getflex.autopay.model.SearchAutopayTasksResponse;
import com.getflex.featurestore.integration.flex.AutopayService;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.flex.PaymentService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import com.getflex.payment.model.GetCustomerBillResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerRatioBillOverwrittenRentFeatureTest {

  @InjectMocks
  CustomerRatioBillOverwrittenRentFeature feature;

  @Mock
  IdentityService identityService;

  @Mock
  PaymentService paymentService;

  @Mock
  AutopayService autopayService;

  @ParameterizedTest
  @MethodSource("testCases")
  public void testGetValue(Long amount, Long amountOverwrite, Double expectedResult) {
    Long customerId = 123L;
    String billTransactionId = UUID.randomUUID().toString();
    String customerPublicId = UUID.randomUUID().toString();
    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerPublicId(customerPublicId)
    );
    LocalDate date = FeatureUtils.getCurrentBpDateTime().toLocalDate();
    when(autopayService.getCurrentAutoPay(customerPublicId, date)).thenReturn(
        new SearchAutopayTasksResponse().autopayTasks(
            List.of(new GetAutopayTaskResponse().billTransactionId(billTransactionId).payDate(date))
        )
    );
    if (amount != null && amountOverwrite != null && amount > 0 && amountOverwrite > 0) {
      when(paymentService.getCustomerBill(customerPublicId, billTransactionId)).thenReturn(
          new GetCustomerBillResponse().amount(amount).amountOverwrite(amountOverwrite)
      );
    }
    FeatureValue featureValue = feature.fetchFeatureValue(
        new EvalParams().customerId(customerId)
    );
    assertEquals(expectedResult, featureValue.getValue());
  }

  private static Stream<Arguments> testCases() {
    return Stream.of(
        Arguments.of(null, null, 0D),
        Arguments.of(null, 1L, 0D),
        Arguments.of(1L, null, 0D),
        Arguments.of(0L, 0L, 0D),
        Arguments.of(1L, 1L, 1D),
        Arguments.of(4L, 10L, 2.5D)
    );
  }
}
