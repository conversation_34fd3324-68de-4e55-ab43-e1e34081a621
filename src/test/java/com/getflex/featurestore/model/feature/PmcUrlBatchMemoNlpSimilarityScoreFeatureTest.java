package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaContainerPortalPortalContainer;
import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseGetBillerAccountResponse;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.similarity.PmcUrlBatchMemoNlpSimilarityScoreFeature;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointRequest;
import software.amazon.awssdk.services.sagemakerruntime.model.InvokeEndpointResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PmcUrlBatchMemoNlpSimilarityScoreFeatureTest {

  @InjectMocks
  PmcUrlBatchMemoNlpSimilarityScoreFeature feature;

  @Mock
  BillingService billingService;

  @Mock
  SageMakerRuntimeClient sagemaker;

  @Test
  public void testGetValue() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    Long billerId = 16L;
    Long portalId = 10L;
    Long billerAccountId = 19L;
    when(billingService.getPropertyByBillerId(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().integrationType(
                IntegrationTypeEnum.PORTAL).propertyId(123L).portalId(portalId)
            .name("70 Emerald Cove Dr")
    );
    when(billingService.getPortals()).thenReturn(
        List.of(
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(5L)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "www.abcd.com")
                ),
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(portalId)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "https://westshore.myresman.com/Portal/Access/SignIn/PEV")
                )
        )
    );
    when(billingService.getBillerAccountByBillerIdAndBillerAccountId(billerId, billerAccountId))
        .thenReturn(
            new ComGetflexBillingDtoResponseGetBillerAccountResponse().integrationFields(List.of(
                Map.of(
                    "name", "portal_url",
                    "value", "\"https://notwestshore.myresman.com/Portal/Access/SignIn/PEV"
                )
            ))
        );
    InvokeEndpointResponse res = InvokeEndpointResponse.builder().body(
        SdkBytes.fromString("0.7", StandardCharsets.UTF_8)).build();

    when(sagemaker.invokeEndpoint((InvokeEndpointRequest) any())).thenReturn(res);

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo).billerId(billerId)
        .billerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "PmcUrlBatchMemoNlpSimilarityScoreFeature");
    Assertions.assertEquals(0.7, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.DOUBLE);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
