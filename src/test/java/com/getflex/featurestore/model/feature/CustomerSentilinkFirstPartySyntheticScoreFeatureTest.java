package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.integration.flex.OfferService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.offerv2.model.InternalOffer;
import com.getflex.offerv2.model.InternalOffer.OfferStateEnum;
import com.getflex.offerv2.model.InternalOfferAllOfEvaluationContext;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerSentilinkFirstPartySyntheticScoreFeatureTest {
  @InjectMocks
  CustomerSentilinkFirstPartySyntheticScoreFeature feature;

  @Mock
  OfferService offerService;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  @Test
  public void testGetValueWithRootEvaluation() {
    Long customerId = 1L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().sentilinkFirstPartySyntheticScore(500));
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(offerResponse);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "CustomerSentilinkFirstPartySyntheticScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 500);
  }

  @Test
  public void testGetValueFromOfflineFeature() {
    Long customerId = 1L;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.ACCEPTED)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().isRootEvaluation(true));
    when(offerService.getFirstEverAcceptedOffer(customerId)).thenReturn(offerResponse);

    Optional<OfflineFeature> offlineFeature = Optional.of(
        OfflineFeature.builder().featureName("sentilink_first_party_synthetic_score")
            .primaryKey(customerId.toString()).featureValue("550").build());
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "sentilink_first_party_synthetic_score", customerId.toString())).thenReturn(offlineFeature);

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);

    Assertions.assertEquals(featureValue.getName(), "CustomerSentilinkFirstPartySyntheticScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(featureValue.getValue(), 550);
  }

  @Test
  public void getValue_noOffer() {
    when(offerService.getFirstEverAcceptedOffer(1L)).thenReturn(null);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);

    FeatureValue actual = feature.fetchFeatureValue(evalParams);
    Assertions.assertNull(actual.getValue());
  }

  @Test
  public void getValue_onboardingCustomer() {
    when(offerService.getFirstEverAcceptedOffer(1L)).thenReturn(null);
    int expected = 550;
    InternalOffer offerResponse = new InternalOffer().offerId("test-id-1").offerState(OfferStateEnum.PENDINGACCEPT)
        .evaluationContext(
            new InternalOfferAllOfEvaluationContext().sentilinkFirstPartySyntheticScore(expected));
    when(offerService.getOfferByCustomerId(1L)).thenReturn(offerResponse);
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(1L);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), "CustomerSentilinkFirstPartySyntheticScoreFeature");
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertEquals(expected, featureValue.getValue());
  }

}
