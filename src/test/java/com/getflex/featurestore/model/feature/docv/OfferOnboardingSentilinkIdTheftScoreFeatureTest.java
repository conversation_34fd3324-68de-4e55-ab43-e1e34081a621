package com.getflex.featurestore.model.feature.docv;

import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.repo.EventRepository;
import com.getflex.featurestore.exception.FeatureNotFoundException;
import com.getflex.featurestore.integration.flex.VerificationService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.income.IncomeUtils;
import com.getflex.featurestore.model.feature.income.PayStubIncomeVerificationFeature;
import java.time.Clock;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class OfferOnboardingSentilinkIdTheftScoreFeatureTest {

  @Mock
  EventRepository eventRepository;

  @InjectMocks
  OfferOnboardingSentilinkIdTheftScoreFeature feature;


  @Test
  public void testGetValue() {
    Long customerId = 1234L;
    String offerId = "abcd";
    Long offerVersion = 1L;

    Event e = Event.builder()
        .customerId(customerId.toString())
        .entityId(offerId + "-" + offerVersion)
        .metadata("{\"sentilink_id_theft_score\": \"120\", "
            + "\"sentilink_abuse_score\": \"130\", "
            + "\"sentilink_first_party_synthetic_score\": \"140\", "
            + "\"sentilink_third_party_synthetic_score\": \"150\"}")
        .build();
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferId(offerId);
    evalParams.setOfferVersion(offerVersion);
    Mockito.when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
        offerId + "-" + offerVersion, EventName.ONBOARDING_FRAUD_SENTILINK_SCORES))
        .thenReturn(Optional.of(e));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "OfferOnboardingSentilinkIdTheftScoreFeature");
    Assertions.assertEquals(featureValue.getValue(), 120L);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
  }

  @Test
  public void testGetValueIncorrectMetadata() {
    Long customerId = 1234L;
    String offerId = "abcd";
    Long offerVersion = 1L;

    Event e = Event.builder()
        .customerId(customerId.toString())
        .entityId(offerId + "-" + offerVersion)
        .metadata("\"sentilink_abuse_score\": \"130\", "
            + "\"sentilink_first_party_synthetic_score\": \"140\", "
            + "\"sentilink_third_party_synthetic_score\": \"150\"}")
        .build();
    EvalParams evalParams = new EvalParams();
    evalParams.setOfferId(offerId);
    evalParams.setOfferVersion(offerVersion);
    Mockito.when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
            offerId + "-" + offerVersion, EventName.ONBOARDING_FRAUD_SENTILINK_SCORES))
        .thenReturn(Optional.of(e));

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "OfferOnboardingSentilinkIdTheftScoreFeature");
    Assertions.assertEquals(featureValue.getValue(), 0);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
  }

  @Test
  public void testGetValueNoEvent() {
    String offerId = "abcd";
    Long offerVersion = 1L;

    EvalParams evalParams = new EvalParams();
    evalParams.setOfferId(offerId);
    evalParams.setOfferVersion(offerVersion);
    Mockito.when(eventRepository.findFirstByEntityIdAndNameOrderByDtArrivedDesc(
            offerId + "-" + offerVersion, EventName.ONBOARDING_FRAUD_SENTILINK_SCORES))
        .thenReturn(Optional.empty());

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "OfferOnboardingSentilinkIdTheftScoreFeature");
    Assertions.assertEquals(featureValue.getValue(), 0);
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.LONG);
  }

}
