package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.service.delinquency.DelinquencyService;
import com.getflex.ledger.model.LedgerMetadata;
import com.getflex.ledger.model.RecordLedgerWallet;
import java.time.Clock;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;


@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDqExcludingCbTest {

  private CustomerDq0Past3MonthsExcludingCb customerDq0Past3MonthsExcludingCb;
  private CustomerDq30Past12MonthsExcludingCb customerDq30Past12MonthsExcludingCb;
  private CustomerDq180Past24MonthsExcludingCb customerDq180Past24MonthsExcludingCb;
  private DelinquencyService delinquencyService;

  @Mock
  private LedgerService ledgerService;

  @Mock
  private Clock etClock;

  private void setupClockAndDelinquencyService() {
    // Set up clock mocks - these are needed by DelinquencyService
    Instant fixedInstant = Instant.parse("2024-08-10T12:00:00Z");
    ZoneId zoneId = ZoneId.of("America/New_York");
    when(etClock.instant()).thenReturn(fixedInstant);
    when(etClock.getZone()).thenReturn(zoneId);
    
    // Create real DelinquencyService with mocked dependencies
    delinquencyService = new DelinquencyService(ledgerService, etClock);
  }

  @Test
  public void testDq0Past3MonthsPayInTime() {
    setupClockAndDelinquencyService();
    customerDq0Past3MonthsExcludingCb = new CustomerDq0Past3MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 20, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq0Past3MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq0Past3MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq0Past3MonthsPayLate() {
    setupClockAndDelinquencyService();
    customerDq0Past3MonthsExcludingCb = new CustomerDq0Past3MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq0Past3MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq0Past3MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq0Past3MonthsPayLateExcludingCb() {
    // This test excludes CB transactions, so clock is not used
    delinquencyService = new DelinquencyService(ledgerService, etClock);
    customerDq0Past3MonthsExcludingCb = new CustomerDq0Past3MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    LedgerMetadata metadata = new LedgerMetadata().metadataKey("product_type").metadataValue("CreditBuilder");

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()).metadataEntries(List.of(metadata)));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq0Past3MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq0Past3MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq180Past24MonthsPayInTime() {
    setupClockAndDelinquencyService();
    customerDq180Past24MonthsExcludingCb = new CustomerDq180Past24MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2023, 9, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 1, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 3, 20, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    EvalParams params = new EvalParams().customerId(customerId);
    FeatureValue res = customerDq180Past24MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq180Past24MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq180Past24MonthsPayLate() {
    setupClockAndDelinquencyService();
    customerDq180Past24MonthsExcludingCb = new CustomerDq180Past24MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2022, 9, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2023, 2, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2023, 4, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq180Past24MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq180Past24MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq180Past24MonthsPayLateExcludingCb() {
    // This test excludes CB transactions, so clock is not used
    delinquencyService = new DelinquencyService(ledgerService, etClock);
    customerDq180Past24MonthsExcludingCb = new CustomerDq180Past24MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    LedgerMetadata metadata = new LedgerMetadata().metadataKey("product_type").metadataValue("CreditBuilder");

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2022, 9, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2023, 2, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()).metadataEntries(List.of(metadata)));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2023, 4, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq180Past24MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq180Past24MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayInTime() {
    setupClockAndDelinquencyService();
    customerDq30Past12MonthsExcludingCb = new CustomerDq30Past12MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 7, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 7, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 20, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLate() {
    setupClockAndDelinquencyService();
    customerDq30Past12MonthsExcludingCb = new CustomerDq30Past12MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateExcludingNonCb() {
    setupClockAndDelinquencyService();
    customerDq30Past12MonthsExcludingCb = new CustomerDq30Past12MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    LedgerMetadata metadata = new LedgerMetadata().metadataKey("product_type").metadataValue("Non-CB");

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()).metadataEntries(List.of(metadata)));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(true, res.getValue());
  }

  @Test
  public void testDq30Past12MonthsPayLateExcludingCb() {
    // This test excludes CB transactions, so clock is not used
    delinquencyService = new DelinquencyService(ledgerService, etClock);
    customerDq30Past12MonthsExcludingCb = new CustomerDq30Past12MonthsExcludingCb(
        ledgerService, etClock, delinquencyService);
    Long customerId = 1L;
    List<RecordLedgerWallet> txList = new ArrayList<>();
    LedgerMetadata metadata = new LedgerMetadata().metadataKey("product_type").metadataValue("CreditBuilder");

    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(2000L)
        .dtCreated(OffsetDateTime.of(2024, 6, 1, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.PAY_BILLER.getValue()).paymentCategoryId(
            MovementCategory.ACH_TRANSFER.getValue()).metadataEntries(List.of(metadata)));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(500L)
        .dtCreated(OffsetDateTime.of(2024, 6, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.DOWNPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()));
    txList.add(new RecordLedgerWallet().billTransactionId("btx1").customerId(customerId).amount(1500L)
        .dtCreated(OffsetDateTime.of(2024, 8, 2, 10, 15, 0, 0, ZoneOffset.UTC)).paymentStatusId(
            PaymentState.SETTLED.getValue())
        .moneyMovementTypeId(MoneyMovementType.REPAYMENT.getValue()).paymentCategoryId(
            MovementCategory.CHARGE.getValue()).metadataEntries(List.of(metadata)));

    EvalParams params = new EvalParams().customerId(customerId);

    when(ledgerService.getWalletLedgersByCustomerId(customerId, true)).thenReturn(txList);
    FeatureValue res = customerDq30Past12MonthsExcludingCb.fetchFeatureValue(params);
    assertEquals(res.getName(), "CustomerDq30Past12MonthsExcludingCb");
    assertEquals(res.getType(), FeatureTypeEnum.BOOLEAN);
    assertEquals(false, res.getValue());
  }
}
