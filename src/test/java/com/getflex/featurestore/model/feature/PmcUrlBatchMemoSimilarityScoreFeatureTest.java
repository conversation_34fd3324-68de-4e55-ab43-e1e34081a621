package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaContainerPortalPortalContainer;
import com.getflex.billing.api.v2.model.ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse;
import com.getflex.billing.api.v2.model.ComGetflexBillingControllerV2PropertyControllerPropertyResponse.IntegrationTypeEnum;
import com.getflex.billing.api.v2.model.ComGetflexBillingDtoResponseGetBillerAccountResponse;
import com.getflex.featurestore.integration.flex.billing.BillingService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.featurestore.model.feature.similarity.PmcUrlBatchMemoSimilarityScoreFeature;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class PmcUrlBatchMemoSimilarityScoreFeatureTest {

  @InjectMocks
  PmcUrlBatchMemoSimilarityScoreFeature feature;

  @Mock
  BillingService billingService;

  @Test
  public void testGetValueFromPortalUrl() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    Long billerId = 16L;
    Long portalId = 10L;
    Long billerAccountId = 19L;
    when(billingService.getPropertyById(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().integrationType(
                IntegrationTypeEnum.PORTAL).propertyId(123L).portalId(portalId)
            .name("70 Emerald Cove Dr")
    );
    when(billingService.getPortals()).thenReturn(
        List.of(
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(5L)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "www.abcd.com")
                ),
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(portalId)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "https://westshore.myresman.com/Portal/Access/SignIn/PEV")
                )
        )
    );
    when(billingService.getBillerAccountByBillerIdAndBillerAccountId(billerId,
        billerAccountId)).thenReturn(
          new ComGetflexBillingDtoResponseGetBillerAccountResponse().integrationFields(List.of(
            Map.of(
                "name", "portal_url",
                "value", "\"https://notwestshore.myresman.com/Portal/Access/SignIn/PEV"
            )
        ))
    );

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo).billerId(billerId)
        .billerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "PmcUrlBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(75, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testGetValueFromPmcUrl() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    Long billerId = 16L;
    Long portalId = 10L;
    Long billerAccountId = 19L;
    when(billingService.getPropertyById(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().integrationType(
                IntegrationTypeEnum.PORTAL).propertyId(123L).portalId(portalId)
            .name("70 Emerald Cove Dr")
    );
    when(billingService.getPortals()).thenReturn(
        List.of(
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(5L)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "www.abcd.com")
                ),
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(portalId)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "https://notwestshore.myresman.com/Portal/Access/SignIn/PEV")
                )
        )
    );
    when(billingService.getBillerAccountByBillerIdAndBillerAccountId(billerId,
        billerAccountId)).thenReturn(
          new ComGetflexBillingDtoResponseGetBillerAccountResponse().integrationFields(List.of(
            Map.of(
                "name", "portal_url",
                "value", "https://westshore.myresman.com/Portal/Access/SignIn/PEV"
            )
          ))
    );

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo).billerId(billerId)
        .billerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "PmcUrlBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(75, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testZeroSimilarityFlexAnywhere() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    Long billerId = 16L;
    Long portalId = 10L;
    Long billerAccountId = 19L;
    when(billingService.getPropertyById(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().integrationType(
                IntegrationTypeEnum.FLEX_ANYWHERE).propertyId(123L).portalId(portalId)
            .name("70 Emerald Cove Dr")
    );

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo).billerId(billerId)
        .billerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "PmcUrlBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(0, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  public void testZeroSimilarityGenericPortal() {
    String batchMemo = "PL*WestShoreLLC866-729-5327";
    Long billerId = 16L;
    Long portalId = 10L;
    Long billerAccountId = 19L;
    when(billingService.getPropertyById(billerId)).thenReturn(
        new ComGetflexBillingControllerV2PropertyControllerPropertyResponse().integrationType(
                IntegrationTypeEnum.PORTAL).propertyId(123L).portalId(portalId)
            .name("70 Emerald Cove Dr")
    );
    when(billingService.getPortals()).thenReturn(
        List.of(
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(5L)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "www.abcd.com")
                ),
            new ComGetflexBillingAuroraJpaContainerPortalPortalContainer().id(6L)
                .portalDisplayConfig(
                    new ComGetflexBillingAuroraJpaModelPortalPortalDisplayConfiguration().loginUrl(
                        "https://notwestshore.myresman.com/Portal/Access/SignIn/PEV")
                )
        )
    );

    EvalParams evalParams = new EvalParams().batchMemo(batchMemo).billerId(billerId)
        .billerAccountId(billerAccountId);

    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(),
        "PmcUrlBatchMemoSimilarityScoreFeature");
    Assertions.assertEquals(0, featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FeatureTypeEnum.INT);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }
}
