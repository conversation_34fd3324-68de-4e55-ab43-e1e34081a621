package com.getflex.featurestore.model.feature;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.dispute.model.Dispute;
import com.getflex.dispute.model.Dispute.StripeDisputeReasonEnum;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.DisputeService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
class DisputeReasonByIdFeatureTest {

  private static final Long CUSTOMER_ID = 1L;

  @Mock
  DisputeService disputeService;

  @Test
  void getValue() {
    when(disputeService.getAllDisputesByCustomerId(any())).thenReturn(List.of(
        new Dispute().id(1L).customerId(CUSTOMER_ID).stripeDisputeReason(StripeDisputeReasonEnum.FRAUDULENT),
        new Dispute().id(2L).customerId(CUSTOMER_ID).stripeDisputeReason(StripeDisputeReasonEnum.GENERAL)
    ));

    DisputeReasonByIdFeature feature = new DisputeReasonByIdFeature(disputeService);
    FeatureOutput output = feature.getValue(new EvalParams().disputeId(1L).customerId(CUSTOMER_ID));
    assertEquals(StripeDisputeReasonEnum.FRAUDULENT, output.value());
  }

  @Test
  void missingDisputeThrowsException() {
    when(disputeService.getAllDisputesByCustomerId(any())).thenReturn(List.of(
        new Dispute().id(1L).customerId(CUSTOMER_ID).stripeDisputeReason(StripeDisputeReasonEnum.FRAUDULENT),
        new Dispute().id(2L).customerId(CUSTOMER_ID).stripeDisputeReason(StripeDisputeReasonEnum.GENERAL)
    ));

    DisputeReasonByIdFeature feature = new DisputeReasonByIdFeature(disputeService);
    EvalParams evalParams = new EvalParams().disputeId(3L).customerId(CUSTOMER_ID);
    Assertions.assertThrows(InternalDependencyFailureException.class, () -> feature.getValue(evalParams));
  }
}
