package com.getflex.featurestore.model.feature;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.integration.zendesk.ZendeskService;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUser;
import com.getflex.featurestore.integration.zendesk.model.ZendeskUserTicketsResponse;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CustomerZendeskTicketsCountFeatureTest {

  private static final Long CUSTOMER_ID = 1234L;
  private static final String EMAIL = "<EMAIL>";

  @InjectMocks
  CustomerZendeskTicketsCountFeature feature;

  @Mock
  ZendeskService zendeskService;

  @Mock
  IdentityService identityService;

  @Test
  public void getValue_success() {
    String zendeskUserId = "testZendeskUserId";
    GetCustomerResponse customerResponse = new GetCustomerResponse();
    customerResponse.setEmail(EMAIL);
    when(identityService.getCustomer(eq(CUSTOMER_ID))).thenReturn(customerResponse);
    when(zendeskService.getUserByEmail(eq(EMAIL))).thenReturn(ZendeskUser.builder().id(zendeskUserId).build());
    Integer expected = 4;
    when(zendeskService.getTicketsByUserId(eq(zendeskUserId)))
        .thenReturn(ZendeskUserTicketsResponse.builder().count(expected).build());

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput featureValue = feature.getValue(evalParams);
    Assertions.assertEquals(expected, featureValue.value());
  }

  @Test
  public void getValue_noZendeskUser() {
    GetCustomerResponse customerResponse = new GetCustomerResponse();
    customerResponse.setEmail(EMAIL);
    when(identityService.getCustomer(eq(CUSTOMER_ID))).thenReturn(customerResponse);
    Integer expected = 0;

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput featureValue = feature.getValue(evalParams);
    Assertions.assertEquals(expected, featureValue.value());
  }

  @Test
  public void getValue_noTicketsResponse() {
    String zendeskUserId = "testZendeskUserId";
    GetCustomerResponse customerResponse = new GetCustomerResponse();
    customerResponse.setEmail(EMAIL);
    when(identityService.getCustomer(eq(CUSTOMER_ID))).thenReturn(customerResponse);
    when(zendeskService.getUserByEmail(eq(EMAIL))).thenReturn(ZendeskUser.builder().id(zendeskUserId).build());
    Integer expected = 0;

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(CUSTOMER_ID);
    FeatureOutput featureValue = feature.getValue(evalParams);
    Assertions.assertEquals(expected, featureValue.value());
  }

}
