package com.getflex.featurestore.model.feature;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.AllowDenyList;
import com.getflex.featurestore.dao.repo.AllowDenyListRepo;
import com.getflex.featurestore.exception.InternalDependencyFailureException;
import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class CustomerDenylistedForReactivationFeatureTest {

  @InjectMocks
  CustomerDenylistedForReactivationFeature feature;
  @Mock
  IdentityService identityService;
  @Mock
  AllowDenyListRepo allowDenyListRepo;

  public static final String FEATURE_NAME = "CustomerDenylistedForReactivationFeature";
  public static final FeatureTypeEnum FEATURE_TYPE = FeatureTypeEnum.BOOLEAN;
  public static final String REACTIVATION_DENYLIST_USECASE = "deny_listed_reactivation_30_days";

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void testGetValue(Boolean isDenylisted) {
    Long customerId = 1L;
    String customerPublicId = "customer_public_id";

    when(identityService.getCustomer(customerId)).thenReturn(
        new GetCustomerResponse().customerId(customerId).customerPublicId(customerPublicId));
    if (isDenylisted) {
      when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(REACTIVATION_DENYLIST_USECASE, customerPublicId,
          false)).thenReturn(new AllowDenyList());
    } else {
      when(allowDenyListRepo.findFirstByUseCaseAndEntityAndAllow(REACTIVATION_DENYLIST_USECASE, customerPublicId,
          false)).thenReturn(null);
    }
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), FEATURE_NAME);
    Assertions.assertEquals(featureValue.getValue(), isDenylisted);
    Assertions.assertEquals(featureValue.getType(), FEATURE_TYPE);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertNull(featureValue.getErrorMessage());
  }

  @Test
  void testGetValueNoCustomerFound() {
    Long customerId = 1L;
    String msg = "Could not fetch customer of id %s".formatted(customerId);
    when(identityService.getCustomer(customerId)).thenThrow(
        new InternalDependencyFailureException(msg));
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    FeatureValue featureValue = feature.fetchFeatureValue(evalParams);
    Assertions.assertEquals(featureValue.getName(), FEATURE_NAME);
    Assertions.assertNull(featureValue.getValue());
    Assertions.assertEquals(featureValue.getType(), FEATURE_TYPE);
    Assertions.assertNull(featureValue.getMetadata());
    Assertions.assertEquals(featureValue.getErrorMessage(), msg);
  }
}
