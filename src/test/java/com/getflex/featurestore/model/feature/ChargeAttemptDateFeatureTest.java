package com.getflex.featurestore.model.feature;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.dao.model.OfflineFeature;
import com.getflex.featurestore.dao.repo.OfflineFeatureRepo;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.FeatureValue;
import java.time.LocalDate;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ChargeAttemptDateFeatureTest {

  @InjectMocks
  ChargeAttemptDateFeature chargeAttemptDateFeature;

  @Mock
  OfflineFeatureRepo offlineFeatureRepo;

  Long customerId = 1234L;
  LocalDate fixedDateTime = LocalDate.of(2024, 10, 9);

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  public void testGetValue(boolean isProjectedDateValid) {
    Optional<OfflineFeature> feature = Optional.of(
        OfflineFeature.builder().featureName("charge_attempt_dates")
            .primaryKey("1234").featureValue("2024-10-08, 2024-10-09")
            .build());
    if (isProjectedDateValid) {
      feature.get().setFeatureValue("2024-10-08,2024-10-09, 2024-10-15");
    }

    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "charge_attempt_dates", "1234")).thenReturn(feature);

    try (MockedStatic<LocalDate> mockedStatic = mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
      mockedStatic.when(LocalDate::now).thenReturn(fixedDateTime);
      FeatureValue res = chargeAttemptDateFeature.fetchFeatureValue(evalParams);
      assertEquals(res.getName(), "ChargeAttemptDateFeature");
      assertEquals(res.getType(), FeatureTypeEnum.STRING);
      assertEquals(isProjectedDateValid ? "2024-10-15" : null, res.getValue());
    }
  }

  @Test
  public void testGetValue_noCustomerFound() {
    EvalParams evalParams = new EvalParams();
    evalParams.setCustomerId(customerId);
    when(offlineFeatureRepo.findFirstByFeatureNameAndPrimaryKey(
        "charge_attempt_dates", "1234")).thenReturn(null);
    FeatureValue res = chargeAttemptDateFeature.fetchFeatureValue(evalParams);
    assertEquals(res.getName(), "ChargeAttemptDateFeature");
    assertEquals(res.getType(), FeatureTypeEnum.STRING);
    assertEquals(null, res.getValue());
  }
}
