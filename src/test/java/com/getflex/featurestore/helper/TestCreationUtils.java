package com.getflex.featurestore.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.model.event.eventmetadata.CreditReportIncompleteMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.EventMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata.Card;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata.PaymentMethodDetails;
import com.getflex.featurestore.dao.model.event.eventmetadata.StripeSetupAttemptMetadata.ThreeDomainSecure;
import com.getflex.featurestore.integration.flex.LedgerService.MoneyMovementType;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.LedgerService.PaymentState;
import com.getflex.ledger.model.RecordLedger;
import com.getflex.payment.model.Model3dsAuthRecord;
import java.time.OffsetDateTime;
import org.apache.commons.lang3.RandomStringUtils;

public class TestCreationUtils {

  public static Model3dsAuthRecord model3dsAuthRecord(Change<Model3dsAuthRecord> change) {
    return change.apply(
        new Model3dsAuthRecord()
    );
  }

  public static RecordLedger fundsOutRecord(Change<RecordLedger> change) {
    return change.apply(
        recordLedger(
            builder -> builder
                .moneyMovementTypeId(MoneyMovementType.FUNDS_OUT.getValue())
                .paymentCategoryId(MovementCategory.CHARGE.getValue())
        )
    );
  }

  public static RecordLedger fundsInRecord(Change<RecordLedger> change) {
    return change.apply(
        recordLedger(
            builder -> builder
                .moneyMovementTypeId(MoneyMovementType.FUNDS_IN.getValue())
                .paymentCategoryId(MovementCategory.CHARGE.getValue())
        )
    );
  }

  public static RecordLedger recordLedger(Change<RecordLedger> change) {
    return change.apply(
        new RecordLedger()
            .customerId(Long.valueOf(RandomStringUtils.randomNumeric(10)))
            .paymentStatusId(PaymentState.SETTLED.getValue())
            .dtCreated(OffsetDateTime.now())
    );
  }

  public static Event createStripeSetupAttemptEvent(
      StripeSetupAttemptMetadata stripeSetupAttemptMetadata
  ) throws JsonProcessingException {
    return Event.builder()
        .name(EventName.STRIPE_SETUP_ATTEMPT)
        .metadata(
            EventMetadata.OBJECT_MAPPER.writeValueAsString(
                stripeSetupAttemptMetadata
            )
        ).dtArrived(OffsetDateTime.now())
        .build();
  }

  public static Event createStripeSetupAttemptEventWith3ds(
      Change<ThreeDomainSecure.ThreeDomainSecureBuilder> change
  ) throws JsonProcessingException {
    return Event.builder()
        .name(EventName.STRIPE_SETUP_ATTEMPT)
        .metadata(
            EventMetadata.OBJECT_MAPPER.writeValueAsString(
                createStripeSetupAttemptMetadata(change)
            )
        ).dtArrived(OffsetDateTime.now())
        .build();
  }

  public static StripeSetupAttemptMetadata createStripeSetupAttemptMetadata(
      Change<ThreeDomainSecure.ThreeDomainSecureBuilder> change
  ) {
    return createStripeSetupAttemptMetadata(builder -> builder, change);
  }

  public static StripeSetupAttemptMetadata createStripeSetupAttemptMetadata(
      Change<StripeSetupAttemptMetadata.StripeSetupAttemptMetadataBuilder> metadataBuilderChange,
      Change<ThreeDomainSecure.ThreeDomainSecureBuilder> threeDomainSecureBuilderChange
  ) {
    return metadataBuilderChange.apply(
        StripeSetupAttemptMetadata.builder()
            .id(RandomStringUtils.randomNumeric(10))
            .paymentMethodDetails(
                PaymentMethodDetails.builder()
                    .card(
                        Card.builder()
                            .threeDomainSecure(
                                threeDomainSecureBuilderChange.apply(
                                    ThreeDomainSecure.builder()
                                ).build()
                            ).build()
                    ).build()
            )
    ).build();
  }

  public static Event createCreditReportIncompleteEvent(
      CreditReportIncompleteMetadata creditReportIncompleteMetadata
  ) throws JsonProcessingException {
    return Event.builder()
        .name(EventName.CREDIT_REPORT_INCOMPLETE)
        .metadata(
            EventMetadata.OBJECT_MAPPER.writeValueAsString(
                creditReportIncompleteMetadata
            )
        ).dtArrived(OffsetDateTime.now())
        .build();
  }

  public static Event createCreditReportIncompleteEvent(
      Change<CreditReportIncompleteMetadata.CreditReportIncompleteMetadataBuilder> change
  ) throws JsonProcessingException {
    CreditReportIncompleteMetadata metadata = change.apply(
        CreditReportIncompleteMetadata.builder()
    ).build();
    
    return createCreditReportIncompleteEvent(metadata);
  }

  public static CreditReportIncompleteMetadata createCreditReportIncompleteMetadata(
      Long productId,
      Long productCategoryId,
      String offerId,
      Long offerVersion,
      String checkpointName,
      String ruleName
  ) {
    return CreditReportIncompleteMetadata.builder()
        .productId(productId)
        .productCategoryId(productCategoryId)
        .offerId(offerId)
        .offerVersion(offerVersion)
        .checkpointName(checkpointName)
        .ruleName(ruleName)
        .build();
  }

}
