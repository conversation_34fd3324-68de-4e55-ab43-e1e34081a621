package com.getflex.featurestore.helper;

import static org.mockito.Mockito.mock;

import com.getflex.featurestore.FeatureStoreApplication;
import java.util.HashMap;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.config.DependencyDescriptor;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * This class provides an application context where literally every bean is mocked.
 * Its pretty much the dumbest way of doing this, but it works and I couldn't get a proper spring test setup
 * and got fed up trying. Someone save me and do it the right way.
 */
public class MockedApplicationContextTest {

  public static AnnotationConfigApplicationContext MOCKED_APPLICATION_CONTEXT;

  @BeforeAll
  static void setup() {
    MOCKED_APPLICATION_CONTEXT =
        new AnnotationConfigApplicationContext(new MockListableBeanFactory());
    MOCKED_APPLICATION_CONTEXT.register(FeatureStoreApplication.class);
    MOCKED_APPLICATION_CONTEXT.scan("com.getflex");
    MOCKED_APPLICATION_CONTEXT.refresh();
  }

  @AfterAll
  static void cleanup() {
    MOCKED_APPLICATION_CONTEXT.close();
  }

  static class MockListableBeanFactory extends DefaultListableBeanFactory {
    Map<Class<?>, Object> canOfBeans = new HashMap<>();

    private boolean canMock(Class<?> clazz) {
      // never mock anything out of the model package
      // since this class just mocks anything you ask it we need to put some restrictions
      // on what in can and cannot mock. This is because sometimes theres multiple constructors
      // and to find the right one Spring will attempt to resolve the most restrictive one first, we might not actually
      // want that one mocked. Hacky workaround, but hell this entire class is a hack
      if (clazz.getName().startsWith("com.getflex.featurestore.model.")) {
        return false;
      }
      return true;
    }

    @Override
    protected Object createBean(String beanName, RootBeanDefinition mbd, Object[] args) throws BeanCreationException {
      try {
        // resolve the class and return a mock of it
        // MUST save mock or else we will consume a lot of memory and eventually OOM
        Class<?> resolvedClass = resolveBeanClass(mbd, beanName);
        return canOfBeans.computeIfAbsent(resolvedClass, k -> mock(resolvedClass));
      } catch (Throwable e) {
        throw new BeanCreationException(
            mbd.getResourceDescription(), beanName, "Unexpected exception during bean creation", e);
      }
    }

    @NotNull
    @Override
    protected Map<String, Object> findAutowireCandidates(String beanName, @NotNull Class<?> requiredType,
        @NotNull DependencyDescriptor descriptor) {

      Map<String, Object> candiates = super.findAutowireCandidates(beanName, requiredType, descriptor);
      if (candiates.isEmpty()) {
        if (this.canMock(requiredType)) {
          // slam a mock in there for any missing bean
          var mockInstance = mock(requiredType);
          if (!this.hasDependentBean(requiredType.getName())) {
            this.registerSingleton(requiredType.getName(), mockInstance);
          }
          return Map.of(requiredType.getName(), mockInstance);
        }
      }

      return candiates;
    }
  }
}
