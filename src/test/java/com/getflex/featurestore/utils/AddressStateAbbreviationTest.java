package com.getflex.featurestore.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

import com.getflex.featurestore.integration.socure.exception.InvalidPiiException;
import java.util.stream.Stream;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class AddressStateAbbreviationTest {

  @ParameterizedTest
  @MethodSource("abbreviatedStateTestCases")
  public void getAbbreviatedState(String input, String expected) {
    assertEquals(expected, AddressStateAbbreviation.getAbbreviatedState(input));
  }

  @Test
  public void getAbbreviatedState_throwsException() {
    assertThrows(InvalidPiiException.class, () -> AddressStateAbbreviation.getAbbreviatedState("Washington D.C."));
  }

  private static Stream<Arguments> abbreviatedStateTestCases() {
    return Stream.of(
        Arguments.of("California", "CA"),
        Arguments.of("New York", "NY"),
        Arguments.of("NY", "NY"),
        Arguments.of("ca", "CA"),
        Arguments.of("North Dakota", "ND"),
        Arguments.of("District of Columbia", "DC"),
        Arguments.of("New-Jersey", "NJ"),
        Arguments.of("Florida 33301", "FL"),
        Arguments.of("TEXAS", "TX")
    );
  }


  @ParameterizedTest
  @MethodSource("statesMatchTestCases")
  public void statesMatch(String state1, String state2, boolean expected) {
    assertEquals(expected, AddressStateAbbreviation.statesMatch(state1, state2));
  }

  private static Stream<Arguments> statesMatchTestCases() {
    return Stream.of(
        Arguments.of("CA", "California", true),
        Arguments.of("New York", "NY", true),
        Arguments.of("Florida", "FL", true),
        Arguments.of("TX", "Texas", true),
        Arguments.of("Washington", "Oregon", false),
        Arguments.of("NY", "NJ", false),
        Arguments.of("North Carolina", "South Carolina", false),
        Arguments.of("ca", "CA", true),
        Arguments.of("New-York", "NY", true),
        Arguments.of("New York", "New York", true),
        Arguments.of(null, "California", false),
        Arguments.of("Florida", null, false),
        Arguments.of("XX", "California", false),
        Arguments.of("North Dakota", "nd", true),
        Arguments.of("District of Columbia", "DC", true)
    );
  }

}
