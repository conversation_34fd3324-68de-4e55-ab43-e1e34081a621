package com.getflex.featurestore.utils;

import static com.getflex.featurestore.model.flexscore.FlexScoreInputFeatureTests.TEST_RESOURCE_FOLDER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.model.EvalParams;
import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class AlloyUtilsTests {

  @InjectMocks
  AlloyUtils alloyUtils;

  @Mock
  S3Client s3Client;

  @Captor
  ArgumentCaptor<GetObjectRequest> getObjectRequest;

  @ParameterizedTest
  @ValueSource(strings = {
      "https://bucket/folder/key.json?a=b",
      "http://bucket.s3.amazonaws.com:8080/folder/key.json"
  })
  void fetchFeatureValue_success(String alloyReportUri) throws IOException {
    EvalParams evalParams = new EvalParams().alloyReportUrl(alloyReportUri);

    byte[] reportContent = getClass().getResourceAsStream(TEST_RESOURCE_FOLDER + "02-input.json").readAllBytes();
    when(s3Client.getObjectAsBytes((GetObjectRequest) any())).thenReturn(ResponseBytes.fromByteArray(
        GetObjectResponse.builder().build(), reportContent));

    alloyUtils.downloadAlloyReport(evalParams.getAlloyReportUrl());
    verify(s3Client).getObjectAsBytes(getObjectRequest.capture());
    assertEquals("bucket", getObjectRequest.getValue().bucket());
    assertEquals("folder/key.json", getObjectRequest.getValue().key());
  }

  @Test
  void fetchFeatureValue_badUri() {
    assertThrows(RuntimeException.class,
        () -> alloyUtils.downloadAlloyReport("^^http://bucket/folder/key.json"));
  }
}
