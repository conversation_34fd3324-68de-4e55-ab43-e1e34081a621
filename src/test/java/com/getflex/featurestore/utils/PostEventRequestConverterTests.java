package com.getflex.featurestore.utils;

import static com.getflex.featurestore.dao.model.event.EntityType.SSN_HMAC;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.getflex.featurestore.constant.ProductConstants;
import com.getflex.featurestore.dao.model.Event;
import com.getflex.featurestore.dao.model.event.EntityType;
import com.getflex.featurestore.dao.model.event.EventCategory;
import com.getflex.featurestore.dao.model.event.EventName;
import com.getflex.featurestore.dao.utils.PostEventRequestConverter;
import com.getflex.featurestore.model.PostEventRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PostEventRequestConverterTests {

  private static final String DEVICE_METADATA_STRING = "{\"ip_address\":\"test-ip-address\",\"device_id\""
      + ":\"test-device-id\",\"app_platform\":\"test-app-platform\"}";
  private static final String INCOME_METADATA_STRING = "{\"estimated_gross_annual_income_cents\":\"12345600\"}";

  @Test
  public void convertToEventIncomeTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    String entityId = "test-offer-id_offer-version";
    String customerId = "test-customer-id";
    postEventRequest.setEntityId(entityId);
    postEventRequest.setCustomerId(customerId);
    postEventRequest.setName("EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME");
    postEventRequest.setCategory("ESTIMATED_GROSS_ANNUAL_INCOME");
    postEventRequest.setEntityType("OFFER_ID_AND_VERSION");

    String income = "12345600";
    ObjectMapper objectMapper = new ObjectMapper();
    ObjectNode requestMetadata = objectMapper.createObjectNode();
    requestMetadata.put("estimated_gross_annual_income_cents", income);
    postEventRequest.setMetadata(requestMetadata.toString());

    Event expected = Event.builder()
        .name(EventName.EVALUATE_OFFER_ESTIMATED_GROSS_ANNUAL_INCOME)
        .category(EventCategory.ESTIMATED_GROSS_ANNUAL_INCOME)
        .entityId(entityId)
        .entityType(EntityType.OFFER_ID_AND_VERSION)
        .customerId(customerId)
        .metadata(INCOME_METADATA_STRING)
        .build();
    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    assertEquals(expected, actual);
  }

  @Test
  public void convertToEventTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    String entityId = "test-entity-id";
    postEventRequest.setEntityId(entityId);
    postEventRequest.setName("EVALUATE_OFFER_DEVICE_DATA");
    postEventRequest.setCategory("DEVICE_DATA");
    postEventRequest.setEntityType("CUSTOMER_ID");

    String ipAddress = "test-ip-address";
    String deviceId = "test-device-id";
    String appPlatform = "test-app-platform";
    ObjectMapper objectMapper = new ObjectMapper();
    ObjectNode requestMetadata = objectMapper.createObjectNode();
    requestMetadata.put("ip_address", ipAddress);
    requestMetadata.put("device_id", deviceId);
    requestMetadata.put("app_platform", appPlatform);
    postEventRequest.setMetadata(requestMetadata.toString());

    Event expected = Event.builder()
        .name(EventName.EVALUATE_OFFER_DEVICE_DATA)
        .category(EventCategory.DEVICE_DATA)
        .entityId(entityId)
        .entityType(EntityType.CUSTOMER_ID)
        .customerId(entityId)
        .metadata(DEVICE_METADATA_STRING)
        .build();
    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    assertEquals(expected, actual);
  }

  @Test
  public void convertToEventNullValuesTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    postEventRequest.setName("EVALUATE_OFFER_DEVICE_DATA");
    postEventRequest.setCategory("DEVICE_DATA");
    postEventRequest.setMetadata("{}");
    Event expected = Event.builder()
        .name(EventName.EVALUATE_OFFER_DEVICE_DATA)
        .category(EventCategory.DEVICE_DATA)
        .entityId(null)
        .entityType(null)
        .metadata("{}")
        .build();
    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    assertEquals(expected, actual);
  }

  @Test
  public void convertToEventUnknownPropertiesTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    postEventRequest.setName("EVALUATE_OFFER_VANTAGE_SCORE");
    postEventRequest.setCategory("VANTAGE_SCORE");
    postEventRequest.setMetadata("{\"alloy_token\": \"test-token\", \"vantage_score\": 500}");
    Event expected = Event.builder()
        .name(EventName.EVALUATE_OFFER_VANTAGE_SCORE)
        .category(EventCategory.VANTAGE_SCORE)
        .entityId(null)
        .entityType(null)
        .metadata("{\"alloy_token\": \"test-token\", \"vantage_score\": 500}")
        .build();
    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    assertEquals(expected, actual);
    Assertions.assertDoesNotThrow(() -> PostEventRequestConverter.convertToEvent(postEventRequest));
  }

  @Test
  public void testGetCustomerIdNotNull() {
    String customerId = "1234";
    String entityId = "1234";
    EntityType entityType = EntityType.CUSTOMER_ID;

    String result = PostEventRequestConverter.getCustomerId(customerId, entityId, entityType);
    assertEquals(customerId, result);
  }

  @Test
  public void testGetCustomerIdNull() {
    String entityId = "1234";
    EntityType entityType = EntityType.CUSTOMER_ID;

    String result = PostEventRequestConverter.getCustomerId(null, entityId, entityType);
    assertEquals(entityId, result);
  }

  @Test
  public void convertToEventCreditReportIncompleteTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    String entityId = "hashed_ssn_value";
    String customerId = "123";
    postEventRequest.setEntityId(entityId);
    postEventRequest.setCustomerId(customerId);
    postEventRequest.setName("CREDIT_REPORT_INCOMPLETE");
    postEventRequest.setCategory("ONBOARDING_CREDIT_REPORT_INCOMPLETE");
    postEventRequest.setEntityType("SSN_HMAC");

    Long productId = ProductConstants.RENTAL_PRODUCT_ID;
    Long productCategoryId = ProductConstants.MOVE_IN_CATEGORY_ID;
    String offerId = "test-offer-id";
    Long offerVersion = 1L;
    String checkpointFailure = "checkpoint_test";
    String ruleFailure = "rule_test";
    
    ObjectMapper objectMapper = new ObjectMapper();
    ObjectNode requestMetadata = objectMapper.createObjectNode();
    requestMetadata.put("product_id", productId);
    requestMetadata.put("product_category_id", productCategoryId);
    requestMetadata.put("offer_id", offerId);
    requestMetadata.put("offer_version", offerVersion);
    requestMetadata.put("checkpoint_failure", checkpointFailure);
    requestMetadata.put("rule_failure", ruleFailure);
    postEventRequest.setMetadata(requestMetadata.toString());

    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    
    assertEquals(EventName.CREDIT_REPORT_INCOMPLETE, actual.getName());
    assertEquals(EventCategory.ONBOARDING_CREDIT_REPORT_INCOMPLETE, actual.getCategory());
    assertEquals(entityId, actual.getEntityId());
    assertEquals(SSN_HMAC, actual.getEntityType());
    assertEquals(customerId, actual.getCustomerId());
    assertNotNull(actual.getMetadata());
    
    String actualMetadata = actual.getMetadata();
    assert actualMetadata.contains("\"product_id\":" + productId);
    assert actualMetadata.contains("\"product_category_id\":" + productCategoryId);
    assert actualMetadata.contains("\"offer_id\":\"" + offerId + "\"");
    assert actualMetadata.contains("\"offer_version\":" + offerVersion);
    assert actualMetadata.contains("\"checkpoint_failure\":\"" + checkpointFailure + "\"");
    assert actualMetadata.contains("\"rule_failure\":\"" + ruleFailure + "\"");
  }

  @Test
  public void convertToEventCreditReportIncompleteWithPartialDataTest() {
    PostEventRequest postEventRequest = new PostEventRequest();
    String entityId = "hashed_ssn_value";
    String customerId = "123";
    postEventRequest.setEntityId(entityId);
    postEventRequest.setCustomerId(customerId);
    postEventRequest.setName("CREDIT_REPORT_INCOMPLETE");
    postEventRequest.setCategory("ONBOARDING_CREDIT_REPORT_INCOMPLETE");
    postEventRequest.setEntityType("SSN_HMAC");

    Long productId = ProductConstants.RENTAL_PRODUCT_ID;
    String offerId = "test-offer-id";
    ObjectMapper objectMapper = new ObjectMapper();
    ObjectNode requestMetadata = objectMapper.createObjectNode();
    requestMetadata.put("product_id", productId);
    requestMetadata.put("offer_id", offerId);
    postEventRequest.setMetadata(requestMetadata.toString());

    Event actual = PostEventRequestConverter.convertToEvent(postEventRequest);
    
    assertEquals(EventName.CREDIT_REPORT_INCOMPLETE, actual.getName());
    assertEquals(EventCategory.ONBOARDING_CREDIT_REPORT_INCOMPLETE, actual.getCategory());
    assertEquals(entityId, actual.getEntityId());
    assertEquals(SSN_HMAC, actual.getEntityType());
    assertEquals(customerId, actual.getCustomerId());
    assertNotNull(actual.getMetadata());
    
    String actualMetadata = actual.getMetadata();
    assert actualMetadata.contains("\"product_id\":" + productId);
    assert actualMetadata.contains("\"offer_id\":\"" + offerId + "\"");
  }
}
