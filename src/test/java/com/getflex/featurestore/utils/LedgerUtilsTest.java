package com.getflex.featurestore.utils;

import static com.getflex.featurestore.helper.TestCreationUtils.fundsInRecord;
import static com.getflex.featurestore.helper.TestCreationUtils.fundsOutRecord;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.LedgerService;
import com.getflex.featurestore.integration.flex.LedgerService.MovementCategory;
import com.getflex.featurestore.integration.flex.utils.LedgerUtils;
import com.getflex.ledger.model.RecordLedger;
import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class LedgerUtilsTest {

  private static final Long CUSTOMER_ID = 123L;

  @InjectMocks
  LedgerUtils ledgerUtils;

  @Mock
  LedgerService ledgerService;

  @Test
  public void getFundsOutRecords_nullResponse() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(null);
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsOutRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

  @Test
  public void getFundsOutRecords_EmptyList() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsOutRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

  @Test
  public void getFundsOutRecords_ValidResponse() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(
        List.of(
            fundsOutRecord(builder -> builder)
        )
    );
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsOutRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertEquals(1, recordLedgers.size());
  }

  @Test
  public void getFundsOutRecords_FilterNonFundsOutRecords() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(
        List.of(
            fundsInRecord(builder -> builder)
        )
    );
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsOutRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

  @Test
  public void getFundsInRecords_nullResponse() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(null);
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsInRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

  @Test
  public void getFundsInRecords_EmptyList() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(List.of());
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsOutRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

  @Test
  public void getFundsInRecords_ValidResponse() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(
        List.of(
            fundsInRecord(builder -> builder)
        )
    );
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsInRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertEquals(1, recordLedgers.size());
  }

  @Test
  public void getFundsInRecords_FilterNonFundsOutRecords() {
    when(ledgerService.retrieveLedgerByCustomerId(any())).thenReturn(
        List.of(
            fundsInRecord(builder -> builder.paymentCategoryId(MovementCategory.REFUND.getValue()))
        )
    );
    List<RecordLedger> recordLedgers = ledgerUtils.getFundsInRecords(CUSTOMER_ID, OffsetDateTime.now());
    assertTrue(recordLedgers.isEmpty());
  }

}
