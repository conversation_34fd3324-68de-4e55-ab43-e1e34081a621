package com.getflex.featurestore.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class FulfillmentUtilsTests {

  @Test
  public void testGetPayDateBefore27th() {
    LocalDate mockDate = LocalDate.of(2024, 5, 10);
    try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
      mockedLocalDate.when(LocalDate::now).thenReturn(mockDate);

      LocalDate payDate = FulfillmentUtils.getPayDate();
      assertEquals(LocalDate.of(2024, 5, 1), payDate);
    }
  }

  @Test
  public void testGetPayDateAfter27th() {
    LocalDate mockDate = LocalDate.of(2024, 8, 29);
    try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
      mockedLocalDate.when(LocalDate::now).thenReturn(mockDate);

      LocalDate payDate = FulfillmentUtils.getPayDate();
      assertEquals(LocalDate.of(2024, 9, 1), payDate);
    }
  }

  @Test
  public void testGetPayDateAfter27thDecember() {
    LocalDate mockDate = LocalDate.of(2024, 12, 29);
    try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
      mockedLocalDate.when(LocalDate::now).thenReturn(mockDate);
      LocalDate payDate = FulfillmentUtils.getPayDate();
      assertEquals(LocalDate.of(2025, 1, 1), payDate);
    }
  }

  @Test
  public void testGetPayDateOn27th() {
    LocalDate mockDate = LocalDate.of(2024, 5, 27);
    try (MockedStatic<LocalDate> mockedLocalDate = Mockito.mockStatic(LocalDate.class)) {
      mockedLocalDate.when(LocalDate::now).thenReturn(mockDate);

      LocalDate payDate = FulfillmentUtils.getPayDate();
      assertEquals(LocalDate.of(2024, 5, 1), payDate);
    }
  }
}