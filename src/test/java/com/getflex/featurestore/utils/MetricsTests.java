package com.getflex.featurestore.utils;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

import com.getflex.o11y.helper.MetricsUtils;
import com.getflex.o11y.helper.MetricsUtils.TagsConfig;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class MetricsTests {
  @Test
  void testBuildTagWithInvalidKey() {
    Assertions.assertThrows(IllegalArgumentException.class, () -> {
      Metrics.buildTag("a:b", "c");
    });
  }

  @Test
  void testConvertTags() {
    TagsConfig tagsConfig = new TagsConfig();
    tagsConfig.setTags(Map.of("y", "z"));
    MetricsUtils utils = new MetricsUtils(null, tagsConfig);
    assertArrayEquals(new String[]{"a:b", "c:d", "y:z"}, Metrics.convertTags(new String[]{"a", "b", "c", "d"}));
  }
}
