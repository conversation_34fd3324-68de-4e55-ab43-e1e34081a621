package com.getflex.featurestore.utils;

import java.net.http.HttpResponse.BodySubscriber;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.concurrent.Flow;

/**
 * Helps capture content sent to {@link java.net.http.HttpClient}.
 */
public class StringSubscriber implements Flow.Subscriber<ByteBuffer> {

  final BodySubscriber<String> wrapped;

  public StringSubscriber(BodySubscriber<String> wrapped) {
    this.wrapped = wrapped;
  }

  @Override
  public void onSubscribe(Flow.Subscription subscription) {
    wrapped.onSubscribe(subscription);
  }

  @Override
  public void onNext(ByteBuffer item) {
    wrapped.onNext(List.of(item));
  }

  @Override
  public void onError(Throwable throwable) {
    wrapped.onError(throwable);
  }

  @Override
  public void onComplete() {
    wrapped.onComplete();
  }
}
