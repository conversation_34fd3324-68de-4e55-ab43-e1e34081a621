package com.getflex.featurestore.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class StringToJsonCustomerParserTests {
  @InjectMocks
  StringToJsonCustomParser parser;

  @BeforeEach
  void setUp() {
    parser = new StringToJsonCustomParser();
  }

  @Test
  void testSimpleObjectParsing() throws Exception {
    String input = "{key1=value1, key2=123, key3=true}";
    JsonNode node = parser.parseAsJsonNode(input);

    assertTrue(node.isObject());
    assertEquals("value1", node.get("key1").asText());
    assertEquals(123, node.get("key2").asInt());
    assertTrue(node.get("key3").asBoolean());
  }

  @Test
  void testNestedObjectAndArray() throws Exception {
    String input = "{outer={inner=[a, b, c], num=2.5}}";
    JsonNode node = parser.parseAsJsonNode(input);

    JsonNode outer = node.get("outer");

    JsonNode innerArray = outer.get("inner");
    assertTrue(innerArray.isArray());
    assertEquals("a", innerArray.get(0).asText());
    assertEquals("b", innerArray.get(1).asText());
    assertEquals("c", innerArray.get(2).asText());

    assertEquals(2.5, outer.get("num").asDouble(), 1e-6);
  }

  @Test
  void testEmptyObject() throws Exception {
    String input = "{}";
    JsonNode node = parser.parseAsJsonNode(input);

    assertTrue(node.isObject());
    assertEquals(0, node.size());
  }

  @Test
  void testArrayOnly() throws Exception {
    String input = "[1, 2, three]";
    JsonNode node = parser.parseAsJsonNode(input);

    assertTrue(node.isArray());
    assertEquals(1, node.get(0).asInt());
    assertEquals(2, node.get(1).asInt());
    assertEquals("three", node.get(2).asText());
  }

  @Test
  void testInvalidSyntaxThrowsException() {
    String input = "{unclosed=[1,2}";
    assertThrows(IllegalStateException.class, () -> parser.parseAsJsonNode(input));
  }

  @Test
  void testFullSocureResponse() throws IOException {
    String input = "{referenceId=3380cc50-6e56-4d2a-8200-9795b2b5d394, nameAddressCorrelation={reasonCodes=[R705], score=0.07}, nameEmailCorrelation={reasonCodes=[I554, I557, I556, I559, I558], score=0.98}, namePhoneCorrelation={reasonCodes=[I621, I622, I624, I618, I623], score=0.98}, fraud={reasonCodes=[I637, I626, I711], scores=[{name=sigma, version=4.0, score=0.332}]}, kycPlus={socureId=a9b80a97-302b-a1d0-422f-f4d9c5202926, reasonCodes=[R919], fieldValidations={firstName=0.99, surName=0.99, streetAddress=0.01, city=0.99, state=0.99, zip=0.99, mobileNumber=0.99, dob=0.99, ssn=0.99, email=0.99}, sourceAttribution=[Credit, Alternative Credit], bestMatchedEntity={dob=AgV4P4TNxcXHPUBYYzzx5vT5/5gA3rz/OZfnmZS6eiVCyVsAXwABABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREExaHpkSERmNUl3d0ZyaUtXWXpUV0tiUHlaS0YrQkJKemRXUHZSTnlVSjRFYUN6Ymc2ZUFaeGJqUk1qTm5LZlk3dz09AAEAB2F3cy1rbXMAS2Fybjphd3M6a21zOnVzLWVhc3QtMToxMDMxODE0MzYzODU6a2V5LzBlZTFlNmNmLWI4MWItNDU2OS04NjhhLTllMzNhOGE4MDUzYgC4AQIBAHgIJvTETbM4CLLXdrEcHIdDUuo+PJpmNQsRGn6bEq+D4gETVyOwjtbRrFy3kp1A/bPmAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMH+eO89k812jylI8/AgEQgDujBo3ClUFxH5RVhoC0W/JGsI3Ng2ooKXKCKqbpEoPrePPWArhxhpDrRW7BL7LcYBQy6li13uAR4bTdUwIAABAAPGGtsafdAZRQ1vyvz0H0URpM4EFLO07Bo9evH/ASeyUbiFLqA2Nax6+SKp5ArTHN/////wAAAAEAAAAAAAAAAAAAAAEAAAAKw85OEDzKa+/K3sJeOQe/o8KKY0+NBDeZJZwAZzBlAjEA9A6yyMg1I9++X0t8FoYsFATrreN9W8985FRZFRiz9mPHzUeMz+o5Uqglc811WLRJAjA9LMQldvAOAxqFSo82gtCkPrlNgLy3CiGNpaw4yqcX6763GoZQRuHHbbnYNugeZzo=}}, addressRisk={reasonCodes=[I711, R705, I704, I713, R607, I707], scores=[{name=RiskAddressUS.V7.1_Uniform, version=7.1, score=0.764}]}, emailRisk={reasonCodes=[I637, I576, I568, I553, I556, I569, I555, I566, I570], scores=[{name=RiskEmailUS.V10.1__Uniform.V1, version=10.1, score=0.184}]}, phoneRisk={reasonCodes=[I637, I626, I632, I611, I614, I636, R660, I608, I630, R607, I618, I602, R659], scores=[{name=RiskPhoneUS.V6__Norm.V1, version=6.0, score=0.538}]}, decision={value=accept, modelName=Flex Finance, modelVersion=1.0, details={synthetic={decision=accept}, kycplus={decision=accept}, watchlistplus={decision=accept}, firstpartyfraud={decision=accept}, namephonecorrelation={decision=accept}, fraud={decision=accept}, addressrisk={decision=accept}, alertlist={decision=accept}, phonerisk={decision=accept}, emailrisk={decision=accept}, nameemailcorrelation={decision=accept}, nameaddresscorrelation={decision=accept}}}, alertList={reasonCodes=[], matches=[]}, globalWatchlist={reasonCodes=[R184, R186], matches={PEP Data=[{entityId=ZaP+/U4QebAuGHxNBSQMyCqAjPCZGko, matchFields=[nameFuzzy], sourceUrls=[http://www.col.gob.mx/], comments={activeStartDate=[2016-01-01], name=[Ricardo Antonio Alfaro de Anda], originalCountryText=[Mexico], country=[Mexico], chamber=[Regional Executive], locationurl=[https://www.col.gob.mx/Portal/detalle_gabinete/4562], aka=[Ricardo Antonio Alfaro de Anda], activeEndDate=[2021-12-31], entityType=[individual], politicalPosition=[Member of the Regional Executive], region=[Colima], offense=[Pep Class 2], otherInfo=[Consejero Jurídico del Poder Ejecutivo del Estado], countryCodes=[MX]}, matchScore=59, entityCorrelationScore=35}, {entityId=ZaP+/U4QebAwHnpGait6zTCCj/eZdjw, matchFields=[nameExact], sourceUrls=[https://www.gob.pe/sineace], comments={activeStartDate=[2024-04-08], name=[Ana Victoria Alfaro Carlin], originalCountryText=[Peru], country=[Peru], chamber=[National System of Evaluation, Accreditation and Certification of Educational Quality (SINEACE)], sourceName=[Peru National System Of Evaluation Accreditation And Certification Of Educational Quality Sineace Leadership], locationurl=[https://www.gob.pe/institucion/sineace/funcionarios/152721-ana-victoria-alfaro-carlin], aka=[Ana Victoria Alfaro Carlin], entityType=[individual], politicalPosition=[Senior Government Official], function=[Directora de la Dirección de Evaluación y Certificación del CONEACES - Sineace], offense=[Pep Class 1], countryCodes=[PE], institutionType=[Agency under Ministry]}, matchScore=87, entityCorrelationScore=60}]}}, synthetic={reasonCodes=[I207, R224, I621, I622, I554, I568, I212, I557, R209, R705, R660, I556, I211, R207, I559, I624, I569, R208, R607, I203, I618, I623, I570, I558, R659], scores=[{name=synthetic, version=4.0, score=0.823}]}, customerProfile={customerUserId=4229568}, firstPartyFraud={reasonCodes=[I1001], signals={}}}";
    JsonNode node = parser.parseAsJsonNode(input);
    System.out.println(node.toString());
    assertEquals("\"3380cc50-6e56-4d2a-8200-9795b2b5d394\"", node.get("referenceId").toString());
    assertEquals("[\"I637\",\"I626\",\"I711\"]", node.get("fraud").get("reasonCodes").toString());
    assertEquals(0.184, node.get("emailRisk").get("scores").get(0).get("score").asDouble());
    assertEquals(0.538, node.get("phoneRisk").get("scores").get(0).get("score").asDouble());
    assertEquals(0.764, node.get("addressRisk").get("scores").get(0).get("score").asDouble());
    assertEquals(0.823, node.get("synthetic").get("scores").get(0).get("score").asDouble());
  }
}
