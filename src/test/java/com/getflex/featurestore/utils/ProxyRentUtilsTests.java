package com.getflex.featurestore.utils;

import static org.mockito.Mockito.when;

import com.getflex.featurestore.integration.flex.IdentityService;
import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.feature.proxyrent.ProxyRentUtils;
import com.getflex.identity.model.GetCustomerResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ProxyRentUtilsTests {

  @InjectMocks
  ProxyRentUtils proxyRentUtils;

  @Mock
  IdentityService identityService;

  @ParameterizedTest
  @CsvSource({
      "zip_code_rent_bp_feature, New York, NY, 11122-111, 123 Main St. unit 10, 11122",
      "zip_code_rent_mean_feature, New York, NY, 11122-111, 123 Main St. unit 10, 11122",
      "zip_code_rent_bp_feature, New York, NY, 11122 111, 123 Main St. unit 10, 11122",
      "zip_code_rent_bp_feature, New York, NY, 00234, 123 Main St. unit 10, 234",
      "zip_code_rent_mean_feature, New York, NY, 00234-11, 123 Main St. unit 10, 234",
      "zip_code_rent_mean_feature, New York, NY, , 123 Main St. unit 10, ''",
      "city_name_rent_bp_feature, New York, NY, 11122, 123 Main St. unit 10, n620_ny",
      "city_name_rent_mean_feature, New-York, NY, 11122, 123 Main St. unit 10, n620_ny",
      "city_name_rent_bp_feature, NEW! YORK!, NY, 11122, 123 Main St. unit 10, n620_ny",
      "city_name_rent_mean_feature, New-York!, , 11122, 123 Main St. unit 10, n620_",
      "street_name_rent_bp_feature, St Louis, MO, 11122, 123 Main St. unit 10, m523_11122",
      "street_name_rent_mean_feature, St Louis, MO, 90402, 100 West Channel Road unit A1, w232_90402",
      "street_name_rent_mean_feature, St Louis, MO, 11122, 123 E 4th plane ter A1, e314_11122",
      "street_name_rent_mean_feature, St Louis, MO, 90402, 100 West Channel Road unit A1, w232_90402",
      "street_name_rent_bp_feature, St Louis, MO, 11122, 123 SW 1st plane pkwy. A1, s314_11122",
      "street_name_rent_mean_feature, SHORELINE, WA, 98133, 1140 N. 192ND STREET, n323_98133",
      "street_name_rent_mean_feature, Phoenix, AZ, 85023, 1944 W. Thunderbird Rd, w353_85023",
      "street_name_rent_mean_feature, Riverside, AL, 35135, 417X Rental Office, r534_35135",
      "street_name_rent_mean_feature, Fridley, NM, 55432, 111 83rd Avenue NE, 8631_55432",
      "street_name_rent_mean_feature, Minneapolis, MN, 55415, 600 5th Avenue S, 5315_55415",
      "street_name_rent_mean_feature, Memphis, TN, 38115, Oro Parkway SPE Owner LLC, p622_38115",
      "street_name_rent_mean_feature, Biloxi, MS, 39532, 1955 Popp's Ferry Rd, p121_39532",
      "street_name_rent_mean_feature, Willis, TX, 77318, 9700 FM-1097, f500_77318",
      "street_name_rent_mean_feature, Roseville, MN, 55113, 1125 W. Roselawn Avenue & 1943 N. Lexi ave, w624_55113",
      "street_name_rent_mean_feature, Marysville, MI, 48040, 1210 Aspen Drive #3, a215_48040",
      "state_name_rent_bp_feature, St Louis, MO, 11122, 123 1st plane pkwy. A1, mo",
      "state_name_rent_mean_feature, New York, NY, 11122-111, 123 Main St. unit 10, ny",
      "NoMatch, New York, NY, 11122-111, 123 Main St. unit 10, ''",
  })
  public void formatPrimaryKey(String featureName, String city, String state, String zip, String address,
      String expected) {
    EvalParams params = new EvalParams().customerId(1L);
    GetCustomerResponse getCustomerResponse = new GetCustomerResponse()
        .city(city)
        .state(state)
        .zip(zip)
        .addressLine1(address);
    when(identityService.getCustomer(1L)).thenReturn(getCustomerResponse);
    String result = proxyRentUtils.formatPrimaryKey(featureName, params);
    Assertions.assertEquals(expected, result);
  }

}
