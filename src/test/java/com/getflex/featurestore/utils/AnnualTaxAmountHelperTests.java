package com.getflex.featurestore.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class AnnualTaxAmountHelperTests {

  @Test
  public void estimateAnnualTax() {

    long[] brackets = new long[] {
        100000, // 12k annually
        200000, // 24k annually
        400000, // 48k annually
        810000, // 97.2k annually
        1750000, // 210k annually
        3300000, // 396k annually
        4200000, // 504k annually
        6300000 // 756k annually
    };

    long[] expectedTaxAmount = new long[] {
        120000, // 12k annually
        240300, // 24k annually
        528300, // 48k annually
        1121200, // 97.2k annually
        3609400, // 210k annually
        8084600, // 396k annually
        11549450, // 504k annually
        20378250 // 756k annually
    };

    for (int i = 0; i < brackets.length; i++) {
      long taxAmt = AnnualTaxAmountHelper.estimateAnnualTax(brackets[i]);
      Assertions.assertEquals(expectedTaxAmount[i], taxAmt);
    }
  }

}
