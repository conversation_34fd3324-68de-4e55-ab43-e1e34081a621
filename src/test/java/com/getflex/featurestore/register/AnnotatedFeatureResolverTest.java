package com.getflex.featurestore.register;

import com.getflex.featurestore.model.EvalParams;
import com.getflex.featurestore.model.FeatureTypeEnum;
import com.getflex.featurestore.model.feature.base.BaseFeature;
import com.getflex.featurestore.model.feature.base.FeatureOutput;
import com.getflex.featurestore.model.feature.base.param.ParamRequired;
import java.time.Duration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;

public class AnnotatedFeatureResolverTest {

  public record TestParams(String value1, String value2) {
  }

  public record TestDurationParam(Duration duration) {
  }

  @RegisterFeatureIgnore
  private static class BlankTestFeature extends BaseFeature {
    public BlankTestFeature() {
    }

    @Override
    public FeatureTypeEnum getType() {
      return null;
    }

    @Override
    protected FeatureOutput getValue(EvalParams evalParams) {
      return null;
    }

    @Override
    public String getDescription() {
      return "";
    }

    @Override
    public ParamRequired getRequiredEvalParamKeys() {
      return null;
    }
  }


  @RegisterFeature
  private static class TestFeature extends BlankTestFeature {
  }

  @RegisterFeature(value = "TestFeature")
  private static class TestFeatureWithExplicitValue extends BlankTestFeature {
  }

  // no params class defined but param in name. This should error if registered
  @RegisterFeature(value = "AnotherName")
  @RegisterFeature(value = "TestFeature_{param}")
  private static class TestFeatureWithMissingParamsClass extends BlankTestFeature {
  }

  @RegisterFeature(value = "TestFeature_{value1}", parameterType = TestParams.class)
  private static class TestFeatureWithValue extends BlankTestFeature {
    public final TestParams params;

    private TestFeatureWithValue(TestParams params) {
      this.params = params;
    }
  }

  // this is a matching error with TestFeatureWithValue because it would match the same pattern
  @RegisterFeature(value = "TestFeature_stuff{value2}", parameterType = TestParams.class)
  private static class TestFeatureWithValueMatchPartner extends BlankTestFeature {
  }

  @RegisterFeature(value = "TestFeature_{propWhichDoesntExist}", parameterType = TestParams.class)
  private static class TestFeatureWithMissingParams extends BlankTestFeature {
  }

  // this is a matching error with TestFeatureWithValue because it would match the same pattern
  @RegisterFeature(value = "TestDuration_{duration}", parameterType = TestDurationParam.class)
  private static class TestDuration extends BlankTestFeature {
    public final TestDurationParam params;

    private TestDuration(TestDurationParam params) {
      this.params = params;
    }
  }

  // this is a matching error with TestFeatureWithValue because it would match the same pattern
  @RegisterFeature
  @RegisterFeature(value = "TestMultipleRegisters_{duration}", parameterType = TestDurationParam.class)
  private static class TestMultipleRegisters extends BlankTestFeature {
    public final TestDurationParam params;

    private TestMultipleRegisters() {
      this(new TestDurationParam(null));
    }

    private TestMultipleRegisters(TestDurationParam params) {
      this.params = params;
    }
  }

  // this is a matching error with TestFeatureWithValue because it would match the same pattern
  @RegisterFeature
  @RegisterFeature(value = "TestMultipleRegistersMissingConstructor_{duration}",
      parameterType = TestDurationParam.class)
  private static class TestMultipleRegistersMissingConstructor extends BlankTestFeature {

    // theres a RegisterFeature with no params, TestDurationParam is not available
    private TestMultipleRegistersMissingConstructor(TestDurationParam params) {
    }
  }

  @RegisterFeature
  @RegisterFeatureIgnore
  private static class BadFeatureName extends BlankTestFeature {
    @Override
    public void setName(String name) {
      // simulate bad name
      throw new NullPointerException();
    }
  }

  @RegisterFeature
  @RegisterFeatureIgnore
  private static class BadFeature {}

  @Test
  public void resolve_set_name_critical_failure() {
    AnnotatedFeatureResolver resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(BadFeatureName.class));

    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        resolver.resolveFeature("BadFeatureName"));

    Assertions.assertNotNull(exception);
    Assertions.assertTrue(exception.getMessage().startsWith("Failed to set name property"));
  }

  @Test
  public void resolve_doesnt_exist() {
    AnnotatedFeatureResolver resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestFeature.class));

    Assertions.assertNull(resolver.resolveFeature("Test"));
  }

  @Test
  public void resolve_bad_base_class_fails() {
    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
            createTestScanner(BadFeature.class)));

    Assertions.assertNotNull(exception);
    Assertions.assertTrue(exception.getMessage().startsWith("Bad annotation on class"));
  }


  @Test
  public void resolve_basicFeature() {
    AnnotatedFeatureResolver resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestFeature.class));

    TestFeature feature = (TestFeature) resolver.resolveFeature("TestFeature");

    Assertions.assertNotNull(feature);
    Assertions.assertEquals(TestFeature.class, feature.getClass());
  }

  @Test
  public void resolve_paramFeature() {
    AnnotatedFeatureResolver resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestFeature.class, TestFeatureWithValue.class));

    TestFeatureWithValue feature = (TestFeatureWithValue) resolver.resolveFeature("TestFeature_fubar");

    Assertions.assertNotNull(feature);
    Assertions.assertEquals("fubar", feature.params.value1);
  }

  @Test
  public void testTestDuration() {
    AnnotatedFeatureResolver resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestDuration.class));

    TestDuration feature = (TestDuration) resolver.resolveFeature("TestDuration_P2D");

    Assertions.assertNotNull(feature);
    Assertions.assertEquals(Duration.parse("P2D"), feature.params.duration);
  }

  @Test
  public void error_duplicateFeatures_noParams() {
    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
            createTestScanner(TestFeature.class, TestFeatureWithExplicitValue.class)));

    Assertions.assertTrue(exception.getMessage().startsWith("Failed to register featureName"),
        "Message not matched, actual message was: " + exception.getMessage());
  }

  @Test
  public void error_duplicateFeatures_withParams() {
    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
            createTestScanner(TestFeatureWithValue.class, TestFeatureWithValueMatchPartner.class)));

    Assertions.assertTrue(exception.getMessage().startsWith("Failed to register featureName"),
        "Message not matched, actual message was: " + exception.getMessage());
  }

  @Test
  public void error_missingParamsValue() {
    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
            createTestScanner(TestFeatureWithMissingParams.class)));

    Assertions.assertTrue(exception.getMessage().contains("has parameter 'propWhichDoesntExist'"),
        "Message not matched, actual message was: " + exception.getMessage());
  }

  @Test
  public void error_missingParamsClass() {
    FeatureRegistrationException exception = Assertions.assertThrows(FeatureRegistrationException.class, () ->
        new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
            createTestScanner(TestFeatureWithMissingParamsClass.class)));

    Assertions.assertTrue(exception.getMessage().contains("Property parameterType is required"),
        "Message not matched, actual message was: " + exception.getMessage());
  }

  @Test
  public void test_multipleParams() {
    var resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestMultipleRegisters.class, TestDuration.class));

    resolver.resolveFeature("TestMultipleRegisters");
    resolver.resolveFeature("TestMultipleRegisters_P2D");
  }

  @Test
  public void test_multipleParams_missing_autowire() {
    var resolver = new AnnotatedFeatureResolver(new DefaultListableBeanFactory(),
        createTestScanner(TestMultipleRegistersMissingConstructor.class));

    // 2 registrations, one with params one without
    Assertions.assertEquals(2, resolver.getFeatureMatchers().size());

    Assertions.assertThrows(FeatureRegistrationException.class, () ->
        resolver.resolveFeature("TestMultipleRegistersMissingConstructor"));

    resolver.resolveFeature("TestMultipleRegistersMissingConstructor_P2D");
  }

  private static ClassPathScanningCandidateComponentProvider createTestScanner(Class<?>... classesToInclude) {
    ClassPathScanningCandidateComponentProvider scanner =
        new ClassPathScanningCandidateComponentProvider(false);
    for (Class<?> clazz : classesToInclude) {
      scanner.addIncludeFilter(new AssignableTypeFilter(clazz));
    }
    return scanner;
  }
}
