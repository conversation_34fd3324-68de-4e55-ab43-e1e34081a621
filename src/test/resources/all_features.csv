com.getflex.featurestore.model.feature.BillTransactionBillAmountCentsFromPaymentFeature, BillTransactionBillAmountCentsFromPaymentFeature
com.getflex.featurestore.model.feature.BillTransactionBillAmountFromLedgerFeature, BillTransactionBillAmountFromLedgerFeature
com.getflex.featurestore.model.feature.BillTransactionDeclinedDownpaymentCountFromLedgerFeature, BillTransactionDeclinedDownpaymentCountFromLedgerFeature
com.getflex.featurestore.model.feature.BillTransactionDisputedDaysFeature, BillTransactionDisputedDaysFeature
com.getflex.featurestore.model.feature.BillTransactionDisputedDaysWithMinAmountFeature, BillTransactionDisputedDaysWithMinAmountFeature
com.getflex.featurestore.model.feature.BillTransactionDownpaymentChargedAmountCentsFeature, BillTransactionDownpaymentChargedAmountCentsFeature
com.getflex.featurestore.model.feature.BillTransactionDownpaymentRefundAmountCentsFeature, BillTransactionDownpaymentRefundAmountCentsFeature
com.getflex.featurestore.model.feature.BillTransactionHasDownpaymentRefundFeature, BillTransactionHasDownpaymentRefundFeature
com.getflex.featurestore.model.feature.BillTransactionHasDuplicatedDdaTransactionFeature, BillTransactionHasDuplicatedDdaTransactionFeature
com.getflex.featurestore.model.feature.BillTransactionHasSuspiciousFulfillmentCodeFeature, BillTransactionHasSuspiciousFulfillmentCodeFeature
com.getflex.featurestore.model.feature.BillTransactionHasValidFulfillmentStatusFeature, BillTransactionHasValidFulfillmentStatusFeature
com.getflex.featurestore.model.feature.BillTransactionInValidBpWindowFeature, BillTransactionInValidBpWindowFeature
com.getflex.featurestore.model.feature.CardLinkageCountFeature, CardLinkageCountFeature
com.getflex.featurestore.model.feature.ChargeAttemptDateFeature, ChargeAttemptDateFeature
com.getflex.featurestore.model.feature.CmmScoreFeature, CmmScoreFeature
com.getflex.featurestore.model.feature.CmmV2ScoreFeature, CmmV2ScoreFeature
com.getflex.featurestore.model.feature.CmmV3ScoreFeature, CmmV3ScoreFeature
com.getflex.featurestore.model.feature.CreditReportFeature, CreditReportFeature
com.getflex.featurestore.model.feature.CustomerBalanceFeature, CustomerBalanceFeature
com.getflex.featurestore.model.feature.CustomerBillerIntegrationTypeFeature, CustomerBillerIntegrationTypeFeature
com.getflex.featurestore.model.feature.CustomerCardStateMismatchFeature, CustomerCardStateMismatchFeature
com.getflex.featurestore.model.feature.CustomerCardZipCodeMismatchFeature, CustomerCardZipCodeMismatchFeature
com.getflex.featurestore.model.feature.CustomerCprReportFeature, CustomerCprReportFeature
com.getflex.featurestore.model.feature.CustomerCurrentBillAmountFeature, CustomerCurrentBillAmountFeature
com.getflex.featurestore.model.feature.CustomerDaysSinceLastEmailAddressChangeFeature, CustomerDaysSinceLastEmailAddressChangeFeature
com.getflex.featurestore.model.feature.CustomerDaysSinceLastFundsInFeature, CustomerDaysSinceLastFundsInFeature
com.getflex.featurestore.model.feature.CustomerDaysSinceLastPhoneNumberChangeFeature, CustomerDaysSinceLastPhoneNumberChangeFeature
com.getflex.featurestore.model.feature.CustomerDaysSinceLastSuspensionFeature, CustomerDaysSinceLastSuspensionFeature
com.getflex.featurestore.model.feature.CustomerDaysSinceOfferAcceptFeature, CustomerDaysSinceOfferAcceptFeature
com.getflex.featurestore.model.feature.CustomerDenylistedForReactivationFeature, CustomerDenylistedForReactivationFeature
com.getflex.featurestore.model.feature.CustomerDistinctCardZipCodesCountFeature, CustomerDistinctCardZipCodesCountFeature
com.getflex.featurestore.model.feature.CustomerDq0Past3Months, CustomerDq0Past3Months
com.getflex.featurestore.model.feature.CustomerDq0Past3MonthsExcludingCb, CustomerDq0Past3MonthsExcludingCb
com.getflex.featurestore.model.feature.CustomerDq180Past24Months, CustomerDq180Past24Months
com.getflex.featurestore.model.feature.CustomerDq180Past24MonthsExcludingCb, CustomerDq180Past24MonthsExcludingCb
com.getflex.featurestore.model.feature.CustomerDq30Past12Months, CustomerDq30Past12Months
com.getflex.featurestore.model.feature.CustomerDq30Past12MonthsExcludingCb, CustomerDq30Past12MonthsExcludingCb
com.getflex.featurestore.model.feature.CustomerDqStatus, CustomerDqStatus
com.getflex.featurestore.model.feature.CustomerDuplicatedCardFingerprintFeature, CustomerDuplicatedCardFingerprintFeature
com.getflex.featurestore.model.feature.CustomerDuplicatedDeviceIdFeature, CustomerDuplicatedDeviceIdFeature
com.getflex.featurestore.model.feature.CustomerEmailVerifiedFeature, CustomerEmailVerifiedFeature
com.getflex.featurestore.model.feature.CustomerEprVantageScoreFeature, CustomerEprVantageScoreFeature
com.getflex.featurestore.model.feature.CustomerEprVantageScoreLastPullDaysFeature, CustomerEprVantageScoreLastPullDaysFeature
com.getflex.featurestore.model.feature.CustomerExcludingCurrentBpUnbalancedCentsFeature, CustomerExcludingCurrentBpUnbalancedCentsFeature
com.getflex.featurestore.model.feature.CustomerFailed3DsCountFeature, CustomerFailed3DsCountFeature
com.getflex.featurestore.model.feature.CustomerFailedStepUp3DsCountFeature, CustomerFailedStepUp3DsCountFeature
com.getflex.featurestore.model.feature.CustomerFlexAnywhereHasDuplicatedAddressFeature, CustomerFlexAnywhereHasDuplicatedAddressFeature
com.getflex.featurestore.model.feature.CustomerHasActiveFulfillmentFeature, CustomerHasActiveFulfillmentFeature
com.getflex.featurestore.model.feature.CustomerHasSettledPayBillerTransactionFeature, CustomerHasSettledPayBillerTransactionFeature
com.getflex.featurestore.model.feature.CustomerInfoFeature, CustomerInfoFeature
com.getflex.featurestore.model.feature.CustomerIsActiveStatusFeature, CustomerIsActiveStatusFeature
com.getflex.featurestore.model.feature.CustomerIsAutopayOnFeature, CustomerIsAutopayOnFeature
com.getflex.featurestore.model.feature.CustomerIsDqFeature, CustomerIsDqFeature
com.getflex.featurestore.model.feature.CustomerIsReapplyIneligibleBankruptcyFeature, CustomerIsReapplyIneligibleBankruptcyFeature
com.getflex.featurestore.model.feature.CustomerIsReapplyIneligibleDeceasedFeature, CustomerIsReapplyIneligibleDeceasedFeature
com.getflex.featurestore.model.feature.CustomerIsReapplyIneligibleFraudFeature, CustomerIsReapplyIneligibleFraudFeature
com.getflex.featurestore.model.feature.CustomerIsReapplyIneligibleSelfPayAbuseFeature, CustomerIsReapplyIneligibleSelfPayAbuseFeature
com.getflex.featurestore.model.feature.CustomerIsReapplyIneligibleSettlementFeature, CustomerIsReapplyIneligibleSettlementFeature
com.getflex.featurestore.model.feature.CustomerLastSuspensionIsByFlexFeature, CustomerLastSuspensionIsByFlexFeature
com.getflex.featurestore.model.feature.CustomerLatestAcceptedCreditLineFeature, CustomerLatestAcceptedCreditLineFeature
com.getflex.featurestore.model.feature.CustomerLedgerBalanceCentsFeature, CustomerLedgerBalanceCentsFeature
com.getflex.featurestore.model.feature.CustomerNumberOfDisputeFiledPast6MonthsFeature, CustomerNumberOfDisputeFiledPast6MonthsFeature
com.getflex.featurestore.model.feature.CustomerOfferHistoryFeature, CustomerOfferHistoryFeature
com.getflex.featurestore.model.feature.CustomerRatioBillOverwrittenRentFeature, CustomerRatioBillOverwrittenRentFeature
com.getflex.featurestore.model.feature.CustomerSentilinkAbuseScoreFeature, CustomerSentilinkAbuseScoreFeature
com.getflex.featurestore.model.feature.CustomerSentilinkFirstPartySyntheticScoreFeature, CustomerSentilinkFirstPartySyntheticScoreFeature
com.getflex.featurestore.model.feature.CustomerSentilinkIdentityTheftScoreFeature, CustomerSentilinkIdentityTheftScoreFeature
com.getflex.featurestore.model.feature.CustomerSentilinkThirdPartySyntheticScoreFeature, CustomerSentilinkThirdPartySyntheticScoreFeature
com.getflex.featurestore.model.feature.CustomerSlcWithdrawableAmountCentsFeature, CustomerSlcWithdrawableAmountCentsFeature
com.getflex.featurestore.model.feature.CustomerSuccessful3DsCountFeature, CustomerSuccessful3DsCountFeature
com.getflex.featurestore.model.feature.CustomerSuccessfulBpsExcludingMostRecentCountFeature, CustomerSuccessfulBpsExcludingMostRecentCountFeature
com.getflex.featurestore.model.feature.CustomerSuccessfulChallenge3DsCountFeature, CustomerSuccessfulChallenge3DsCountFeature
com.getflex.featurestore.model.feature.CustomerTotalCardDeclinesTransactionNotAllowedFeature, CustomerTotalCardDeclinesTransactionNotAllowedFeature
com.getflex.featurestore.model.feature.CustomerTriggered3DsCountFeature, CustomerTriggered3DsCountFeature
com.getflex.featurestore.model.feature.CustomerUnderwritingScoresMaxFeature, CustomerUnderwritingScoresMaxFeature
com.getflex.featurestore.model.feature.CustomerVantageScoreFeature, CustomerVantageScoreFeature
com.getflex.featurestore.model.feature.CustomerZendeskTicketsCountFeature, CustomerZendeskTicketsCountFeature
com.getflex.featurestore.model.feature.CustomerZipToIpDistanceFeature, CustomerZipToIpDistanceFeature
com.getflex.featurestore.model.feature.DisputeReasonByIdFeature, DisputeReasonByIdFeature
com.getflex.featurestore.model.feature.DistinctPaymentMethodsFeature, DistinctPaymentMethodsFeature
com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFirstNameFeature, EditDistanceToStripePaymentFirstNameFeature
com.getflex.featurestore.model.feature.EditDistanceToStripePaymentFullNameFeature, EditDistanceToStripePaymentFullNameFeature
com.getflex.featurestore.model.feature.EditDistanceToStripePaymentLastNameFeature, EditDistanceToStripePaymentLastNameFeature
com.getflex.featurestore.model.feature.EstimatedBillAmountCentFromFirstAcceptedOfferFeature, EstimatedBillAmountCentFromFirstAcceptedOfferFeature
com.getflex.featurestore.model.feature.EstimatedBillAmountCentFromLatestOfferFeature, EstimatedBillAmountCentFromLatestOfferFeature
com.getflex.featurestore.model.feature.FlexScoreFiveFeature, FlexScoreFiveFeature
com.getflex.featurestore.model.feature.FlexScoreSevenFeature, FlexScoreSevenFeature
com.getflex.featurestore.model.feature.FlexScoreSixFeature, FlexScoreSixFeature
com.getflex.featurestore.model.feature.FraudTransactionModelScoreFeature, FraudTransactionModelScoreFeature
com.getflex.featurestore.model.feature.FraudTransactionModelV2ScoreFeature, FraudTransactionModelV2ScoreFeature
com.getflex.featurestore.model.feature.FraudTransactionModelV3ScoreFeature, FraudTransactionModelV3ScoreFeature
com.getflex.featurestore.model.feature.FundsInPast30DaysFeature, FundsInPast30DaysFeature
com.getflex.featurestore.model.feature.FundsOutPast30DaysFeature, FundsOutPast30DaysFeature
com.getflex.featurestore.model.feature.HasAcceptedDdaAgreementsFeature, HasAcceptedDdaAgreementsFeature
com.getflex.featurestore.model.feature.HasBypassDisputeRuleTagFeature, HasBypassDisputeRuleTagFeature
com.getflex.featurestore.model.feature.HasOffersCancelledByFraudFeature, HasOffersCancelledByFraudFeature
com.getflex.featurestore.model.feature.HasPendingWithdrawalLast24HoursFeature, HasPendingWithdrawalLast24HoursFeature
com.getflex.featurestore.model.feature.HasSentilinkCarrierIndicatorFeature, HasSentilinkCarrierIndicatorFeature
com.getflex.featurestore.model.feature.HasStripeConnectAccountFeature, HasStripeConnectAccountFeature
com.getflex.featurestore.model.feature.HasWalletCxEnabledTagFeature, HasWalletCxEnabledTagFeature
com.getflex.featurestore.model.feature.HubUserForPmcFeature, HubUserForPmcFeature
com.getflex.featurestore.model.feature.HubUserKycFeature, HubUserKycFeature
com.getflex.featurestore.model.feature.HubUsersSharingKycSsnFeature, HubUsersSharingKycSsnFeature
com.getflex.featurestore.model.feature.IpToGeoLocationFeature, IpToGeoLocationFeature
com.getflex.featurestore.model.feature.IsAccountStatusFraudCancelledFeature, IsAccountStatusFraudCancelledFeature
com.getflex.featurestore.model.feature.IsBatchMemoDenylistedFeature, IsBatchMemoDenylistedFeature
com.getflex.featurestore.model.feature.IsBpWindowFeature, IsBpWindowFeature
com.getflex.featurestore.model.feature.IsCapitalOneCreditCardFeature, IsCapitalOneCreditCardFeature
com.getflex.featurestore.model.feature.IsCardIssuerBlockedFeature, IsCardIssuerBlockedFeature
com.getflex.featurestore.model.feature.IsCustomerFlexAnywhereByCustomerIdFeature, IsCustomerFlexAnywhereByCustomerIdFeature
com.getflex.featurestore.model.feature.IsCustomerFlexAnywhereFeature, IsCustomerFlexAnywhereFeature
com.getflex.featurestore.model.feature.IsCustomerSlcFeature, IsCustomerSlcFeature
com.getflex.featurestore.model.feature.IsDeviceIdBlockedFeature, IsDeviceIdBlockedFeature
com.getflex.featurestore.model.feature.IsInNetworkFeature, IsInNetworkFeature
com.getflex.featurestore.model.feature.IsOfferValidForFulfillmentFeature, IsOfferValidForFulfillmentFeature
com.getflex.featurestore.model.feature.IsOutOfNetworkFeature, IsOutOfNetworkFeature
com.getflex.featurestore.model.feature.IsPastDueRentFeature, IsPastDueRentFeature
com.getflex.featurestore.model.feature.IsSuspendedBeforeCancelledFeature, IsSuspendedBeforeCancelledFeature
com.getflex.featurestore.model.feature.IsTrustedDeviceIdFeature, IsTrustedDeviceIdFeature
com.getflex.featurestore.model.feature.IsTrustedEmailFeature, IsTrustedEmailFeature
com.getflex.featurestore.model.feature.IsTrustedIpAddressFeature, IsTrustedIpAddressFeature
com.getflex.featurestore.model.feature.IsTrustedPhoneNumberFeature, IsTrustedPhoneNumberFeature
com.getflex.featurestore.model.feature.KycFeature, KycFeature
com.getflex.featurestore.model.feature.LatestCardAddedAgeInDaysFeature, LatestCardAddedAgeInDaysFeature
com.getflex.featurestore.model.feature.LatestEstimatedIncomeCentFeature, LatestEstimatedIncomeCentFeature
com.getflex.featurestore.model.feature.NumberOfCustomersLinkedToRecentFundsOutFeature, NumberOfCustomersLinkedToRecentFundsOutFeature
com.getflex.featurestore.model.feature.NumberOfEmailLinkFeature, NumberOfEmailLinkFeature
com.getflex.featurestore.model.feature.NumberOfPhoneLinkFeature, NumberOfPhoneLinkFeature
com.getflex.featurestore.model.feature.NumberOfSsnLinkFeature, NumberOfSsnLinkFeature
com.getflex.featurestore.model.feature.OfferByBillerAccountFeature, OfferByBillerAccountFeature
com.getflex.featurestore.model.feature.OnboardingOfferSocureFeature, OnboardingOfferSocureFeature
com.getflex.featurestore.model.feature.PayBillerAlreadySettledByBillTransactionIdFeature, PayBillerAlreadySettledByBillTransactionIdFeature
com.getflex.featurestore.model.feature.PaymentMethodIsNotDebitCardFeature, PaymentMethodIsNotDebitCardFeature
com.getflex.featurestore.model.feature.ProductInfoFeature, ProductInfoFeature
com.getflex.featurestore.model.feature.PropertyInfoFeature, PropertyInfoFeature
com.getflex.featurestore.model.feature.ProxyRentAmountCentFeature, ProxyRentAmountCentFeature
com.getflex.featurestore.model.feature.RealtimeCardLinkageCountFeature, RealtimeCardLinkageCountFeature
com.getflex.featurestore.model.feature.RentCredibilityScoreFeature, RentCredibilityScoreFeature
com.getflex.featurestore.model.feature.RestingPeriodFeature, RestingPeriodFeature
com.getflex.featurestore.model.feature.RootOfferFeature, RootOfferFeature
com.getflex.featurestore.model.feature.SelfReportedAnnualGrossIncomeCentFeature, SelfReportedAnnualGrossIncomeCentFeature
com.getflex.featurestore.model.feature.SocureFeature, SocureFeature
com.getflex.featurestore.model.feature.SocureMobileNumberScoreFeature, SocureMobileNumberScoreFeature
com.getflex.featurestore.model.feature.StripeAddressLine1JarowSimilarityScoreFeature, StripeAddressLine1JarowSimilarityScoreFeature
com.getflex.featurestore.model.feature.StripeFirstNameJarowSimilarityScoreFeature, StripeFirstNameJarowSimilarityScoreFeature
com.getflex.featurestore.model.feature.StripeLastNameJarowSimilarityScoreFeature, StripeLastNameJarowSimilarityScoreFeature
com.getflex.featurestore.model.feature.StripeNameMismatchFeature, StripeNameMismatchFeature
com.getflex.featurestore.model.feature.StripeStateMatchFeature, StripeStateMatchFeature
com.getflex.featurestore.model.feature.StripeZipCodeMatchFeature, StripeZipCodeMatchFeature
com.getflex.featurestore.model.feature.StripeZipCodeMatchV2Feature, StripeZipCodeMatchV2Feature
com.getflex.featurestore.model.feature.TotalCardDeclinesCardNotSupportedFeature, TotalCardDeclinesCardNotSupportedFeature
com.getflex.featurestore.model.feature.TotalCardDeclinesFeature, TotalCardDeclinesFeature
com.getflex.featurestore.model.feature.TotalCardDeclinesInsufficientFundsFeature, TotalCardDeclinesInsufficientFundsFeature
com.getflex.featurestore.model.feature.TotalCardDeclinesInvalidAccountFeature, TotalCardDeclinesInvalidAccountFeature
com.getflex.featurestore.model.feature.TotalDisputesFeature, TotalDisputesFeature
com.getflex.featurestore.model.feature.TotalLostDisputesFeature, TotalLostDisputesFeature
com.getflex.featurestore.model.feature.TotalSuccessfulPaymentsFeature, TotalSuccessfulPaymentsFeature
com.getflex.featurestore.model.feature.ZipCheckFailFeature, ZipCheckFailFeature
com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInsufficientFundsLast6MonthsFeature, AvgCardDeclinedInsufficientFundsLast6MonthsFeature
com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedInvalidAccountLast6MonthsFeature, AvgCardDeclinedInvalidAccountLast6MonthsFeature
com.getflex.featurestore.model.feature.billpay.AvgCardDeclinedLast6MonthsFeature, AvgCardDeclinedLast6MonthsFeature
com.getflex.featurestore.model.feature.billpay.BpRateLast12MonthsFeature, BpRateLast12MonthsFeature
com.getflex.featurestore.model.feature.billpay.BpRateLast6MonthsFeature, BpRateLast6MonthsFeature
com.getflex.featurestore.model.feature.billpay.CprEads142At24sFeature, CprEads142At24sFeature
com.getflex.featurestore.model.feature.billpay.CprVantage40ScoreFeature, CprVantage40ScoreFeature
com.getflex.featurestore.model.feature.billpay.MaxRentFeature, MaxRentFeature
com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast12MonthsFeature, MonthsInactiveLast12MonthsFeature
com.getflex.featurestore.model.feature.billpay.MonthsInactiveLast6MonthsFeature, MonthsInactiveLast6MonthsFeature
com.getflex.featurestore.model.feature.billpay.MonthsSinceFirstSignupFeature, MonthsSinceFirstSignupFeature
com.getflex.featurestore.model.feature.billpay.MonthsSinceLastSignupFeature, MonthsSinceLastSignupFeature
com.getflex.featurestore.model.feature.billpay.PaySuccessRateLast6MonthsFeature, PaySuccessRateLast6MonthsFeature
com.getflex.featurestore.model.feature.dispute.DisputeAmountInCentsByIdFeature, DisputeAmountInCentsByIdFeature
com.getflex.featurestore.model.feature.docv.OfferHasOnboardingKycReferEventFeature, OfferHasOnboardingKycReferEventFeature
com.getflex.featurestore.model.feature.docv.OfferOnboardingSentilinkIdTheftScoreFeature, OfferOnboardingSentilinkIdTheftScoreFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedAbuseFeature, IsFraudTaggedAbuseFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedAccountTakeoverFeature, IsFraudTaggedAccountTakeoverFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedBlacklistedFeature, IsFraudTaggedBlacklistedFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedFraudInvestigationFeature, IsFraudTaggedFraudInvestigationFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedFraudulentDisputeFeature, IsFraudTaggedFraudulentDisputeFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedIdentityTheftFeature, IsFraudTaggedIdentityTheftFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedRepetitiveDisputeFeature, IsFraudTaggedRepetitiveDisputeFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedStolenFinancialsFeature, IsFraudTaggedStolenFinancialsFeature
com.getflex.featurestore.model.feature.fraud.IsFraudTaggedSubscriptionCanceledDisputeFeature, IsFraudTaggedSubscriptionCanceledDisputeFeature
com.getflex.featurestore.model.feature.income.BankIncomeVerificationFeature, BankIncomeVerificationFeature
com.getflex.featurestore.model.feature.income.BankIncomeVerificationRawFeature, BankIncomeVerificationRawFeature
com.getflex.featurestore.model.feature.income.IncomeVerificationUnderwritingFeature, IncomeVerificationUnderwritingFeature
com.getflex.featurestore.model.feature.income.PayStubIncomeVerificationFeature, PayStubIncomeVerificationFeature
com.getflex.featurestore.model.feature.income.PayStubIncomeVerificationRawFeature, PayStubIncomeVerificationRawFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeCancelledPayBillerCentsFeature, CustomerLifetimeCancelledPayBillerCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeCustomerCreditCentsFeature, CustomerLifetimeCustomerCreditCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeDisputeCentsFeature, CustomerLifetimeDisputeCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeDisputeCountFeature, CustomerLifetimeDisputeCountFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeDownpaymentCentsFeature, CustomerLifetimeDownpaymentCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeDownpaymentProcessingFeeCentsFeature, CustomerLifetimeDownpaymentProcessingFeeCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeFundsInCentsFeature, CustomerLifetimeFundsInCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeFundsOutCentsFeature, CustomerLifetimeFundsOutCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeRefundCentsFeature, CustomerLifetimeRefundCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeRepaymentCentsFeature, CustomerLifetimeRepaymentCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeRepaymentProcessingFeeCentsFeature, CustomerLifetimeRepaymentProcessingFeeCentsFeature
com.getflex.featurestore.model.feature.ledgerbalance.CustomerLifetimeSettledPayBillerCentsFeature, CustomerLifetimeSettledPayBillerCentsFeature
com.getflex.featurestore.model.feature.movein.MoveInCustomerFeature, MoveInCustomerFeature
com.getflex.featurestore.model.feature.parameterized.CardDeclinesCountFeature, CardDeclinesCountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.CardVelocityExceededCardDeclinesCountFeature, CardVelocityExceededCardDeclinesCountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.CurrentCardSameAsDownpaymentCardFeature, CurrentCardSameAsDownpaymentCardFeature
com.getflex.featurestore.model.feature.parameterized.CustomerHasTagFeature, CustomerHasTagFeature_FRAUD
com.getflex.featurestore.model.feature.parameterized.FundsOutFundsInDifferentCardCountFeature, FundsOutFundsInDifferentCardCountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.FundsOutFundsInSameCardCountFeature, FundsOutFundsInSameCardCountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature, MaxNumberOfCustomersLinkedToFundsInTimeWindowFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfAddCardAttemptsFeature, NumberOfAddCardAttemptsFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfAddCardSuccessFeature, NumberOfAddCardSuccessFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfDeclinedFundsInFeature, NumberOfDeclinedFundsInFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfDeviceIdChangesFeature, NumberOfDeviceIdChangesFeature
com.getflex.featurestore.model.feature.parameterized.NumberOfDeviceIdChangesFeature, NumberOfDeviceIdChangesFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfEmailChangesFeature, NumberOfEmailChangesFeature
com.getflex.featurestore.model.feature.parameterized.NumberOfEmailChangesFeature, NumberOfEmailChangesFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfFundsInAttemptsFeature, NumberOfFundsInAttemptsFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfFundsOutAttemptsFeature, NumberOfFundsOutAttemptsFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfHighAmountFundsInFeature, NumberOfHighAmountFundsInFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfIpChangesFeature, NumberOfIpChangesFeature
com.getflex.featurestore.model.feature.parameterized.NumberOfIpChangesFeature, NumberOfIpChangesFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfIpStateChangesFeature, NumberOfIpStateChangesFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfLowAmountFundsInFeature, NumberOfLowAmountFundsInFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfPhoneChangesFeature, NumberOfPhoneChangesFeature
com.getflex.featurestore.model.feature.parameterized.NumberOfPhoneChangesFeature, NumberOfPhoneChangesFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfSuccessfulFundsOutTimeRangeFeature, NumberOfSuccessfulFundsOutTimeRangeFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.NumberOfUniqueCardFingerprintFeature, NumberOfUniqueCardFingerprintFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.RentSplitDqFeature, RentSplitDqFeature_DQ5_2Months
com.getflex.featurestore.model.feature.parameterized.RiskyCardDeclinesCountFeature, RiskyCardDeclinesCountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.TotalFundsInAmountFeature, TotalFundsInAmountFeature_PT15M
com.getflex.featurestore.model.feature.parameterized.TotalFundsOutAmountFeature, TotalFundsOutAmountFeature_PT15M
com.getflex.featurestore.model.feature.proxyrent.CityNameBpMeanFeature, CityNameBpMeanFeature
com.getflex.featurestore.model.feature.proxyrent.CityNameRentMeanFeature, CityNameRentMeanFeature
com.getflex.featurestore.model.feature.proxyrent.PropertyBpMeanFeature, PropertyBpMeanFeature
com.getflex.featurestore.model.feature.proxyrent.PropertyRentMeanFeature, PropertyRentMeanFeature
com.getflex.featurestore.model.feature.proxyrent.ProxyRentModelPredictionFeature, ProxyRentModelPredictionFeature
com.getflex.featurestore.model.feature.proxyrent.StateNameBpMeanFeature, StateNameBpMeanFeature
com.getflex.featurestore.model.feature.proxyrent.StateNameRentMeanFeature, StateNameRentMeanFeature
com.getflex.featurestore.model.feature.proxyrent.StreetNameBpMeanFeature, StreetNameBpMeanFeature
com.getflex.featurestore.model.feature.proxyrent.StreetNameRentMeanFeature, StreetNameRentMeanFeature
com.getflex.featurestore.model.feature.proxyrent.ZipCodeBpMeanFeature, ZipCodeBpMeanFeature
com.getflex.featurestore.model.feature.proxyrent.ZipCodeRentMeanFeature, ZipCodeRentMeanFeature
com.getflex.featurestore.model.feature.rentsplit.RentSplitCustomerFeature, RentSplitCustomerFeature
com.getflex.featurestore.model.feature.similarity.AddressLevelBatchMemoNlpSimilarityScoreFeature, AddressLevelBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.AddressLevelBatchMemoSimilarityScoreFeature, AddressLevelBatchMemoSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature, BillerAccountPropertyNameBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerAccountPropertyNameBatchMemoSimilarityScoreFeature, BillerAccountPropertyNameBatchMemoSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerLevelBatchMemoNlpSimilarityScoreFeature, BillerLevelBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerLevelBatchMemoSimilarityScoreFeature, BillerLevelBatchMemoSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerPmcNameBatchMemoNlpSimilarityScoreFeature, BillerPmcNameBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.BillerPropertyNameBatchMemoNlpSimilarityScoreFeature, BillerPropertyNameBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.CustomerLevelBatchMemoNlpSimilarityScoreFeature, CustomerLevelBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.CustomerLevelBatchMemoSimilarityScoreFeature, CustomerLevelBatchMemoSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.PmcUrlBatchMemoNlpSimilarityScoreFeature, PmcUrlBatchMemoNlpSimilarityScoreFeature
com.getflex.featurestore.model.feature.similarity.PmcUrlBatchMemoSimilarityScoreFeature, PmcUrlBatchMemoSimilarityScoreFeature