{referenceId=89ad8a2b-df3e-4ce4-bc33-e90313a4029a, nameAddressCorrelation={reasonCodes=[I709, I719, I708, I710, I718], score=0.98}, nameEmailCorrelation={reasonCodes=[I554, I557, I556, I559, I558], score=0.98}, namePhoneCorrelation={reasonCodes=[I621, I622, I624, I618, I623], score=0.99}, fraud={reasonCodes=[I610, R662, I626, I711, I632, I568, I212, I609, I627, I553, I611, I614, I636, I704, I556, I630, I708, R655, I569, R208, I618, I555, I566, I570, I707, I602], scores=[{name=sigma, version=4.0, score=0.032}]}, kycPlus={socureId=b43860b0-a7cb-7a7a-08ce-d21ca0f51e94, reasonCodes=[I919], fieldValidations={firstName=0.99, surName=0.99, streetAddress=0.99, city=0.99, state=0.99, zip=0.99, mobileNumber=0.99, dob=0.99, ssn=0.99, email=0.99}, sourceAttribution=[Credit, Alternative Credit], bestMatchedEntity={dob=AgV4Js/CNA76SF1qGCGhUSdGnEgt8pJ6/P/ozU8TQgevnwYAXwABABVhd3MtY3J5cHRvLXB1YmxpYy1rZXkAREFvYWxLTjNVZXlyV0J2eGhNT2RISnlqMzRlMzJHRXVOUVdmandWMXdWa3NndVV1RWg1VWt1U2pIMythVXFnQ3h3UT09AAEAB2F3cy1rbXMAS2Fybjphd3M6a21zOnVzLWVhc3QtMToxMDMxODE0MzYzODU6a2V5LzBlZTFlNmNmLWI4MWItNDU2OS04NjhhLTllMzNhOGE4MDUzYgC4AQIBAHgIJvTETbM4CLLXdrEcHIdDUuo+PJpmNQsRGn6bEq+D4gGVIWkowyfrOSHBTpBiy+UwAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMY4+7mjGHpGr2ZIu8AgEQgDtl+lfnd4a9C2yem3CtxQJf3ulgwnwYrzS4+w0lgnS63toQBVqIcu581TXkYC5NQ4o78JON0vXNMJy5WwIAABAAnEcgqfYxUKY35dC7jZ2AxOXCyps302UtqxoIgYqY0flqcknpM7T8MbrO8d5PmMKo/////wAAAAEAAAAAAAAAAAAAAAEAAAAK1j3/7mtdu/hiBD6ZcujQp4dcIADGUyGnVyIAZzBlAjEAoxOkPFmzytE09cMLOGEiga3Yv3B3YNquIHypvgAKLmy17VrcVjFDtm5EmYIxOIdEAjBVvd3ATkXRwPboJTp2iVXTfHuyDbKQVC8i1zMIAtwW2puGF5NO/pJx1qIqMJUvQ+Q=}}, addressRisk={reasonCodes=[I610, I711, I704, I708, I707], scores=[{name=RiskAddressUS.V7.1_Uniform, version=7.1, score=0.685}]}, emailRisk={reasonCodes=[I576, I568, I553, I556, I569, I555, I566, I570], scores=[{name=RiskEmailUS.V10.1__Uniform.V1, version=10.1, score=0.628}]}, phoneRisk={reasonCodes=[I610, R662, I626, I632, I609, I611, I614, I636, I630, R655, I618, I602], scores=[{name=RiskPhoneUS.V6__Norm.V1, version=6.0, score=0.446}]}, decision={value=accept, modelName=Flex Finance, modelVersion=1.0, details={synthetic={decision=accept}, kycplus={decision=accept}, watchlistplus={decision=accept}, firstpartyfraud={decision=accept}, namephonecorrelation={decision=accept}, fraud={decision=accept}, addressrisk={decision=accept}, alertlist={decision=accept}, phonerisk={decision=accept}, emailrisk={decision=accept}, nameemailcorrelation={decision=accept}, nameaddresscorrelation={decision=accept}}}, alertList={reasonCodes=[], matches=[]}, globalWatchlist={reasonCodes=[I196], matches={}}, synthetic={reasonCodes=[I610, R662, I207, I621, I622, I554, I568, I212, I557, I709, I556, I211, I204, I559, I719, I708, I624, I569, R208, I710, I618, I623, I570, I718, I558], scores=[{name=synthetic, version=4.0, score=0.271}]}, customerProfile={customerUserId=4227257}, firstPartyFraud={reasonCodes=[I1001], signals={}}}