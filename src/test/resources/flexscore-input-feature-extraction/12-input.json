{"status_code": 201, "error": null, "timestamp": 1740010872999, "evaluation_token": "L-TJJSdenO9ii3jQR87Yeq", "entity_token": "P-jtG4NJd8TaLCSEMFzNaY", "parent_entity_token": null, "application_token": "5hiyzkaF3x0B9XPxKY6l4WI3i6diVaHt", "application_version_id": 20, "champion_challenger_id": null, "summary": {"result": "success", "score": 0.71, "tags": [], "outcome_reasons": [], "outcome": null, "services": {"TransUnion Credit": "executed", "Sentilink": "executed"}, "custom_fields": {"financial_partner_id": 2}, "alloy_fraud_score": null}, "supplied": {"meta": {"customer_id": 4470239}, "name_first": "<PERSON>", "name_last": "Alba", "address_line_1": "100 Integra Shores Dr", "address_line_2": "04-304", "address_city": "Daytona Beach", "address_state": "FL", "address_postal_code": "32117", "birth_date": "1997-07-10", "document_ssn": "593616307", "email_address": "<EMAIL>", "phone_number": "+13056991051", "financial_partner_id": 2}, "formatted": {"name_first": "<PERSON>", "name_last": "Alba", "address_line_1": "100 Integra Shores Dr", "address_line_2": "04-304", "address_city": "Daytona Beach", "address_state": "FL", "address_postal_code": "32117", "birth_date": "1997-07-10", "document_ssn": "593616307", "email_address": "<EMAIL>", "phone_number": "+13056991051", "financial_partner_id": 2, "phone_country_code": "US", "age": "27"}, "meta": {"customer_id": 4470239}, "matching": {"address": {"score": 0.85, "matched": [], "unmatched": ["TransUnion Credit"]}, "name": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}, "dob": {"score": 0, "matched": [], "unmatched": ["TransUnion Credit"]}, "ssn": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}}, "diligence": {"watchlists": null, "fraud": {"average_score": 0.07, "flags": [], "sources": {"Sentilink": {"normalized_score": 0.07, "raw_score": 68, "attribute": "Abuse score"}}, "score": 0.07}, "financial": {"credit": [{"source": "TransUnion Credit", "models": {"vantagescore30": {"name": "VantageScore 3.0", "codes": ["vantagescore30-04", "vantagescore30-12", "vantagescore30-07", "vantagescore30-63"], "score": 573, "model_codes": [{"code": "vantagescore30-04", "description": "The balances on your accounts are too high compared to loan amounts"}, {"code": "vantagescore30-07", "description": "You have too many delinquent or derogatory accounts"}, {"code": "vantagescore30-12", "description": "The date that you opened your oldest account is too recent"}, {"code": "vantagescore30-63", "description": "Lack of sufficient relevant real estate account information"}], "bureau": "TransUnion Credit"}, "vantagescore40": {"name": "VantageScore 4.0", "codes": ["vantagescore40-68", "vantagescore40-04", "vantagescore40-10", "vantagescore40-29"], "score": 564, "model_codes": [{"code": "vantagescore40-04", "description": "Balances on accounts too high compared to credit limits and loan amounts"}, {"code": "vantagescore40-10", "description": "Too few accounts paid as agreed"}, {"code": "vantagescore40-29", "description": "Balances on bankcards are too high compared with credit limits"}, {"code": "vantagescore40-68", "description": "Lack of real estate secured loan information"}], "bureau": "TransUnion Credit"}}}]}, "identity_questions": null, "device": null}, "related_data": {}, "raw_responses": {"Sentilink": [{"transaction_id": "01JMGBNP-1BH9-8E9MD02Q", "application_id": "L-TJJSdenO9ii3jQR87Yeq", "scores": [{"name": "sentilink_abuse_score", "version": "1.7.1", "score": 68, "reason_codes": [{"code": "R014", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R011", "rank": 2, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}, {"code": "R010", "rank": 3, "direction": "less_fraudy", "explanation": "The depth of the consumer's history with this information"}]}, {"name": "sentilink_first_party_synthetic_score", "version": "1.7.1", "score": 68, "reason_codes": [{"code": "R014", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R011", "rank": 2, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}, {"code": "R004", "rank": 3, "direction": "more_fraudy", "explanation": "Whether the supplied SSN aligns with the consumer's DOB"}]}, {"name": "sentilink_third_party_synthetic_score", "version": "1.7.1", "score": 28, "reason_codes": [{"code": "R022", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the email domain or structure of the handle are suspicious"}, {"code": "R011", "rank": 2, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}, {"code": "R010", "rank": 3, "direction": "less_fraudy", "explanation": "The depth of the consumer's history with this information"}]}, {"name": "sentilink_id_theft_score", "version": "1.6.3", "score": 35, "reason_codes": [{"code": "R022", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the email domain or structure of the handle are suspicious"}, {"code": "R029", "rank": 2, "direction": "less_fraudy", "explanation": "Whether the applicant appears to be the best owner of the phone"}, {"code": "R034", "rank": 3, "direction": "less_fraudy", "explanation": "Length of history of the email"}]}], "customer_id": "01EFND5G6EZXKKT7PVY904YPBY", "environment": "PROD", "notes": "", "timestamp": "2025-02-20T00:21:11.005997755Z", "latency_ms": 174}], "TransUnion Credit": [{"creditBureau": {"$": {"xmlns": "http://www.transunion.com/namespace", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.transunion.com/namespace"}, "document": "response", "version": "2.26", "transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "********", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2025-02-19T18:21:10.804-06:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "false", "market": "16", "submarket": "NF", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "2014-10-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "indicative": {"name": [{"$": {"source": "file"}, "person": {"first": "STEPHANIE", "last": "ALBA"}}, {"$": {"source": "file"}, "qualifier": "alsoKnownAs", "person": {"unparsed": "ALBA,ESTEPHANIE"}}], "address": [{"$": {"source": "file"}, "status": "current", "qualifier": "personal", "street": {"number": "308", "name": "SUTTON", "type": "CI", "unit": {"number": "79"}}, "location": {"city": "DAYTONA BEACH", "state": "FL", "zipCode": "32114"}, "dateReported": {"_": "2022-02-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "1104", "name": "BELCOURT", "type": "PY"}, "location": {"city": "ROSWELL", "state": "GA", "zipCode": "30076"}, "dateReported": {"_": "2020-08-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "3624", "name": "156TH", "preDirectional": "SW", "type": "CT"}, "location": {"city": "MIAMI", "state": "FL", "zipCode": "33185"}}], "socialSecurity": {"$": {"source": "file"}, "number": "593616307"}, "dateOfBirth": {"_": "1996-07-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false", "source": "file"}}, "employment": [{"$": {"source": "file"}, "employer": {"unparsed": "THE PIER HOUSE"}, "dateOnFileSince": {"_": "2024-08-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2024-08-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "employer": {"unparsed": "STUDENT"}, "occupation": "STUDENT", "dateHired": {"_": "2015-01-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateOnFileSince": {"_": "2017-01-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2017-01-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}]}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "B", "memberCode": "0235064B", "name": {"unparsed": "SYNCB/ONDC"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2017-05-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2019-05-29", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "09", "remark": {"code": "PRL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2019-11-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "C", "memberCode": "01NZ8326", "name": {"unparsed": "CCB/FE21CC"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2017-07-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2022-05-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "9P", "remark": {"code": "SET", "type": "affiliate"}, "account": {"type": "CH"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2022-05-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "********", "name": {"unparsed": "DISCOVERBANK"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2016-07-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-03-20", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2022-04-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "9P", "remark": {"code": "SET", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2022-04-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2023-11-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2025-01-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************11"}, "historicalCounters": {"monthsReviewedCount": "14", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2025-01-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "E", "memberCode": "039ED388", "name": {"unparsed": "EDFINANCIAL"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2014-10-23", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "120", "scheduledMonthlyPayment": "*********"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11XX11X11X11X************************11111111111"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2023-08-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "F", "memberCode": "01W2K001", "name": {"unparsed": "CAP ONE AUTO"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2024-09-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "072", "scheduledMonthlyPayment": "*********"}, "account": {"type": "AU"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "1111"}, "historicalCounters": {"monthsReviewedCount": "04", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2025-01-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "E", "memberCode": "039ED388", "name": {"unparsed": "EDFINANCIAL"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2014-10-23", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "120", "scheduledMonthlyPayment": "*********"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11XX11X11X11X************************11111111111"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2023-08-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2024-09-03", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "111"}, "historicalCounters": {"monthsReviewedCount": "03", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2025-01-20", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}], "collection": [{"subscriber": {"industryCode": "Y", "memberCode": "********", "name": {"unparsed": "I C SYSTEM"}}, "portfolioType": "open", "ECOADesignator": "individual", "account": {"type": "AG"}, "dateOpened": {"_": "2020-03-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "BANFIELD PET HOSPITAL"}, "creditorClassification": "retail", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "mostRecentPayment": {"date": {"_": "2021-06-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "remark": {"code": "CLA", "type": "affiliate"}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "Y", "memberCode": "021T9002", "name": {"unparsed": "LVNV FUNDING"}}, "portfolioType": "open", "ECOADesignator": "individual", "account": {"type": "FC"}, "dateOpened": {"_": "2023-06-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-07", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "CITIBANK N A"}, "creditorClassification": "financial", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "remark": {"code": "CLA", "type": "affiliate"}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "Y", "memberCode": "01ZW1002", "name": {"unparsed": "PDQ SERVICES"}}, "portfolioType": "open", "ECOADesignator": "jointContractLiability", "account": {"type": "AG"}, "dateOpened": {"_": "2021-12-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "2200 BIG CREEK APTS"}, "creditorClassification": "rentalOrLeasing", "balance": "*********"}, "pastDue": "*********", "accountRating": "9P", "mostRecentPayment": {"date": {"_": "2024-07-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "remark": {"code": "DRC", "type": "affiliate"}, "updateMethod": "automated"}], "inquiry": [{"ECOADesignator": "individual", "subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "33PC", "name": {"unparsed": "CAPITAL ONE"}}, "date": {"_": "2024-09-03", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "Q", "memberCode": "********", "inquirySubscriberPrefixCode": "16TA", "name": {"unparsed": "VYSTARCU"}}, "date": {"_": "2024-09-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "Q", "memberCode": "********", "inquirySubscriberPrefixCode": "16TA", "name": {"unparsed": "VYSTARCU"}}, "date": {"_": "2024-09-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "F", "memberCode": "03025836", "inquirySubscriberPrefixCode": "11DT", "name": {"unparsed": "TD AUTO FIN"}}, "date": {"_": "2024-09-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "F", "memberCode": "01246539", "inquirySubscriberPrefixCode": "33PC", "name": {"unparsed": "COAF"}}, "date": {"_": "2024-09-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "F", "memberCode": "06139738", "inquirySubscriberPrefixCode": "06CH", "name": {"unparsed": "NATIONWDSE"}}, "date": {"_": "2024-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "33PC", "name": {"unparsed": "CAPITAL ONE"}}, "date": {"_": "2023-11-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}]}}, "addOnProduct": [{"code": "06400", "status": "delivered", "idMismatchAlert": {"type": "address", "condition": "mismatch", "inquiriesInLast60Count": "00", "addressStatus": "current"}}, {"code": "07030", "status": "delivered", "phoneAppend": {"$": {"searchStatus": "notFound"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "0000000036"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "0000000013"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02F", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02H", "value": "*********2"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000032170"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000038"}, {"algorithmID": "00WR3", "id": "AU20S", "value": "*********5"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "*********5"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "0000000101"}, {"algorithmID": "00WR3", "id": "AU36S", "value": "0000000999"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "0000000072"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "0000000096"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "0000002003"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "BR34S", "value": "0000000096"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000006"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "0000000101"}, {"algorithmID": "00WR3", "id": "FI101S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G202B", "value": "0000000677"}, {"algorithmID": "00WR3", "id": "G213A", "value": "0*********"}, {"algorithmID": "00WR3", "id": "G215B", "value": "*********2"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********2"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "0000000124"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "RE03S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "RE24S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "RE28S", "value": "0000002800"}, {"algorithmID": "00WR3", "id": "S061S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "S071A", "value": "*********1"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "0000009033"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000005"}, {"algorithmID": "00WR3", "id": "S207S", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G242F", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G243F", "value": "*********3"}, {"algorithmID": "00WR3", "id": "US21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US36S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G404S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "G208B", "value": "0000000100"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000385"}}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "0000000091"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "*********0"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "0000001.00"}, {"algorithmID": "00H91", "id": "PAYMNT10", "value": "*********4"}]}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000001"}}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "0000000999"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "*********4"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "0000000017"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "*********6"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "0000000013"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "*********4"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "0000000071"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "0000000011"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0000021137"}, {"algorithmID": "00H86", "id": "AGG208", "value": "0000000300"}, {"algorithmID": "00H86", "id": "AGG218", "value": "0000009033"}, {"algorithmID": "00H86", "id": "AGG223", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG407", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG504", "value": "0000005748"}, {"algorithmID": "00H86", "id": "AGG518", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG602", "value": "0000002800"}, {"algorithmID": "00H86", "id": "AGG801", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG803", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG807", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********2"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000082.85"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000088.69"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "MIXBHVR"}}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "*********6"}, {"algorithmID": "00WG9", "id": "REV255", "value": "0000000104"}, {"algorithmID": "00WG9", "id": "STD255", "value": "-000000002"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "0000001.20"}, {"algorithmID": "00WG8", "id": "REV222", "value": "0000001.20"}, {"algorithmID": "00WG8", "id": "REV225", "value": "0000005.07"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "0000001.00"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "REV322", "value": "*********0"}, {"algorithmID": "00WH1", "id": "BKC320", "value": "*********5"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "*********0"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "0000000149"}]}}, {"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+573", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "04"}, {"rank": "2", "code": "12"}, {"rank": "3", "code": "07"}, {"rank": "4", "code": "63"}]}, "scoreCard": "06"}}}, {"code": "001NN", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+564", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "false", "factors": {"factor": [{"rank": "1", "code": "68"}, {"rank": "2", "code": "04"}, {"rank": "3", "code": "10"}, {"rank": "4", "code": "29"}]}, "scoreCard": "04"}}}, {"code": "00H87", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H87", "id": "TRV07", "value": "*********2"}, {"algorithmID": "00H87", "id": "TRV08", "value": "*********4"}]}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00Z23", "id": "LINKF001", "value": "CLEAR"}}}, {"code": "07051", "status": "delivered", "militaryLendingActSearch": {"$": {"searchStatus": "noMatch"}}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "0000000169"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********2"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "*********2"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "*********2"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "9999.99999"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}, "printImage": "                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           16 NF     10/14      02/19/25    18:21CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> ALBA, STEPHANIE                                    ***********   7/96        <ALSO KNOWN AS>                                                              ALBA,ESTEPHANIE                                                              <CURRENT ADDRESS>                                               <DATE RPTD>  308 SUTTON CI., #79. DAYTONA BEACH FL. 32114                     2/22        <FORMER ADDRESS>                                                             1104 BELCOURT PY., ROSWELL GA. 30076                             8/20        3624 SW. 156TH CT., MIAMI FL. 33185                                                                                   <POSITION>                          <CURRENT EMPLOYER AND ADDRESS>                     <VERF> <RPTD><HIRE>       THE PIER HOUSE                                                                                                                   8/24A  8/24              <FORMER EMPLOYER AND ADDRESS>                                                STUDENT                                  STUDENT                                                                                 1/17A  1/17  1/15        ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVALIDATE ID MISMATCH ALERT: CURRENT INPUT ADDRESS DOES NOT MATCH FILE                                    ADDRESS(ES)***                             ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +573  : 04, 12, 07, 63 SCORECARD :06*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +564  : 68, 04, 10, 29 SCORECARD :04***              ---------------------------------------------------------------------------- C O L L E C T I O N S                                                        SUBNAME       SUBCODE   ECOA OPENED  CLOSED $PLACED  CREDITOR           MOP  ACCOUNT#                     VERIFIED       BALANCE  REMARKS                 I C SYSTEM    Y 2834001 I     3/20          $518     BANFIELD PET HOSPI O9B                                2/25A         $318     PLACED FOR COLLECTIO                                                                                 LVNV FUNDING  Y 21T9002 I     6/23          $1911    CITIBANK N A       O9B                                1/25A         $1911    PLACED FOR COLLECTIO                                                                                 PDQ SERVICES  Y 1ZW1002 C    12/21          $3921    2200 BIG CREEK APT O9P                                2/25A         $0       DISP INV CMP-CNS DSG    ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   SYNCB/ONDC   B 235064B  5/17   $3771                                    R09                          1/25A  $4000    $3323                                I    CREDIT CARD        5/19F  $3323    UNPAID BLNC CHRGD OFF 0                                                                                           CCB/FE21CC   C 1NZ8326  7/17   $2050                                    R9P                          5/22A  $1400    $0                                   I    CHARGE ACCOUNT     5/22F  $0       SETTLED < FULL BLNC   0                                                                                           DISCOVERBANK B 9616003  7/16   $1326                                    R9P                          5/22A  $1000    $0                                   I    CREDIT CARD        3/22F  $0       SETTLED < FULL BLNC   0                                                                                           CAPITAL ONE  B 1DTV001 11/23   $829     MIN25              ************ R01                          2/25A  $800     $0                 11                I    CREDIT CARD               $762                           14   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388 10/14   $3500    120M42             11XX11X11X11 I01                          1/25A           $0                 X11111111111      I    STUDENT LOAN              $3954                          48   0/ 0/ 0                                                                                CAP ONE AUTO F 1W2K001  9/24   $20.3K   072M477            1111         I01                          1/25A           $0                                   I    AUTOMOBILE                $20.5K                         4    0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388 10/14   $5533    120M74             11XX11X11X11 I01                          1/25A           $0                 X11111111111      I    STUDENT LOAN              $6872                          48   0/ 0/ 0                                                                                CAPITAL ONE  B 1DTV001  9/24   $1928    MIN66              111          R01                          1/25A  $2000    $0                                   I    CREDIT CARD               $1924                          3    0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        9/03/24 BPC3575459(NTL) CAPITAL ONE                                          9/02/24 QTA2762142(FLA) VYSTARCU                                             9/02/24 QTA3129826(FLA) VYSTARCU                                             9/02/24 FDT3025836(MCH) TD AUTO FIN                                          9/02/24 FPC1246539(NTL) COAF                                                 8/31/24 FCH6139738(CHI) NATIONWDSE                                          11/16/23 BPC3575459(NTL) CAPITAL ONE                                         ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ", "embeddedData": {"_": "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", "$": {"type": "pdf", "contentTypeEncoding": "base64"}}}}, "xml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><creditBureau xmlns=\"http://www.transunion.com/namespace\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.transunion.com/namespace\"><document>response</document><version>2.26</version><transactionControl><subscriber><industryCode>F</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>1702</inquirySubscriberPrefixCode></subscriber><options><country>us</country><language>en</language><productVersion>standard</productVersion></options><tracking><transactionTimeStamp>2025-02-19T18:21:10.804-06:00</transactionTimeStamp></tracking></transactionControl><product><code>07000</code><subject><number>1</number><subjectRecord><fileNumber>01</fileNumber><fileSummary><fileHitIndicator>regularHit</fileHitIndicator><ssnMatchIndicator>exact</ssnMatchIndicator><consumerStatementIndicator>false</consumerStatementIndicator><market>16</market><submarket>NF</submarket><creditDataStatus><suppressed>false</suppressed><doNotPromote><indicator>false</indicator></doNotPromote><freeze><indicator>false</indicator></freeze><minor>false</minor><disputed>false</disputed></creditDataStatus><inFileSinceDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2014-10-28</inFileSinceDate></fileSummary><indicative><name source=\"file\"><person><first>STEPHANIE</first><last>ALBA</last></person></name><name source=\"file\"><qualifier>alsoKnownAs</qualifier><person><unparsed>ALBA,ESTEPHANIE</unparsed></person></name><address source=\"file\"><status>current</status><qualifier>personal</qualifier><street><number>308</number><name>SUTTON</name><type>CI</type><unit><number>79</number></unit></street><location><city>DAYTONA BEACH</city><state>FL</state><zipCode>32114</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-02-26</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>1104</number><name>BELCOURT</name><type>PY</type></street><location><city>ROSWELL</city><state>GA</state><zipCode>30076</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-08-01</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>3624</number><name>156TH</name><preDirectional>SW</preDirectional><type>CT</type></street><location><city>MIAMI</city><state>FL</state><zipCode>33185</zipCode></location></address><socialSecurity source=\"file\"><number>593616307</number></socialSecurity><dateOfBirth estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\" source=\"file\">1996-07-10</dateOfBirth><employment source=\"file\"><employer><unparsed>THE PIER HOUSE</unparsed></employer><dateOnFileSince estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-28</dateOnFileSince><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-28</dateEffective></employment><employment source=\"file\"><employer><unparsed>STUDENT</unparsed></employer><occupation>STUDENT</occupation><dateHired estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2015-01-01</dateHired><dateOnFileSince estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2017-01-08</dateOnFileSince><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2017-01-08</dateEffective></employment></indicative><custom><credit><trade><subscriber><industryCode>B</industryCode><memberCode>0235064B</memberCode><name><unparsed>SYNCB/ONDC</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2017-05-17</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-26</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2019-05-29</dateClosed><closedIndicator>normal</closedIndicator><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>09</accountRating><remark><code>PRL</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2019-11-01</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>C</industryCode><memberCode>01NZ8326</memberCode><name><unparsed>CCB/FE21CC</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2017-07-13</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-05-28</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-05-01</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-05-21</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>9P</accountRating><remark><code>SET</code><type>affiliate</type></remark><account><type>CH</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-05-21</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><name><unparsed>DISCOVERBANK</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2016-07-28</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-05-15</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-03-20</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-04-26</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>9P</accountRating><remark><code>SET</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-04-26</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>01DTV001</memberCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-11-16</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-05</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><terms><paymentScheduleMonthCount>MIN</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-05</startDate><text>************11</text></paymentPattern><historicalCounters><monthsReviewedCount>14</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-14</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>E</industryCode><memberCode>039ED388</memberCode><name><unparsed>EDFINANCIAL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2014-10-23</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>120</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>11XX11X11X11X************************11111111111</text></paymentPattern><historicalCounters><monthsReviewedCount>48</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-08-01</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>F</industryCode><memberCode>01W2K001</memberCode><name><unparsed>CAP ONE AUTO</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-02</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>072</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>AU</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>1111</text></paymentPattern><historicalCounters><monthsReviewedCount>04</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-16</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>E</industryCode><memberCode>039ED388</memberCode><name><unparsed>EDFINANCIAL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2014-10-23</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>120</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>11XX11X11X11X************************11111111111</text></paymentPattern><historicalCounters><monthsReviewedCount>48</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-08-01</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>01DTV001</memberCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-03</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-25</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><terms><paymentScheduleMonthCount>MIN</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-25</startDate><text>111</text></paymentPattern><historicalCounters><monthsReviewedCount>03</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-20</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><collection><subscriber><industryCode>Y</industryCode><memberCode>********</memberCode><name><unparsed>I C SYSTEM</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>individual</ECOADesignator><account><type>AG</type></account><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-03-08</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-09</dateEffective><currentBalance>*********</currentBalance><original><creditGrantor><unparsed>BANFIELD PET HOSPITAL</unparsed></creditGrantor><creditorClassification>retail</creditorClassification><balance>*********</balance></original><pastDue>*********</pastDue><accountRating>9B</accountRating><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-06-01</date></mostRecentPayment><remark><code>CLA</code><type>affiliate</type></remark><updateMethod>automated</updateMethod></collection><collection><subscriber><industryCode>Y</industryCode><memberCode>021T9002</memberCode><name><unparsed>LVNV FUNDING</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>individual</ECOADesignator><account><type>FC</type></account><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-06-28</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-07</dateEffective><currentBalance>*********</currentBalance><original><creditGrantor><unparsed>CITIBANK N A</unparsed></creditGrantor><creditorClassification>financial</creditorClassification><balance>*********</balance></original><pastDue>*********</pastDue><accountRating>9B</accountRating><remark><code>CLA</code><type>affiliate</type></remark><updateMethod>automated</updateMethod></collection><collection><subscriber><industryCode>Y</industryCode><memberCode>01ZW1002</memberCode><name><unparsed>PDQ SERVICES</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>jointContractLiability</ECOADesignator><account><type>AG</type></account><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-12-17</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-06</dateEffective><currentBalance>*********</currentBalance><original><creditGrantor><unparsed>2200 BIG CREEK APTS</unparsed></creditGrantor><creditorClassification>rentalOrLeasing</creditorClassification><balance>*********</balance></original><pastDue>*********</pastDue><accountRating>9P</accountRating><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-07-10</date></mostRecentPayment><remark><code>DRC</code><type>affiliate</type></remark><updateMethod>automated</updateMethod></collection><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>33PC</inquirySubscriberPrefixCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-03</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>Q</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>16TA</inquirySubscriberPrefixCode><name><unparsed>VYSTARCU</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-02</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>Q</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>16TA</inquirySubscriberPrefixCode><name><unparsed>VYSTARCU</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-02</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>F</industryCode><memberCode>03025836</memberCode><inquirySubscriberPrefixCode>11DT</inquirySubscriberPrefixCode><name><unparsed>TD AUTO FIN</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-02</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>F</industryCode><memberCode>01246539</memberCode><inquirySubscriberPrefixCode>33PC</inquirySubscriberPrefixCode><name><unparsed>COAF</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-09-02</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>F</industryCode><memberCode>06139738</memberCode><inquirySubscriberPrefixCode>06CH</inquirySubscriberPrefixCode><name><unparsed>NATIONWDSE</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-31</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>33PC</inquirySubscriberPrefixCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-11-16</date></inquiry></credit></custom><addOnProduct><code>06400</code><status>delivered</status><idMismatchAlert><type>address</type><condition>mismatch</condition><inquiriesInLast60Count>00</inquiriesInLast60Count><addressStatus>current</addressStatus></idMismatchAlert></addOnProduct><addOnProduct><code>07030</code><status>delivered</status><phoneAppend searchStatus=\"notFound\"/></addOnProduct><addOnProduct><code>00WBN</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBN</algorithmID><id>AD06C</id><value>0000000036</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD09A</id><value>*********0</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14A</id><value>*********0</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14H</id><value>0000000013</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WBO</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBO</algorithmID><id>P02E</id><value>*********1</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02F</id><value>*********1</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02H</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WR3</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WR3</algorithmID><id>AT24S</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT28B</id><value>0000032170</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT32S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT36S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT104S</id><value>0000000038</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU20S</id><value>*********5</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU21S</id><value>*********5</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU34S</id><value>0000000101</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU36S</id><value>0000000999</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU51A</id><value>0000000072</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU101S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC34S</id><value>0000000096</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC103S</id><value>0000002003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC110S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI09S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI34S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR02S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR34S</id><value>0000000096</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI25S</id><value>-000000006</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI34S</id><value>0000000101</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI101S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FR101S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G202B</id><value>0000000677</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G213A</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G215B</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G216S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G224C</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G228S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>IN20S</id><value>0000000124</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>OF27S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE03S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE06S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE24S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE28S</id><value>0000002800</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S061S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S071A</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST01S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST24S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST27S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST28S</id><value>0000009033</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST99S</id><value>-000000005</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S207S</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G242F</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G243F</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US36S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT20S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G404S</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G411S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G208B</id><value>0000000100</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H88</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H88</algorithmID><id>BALMAG01</id><value>0000000385</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V26</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V26</algorithmID><id>CV23</id><value>0000000091</value></characteristic><characteristic><algorithmID>00V26</algorithmID><id>CV28</id><value>*********0</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H91</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT02</id><value>*********0</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT07</id><value>0000001.00</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT10</id><value>*********4</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG4</algorithmID><id>PLATTR04</id><value>-000000001</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V92</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V92</algorithmID><id>RVDEX02</id><value>0000000999</value></characteristic><characteristic><algorithmID>00V92</algorithmID><id>RVDEXQ2</id><value>*********4</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V53</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V53</algorithmID><id>AGGS106</id><value>0000000017</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>AGGS109</id><value>*********6</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ1</id><value>0000000013</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ2</id><value>*********4</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ3</id><value>0000000071</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ4</id><value>0000000011</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H86</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H86</algorithmID><id>AGG205</id><value>0000021137</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG208</id><value>0000000300</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG218</id><value>0000009033</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG223</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG407</id><value>*********0</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG504</id><value>0000005748</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG518</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG602</id><value>0000002800</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG801</id><value>*********0</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG803</id><value>*********0</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG807</id><value>*********0</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG901</id><value>*********2</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG906</id><value>0000082.85</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG910</id><value>0000088.69</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WP4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WP4</algorithmID><id>RVLR80</id><value>MIXBHVR</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG9</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG9</algorithmID><id>ALL252</id><value>*********6</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>REV255</id><value>0000000104</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>STD255</id><value>-000000002</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG8</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG8</algorithmID><id>REV202</id><value>0000001.20</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV222</id><value>0000001.20</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV225</id><value>0000005.07</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>AUT225</id><value>0000001.00</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WH1</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WH1</algorithmID><id>REV322</id><value>*********0</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC320</id><value>*********5</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC322</id><value>*********0</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC326</id><value>0000000149</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V60</code><status>defaultDelivered</status><scoreModel><score><results>+573</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>true</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>04</code></factor><factor><rank>2</rank><code>12</code></factor><factor><rank>3</rank><code>07</code></factor><factor><rank>4</rank><code>63</code></factor></factors><scoreCard>06</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>001NN</code><status>defaultDelivered</status><scoreModel><score><results>+564</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>false</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>68</code></factor><factor><rank>2</rank><code>04</code></factor><factor><rank>3</rank><code>10</code></factor><factor><rank>4</rank><code>29</code></factor></factors><scoreCard>04</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>00H87</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H87</algorithmID><id>TRV07</id><value>*********2</value></characteristic><characteristic><algorithmID>00H87</algorithmID><id>TRV08</id><value>*********4</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00Z23</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z23</algorithmID><id>LINKF001</id><value>CLEAR</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>07051</code><status>delivered</status><militaryLendingActSearch searchStatus=\"noMatch\"/></addOnProduct><addOnProduct><code>00Z17</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z17</algorithmID><id>LINKA004</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA002</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA023</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA024</id><value>0000000169</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA048</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA049</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA001</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA050</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA051</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA052</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA029</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA030</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA053</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA022</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA026</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA027</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA028</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA021</id><value>*********2</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA020</id><value>*********2</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA006</id><value>*********2</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA003</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA025</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA011</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA005</id><value>0000.00000</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA010</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA040</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA033</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA034</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA035</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA036</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA007</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA037</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA008</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA009</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA039</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA042</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA043</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA044</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA015</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA045</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA046</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA047</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA017</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA018</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA019</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA041</id><value>9999.99999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA012</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA014</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA016</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA013</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA038</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA054</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA055</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA056</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA057</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA058</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA059</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA031</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA080</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA060</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA061</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA062</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA063</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA064</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA065</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA066</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA082</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA067</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA068</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA069</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA070</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA071</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA072</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA032</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA081</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA084</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA085</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA073</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA074</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA075</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA076</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA077</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA078</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA079</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA083</id><value>0000000.00</value></characteristic></scoreModel></addOnProduct></subjectRecord></subject><printImage><![CDATA[                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           16 NF     10/14      02/19/25    18:21CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> ALBA, STEPHANIE                                    ***********   7/96        <ALSO KNOWN AS>                                                              ALBA,ESTEPHANIE                                                              <CURRENT ADDRESS>                                               <DATE RPTD>  308 SUTTON CI., #79. DAYTONA BEACH FL. 32114                     2/22        <FORMER ADDRESS>                                                             1104 BELCOURT PY., ROSWELL GA. 30076                             8/20        3624 SW. 156TH CT., MIAMI FL. 33185                                                                                   <POSITION>                          <CURRENT EMPLOYER AND ADDRESS>                     <VERF> <RPTD><HIRE>       THE PIER HOUSE                                                                                                                   8/24A  8/24              <FORMER EMPLOYER AND ADDRESS>                                                STUDENT                                  STUDENT                                                                                 1/17A  1/17  1/15        ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVALIDATE ID MISMATCH ALERT: CURRENT INPUT ADDRESS DOES NOT MATCH FILE                                    ADDRESS(ES)***                             ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +573  : 04, 12, 07, 63 SCORECARD :06*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +564  : 68, 04, 10, 29 SCORECARD :04***              ---------------------------------------------------------------------------- C O L L E C T I O N S                                                        SUBNAME       SUBCODE   ECOA OPENED  CLOSED $PLACED  CREDITOR           MOP  ACCOUNT#                     VERIFIED       BALANCE  REMARKS                 I C SYSTEM    Y 2834001 I     3/20          $518     BANFIELD PET HOSPI O9B                                2/25A         $318     PLACED FOR COLLECTIO                                                                                 LVNV FUNDING  Y 21T9002 I     6/23          $1911    CITIBANK N A       O9B                                1/25A         $1911    PLACED FOR COLLECTIO                                                                                 PDQ SERVICES  Y 1ZW1002 C    12/21          $3921    2200 BIG CREEK APT O9P                                2/25A         $0       DISP INV CMP-CNS DSG    ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   SYNCB/ONDC   B 235064B  5/17   $3771                                    R09                          1/25A  $4000    $3323                                I    CREDIT CARD        5/19F  $3323    UNPAID BLNC CHRGD OFF 0                                                                                           CCB/FE21CC   C 1NZ8326  7/17   $2050                                    R9P                          5/22A  $1400    $0                                   I    CHARGE ACCOUNT     5/22F  $0       SETTLED < FULL BLNC   0                                                                                           DISCOVERBANK B 9616003  7/16   $1326                                    R9P                          5/22A  $1000    $0                                   I    CREDIT CARD        3/22F  $0       SETTLED < FULL BLNC   0                                                                                           CAPITAL ONE  B 1DTV001 11/23   $829     MIN25              ************ R01                          2/25A  $800     $0                 11                I    CREDIT CARD               $762                           14   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388 10/14   $3500    120M42             11XX11X11X11 I01                          1/25A           $0                 X11111111111      I    STUDENT LOAN              $3954                          48   0/ 0/ 0                                                                                CAP ONE AUTO F 1W2K001  9/24   $20.3K   072M477            1111         I01                          1/25A           $0                                   I    AUTOMOBILE                $20.5K                         4    0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388 10/14   $5533    120M74             11XX11X11X11 I01                          1/25A           $0                 X11111111111      I    STUDENT LOAN              $6872                          48   0/ 0/ 0                                                                                CAPITAL ONE  B 1DTV001  9/24   $1928    MIN66              111          R01                          1/25A  $2000    $0                                   I    CREDIT CARD               $1924                          3    0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        9/03/24 BPC3575459(NTL) CAPITAL ONE                                          9/02/24 QTA2762142(FLA) VYSTARCU                                             9/02/24 QTA3129826(FLA) VYSTARCU                                             9/02/24 FDT3025836(MCH) TD AUTO FIN                                          9/02/24 FPC1246539(NTL) COAF                                                 8/31/24 FCH6139738(CHI) NATIONWDSE                                          11/16/23 BPC3575459(NTL) CAPITAL ONE                                         ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ]]></printImage><embeddedData type=\"pdf\" contentTypeEncoding=\"base64\">JVBERi0xLjUKJeLjz9MKMyAwIG9iago8PC9Db2xvclNwYWNlL0RldmljZVJHQi9NYXNrIFsyNTUgMjU1IDI1NSAyNTUgMjU1IDI1NSBdL1N1YnR5cGUvSW1hZ2UvSGVpZ2h0IDM4L0ZpbHRlci9GbGF0ZURlY29kZS9UeXBlL1hPYmplY3QvV2lkdGggMTEyL0xlbmd0aCAxNDIwL0JpdHNQZXJDb21wb25lbnQgOD4+c3RyZWFtCnic7Zo7axRRGIYFO1t7W0s7Kztbe3trG1t/gT/AP2ATSSGkEEFEEEGQGRUSvBFECRHFG8HgPcT12fPsfp7M7OyuwezswnwcwszZc/nOe97vcs6k1+ukk0466aSTA5EvO7vLG5/O3Fk/trJ6+GpJOXrt0albzy49fbP59Wfb2i2YrGxuHb++dmipoJy48fjsvRcUwDyy/IAa/oJq2zoujFxcfR2gVaj44cfOlZcfhRrqwuG2lFwUAcNDSyWcXN/+3tQGGM/df0UzIP21+3uW6i2WFJ++aODwkFfIeff9ts91SZAWneGPEfgGRGDo6+nbzyFhE1FhKYaPW+jC00gBH8ABIl+hJTEdhMd0IQHoKNokeMIUaEpC+YWHG3L15M0n58sNKHp5/R1xSm9JSKISwN0C4n7bus+pYOk4TyA1U7KQdlJPJebfG9KYSrGlkte2FZ9rMQxh7JATZvIKOcETWvLrzbefARmu2hgy07JVfRdAyOcBDRh9NZM30GPp/EQm4E8dnk0CJ0mBDNYpCy2gYi/5VawbP9BL5MyNHaGeA2mLas+taNRyMjnM/hEJrDBzYxPPyaOWxilMfm3rG+SEom3rPo+COQMgfIsgDmi8gjCkBTSeITBQE/3JBOCwJ1OcQ9u6z6mIz5T5pOQUfxwsD02FlgeteV03p64fRiSGsXWi4N+wzSkb18UjD5CSqI9viZ7e43mY4iFcQSXdyoPXzCQdnPtq1LcyJYTTcgYL1dHtWxMUINyYETVdH4F2alPGxqG/BdtP2PZ/isrZX0ONwVOIpsSTVWB6kefsT9AhTVqyL6AKA/GfFJRkZMY3VI3ksE545EJmKV7sNONZzviMDKOYcRjQi6EVlyJJVGq6JKE+4Vlq47hW/BVnKDYaqNkdtoNXBjfq4aAYjUoKLdkvR2azrKEXG+ptNq6MBvkNod2Nm7SJyDjEs2zip0HWMSUMQVbdUDKu1FCD8XP/yci0pBmN+cmU0npHY0aKi3KlYZ480J4acngKEzH++NukOp7B5+GFf+n9Kj954+ev9qKY3KbkbfB1wIdw0fRyLhM8T8Sx9R7cJvGzcOM0JfrqvkKHuLFUw0gIgSKLFIOiPp4oefXOzUXZElT37fEqeLopabH9L1Dow7xsN808MrAQqUtJt1V9PF0LNBAuTxnS2yWAkqcMkeGZwsjUUJhxIp7au2dnAaSLOqi/DTwMilhqPNCHuWiPPsKrgw0OQCEWyGjUiOrE+D4Jzz0x3Wur3GWxZNHITYm+IiyefilgL3IDdygxDFWjAcNqPhPtXWUMW2CSayuGXlTmeKZDTfXwYobpJae3l2iVO0N7Mc7/wnN4Dbgn7ff8JZfG4llULmDTFfcADddiF1Bi1UGDSfF9sLnem8XVhCLNvITM8ayvopdid4yQLj2qBNDK9n2EnBJP71sqUyfd/gFPAQFV2shb2zPmGHvP8884VudBoYbnwEPqtSp4Jg5MxLMIn39AeMqfimm4kIq9j8JzYO+AUKG3UUlPmLvBfN7coQU/a3iWdX46dcVy1VkM9f+t4Dk8iPVjHxRi+Xh7PeoUeA6yHa9r8sDnRB7ZMDGzAghMGxTDVws4f52imZ8j8NQ9GlWNjwzorplUjMQzHwENAYEyfbjXB+bxncFdRSXGOXs4QL26XwcAjV7i6UeBELMvWSe2rMh/t3AWGqgt6zX0ZPlzYf4QHxzFE4hyPJ2XvkM0/no/X6W0KVaeDgFv5Hsxmk6eSjM0PxtNfwA3SfZiPyrRh5pYRQicRBmTHNr4XQBIAVbzoRf2kndhRVS6NazCmy7/HQg8ec0jCw080JlhMjINcsU8CaJD3st5tWu685w7DX4NnT2KBnosh8aV/0lgUbb3bEL7OLN00kknnXTSyZzLH2Z85lIKZW5kc3RyZWFtCmVuZG9iago3IDAgb2JqCjw8L0NvbG9yU3BhY2UvRGV2aWNlUkdCL1N1YnR5cGUvSW1hZ2UvSGVpZ2h0IDIzL0ZpbHRlci9GbGF0ZURlY29kZS9UeXBlL1hPYmplY3QvV2lkdGggNzU4L0xlbmd0aCAxMTEvQml0c1BlckNvbXBvbmVudCA4Pj5zdHJlYW0KeJzt2UEKABAURVH7X4q12NM3UMYSEed0p+oPX4kAAAAAAAAAAAAAAAAAAAAAAAAAAPZKubROHwIAsEafN5IkSfdn4UiSpPeycCRJ0ntNfFSNPwEAAAAAAAAAAAAAAAAAAAAAAAAAAAA+UQEFBy4OCmVuZHN0cmVhbQplbmRvYmoKOCAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDIwMDE+PnN0cmVhbQp4nMVaW3ebOhZ+z6/Ya83D9KxpsCQQl74Rm7Tu+HaA9EzWnHkgWI6Z2pACTpt/P1v4EjcJRDFMz0rTKEbo2/vTvkr5dnYRnukmWNyEcH7mhWe/nzH4LD+lQPBL/m85DML1We+SAiUQLs7e/Rb+V879BvhEztKpxihwHSzGNcuBeA29ZH1LYJDB78eLPU7BBauXEfV8P/COUQmucvwqtQmYDtWosRWG7YQhmmPbzACi2YZFTJyb3569C/2rL8NgOJ1A3/cGwxB8bzb1Qyk4gdsXkP79H/w5r5Sn8B0nfcZvopmcODb+RA6oCf7H/TzdNjUOlslhvRubpgGrs+DnheqU0R0DOTc0e6+LVanyDFCqEmxuijhPbkQOk2gtPijq0AhtvBm6n81FbxzlX0XZiQS6mgS+KDarsoBhUWzEvC2ygTZ0RLt+QN5ZzchzB73LkfevLnCMepxLtBxkgONcCwjrAk2vR2M96vQYRw/6gF7aV3WCxu2jhmbQ/f7Z9fs3nMyuQpi5vjv2Qs8P4HLqw8wfjl3/GoKri89eN/IQVXNaiFyksYDhoAszNh2u5kjBpBM4jDQqcF3FCdOylPD6mxxZLcGdz3NRFCdBn2r5R1vwzPJ//Phxjv9MnVgd+NgR+8+Q3NVNBPAeglLcLaM0EV3gIfuOWQNICYFhWorbPIJgmSHtMMiBGOc6MfbYPy1mOrhxcrHHNQbRQ5mlEVyIKF7C5UgDnVGqStUuST5Lv49JkktYqskcyU1rN1bOkfJtAxl4Fmb2lI8u3Iry0Jt9cidDryXlEg+jKNH3eE69wburIoN/ptn3FNzTzP0JMKOawfZb/YKi772u1GSEabhfyqp2ELoYsyu8Wg25o5+btAs/RevXHF1du9kyS1uHSsOmGrHVQYcpXCYrAUGCmai19ehGM7mU9KjREoRi9LCRVqaraTiISgHZAi6SvFx2Yj6IXKshsXqOqQRCMAC9XtxLh6T8MW5V4ydxS9mziaJRdJFDf4bmmBmpWcOZjgYbXIWh7JCGGsDfLEc7gnyyks01JOLllQbuNa7iwoXn9j8dskhbi2M6U+dulov7JNsUXZEnwRvZo5QYqPCoP73ysby9rqGuWqeJO38a/OGNRvDRRdYIsdSsuKlo+wtZk+DNNmdi4gn+QGuj3Aw/YStSw1u1UhNv46E7Hm5tTac2VxJc2W0s5G+ngnHgb79h4i7LSzFvSZVtN8OwHmvbFVa29wtUYdYrlNk9RrptCJR3kpsaV/EEbzwbTa/H3kStF/0GBre3T7Grxm/O2f7Miz4789pPQDG2r8kjr8Oo8cxL6mDoL3TaO2rDTx7Mhp4Pn6ZXwUmF4UnFgMU1w6gkU4szWZGUSZb+srZQ50RjurqAQRnlp51qnVQtciarRWXpuq0Wd8i1tRR6a9vUrXMuowHXqZqCXnrawd5J5JtYy9E3yLZYiLhM7rshXm/o8rAvM86Jfc7sbitZzsxDJbsdv6UD51j71oafILwaqMbMDuMOivT/jzvHGY46Wx7qtq4FDy9ELVX1To9ax6A2bdaN9qhaefV6wFNVrPuA94qCrQ8ddgGPKEb0vyLgKcvWacAjDZmGEWqdE4oxr32ra9i6WqXnjjw/DMCdDCCYef2hO4KxFwTuRy9QLP10m2+fygsVG4zH684XSr/dhFBeEsrXZOl3GL1W+hmmo6ZU+HDX9rwZmyh1PO/H3SpKIxlbO9g63nDGG+abL9EqmcvjpOEAxkmxjsp4Ce5K5GVrlY0KvNZA+1e+j2EdtndY7mDgo6XAYOoFMJmGMHZDed4xHHn7Z3++84I/f3trM6qcyQ3DOmTy7fgtmdzQzWaekwL3EyleJZhYHmAk0nmS3oIblxCIKI+XXfCNQtTyPZluOe20m9cdQ82og/7UH04+qjaA1qEBdDiCkPogsHsu2z/r0P5Ziu2fbpNfFwMsQx0viLO8LSCjtjpgp0FHNxuCzhd3EsqEgAahd0Gp+Vj3GED1ehX/wa22gBWlUrnas/L2B7NOM0K4FHAT4V7FogCMKA/ZJocojrNNWhYQ5QLKLINlcruEOFvf4Qdz/ARWWZRCtK5maV2wwBtkpO1P15xmBMlClbbKZVRKDiC7EymqWtGRreaiKPesQFJUnOQiFmnZifJGkwm0rXcr5ZsQrlHdZXS/3el1lD7AXKyS9NtGXnBkOf6WZ7dRmWGa2RtGJ1rrDTKZrV1Lat2EMIrir/LSq9gsFkmcSF1zsRL3UTWIVoBbLi3isOvpIsvXVURrrb2UjTXIJoOZ13f9wQcgbS8ZKjBq15/zT7JSfABs46L5vOrApX+X6BCLKMZNL2CVFCW6QnST3Yv31ZN0s5Z/ZYbsSTNJ8mQbO+SjOEuLzVrkfy+e/l1DJQixd9cEj/gxhpSkhIVsIpcRxpz5vcgLsXqABONNLKGrhbfTCpnJ1DZAuVRjtnMo1bbjE68PmcXrE1X/CxzlKg9a3/JitmKWrpytzNa5BJ220rDWadv2htJEGhH2TnvsoIWINzIrVSmpSy+V6poNwnSSnBsRLo4SM8ahsng5Ge9cY5WsE5mz03nn6ZnxBinpSRdGT3loQghR64X4/liX3EUJBiTU9TYXYt6JhkYDPnO60LAJ4Xinb6L0axzl87ry63tSLn/e8y4SEtMbxDtOSGpW/+rwfwAP8R8KZW5kc3RyZWFtCmVuZG9iagoxIDAgb2JqCjw8L1RhYnMvUy9Hcm91cDw8L1MvVHJhbnNwYXJlbmN5L1R5cGUvR3JvdXAvQ1MvRGV2aWNlUkdCPj4vQ29udGVudHMgOCAwIFIvVHlwZS9QYWdlL1Jlc291cmNlczw8L0NvbG9yU3BhY2U8PC9DUy9EZXZpY2VSR0I+Pi9Gb250PDwvRjEgMiAwIFIvRjIgNCAwIFIvRjMgNSAwIFIvRjQgNiAwIFI+Pi9YT2JqZWN0PDwvaW1nMSA3IDAgUi9pbWcwIDMgMCBSPj4+Pi9QYXJlbnQgOSAwIFIvTWVkaWFCb3hbMCAwIDYxMiA3OTJdPj4KZW5kb2JqCjExIDAgb2JqCjw8L0ZpbHRlci9GbGF0ZURlY29kZS9MZW5ndGggMjQ5OT4+c3RyZWFtCnic5Zxvb9vIEYff61Ms0L7wAQ61/0nmzYGiaId3NKVItItcXRxUW3bUWnIiyWjz7TtLyY7jmOuxuCINFEEiIiL1m1nOLGce7eprp1d0hCa+0qS47CRF52OHk9/M/zJC4Y/51w85Kead7hEjjJLiqnPwS/Evc+73Uyi5mD++SHPiy8BjcnMh315IvTAIuCTUC6RPNZy6vO4cFKPTs3ScDnISj5J+WpBRMhyMCiNCyfUzQn//B7xeloYy8h846Tf4Sz2taBjAK9jLNBkdP5ynpKeIr30yvz+WnNx0xj9+UJUv3IchUGDyvS9+6cpPgsaV0XR1d7NekXS1uptevie8y8IuV4QF7zkjMdYnqymSo0wZTq6nYAC5vaopKqhAi0qUFkpWgas88JS4lw2rZeNBliVxASE0RhnwlUilN+/CvWE+KEk4hXRn82tG+rfk42NL7k8AOzaXQaa8eziy5oFxgjHvPoEECbZObEM/JTEZfxoXyQk5P/hEeCAkpez8l7pxwoUHUWeVji4ubu8Wa/KXXbR2MUqG1IMkQBk1mqxni+uao6DkC0MwCHvu4lX7XhgSn1KPSkTIDpaz69lickPi5fRytr5dvq/pLRMbdQjW573tRflRmmR9Mkwe5qHHF2u4P7q8+Ps1HwbjYVpE2fnBKCmiNHMQmb6nXzFMo+l8svz3qu7gcB3YB2eYRXHSJ0eD0dOhMZc+NzTf55y686tinlKgEeImu2hepsjwZnIBj5ia4hI+1ChX5shfFQvqaoTKExzv4ODLdFHfM7WVrPRMdDmtqRFAQUN0gIzl+8mt+PZl6iLbjXBlQA+GSd3A9DUYT7QOsKm6+nK7WM3+ObuZrb85cRCkKx1M3U3dwg88DXkQ+LgA7U1uJouL2vewzD3QrM49UTv3FA08KF3Qrg0ns90yb6eZz1ceh9sML0jjVmvSv3Mz8CC6z4GXMPUwgfctvrld7Tjp7fbQ8cuh1xxnXmaGfjj5Np8u1o0ZaR4cUuCNPJsuZ1czR48OEK2MDw49Xd340Kqcc6TA+Xb65XKynpKT6frzrRsHpfLgsyvq8NNicBL9VCaW1wn1Uy2U9N1NxkzCdAX9i9raJh9GZSvWn97MFl/vpouLb3WrQrYpTDjyHpxM/jub380bi3/TxpnHIPQwr6gMmzMPIgHuksLWdX0I4OYmOIOfhD2StvMZzGvr9XS5aO6xRz3J8Ld1vJ4s4bnnfvQeHVJPIUiaonC3YUDJvKNgDtgcPwFpWLijfEvlk53lZ+ToNO+n+fGGjLAipJS7IiNW8bbICMooZ2TEqrYHMqK0bpGMGPXKViJOi7QX5b+TnETnB0dpHuVxGjmDHWjPHcIOq7922KGU3jvsUBpZFjuHHUa5uu5nhyFjbnAH2kV3uMPqm+5y4QJ3KCXawR1GeP+4Q0nWFu4w0g3iDjOeTeMOo7nf7NsCD7RzbQAPc6MbBx5GdN8TX4k80N61gzwU9kvFNpEH2kiXyMOIVkYIc4Y8FNMtIQ/Fgp2Qh5lT9o08ZKgbRB6l2htGHjKQbxl5SB9JJNtAHtZIahd5oG/rm0IeEnqpe+SxOd4ReUhFqyfYYf8jGSejszROxiXyYH/8jTlEHlbxtpAHyihnyMOqNgiHrpGHFGGLyMOoV7YTnFNKeumxWXKX/P7cchDJw58eetGwGJulIHkRZX8ORn9mSTTGNSa28CwhCXqsHEIS6wj10/HwtEieIyTPDU2anyXjIj2OHkOSHy+iHgueYpWTYZYUya/x6Riqj2cvYzAITy5LRgSsi47h1uHWu9mK4pLHSIGsN53zGKNs+R72EJTd8Bi0i+54jNU3xru1XSuBjGTIzHENZIzw/oGMpH5bQMZIV0Nj50BGcmRR7hDIGM3q9Ku7PmoLY9COtQFjJEPWpC5hjBHd37BvQQzas3ZAjKRITtQmiEEb6RLEGNEG1p6IANnFOgcxAvrAXUCM8OXeQYwwq40aAzECu7apJRAjoGV6wyBGYIu6NkCMNZLaBTHo29oEiLHVD0Qw5NeoxSjqI9uhr0QGavOuIkEAGoxU7v7Zvm82/5QXlZt/7o9e2vwjqIU4jD/lca87yPsxOT/oEQoxTbXs1Qc+UJdzZdduC/igjHIDfFS5Z8MqOKKhw4cHFLuhJDxArpd304ZtBCu7lFFyNsjO0vzYnZ8gJ81WRIVzM7udLBy1nLJUre7INrtH42jksCTwmWlCOUzlrTShspRuYlVAKAyG47JZDFd6KC0c4TQfRmmf9KIsyuOExB+i0XHSJ4OjI3eucwmeaUgk5CaJDaUl2Ww+263ieawNkxO3bgKRh5TW7Qi5+fI/gPRBdoQfZteftzDaiYO+rd0Vh75fl4Rxvc1TZLtWTJfz3YJ3t6qLm4YLHWDOAA99IbbEoai9Kkzo0LTz6Nhyx1Hoi4HlwDvFSu+wgdUKrDCdhjBZjt0v/3myvJ6SwdVVczzFD0y/i04BRxQeCnxrBqgu82vTNmYg5yviv0HIKQNuWlF0+NZAgU+HXVsgFgw7rvK2B5QU+Jh3BuiMb9LFSimcm7L8bqDkHc2zOuOrsCwq3rI6lwxOQx0qS/xUyU4y4+F2jl3tNZP2mpswL0moyZhlo+H5Qc07CHOTL+waJ7eL9efVbtSBeoFgwidPX+FzGQffuKnwyTt4Ui+nnasfC3/qKeO98B7CevPDMlCoUhEaDTiS8FgzRoqdKmAKM0TIOXn6asyT0mqe1K8xT+9oHiQXFBdPX415mlrNM2gRb15Yt39gAbOnpEMwLv2yUHizmzKVMmCcv9VNmdo3d4m9zU2ZPi2rkT1vynz8+FR+2Reio6mcDAl7x/g+gTdq3SF7tNWSPbfV0vLgpiZlq+f8OO51jxLOYoOeY0JZ/kcguHaFnq3abaFnlFHO0HPwwiiMXC433KJnppHwzhWONYLNo2emkPspnaJno1qNnkssSaI4HpzmuF/5ewV9ZhK55c49fTbSzdFnUxQ2TZ+NZqWH46QosqT/a5aMx6T4EOXk6DTL7mm0cwLNNBbgOCbQRti2OUy6ItBMIftYxwTa6FY7yA+pqu2g3uYqckFHKwQaHWAOCbQ9tuoO+5Y+o+PKJX22B1VtzzbkGR1QbZJnxpHtRnvkGR367sizNfJ9Z+T5FbG/I3l+6pct7lWX87p+baA1OvLdQWsjuVfPNtAanS4uobURre/ba6A1a2enb/mbzpZl7XuD1ozK/xNoHVp+xsERs7ZJ7BFZM8pfQNahbI9Y26zbEGukdXsB1jbrNsAaaZ0TXm3NRue8ulR7w7w6RD5L28HVAXI5chu02hpHe6HV+Fh6Q7TaLH/YwurysHqP/P8AtGGE0QplbmRzdHJlYW0KZW5kb2JqCjEwIDAgb2JqCjw8L1RhYnMvUy9Hcm91cDw8L1MvVHJhbnNwYXJlbmN5L1R5cGUvR3JvdXAvQ1MvRGV2aWNlUkdCPj4vQ29udGVudHMgMTEgMCBSL1R5cGUvUGFnZS9SZXNvdXJjZXM8PC9Db2xvclNwYWNlPDwvQ1MvRGV2aWNlUkdCPj4vRm9udDw8L0YxIDIgMCBSL0YyIDQgMCBSL0YzIDUgMCBSL0Y0IDYgMCBSPj4vWE9iamVjdDw8L2ltZzEgNyAwIFI+Pj4+L1BhcmVudCA5IDAgUi9NZWRpYUJveFswIDAgNjEyIDc5Ml0+PgplbmRvYmoKMTMgMCBvYmoKPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCAyNTI3Pj5zdHJlYW0KeJzVnW9T20gSxt/7U0zV3Qu2Ksjzf6S8uTLY2fWusVkjuFQd98IHhvgOmwSb2su3vx5LJIRbTdpWS0OSEKuCnadbejSa+bVafOoc5R1lmTOW5dedQd75vSPZr/5fBePw2//tMsnyZaf7TjDBWX7TOfgp/7d/79e3cHa1fP4hK5nTaSJ08UFZfpAnWZpKzXiSasctvPXhtnOQT88vhmfDyZgdTwf9Yc6mg9PJNPcinN3+idA//gmv19tABfsD3vQrfPHEGp6l8ArxCsumP395n9GJYc46tnza1pLddc6+/Y+qcpEOdoGBkJ9ycdtU/k/QpzKdrx/vNms2XK8f59dvmeyKrCsNE+lbKdgxNqdgKFqiQjmd3c7fMsXub2qKKq7QohqlhZI1kKpME6OeZLNq2Xza6w/OUNqfmE5N8V3D0hQ0NLyDdRfLW8H69+z35zGU34cAig/BKXL4ZSt4AvjohUqezhzF0jL60vP94dnx5GIwPeqNf2OXB0eMZ1ZYztXlT3U9IrIEHBdU711d3T+uNuwv+2jtE5TOeAInACqo6WyzWN3W3AvGn7Df2QvT7JTOroKnSaaZ4yLhGmPZzx/nb2smKVQhKFxVhoOLyehiOP6ZLs80TTSM7/CCSnN0P1sxqly9amWu5bXjuDft02XrBOTCrDO4bGH4/3i/Wi/+tbhbbD6TpAzSlSkP6RLNVGIhUSuxiS5nD/9Zk2QImpUZng3yfDTo/200ODtj+S+9MXt3Phqxo96oNz4e0KUvNWQH0yDOcRec44f59WLDRovlYlN3J0gYoLxw5Tj1V/GGc15XRQuYfMEp5HAZ/rK4/cCKNCkS9LqhBJW0dVVsea5q5Jxh/rDcz8B7TaKcTOwOBjua3c1WV7VHTQ3z7rC36vpKWZhuZHhfnc7WG9Z/JMksbKramRmxzQxrqJHP7HT2eTlf7XfK7Lf7ZaLgegzmxw1cH2YPt3M2ublpLUTt0kRJvPUnH+crWDvVnQDC9D7ofNcVdYccnYFD9C7eX5DkFfS97kpZN68ULskK7/zju/s1zRHzkpWZKYLMwIta4U+Xi/nD4mZBlBuIVuZmsLnh0tSpv9pYjVw/n3+8nm3m7GS++XBPkysoVy/0zvPJSS8fEM7TLUxf4URUNvEgy6vqL/mWqiOfYTk8rxsd+xodTeHU1zCNk6Z6/14e1DyCMKw5FdY4uV9tPqz3gxWwMldCOfbyFf5fISE3WBTA6HMIF/iHeefm2/UCXEN89jb5Ymv3BBa5yrwGbGm4Im4HjL2mABxGiExK9vLVh6d1MDxtdwnP7hmeVAbmJS9ffXiWB8ODucIO4WV1J1AiFeFTsj+/W6w+Pc5XV59rr26cn2Ns1TAD3snsv4vl47K1OZA0JoEdbCXyitpbehTWXnjW+aNkhcSF14extMWlE9/ORkJOKsd1GN83m/nDqu4swbjtUhLtpu1gyMShkMR7ZcdKh4Hzn0PQbNkx1pXb6EIHfNpkgfnXce90mPdGbDIeFMha9PMLzgUVsg6Kx0LWqKDIkDUctKDglAtyZG1SJPSjwrhesH1kbWwWAVl71RjI2hgbC1l76faQtdGqeWRdM9KSLpsUC2mI6bIXruZkKRVbNha53CRmy143kJ7MaMiygTlcw2T5ZWImtAIbjqWpizgLKo02JiGVDnvS2bqkp+TSaE9ScumwIYm4NNqOMbm00cgVRTwujTY/HZcOel+IrlQ0YHoH8+8JpvcKrqDLaP/WoMv72kIrvHMpEbEXrTSG7CLH+10QsZFZJETsldtHxEa4BhHxNzNhmUi71avMsjZgg2PYuIbVjWvAGWdgIOY2xLQF7t7CapWCagdVGqTa3vJhqm24i0e1Q+EVVBsbXiNUOxReQbWx4ZFQ7eAoQk61t2qvmGobjrySx6HaOlOvl2oHndQI1ca7qSbVfnm9D11BxLNftdOUiTN4Uz6lqQ6lJsmTBxbtyOx2x/7a34NSYv9iexfsr10g5kH/3XDcGx8PeyN2eTBgMKYO+ipNqbB/UDwW9kcFRYT9Yc5vw4LDBrC/tkhwSoXCvWA1Fx6f5b3R6GQwxvXI7AD+teERwL9Xrb6tOT/vQ6ZsNOmNqcm/Vi4W+ffS7ZF/LfWPQv61xWKwuuR/v2nmFt9rjUQBxPje61bTUvXG1K9P2PLMQNbzyQC+V6yeC0h+omtj7oLgow1GSPC9Zui4Zabuir1k+GhjUjL8sCuJGD7akTEZvpbI9VM8ho+2Px3DD7pf8G5tXFUy/B3M3z7DR/s3DsNHO5eS4XvRamM0wfBhuIrE8L1y+wxfZWmrDN/rNc3wm9UoGH6jGgXDV6HOlMsDnZIw/KBKgwxfwTo9zPAVLDmjMfxQeAXDx4bXCMMPhVcwfGx4JAw/OIqQM/yt2itm+CpFXsnjMHyFbUWLwfCDTmqE4ePdRMvwg1cQId6/h6/tHxqGjzYlNcP3wpV5vt+1VrFTylijlylLc6gsScqhzsudyzM7pYxdvJUpK3eoU5KUg0u4WimjajbKfG3VKLZ3qdkoHW7V2LZp+Dk4uzx4x7j4u/yNsFcjqB6raIMKirJoExRsomijVMu9Gl4wRtFGiRjdGl61MtvtenZyNBwRPnemKNkoHq1Zw0u3V7KR2Q/TrKFUW80ae43CRclGiTgdF163Go5L/kYpR1OzUdjbOMhqNip0/wZ38kS7urmVRRu0xQiLNl4zeORM7WdslFUbtDcpqzZhYxJVbdCejFm1ASu/9qoN2v90VZug/bOuJCra7OD99os2aPvGKdqgjUtZtPGi7RZtpIvVeOGV2y/alCCvtaKN12u6aNOsRlG0aVSjKNpIE2y84DSNF0GVBos20n6v8UKaiI0XofCKog02vEaKNqHwiqINNjySok1wFCEv2mzVXnHRRmJbKOMUbaR+xY0XQSc1UrTBu4m2aBO8gjSIt6X62pJQbO+Ct2Xo6XNNtyQExWPRbVRQlHQ7KNgE3Zai5ZYELxiDbkseoyXBq0ZpSRBptJYEL90e3xbuh2lJkNhn/cXk2yKL05LgdasxonljVN0ndJR4W2DvcCDD2yJ0a4NvSXC179ov6DbaYIR022tWHzf7JnVEcBttTEq4HXYlEdxGOzIm3Bbu1bckoO1PB7eD7idsSdjB/O3TbbR/49BttHMp6bYXbZduCxurJcErt0+3PRBuk26L0PP1ieh2sxoF3W5Uo6DbQrXRkhBUaZBuC/29lgShIrYkhMIr6DY2vEbodii8gm5jwyOh28FRhJxuC+xPB4lEtwW2uTAO3RbYZ/nHoNtBJzVCt/FuoqXbwSsIfUsC2pTULQki9CiBRlsS0EanbknwwnFaEgR28UbdkiCCS7jmWxLEs58eIf7sp0c82/wfvW02GgplbmRzdHJlYW0KZW5kb2JqCjEyIDAgb2JqCjw8L1RhYnMvUy9Hcm91cDw8L1MvVHJhbnNwYXJlbmN5L1R5cGUvR3JvdXAvQ1MvRGV2aWNlUkdCPj4vQ29udGVudHMgMTMgMCBSL1R5cGUvUGFnZS9SZXNvdXJjZXM8PC9Db2xvclNwYWNlPDwvQ1MvRGV2aWNlUkdCPj4vRm9udDw8L0YxIDIgMCBSL0YyIDQgMCBSL0YzIDUgMCBSL0Y0IDYgMCBSPj4vWE9iamVjdDw8L2ltZzEgNyAwIFI+Pj4+L1BhcmVudCA5IDAgUi9NZWRpYUJveFswIDAgNjEyIDc5Ml0+PgplbmRvYmoKMTUgMCBvYmoKPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCAxNjI1Pj5zdHJlYW0KeJzVWltP40YUfs+vOFL7wErgzN1j3owTFleJHRwHuip9yIIBtyRhc+mWf98zuQDLbkZD7EVUCGyRsb/znTn3zJfGUd7gCnypIL9qtPPGaYPBb+a/FAj+mL9+wCAfNZrHFCiB/Lqx9yH/y6x9WkLgcvT8IcXAF9qjYvUgWz9IvEBrJoB4WvhE4dLpTWMvzwZncT9OE4iydivOIWv30iw3IARufgD0x594vVoKSuErLvoNf4mnJAk0XlFeqiD7+LhOCk+Cr3wYbe4Fg7tG/9sXbePCfFSBRJE3XPwlle8ADZWsmC3u5jOIZ7NFcXUIrEmDJpNA9SGjELlysooimJMoveFNcQgCJtcVQTnhzqDCCcsJViJVpj3JN7DBdtg8C1vtvhP2FxBarj6VoDViCFwBzXJ0Q6E1gdPnMqw/RwFWD6GLHDzeWR3ASE+5t/EcDnot/drmo7AX52EH0qQNF3tHQGgrPyOEXnyoaiI08NDgrODh5eVkMZ7DL7tg7SKUCIiH9u8kVDacl+ObilqQ6K/o71bAjND6rJUS7QUCfEI9Ilws9uG+OKxIkvIVIPW3MWyfpZ2zOPlYH0+tPYHhHS9ONDuT4Rjq4mpQt3Jdp44ozFr1sfUpcgHlSze2GP3vJ+NZ+bm8K+cPtVBG6K2U4/qIBtxTSFQxV6Kj4fTv2U4MK0rKBAqCBQshbqkhmhZX5Rw65aicV90RhrHEAG8NKb+yfUJIVRRBsUxCa/fdGJ6UN7ewolkHQYO7nSDdD5iuiqLWbiUcs3sxHe1may+pIeJWat04UapqqeQzT73COI+Gd8PxZeXgKLC6ttul2Ta32sxCTmFlEbjbZW84m0NrUQs7u1FW9Tgu6ZKZq0F2DLPe8GFUjHdzud3UzzyOqRedxy3w3Q6nNwWk19dvJqLwtceZu/mn98UYu6SqtR4W8lbrxw6squ2LAC1EvMb2y9147SScZh7l7uYb3U1mO6p9V6sQ3N1wz4ppeV3WYxcGdKtdUOzM6yuhhNAm9ivh2LMO7q+G8wK6xfx2Ug9XRN7eXQ3ytBvm7RqLY4U1I7oEV54ZHhlU8ch3jdoxDNeBcla5HGYeU0u8rSyrJgKKKvzpGEr8dAz0OInlDpPbIS72CK+IgjHR53aU7mQ8v53tNtQgnuaU+/Dyiu+lDOtjbI8w6h1gdTAtGtffmgomIFzBlPfoif5m/kh4YDDwTmA6NULyndRNMKgFjMHLqxFPCKt4Qr1GPLWjeIxLLGpeXo14iljFw0LjFeIFlR1CU3sUaRV35fjLohhfPlRurXxToCzRXGJ0d/hvOVqM3ixVMik9VLBijpk8HJmR2duJp3yzS4oyN/FaGP7frj72ybIKslnSOhVhSprPi+m4alEo/WUf62xNy2AI9ICyWvK9LYNQWuNwUxKQgeOex8npIM5i92m8epzGU6JBar19HL9ZYObx6nEerxzn8dJ3LN6N2VaNaZgInPH6i8+zy2n5uZhCMhwVcLEXTa6K6t8EoKacZTAz2qpddCDd8VaRq6oX4KYqy0gnaBJevfdb7qUN5sV3Ob2IS18KGUCSd3bbxZ8aKI3WpLVdJqwmrdlgzj718zCLBqiy0zxkvmJUMDjuhO9UZbZoW6PKbDDfqoxTFmjsJN6vyhh7E5XZYPIWmNYXjuMEtXbcyjlhUnMF3ejknWqNWNr4GrVmg4nS8NioqxdRhvmVv+NQJgJLZNZNTutRlxUmCfM4Tc5bfRP/j6MTRXngcw3RSfxOlaYtgZnSJlVNVnU6sNSaDef/lzaF62xvdYAJ+u3sLI7aLTj65FoNc776FKthYQAth1M2C0w1vHxsWQ1v7hDImRbnntgaQLMw6Q8StO/qxZpgFqCLPU3IxQfQWh8IRquan4GjFriel3pwlP6OTQUh++iq7X7ezvahFwINCGM1wBMLfJSiVrvtDFpxP+qk/UHW7kMUJnDUhvQoD+MEzSZNOniF/CRLBx9P4GkrIMwrd5AEC3aLgCd53jtsNs/Pz70nXC9Ku69tK52l4R554Vs/PC3YTlAxx8+Vsctxwe9e/XRcEGVh2MXAqCGVv7p1Pisojeua8wlrpVJqaYEub8vinwJGk2nldpMilN6M67jthF7utoG2uC7cOX4t57eVZ/L+Ek8+HiW14OXT4Xg2GJcTt9nOs9v/AFBIvUQKZW5kc3RyZWFtCmVuZG9iagoxNCAwIG9iago8PC9UYWJzL1MvR3JvdXA8PC9TL1RyYW5zcGFyZW5jeS9UeXBlL0dyb3VwL0NTL0RldmljZVJHQj4+L0NvbnRlbnRzIDE1IDAgUi9UeXBlL1BhZ2UvUmVzb3VyY2VzPDwvQ29sb3JTcGFjZTw8L0NTL0RldmljZVJHQj4+L0ZvbnQ8PC9GMSAyIDAgUi9GMiA0IDAgUi9GMyA1IDAgUi9GNCA2IDAgUj4+L1hPYmplY3Q8PC9pbWcxIDcgMCBSPj4+Pi9QYXJlbnQgOSAwIFIvTWVkaWFCb3hbMCAwIDYxMiA3OTJdPj4KZW5kb2JqCjE2IDAgb2JqClsxIDAgUi9YWVogMCA4MDIgMF0KZW5kb2JqCjE3IDAgb2JqClsxMCAwIFIvWFlaIDAgODAyIDBdCmVuZG9iagoxOCAwIG9iagpbMTIgMCBSL1hZWiAwIDgwMiAwXQplbmRvYmoKMTkgMCBvYmoKWzE0IDAgUi9YWVogMCA4MDIgMF0KZW5kb2JqCjIgMCBvYmoKPDwvU3VidHlwZS9UeXBlMS9UeXBlL0ZvbnQvQmFzZUZvbnQvSGVsdmV0aWNhL0VuY29kaW5nL1dpbkFuc2lFbmNvZGluZz4+CmVuZG9iagoyMCAwIG9iago8PC9MZW5ndGgxIDE0MzA0L0ZpbHRlci9GbGF0ZURlY29kZS9MZW5ndGggOTg5OT4+c3RyZWFtCnic7XuLfxtXne/5nZmRNDPSzGikGY1Gb43esiTbkiXbcWzZsR07afN+2HHTpO8mbdokTd9PCk3atLQ0QGlpoS2XLpTtlnTZQlpgW2DLY5ddemF3W6Cwhctr2X6WAl1gaWLf34ycF8293D/gWjpz5szjzO/8fr/z/X1/Z2QChBCB3EYYkrxg13m7H/nBd9+FR54ihF5+wTX7ksIBrkUIY+Cx/ot3X7LrG4898zC21xAC6y+5/PqLv7lD8RPCfpyQrsFLLzrvwldnN9xHSPfreH3zUjzgfonlCOmx789cumvfdd+Mx3Ziewj7eOzyKy8475n3fOJxQhqvYX/f3nXedbvpH+FFvPUwXp/cvfei3XP8z67B9st4XsZjlNjyEhJEeXEPTCwusngQawY3DDn9D08yLOdye3hB9PokWfGrgaCmh4ywGYnG4olkKm1lsrl8oVgqd1Wqte6e3nqjr9nqHxhcMrR0eKQ9OrZsfGJy+dT0ipVnnb1q9Zq169Zv2Lhp88zslrlztp67bft55HxywYUXXXzJpTt2Xnb5LvuZV1y5e8/eq/Zdfc21111PyA3kxptuvuXW2951+7vfc8d+5cCddx28+5733nvf++4/9P4PfPCBDz340IcffuQjH330scc/xnz8ib/4xCef/NRfPvVXT3/68DN//Zm/efaznzvy3POf/8IX//aFF7/05a/83Utf/drXv/H3//DNf/ynb738P7/9nX/+l3995VXCwjp86jBJEpbEyS1oz3vII+QxtONfkQXogTFYD+fBdXALHKLfoD9gbmG+kAwm08lcsjs5mHwqlU5r6XD6pbfpwoKtebz/dnI/eZQ8jvc/jfePwjrYjvffjPd/lb7q3B9IGs79A879qnM/LCws/C9CFn608APcfg/LdxcOL3zMVskCJWT+2/be0ceOfvv17YS83n59kJB/++y/bSDkh9f+4B+4Vx1DDjlW618s9l8vaeC2UwYXjw0721XO9qwTtj4fdc0Qhewi15PLyLXkZ+SnZAexvfFO8n3yGvkB+TdyNfkhuYJcSS7Ao7vJHnIR1peQS8lecjm5jvzc6efHZBu5EO+8glyFx25GbRw4Ud+DmnmAPEweg3kSpCksD5EW4Uh4YY60juBEWHmE8GtmngG4d/YILNxxhLwn9hzhCbPt3MoRAl3J5MSO8cOwHRu0Cw+UUrjHdCUnDzPZyXUz1mzyYPLg9IUHk5PJS8+78DCbdWo8cdHB2VryMFk/swO3G2ZSh9uzkRO7F83ODmI/rN0P6/RzcBZ72LnYw06nB+zgGF7Eda1MHmZya2bWzhy+bTxyuD0+G0mlkhOHX1wzc/jF8Uhqdhavcp2QFOubdxiLMrtRZlcJdzydXtZjH9jF7MGDiy2aSx1+8eDByEEciXPESh0BsngAR2pfw2QnjkB7jXOqbaUi9gErZaVQjtlx7JvvWrl+ZgIlSc1W7FlfwI1AP41a5onYZjmD0cnISHdPKsBYTISKAGufe27fs8+2brjhGpiBzfNPzn+Szs0fmz8KrI0AhYXNcBbjIzkSbvuIGUp5BcmbELwJtqwcVY6SGvbFtaqQd75LodFsDUPL2eRdbgnwqwVDccAvFK1cRQ1qLpYFoKKg9/mCmrVhabIm8iLDqf7EFU+uCpbT1spmfm06L2piZTA63udT5NXZUWtzJXdR3SpPbCutvIzAwhsLMyAxEskTue1OmgLx6mzZkQb6GrmONHl3nOk8WguiJI44IUe0YQAJgFOV+JXTViAWCgrBoItjXAIf6pOCwfSG4WS3KPCQtYSAiI9cdam5SgyGTD/Kllubym9ZEhnvEwPS6vS4tamSu6SjqSvJLxE7/KTcNlUZHmLhQQ4eVCAhQUKGuAIyQ1wMColACVudP7K11chZaZejIi3ocoeG4ZetSqael8V8qm9kVaY51pvp1nmfoCY8/f7z7eeQV8GAs3C2Sm03ZYEFgp2Wu3ta9T6r8Oabr+7rSHMx3EOP4lV8mzLUeWgILLgNvDPzb32YeeDoTvSO0sJvKEefIAHUY3c7cakKy9Ob03QqNBOiMoQ5sMKGpQtynA3KhC2X0XVsq3fkh0aVWmmJasE4rfcO01ZQwnaV9jWGsR2nsNtnJANmOsjbddgK8h+0RmYarZmlKWtkc19r83AKps86r1+zRrc0z8Y6054bXH3bXE/PlltXLdY4klEczpfpw8RLkm0/z4iEEQ2PLvAEeBHQC19TXkOxHKladT+aOI9VXUtddXV0KpX+1tzm+Wd2wQf8ljh1/7Fv77J1k7YnBo66i3y+/Z5ronC1Fy6JwsUxuDgCl3hhiwkzEZgTYTYOWzRYIW+R6bQJUwKsEGHKC1MxmEjBRBomLJjIwEQW+mXoj0A9DvUE1JNQT0E9DXUL6hmoZ2FAm9aooJlaSWNKiLFciBghPUDKPj4aJahUpuxXB5SjuDlNv3/mr943DCNgOzPT5ziROz8Mtt5tP3ImnnuYWQLuplwOVUXNkyqF+Z50cllpqnndqspUPTLSDI2kPLqHD8fi3q/HzpXKoZpXE7JDvQW1nk9YG7vr0d7lpeaeofBIwhPkgALstvWXX/gxvI366yPb2mObi5Arwp4S7CjAbBGaMAmbgCkXQa/mqjRcBK4KdSORYcsJarA+4mLLerecYomism0CbRWHajtWuexo4JShk61bs+hSZUjnaoC6RRfrTG0cq9VnH3jnuBFl3m5sfdcqTYpoyVDMaGwaThtBqZryhX1+y39loVJfEZqsX9+urR5MDpUHhr9pX0GZrRwNFQbS4RLv5mA3pauT5WxgY75i1trZ1iopsNrxm4VXqUw/glF1tj2UNSAbAk3P6tQV1sPUZegGdengCukh2gs9VT8bx8lZZQuqrmu6p9fdxafYgOAWBNtp8YtoNWIP+2Xl5ZOjzlrpGoT0GLgRS0cQtMqQ6gvUGbRmq7kEekP1fF+jOeIMtt5cQjX4sc/NeChzKwDPuH3zv//SwXu9YcHFePf4GA8wrLgDwZZ1C6WbAQTef5Vyn9vF8BTm4KX5p2M9MgC4RW1l9mrjJf+ABlfZI51CC7+B0dkic+2lK2VYwqxk6KAMrhSACSQMRRkyJqID5CJgskaCBlkv4VhDjwgGy0dDUV+IJZKfDd9UM8q2ey9a+DTPzjqAcapxbWsGUm7L3bEovFFbv2/CiBqxWFkvjHVHhnoSy7qTsDH4d5Lil6qrWonX+7cuy1HuAo9LyzeT3YM+GW7ce5ChsfpynORZxDeBfov4cK5f2F62PgEXRWC9CesisCEKG2IwrsGkDqUE6FnQM6ClIJwAVxSSNC4ygQLDsAEjrieI5JEMVvLZGNiZpuUzzVOyNdsZAwK6Xu9tnkREJ+qEJAaDUNMZ2c09JaM3ztS7K833ZkY29bTOWZadGmvcORFL9DeqWtZfWFZZPZD65+4BxR9LtJePLenesNQqrbxkeO7BkZ4V+navEc9Fqs1UcuBs22IDC99GJH+IRMn29tiIAKuFbcKVwq3CfQI37YJpDpp+GHCDhTGNgywDGg+aJ+uhph9MDUwdzBC4GYT8CG9EdIGg3WwsegcS4aQEG1wYmQ4zTihlWi4r1UrBryK9ujebS7l97xf8iioGixHZ0DSvLzL/+zg8TTP5dYXQqrlt+clsqd2sx5rn9sXL6bjCXm9nHvrCGzSMmOIlBllKftr+xM4C7MzDhADjPOge0LywU4UdfthZhEtKcGkLLh6CHUvg4hxcloedMlyiwDlF2KrACj+sKAHbAKEKQhHEEviyIOSA94OQBh0SFHoSRo8ekDNsIgDbAlcGaCID2zJXZmhY9rG3+YD4IOnr9tE3ffCYD17GbxgeC8NtYUiGbe9/Mwyc3MU2nZh4qnacRvnPojjpVNBwQn+cYtQPNPK5/Am3sQH8uONUmeMB1QE4BnbOlXqD8YwSrxu54lR7aaMy8CErWdrRrMwsL4/2GdmwD+Nss75haXpiKN7qMuHtqWJai+mKW6rNf6tqhs3sRHeru780HcmEtcp038DWAJ8qVvXieE/ErLZz9bWKECu17DBZXvga9aJdfKREzm2PKXJSpoMhGDRgIAw1DWo61EJQCUPMqBg0FoKYDgJ+QyAYIIQRk6IsQ3N6wCWlWElySERt5Gj5DI4Fi1TIUQEqwY5geXSv46pYZBU3NirlequKfEK4/+PV3c3pwoYVLTGU0aurBlOZ0Zm+/tmlKTgysnxisDnqDyY8X70nNxZYJ1SHliVET3pobXd944iVm7wIRze08Cb5JXyNiCREzLZP1hiGN1wq8XpVxgFmMvKazaww7ByXLHCKlP/em8nU61amjpE+09OLO+PZOjSyVk8PrM7UGxmrt5fQhT+gIlfR54ibRMhftK++Q4C7TLiLwH4O9qM5TbhIgE3MxQxdDjDFQikEJQwuXsiKUGQhBxA2IKxBWIdwCHQRdC/oEug+CDEQwrOkSKiL07kcxwjIilgX0nyCRN/vcvOYvbvcCPg2bHW+xI46f45hpOqYHtRb9bxlT3E6giEJMTKErgpHHvj+h35yqOeb6TtFReBEgWEZluWRPPtElj53bPkT+6z+XKBW9EmypBWD3nBvwWfP8EnUQjf9HAkjg22R6XZNDwEXApcAPaycZP1MzaqxFus3ZD3lU7p9CuuRiizxm+waAmtMhyYg6trY69Cl4xMMITdvpxx9jXyVKYPf9p5hJu/MoTL01XtRbGBOb9LuqTEjLNcGlFSxJ/kf34+V/dXxSuijQUVM6nObg5ovFJV1iTfD69eGTD6gfXlic8AjSV1TK3qlgJc7No0ZTLpvWV+4y8u7v0BZdzcQreTh2Ec51lNw+PdbNEm/ShJkQ7vVB3A1gRsVuBxglwLbALYpMEFghYI5TJBlYnE7xhkB5AlBPSciVIXZHAcIJ7axRt4BJxhn8vasQC7U12jZsTLUcmiRHXhCtmfSZDsxkJgrK758oG0N9o3EtjU23l2qbdhw39Bc30w4uCq2ceq6JdFkfls4vvGs2VsfPnd7wp7vtpXG0EoBlHy4nZ+OQFGHYgyKUTAZPswIBiOGGK8hGoLO6wbanCjBTmbgkPBTJIXjedhxo6CR0CxOyzHCWK07ubKiTcbveTLRE+yZSU5+ZHRiZbtU92sSzJ57oDuZYik9Nk0p21fedsfunfBKLGZdgNpFKeH7KKVIutphDyugx7OUJSiT4OEYyvhED0qG3vLa8fTA4R71VF+qD7OkvnorNQkfnP/vl772n+r8vuue3/v43sVeX8BeeZJpB904jQgSOAN0twc8LOtjsMOXO90pDoC1Un7MNLA7+LQyf+CeZ56hk3vQM/r2PtrJwP5AJ+mXkUNNtiuCaqollcliLMPAK2UlqjC6m0lRnDwpQ9dVjohKhKUckTiHHr6DZaDN/alQXXOh2XN2XGieZnVbobbdJ+d/q+5MVyYKDcPKDa8eW1HdPVJfk7s/kytdvWpkbxPO2vtRf75xaNXutNXcXCiEzPO+v2pfT7MRJx0N0Lyj10LbYFmR5RFI3DrVOZ73eViGgE2BlJfLNjJ2cCRVR15tf1AV8NC993797ru/Tj83/xuQUBXNTp/kVewTM5+2BxCWqISadBYhloDfmjx0CE86Gf055Lu4q9nXeU2qE9/x61rYPVIpJ3+3WQfz3XuBcbF8JTJq1CtpD8NSOEC7j30pWojpvFb1T/GxYiMpBOLl46Pajj0L5Pz2eFMAF4UZFjYj2JIZQlewMEigQiBNIQCgM2BwkGBrLHUBcFSj1MNCRw+MrQfJwx4f/pnYESJnwGIcfcAnj/z+h0999b774GoYm/8KLEWNXLboZ/+E8nAk1PYCui2rA6Udw9u+ikpt2RqFZ8P/fd8+uuzYtD2KXhtE8a40ebZ9Y0KDRAIUN+yP2MFkP4W7JNjvgzsZuJOFOzm40wUHvHBAhAMCHODhgAeaDPSDHU0Ksh2klThoMQh6IOwCVwRcPnBJ4FLB5YeIIeNwJTS7TzEZBRTTbUaYCCGRCG/aQnYCifLa1sVwcgZNvJPwOCEFLVhl8nZsCaAH9+mYLAXqAUQGdGB67V3Df32ux8tR1uXm5j4zerdBgXIej/uCa6/gRRdD0a8+keg3fUY4FoBzbG0mE/F4LJUy598PNyUr8WgrtWjvImoqRsbaXTjlkgKUIlAyoRSGLgIWDtWn+6iyOEjFK7M8Q73O/C7/qWFt/w7YHA2hjEGrMg3cyeU7sfBDh+5AtGGpmDfvePiAvayFAvOCGZNR1J+oPX31YO7iAdBR1KJeb4TDmhIOL12WclbmFv6L3kZfRH8vkt52Mhuxo34pB6U8ZBOQZrgwoxicHlNsiNU7EHtKGOisvriOr691ABbhtQMPrU4CZaPsbcVVV07kVyRWrltTuGx110jyrNWPj073poYtedCKluIqy/Owbd3t5/T4/aUn93740kZV9pc+tfeOa4yuZ29cFk2pZsIXW7UYG9Ko1yBJ2vjwAIUY64mwPtZekImKok9ASTW23Jkei9meEwWcuOx3FokcIU/EYpqu98iJ0lDlc/ecHSslwt6BkYc8vJQtTc9WeuBHQ1sjHi9vh1vCcLwrsnMsnJJ06Z9bjv5uod9A/eXJEjLSLuR6IFeBYgmK/VAcgAajCuVYmYmxkGVjhqrXFIP1KYWOHjtrDSeVybXcqDgnXA1jSD1ds/YKIlPvbTFnUO7Hs8FAKNQV8GakkRWnKnpVaUVYiKRywaT82T/V9jVzlFFFj86VPnbVaXr3Ffwuvyy64JU/Vf6i9odR+xbptrPysr3+DeU0lCzIcJBzQd4NxTi4WB2Ry2fP4zITFEqGwZTYELp6UC+FQl6dlaXMcRMtMqnTJ2+qXmUdRTCOIuwYh87O4JcJnuRQOjz0BIRLlZLXLYrJgZIRa66szh+7yyvwAdMyBUWSfRGpWqn1mkVvLy0vXzEq814PJBoT+erKZgxNmqYsG8gN19SIacIrlbBP+ALHqTh/4wu/piX6TVImd7fPz1poQLA40FkwfSUfDVrAWTDks6EMSZJGQePA6yQbAibubF5g/Yof3vTDy3447H/R/7L/dT+723+b/33+x/xs3vDrlu72xYkbZxVR4p2UvoNke/fu2bv3HWSr84c8swY24Wra43cir732iV6AzFgL2u7j7ixk0NLs5g2VZLOcX9k9Vr2h3T83lokFxhpnz93QnOlpNG8cGt25svj9VTvRHomIVTFiF6ayiYHV3UtqyZKkhUe3xc1d8VRqeJON+4IdHNHubnv9k2EBSYkLSQm4WZ+bpbXyIiFxZG6ltLodeH4Rmhc/dS9dtm/fsS/SZXYv4wu/pf3YSwq5jc5FgdeB58KGooMuSwoLEl0kcZ2hZ+2I28lCbZJptxyH70xhSvvvxdjYqk6GXa77GJeXj+1Z279+IOVmOfo8HJuPcF4uV+4WC0n4xnzUyMVNX7UWzlc1MaB3Yv3bdBqlkchF7SnFk/RQ8MB+L9xJYb8I+wXYz58Wyu5029Yu+iAgWiKNMGVEXS8I1MSwafhwDESUHK5wpkCEBuQwJEuM24HwAGz+zfuv4UTMXDyK56qH3uDoF+Y/KVnZrJIbCsHOY+O2hD/H2VZzJBxo58EFTYybYr9I8ygGBVYIChmBWRRARAEkyfcnAmBCG7BsylKFGlgBZEdrf/jEFjHocsnS7CdfPQRxeOjFcE0OFmJ/O3/J/I/tOW4t/Ax+QZHVkibZ115jaiBq0K1BiUARoFyGcgnKKSgFgPUGvRkvY6YgEgTRilhli+FTwAehB7JxUmENRsoaklqrRhpSGgOqW4wsMreXOwT2TMtXCIRVpq91Ypm1w7jQ6noMcmVYzHttJh+HfN3BTJwP8Phyf0DAOMjJG8asZblIJjhQWpHMhjhBvumG2c2jOY/sEfJzLhovmKmWuc4XMhPaU5YWFstVSRFV3/ZoPFXWYOPcqnVRTvIEk29eYWXj1aStk4GF31FCf0oyqJNaO8owmC9yJTYRNhKYKnJq1asKXpUlcpZdzA9P5kudIOketl9MOdNTc3hk3RnAiZDZwf4+Z5w2qJNo38ra8hzGcuYujgWGY69zs4zezCWbZnftQRev9ETSki9rmnkPMk9+aPt4puiKitebeTOp93fP5yLpkMKLXskIBbTctStrvVpB874hRZIpv9XJDd6Cz1AZx1RtRzMAfRQwR5yVYYUMXooAnjB0lZej7KlB3waibN4WNn8qDJ2w1OLKyTDAZ5Z6uruyIZ+vVAmUgqP+gKQ1zOSEWRm6emd2aURSB9e6SzH/0vrcWi0wI0ke0av5E/u23HJQUTyyE1t/C7+hPyER5CbldrjASEnGAMNADiypWTnAeqMxlxizHWqRMXU0jrGD6fPn7XQkd/LVUEtntBPrWfVeuOE6xs2xd1CabfasGUzGW6t6JnqYsJANmBmdL1Ysd7g8WqNB7XCo4onlpy4YGtq+LFtWYddbXk1O9xc3n+3I+Bv4Ec6VCMmRC9rjH9A/rtOL9Kv1O3QmixNSh506bNRgTodJDVoaDOjTOjJLSIPHgEAMgA0YHjUuSRrD+GIskSQnLHZWGE6PAU4IaKVOnRaLI8s7U6ClWc7I0Hmeg0LKCq+q1Fcl0/H0yGz/jpnG8Hij/yMPl3L5IO/+18TayzJmNG3ltk8MnT+RW76m3h663EWbA3EpjZ5hoHucj94exqyxXOKg5IKIC3gXiByEgFWI6iWqbKiqoLKq4vX5RIV1i3YG7mS6tUVTnMITOzmibRLLCQ71VkdYHVrlxEBq9SaG9lTfvS19e9z0anTver+8/ix/Vjln/hz4+DnT0bgLdR1fGKKjNEbWk3PJH9qfS+YhWYBkEdQ8qAVQi6BUk1WqVJIVqnQlu6hSSpaoUk6WabwMZg9EuiHeBYkKJKoQr4E6CMFpSCiQnYZUE6xByAxAchSSbUiOQHIYEiYkwpAwIBEEO9XRIRECMwCmCueAsIxsYrNQ316HCBlm3xcBElEi9M0I1M2yIagbfCvZvFhlE95+tq0zs0jvFRmU2c4KUifKbd2ztbz1jCD4zsTl+B84ay4dF3Aiv8TYWtUT0Hl3a5+1E3PrFErurEoh/jsn9CWw+A7LOekOduDTpsGj/aFW1BfToiW9Z01/4opkNuDS1NSWgUJTlZWSuZ2yN7GeknVpa+jydLErMjk4pUdMlkrFiXpUll0BIdr1yjqrq5RDmP76Ri3k4tyucKWd3xPzBHXDa6bdEgUIW0qEF9wjoo/3hLRGPhmKZ7qGx4vhIIBXDHghVOhPmQUVqDo7nc5nCgUbrYZw8yv0yRQZbZc2emC5B/o8kPSA6gHiAY6HOCOYSDE1XVAV0/SGWepNnSCZp71GwHQKMXlxGjWaJ3G57nfwV4PL320jrneqJ7/GpCx3F8fdGRoMx0JwsWpqATGdj4c503txdpmWVEYzxw7DlcFIMmdHihYh5DX6OqbJsp1RuX0UDKqyAnM/C1exkGHhdhYiAjwtgJPtdSRylutfe81ZjXC3Tlndndy//4Fa2urpTlvd1Ks9YHV3W1jsJy3FTZj+nPD4pP525iwBCj6oSWAKJQGzZhBRGS6ESq8oU1bEp/H4NOXlo6cll/i8xcFDnWlZ7sn9DHfAxUDfR7/xPx5/Nx2tb4qlCkvSx16A0r/+0LbDCEaNMKJyzF4hiSJ3E73BxRWSbN6F4Tlvo++JQLdI2CAcclF3wIPUERj2LpbBLettl8t0DZU4r1FIwX3NQS1hjdfnb49kDFWwtQmkjmP8DuVJhXylfUiV0OVVX9pHKauyabaXHWPXsS7KqVya6+XGuHUc8hpgJWAx9fBCifGkGC1nRG2vUBXVo+bELLsmCzExypIo3BcFEoP7YvBpBGKRsLsJvElAIfACgUcJrCbwLQJtsobQwwTWALwJ8ALAaoBvATwKsBsAZ/CePXtsPdqVPZ3tI+Wt5TMvmdp+dxy97QQeWUHnByInPe/EDl17u6L7m/HSuMUJXHFkqo91u693Idt+t14LRNVb9e5ARIbLG/1+2Z/IeLxcqRHUxBQfihej8w/D1cFAonv+M7A7GIpWUJMNVOf3qA8jVaUdP6kyzmDdQdSPrLrVoBhgqc3QyqfMGFvmkzyskXefIiNseo/gHS+HeoL+iBy4Vt0f6g0iQF5TM1NFUeRVMZlM9y2bfy/s1MKJrg63/C+M6QJm7zn7FyochZ+5YTkDmE4ZIBucGpMwf5e001cayB57hp4SzE8sM+Q6y5DO6rMOv4kmRnc1Ng6lUkMbG0NrYqnEoVwlmOf9brcna2S1CNCRmczZueUYzDHs5VK5c5fNrdO00uZcf9CIRiyUcHghg0xPQJypkXa78AQPT7jhYkx3YuiymM26soypmqbhVV1qUVQDIrI+5LbvYH2dt5Snmtsdp5q/dQJ5O+8HHNLXidqUJMzwVCk/6QeWvZZh6LsA9Fa8NGEGI373oWI9WJCyjR7Z7QJp7JxYLBPRvLqkKWZEPbY//Vn0Ep8Uj2pdfe3ozIqA+N/5cmBgcQ7BTuolARJoC7LBY8yWvSczlcXl2xMIaJOjiawSkHNDOqNe5xH3ZytBuPsaN9sVKyWS+lmN+U/BTmdNM7zwJnyFSqSbvNL+8AMJOOiCa2Owg7ueo/YPWeLXxOlEHurFZUVajkEpbr/2zRRBK4KrCB9MwMMyfLhk33WP7LyBojAXgTn5MvlGmRmnMM7AighUTGRL9rpoloGIDhENIhEIMBCogVcDbwS8JiCeuGqQgS4vBLqMgFo0eDlFeMlA26ROza3/7A9byOnNbCdyvjPjjtPFiGszSpwUdk4KX4lKxdqwFcvEYktyXdam4cp0IypHm+ZQKZuP6bVKUe7ec27P2oHkx/rS/phX4UPBUMQf3OJPGF3D2XBvOs6JnpgZTol+jT9/iVkbRS0HEHEn6cPoka12XCdBnD0sSSLyBlWvaoSCWtDr4wUfbh0qbFOwTn61+C7EBuZF5mvzLpwuNnW0edgJdJ70qxqv6j5uU/o6lsWMt15Hasp4V9Yb4hqOcweSBsA5+bVWOtbMnTP/SzXk93nSRdsHRpEB/xpxJUbybVM0IkbZWGKwfAhZpIkM0Y+0kBXdi5jiqN7+pV6f3+2XmI5Mbv/ii4OWDoe2ycptrBoyvXJEcL2Hc/k2NDfUfVJT7THhQGT+QV6VPD49Z8F0eP5IvurPVnsSmCn/zkE3HjPlXe01lhssD5QJdHHQxdoZO+btQTEjUpMFg9gsVmSB9QDLB/kMzywm0i4JeAKcCAJjMiWGkXCOUxWD9P8pp7cRvZNd56u0Bu4AJD5/z4RXYdwsz7Mr7nqekldyE6Gokk76fu6f93R+6Ew/T104Y/6yfZOlgxUCy4CqG6oeqLqggIl9FxQrkC1ANg/ZHGSzkM1AmYMya48oaEAwnAnTKAdRFqIEnGWIMA7PzJg0mIBgHCI1m+qGKegR4Ah4YuDC8BaFbiNmRFRZBVWSmfL/05L5qaNkOqPEPSrD4oDrLedNSMuhnwmgxUMH8xzv4YChgsQXb+nmJZ6hDOUFrnTHPTebrNcnsgxLMasFjlM8VUo+ZzRqGY8s945mnnwqNVJXZMXf09Jfkuf5HWp3veqXVJ+Xl/mIuSyBYXjh16hBBrPWFHmyfb0l2D//QQ2iHstxKCegnLTXJjCbxUy+4GBGWoS8YKcwJTdUPNDFQ9oHaS9kJcjKkPVDVgWkniHUF9hvuDUlq9CwG1wKeNz2axo3gEcDIQg6xkkV0ZPozDtW6P8vf8R+62AT8s46DPKjvDvE1Fv27ws6K/iznzry4EcZ2RUMUYZn3T45KN197xefKgclHws4B7wMBN/gTF+p4UIOJYejKSP/yvyvlqeiUZ7hBSNk/4h+4e2FJPyOejCf39JemhUgT6DggYwIBd55ke+zh5yRkRZBHiCrgKGC4bcdBUfIY4ST0b0UdPmEk8GdaXDEXsRyftdHNXSBPOMOhOrOD4o6azR23j/0/HvPdokM4+GwCMzsXdd3pzEPcHGKLlf1qjViRrs81POWWvB5ixHq9aYzXigr8w++T1R8Xo+piH7XDBcKBM7q/PqfoA/fFP+9tU0e+i8SZn5hz6HPnn3zEbv+2/n3lxfGFpLsI8zT7/ivgacXkqTJPo/nx9hHTvyTwfG/X9Be+3fJeOlbuPkR1nkyCr8go3g8TqdInMFCv0QKeOMo9GPdXHgDXiNhug/3o1jaWIKkF/6GFGg3KWEZxZLGkl+sp7BksQxg0bGUIUeGYBDLAyTo9PMAlu8Ri7kR+5hc+AOdIZO0gfs7yST8Csuz2L4d27twP44lsfAGPQvr75BJppf00ivx/CfxfHOxnsIa5Yb/IAK9iYzTvoU3mDbOmN6FX1N94edUxXNdeP8zRMA6SDeQij1+mMCxEmLRAhmAr2E/WZTrj1i6iUGLeK4PZVZJC/yEoxxZCmEywoRJnSZJg2rOfcM0j9fPkTr8iIRhIwlQA/X28sLv6OdtHS/8Gn698Db8Cz7/FhIEAWXAGu+r0N14jMHSj3Hup6h3D+r0J3jNm1g+gLHu+Y49UN4wM47PYIjlXP8TrH+C9ffIEG2RAspTsK9njmJty2Bfn0bZcNyObe1xLcdzL5A+Wl34rS0D6rIAL5Nexrbvk/icONqXx77s9vn4/Pux/9/j/jVYX4HF1lMM71uD5ygeL6Csg3h88V7mAyj/ISxfd+RqOTY9Q2HWoh3Rzo6NTy1x1FEc740vfNcudOfCH0/Y+Hh5BH3Lri9dPG7b+dRi29n2hy3E49j0TOWPqLuvLdr41KKij6skhfV37EKlhaMnbPynZRqfbddo51OLbWeaQT+y6zb6zzgx4GZk/P+I5Y+oN6wdvaGt8FmTQBbG0J8l9Km47R+2j9r2dP6T5Uyfy8mzzuffyVFyFGQ4Dw7SEv0LJsLcyrzFbuCS3H3cl0/9uCZcX3Ab7svcD7i/6Al5Rj0bPbd7/t7zU/4G/h+FnLBL+Km4RfyRl/Wu9D7ifF45/eM7z/cPUkTaJb0qW0pauUb5S/z8zB/zf1nl1E3qC/h5I/Bo4D+DPcE7tJz2GT32/z9n+izi+S/IZcTtYDB1toLzj2Hr7V2ECqLah8vlLxIec8Q4+oyXeLAlYFyLnmiJp7W8p7V8yA3CJ1oSeszJlow9nmwpp7X8yGFPtghytCzR8Q67BaRCrBMtSsokfaLFkCI+43iLRc9NnGhxzhiOt1yOnMdbbuwxcqLlwR7NxdbzqASOuMvkfwP1xNaNCmVuZHN0cmVhbQplbmRvYmoKMjEgMCBvYmoKPDwvRGVzY2VudCAtMjM1L0NhcEhlaWdodCA3MDAvU3RlbVYgODAvVHlwZS9Gb250RGVzY3JpcHRvci9Gb250RmlsZTIgMjAgMCBSL0ZsYWdzIDMyL0ZvbnRCQm94Wy0xOTAgLTIyNCAxMTgwIDk0Ml0vRm9udE5hbWUvVkJRRklJK01ldGFMRkJCb2xkL0l0YWxpY0FuZ2xlIDAvQXNjZW50IDkzMj4+CmVuZG9iago0IDAgb2JqCjw8L1N1YnR5cGUvVHJ1ZVR5cGUvRmlyc3RDaGFyIDMyL1R5cGUvRm9udC9CYXNlRm9udC9WQlFGSUkrTWV0YUxGQkJvbGQvRm9udERlc2NyaXB0b3IgMjEgMCBSL0VuY29kaW5nPDwvVHlwZS9FbmNvZGluZy9EaWZmZXJlbmNlc1szMi9zcGFjZSA0MC9wYXJlbmxlZnQvcGFyZW5yaWdodCA0NC9jb21tYS9oeXBoZW4gNDcvc2xhc2gvemVyby9vbmUvdHdvL3RocmVlL2ZvdXIvZml2ZS9zaXgvc2V2ZW4vZWlnaHQvbmluZS9jb2xvbiA2NS9BL0IvQy9EL0UvRi9HL0gvSS9KL0svTC9NL04vTy9QL1EvUi9TL1QvVS9WIDg5L1kgOTcvYS9iL2MvZC9lL2YvZy9oL2kgMTA3L2svbC9tL24vby9wIDExNC9yL3MvdC91L3Yvdy94L3ldPj4vTGFzdENoYXIgMTIxL1dpZHRoc1syMjUgMCAwIDAgMCAwIDAgMCAzMTIgMzEwIDAgMCAyODQgMzIwIDAgNDM2IDU2MCA1NjAgNTYwIDU2MCA1NjAgNTYwIDU2MCA1NjAgNTYwIDU2MCAyOTQgMCAwIDAgMCAwIDAgNTgyIDYwMCA1NjQgNjE4IDQ5NSA0MzcgNjQyIDYyMSAyODYgMjg3IDU4OCA0NjcgODE4IDYyMyA2ODAgNTY2IDY4MCA1ODQgNTg0IDQ5MSA2MzYgNTY1IDAgMCA1MzQgMCAwIDAgMCAwIDAgMCA1MTIgNTUyIDQ1NSA1NDkgNTA2IDMwNCA1NTMgNTY1IDI3NCAwIDUxNyAyODEgNzkzIDU0NCA1MzMgNTUyIDAgMzQ3IDQ4MSAzNDEgNTM2IDQ2NCA2OTkgNTAwIDQ5N10+PgplbmRvYmoKMjIgMCBvYmoKPDwvTGVuZ3RoMSAyMDIwOC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDEzNDA4Pj5zdHJlYW0KeJztfHl8XMWVbn11b+/dt2933973vVutpdWtXbLUsmXL8r7bkjFgvEqGsHnBNlvAGCwbSMCQ2IaEMIEMJCFkkiFAJpNlsidvhklIQhgSkpnJMsm8SSb7y0DrnbotCRvI8n5v3n/vtqpu1V1qOefUqe9UnSsGxpiV3cwklthxxfar7l7yhR668gHG+DU7Du5PmHfwHzImJeha1+6r9lwxeOzWiym/he4/uufyw7u1GyozjBnp9kLz3l3bd75wM7+UsaVm8fxeumD6J3kx5WuUz+y9Yv91n7uuuYnyO6mMhy6/csf2Myum6f0VMmP42hXbr7tKkqU+xla+QM8nrrpm11U/fPrzl1P+d5T/LQXORHsZ06i9lEKIAlXeuEhniSKJXXjQTUk2GE1mi9VmdyhO1eX2aF6fPxAMhSPRWDyRTKUz2Vy+UGwqNbe0tpXbK9WOzq7unt6+/oEFg0O14YWLRhYvGV06tmz5ipWrVq9Zu279ho2bNm8Zn9h60baLL7l0O7uM7di5a/eevZNT+y6/QtT5liuvuvqaa/cfOHjousOMHWFHr7/hxptufusttx677bh6+x0npk+euvOuu9/29nvuPX3f/e9455mz5x548F3vfug9D0vvfeTR9/3lY4+//wMffOJDT374rz7y0b9+6mNPP/Psx//mE3/7yU99+jN/99nPff4LX/zSl7/y1f/x9//w3D9+7evPf+Ob33qBySCOsBr9DHS+kfh5ij3AHiI+fpDNoB0LsR7bcR1uxD38S/w70o3S3yS0RCqRS5QTfYkPJFMpbyqY+tz3pP/iMzOC9lTCLezt7N3sPVTCE1TCMNbhUirhBirh8/zbegmeREAvoVcvwT1bAmZmZv6FsZnvz3yH4hcpfHvmQzMPC7LMcMbqXxOpVx565WvfI0n5Xu17xO+Xn3p5A2PfPfSdryReNrygs3NQ513fbBBHB+sSUqWHgdlrjadW6fGK83h+GdFcYiq7gh1m+9gh9kP2AzbJXqY7d7B/Yi+x77ID7DvsLexKtoOuXcWuZrvovIftZdewy9l17Ed6Kd+nMMCG2RK2nK1hG9kEu+T1efyMadzNnPyDJJOcuWZGWYxS7GnGmpc/zSxrtnwYuGv8aczc9jQ7Fn2GWZh0ycUtTzM0JxKLJ0eexKWU4c10oSlJKak5seRJKbtk3Zb0eGI6MT22czqxJLF3+84n5ax+phu7psfbEk+y9VsmKd6wJflkbTw8n9w1Pt5H5ciiHFkvZ3qcSpiaLWFKL4EKeJUeMjQvTzwp5dZsWbvlyZtHwk/WRsbDyWRi8ZOfWrPlyU+NhJPj4/SUcb6ldL5hMjDbZhO12dhECXOjlPVUBhUxPj09m+O55JOfmp4OT1NP9Cvp5NNgsxeop+IZKbv4adTW6Ldq6WRYXEgn00lqx/gIlW1pXr5+y2JqSVK0xPpGko6cR1Lb+SS1U/NsOkkd/00kVf4ckjr/LJKqb05SF7VZFSR1/z8kqec8kuoatUKRxj9E48XCsjUflwxMMsk+g8+smTSLQ5LhMMqlEhuiQ31FfaXcDikMKS15qh7Ezpx58MpNn/3spi/yPfX/rP8cav0vsQWb649RyTEqWeGfpZLj7EpiX4mGha/taRaiYKZgb3uWeWi0OErULPVZZqP2iLRNFXkaFs/RMKKz/FzjmnH2rMxeV+ms0Tkwe44813gv9ly53VM1pburnenutEkP3qoeqt166Ez/843eG7+577LYotiNFC6/TLv8tTQ+9M53Lvw6HQvfufAbdCykHlRm7uMmyclMTGHd7HBtWaVdairFzTGvpphjMe438wJvlySutfOS5vP7SlpBa604S+aCHM7GNbNfrpgV2aZk5XazWZbMMZmb47JTYURb/XC5e0WYpzOl504vzUXl9mxH3jsoSQq8VIlBU7jJm+5M5fLdMVRd+Vbkc50dXd2dVZrgTJTz+mMSPnB5oBBR31fubOsCj3WOlfoHdrdnmja0B9u6tdKezf2XbsiUhjPJ1q6mwFMbcr+RfckmvGDubclXtBezIz1Z4/aLE7FaNAaH2RnSWheunbRsWG/u7Y+EpJ9ZlXh5WMyyE3iePaxzOyx4/ayuxgU7oRLfOfGbtQm+dKYnli7F8z/X35nZhXGJsyiz1GS/3SyXyu0Ganar1NkxyLsH0e1XYGp0NgY/Fg3titpUi+wIpn2RsaynusLjoLS3q3AucPmy6JKli2PDe9YvDKd7lFxs3aLWgwu3v28w2+mjumb+eWYP+74kN+pidqeoyyMISDXkqaZBdHbkBAWJlES177+xDt9s7Si8aSWF2SZQvwZmrmFfYzeQbF/doIXASI7SsIG9SImfUODbhsM04IwsSKFIoZfCGIVxCpMUDlM4QeEMhccoPEPhS6KUbc8yu0BcQlZI4RBdpTZxyckkXUK6O3LplJEoVq10tRtNfr1rX2tOR4sJv8Fo7o/4W51uLdrU1RQLZTEQbXZktECH4EaFZsn/RDtxUKmZmASZQyoxIpOQp8onf7BihXhqycyv2FdpfrWx7Rf0LazDQCI1y83O00sobKKwm8JBCscp3E/hUQpPUfi8EJBtRJRvUOIHQhltY8/qekiIjaRe2Jev5qORXD4azvdEcrlINJ/X6XwF3sp/Te84Z/ULZuXMjzShHWlh/dV7pTteuU7ouZmf41V+llrYxG6vXfZUAY8U8FjsmRh/NP5UnN9vxv1OnPHgeBG3SdhfxH4J/R70aehyoqqgK44+GVUzOizoSiNrQUmDZkFIg1fJKtzFIjlmlCM+m5KU/fq4ZkMlXV9u+0MHdImjLpqMDRGnfgqpJ7FMp1q5GATVSozjEbPDNVSM9Wgpq9Vdy6aGPE77ltTgxo7apk5/ZnB96/B4VwBLupbnOnubiyMHu7dEuxZ0NKe39a28caKydN+R6qLrJjoX7byOaLaemPgUv5O52F217c9Y8Ekrzplw1oh3GfCghFMGnJJwvRFHDNhq2GfgExJWGDFmwpiEv3bgGTs+bsWjDpx24DYHdjuw2IEuB3wOELaGC2AuH/OpTnCnncsl9SX1JdbQaM+9dAEt2OuIUe2susRw5KZ8d9VV9b7tGq5lVKUpxF2fGtw1hJ/D6TObPDaUbn/1r4aoJ4T78DI/Q/y8rzb1oAvnNEyq2CjjGRX3xXBvBqfjuCeB00m8PYW3p3FPFufMOGPCtIQ7TThlxPX2U/YH7NJRE44Ysd2LKTt2xrE7id1pTBjxES+awBzcJwvLwwdfWLHKJb07f5Szja4V0SkGH+kykmLRrUEIbno1YrdJkUyVQfQD/1C/JTpWLvZn3SOdvnwmo6V69nblRyrRVCzdGWzNR4w8UHQ/36It7mpdtWgg0jNmc/ntuXJrtH0kX7qoqa03VM4FJC5aODjzMr5LFBlml9dWPNCKsxWcLeEeCffJOBXCtBdH8rhhAW4o4XrtlMb3aVhH9PQKUY54sSCV7ESznGJJGUm4mVnucxRlpgSELJe26XPUGzpNfc021Gg6lWtDjCS2IcW6OpVK6JzPCkHPD0pzRKCp6buBYk8ifVmX26W4TTar0WRdONkSDrbkbO5ALma1dA26Q7HhrpbV6bSn0t0bblpcjdR6I5Wc/4vJgY6i4nSD06SGcDEwmCoO2AitoEc22zoS5f5kqOiO+xyB5v5M12qnKZyvCB038wP8L343ayMKLb7fg/s03Ep/Xhzz4UYXbnJj0oPL3VjsQ4cHnW50aKh6UfUhV2Q8yriNm3xtxqjFk82pFqPNIiRcTM/PCTF/6Tkh5y+9UTCyqVx72ReFiWbn9rIxnez0VGkSby939aPiay+LWXtIEEW/4sVnbCpWwu+q1//n5r+3B0KOcxYHddM+CKe5/wb41a3+q5UQBvF8fW8gL8FvcARdH/b8TnFhkHp56cx38G1+O/Xywdo17/DigBePZXEmgjMhnAvjrA9nvbiHxkcW9ydxVxgnfDjlxckEpsJHwnw8jI0ZbE5ihROr7VjtwGoFyxNCUEhK/EkoCuwOWLywZmHPIIW0XGRR2UMSwxwkLYzwzB/Te28QnFZOkkOCMUg6b1YjepKmtGlQlyZSjPi2O9kaSY0XbS6Xy65YXarb0bq6LxX09LdlRnwJFOzfcDlcislkvaJX830lNtDRrLi8gCwNGAwSIu2Lcq2pUMBmw9qFnyZSLgrWBPodnvkZ6vyjzM3a2eO168/mYE/CmcBkDuNZjJWwNIu+1LLURGoqJfclUaS/FM4kcEsO1+YEYfeXcW0bjqSxO46dCnaFsaGws8DXxzHBpzgflzDunnTzjQo2h7FEweICAaJACzPIAV9aa3JGbQpRTp8v1FdItfxBxXK+vszOUUlgIzHkul6bM7pew02mhsbBe5Ou9lBqcYIkKFYtDCyZSPWvaauu70+0VnpuW+tZclPRpVlDq4tNY53xL9Qybm845NKU5EhXev2yttX9yfzozsG1R/sXbghU/BMDgd6mQCTWMSbQwNKZ7+IVfpxgzC21ix/z44wLZ504q+CsA2ftOGMDTawn1DMqv8OJ2yUc5zhuwLQNx033m/hRFYc4DhqxV8GkHVvoz4AJC8atWGLAEhNGzOg2E/ALyGYfzaZ/QqYgOi05MSgJEqSIPulkdxL/1n5R3pmKBYzbDaFsa6DlsmZutzX767/z4D28a+RtK6NjK1YkA8k1Wy5q2XC6BQLv95Bc/I40qZPw4iIEal94OIiHI3hvCI/Q+JFxTsLBFlwbwYEgHCHYw3goiMcj+ADd4jgr4ZyM00Ec78Vt3bhjANMR3BrEsYWYNmO6CbfacVLBdBKHm3BwIY7q5Rzw4/AADkdxqCoU0dV2HE7iMu0aje+wY0o5ovCt7n1uvkHDJhvGSEdpGLWhbxjNEorDCA7AwzplB0v7wj7VZyuXC1bV1tfHYgYrs9oLcswuhIyUlW5azEVs6JU/PVb/qBzmFakB0aRZuEbm0CA854GY7lbMYRmvGPKk+R8NuRYt9BdirrWLBleoI329I6Fi2bug67Lc0LpSee1A0pfriPYuyPavyJdGK5FAvhKO9rVG39MccedjjnC6FB0YXYi7W7u728LFuM+SGKp/p22sGoq0jxSS3S0JW2htuXUo7/YVelKJaj5mVSK5DjHiW2eeI4k9x1TWwz5Ru+t+B+5TcJ8bJwo46MaSNBb7ca8XJ1I4kcWJHKYLuKMZR/Mn8/xoAbu82K0d1I5r92vyZB5TzUea+WTL4ZYTLdLuLuzuxGQFm72YkLHJisVWLJMmJL5cxhLjJiPv0lDMophBXkPQg6CGJmaTDaxDNittssuhY8Y/rTnnGICOVj4Pj3XYKPSBQBb5xgigP+MFOPJRT7ocG1g63JseTBvlTaOD6TVd3mgo7M47A16fsuhwzuA09ow0j3XG0gPr2qpruiN4rLCoLTQxtnh1MBKK+Te+fyTX7bfYzDIvc9loNkYvXmyOu/Lx7pUt5XUDqfyizUTjNTO/Zt/Ex8hG8LPD//1WwtPMTqa+kUx993MXWAzP0nj16mnn66wH13npb+bD0bxuQtA5H4mQRZEXxgWp/Ei+IKwMISeFmV/gCBd9KDBvzR7nOc1j0WS7PaIje1LSWY2oK2buasWnW5JGfzrXNQf4jA2854NCVRREuDySTRkXBxcp7pIzHnZV85mhbDK9s33xxu5ogerN5/GP2Z5CwNB9Rd/uouILpkhd+f3d4cQaao+BGtXDn6CUlf1H7f1Rhpv0NfA2htMWnDXjQRPutWKNBWusWGTBsBUJS9nCU9aKlfstMAJGM34AfFPGCwZ8S8K3jTgj4YyM+w14hxHvAM6QdjbhOHCLFbdYcIjhkBm7gEmOPcZDRr7HhL3SdRLfa0XRgrwBBRk+K0IWWCxkLnL5MIPVZ/JZNLNmlUxGZpQko1TSly90M2BoaNufFO+r5445Wb8aUtWTJzggVpwil39j0/AMW7j+B/v+he+uP1n/yde/zp94dR1JwQRRKEwUsrEw62YP167KdiDTiWwFD3fhlBUnlDMKP+nEXe24u4LDHEcUXN+OKQlbFWx1YqIdxXYYYzC2wFjBxgoqXhIZD8+bkTWaTFyTM76sL6+RJNhzcptil832rByhcbuTYWeEuiUWcvRullQdCqrPUd+3vVmPma49hflXgquhO/W0V4Cg7i6PGMuSfou0aQw6XoYAQp9rzSqF0OF7VgymSmklHdp+KJIPu82263qqSihTzX14R3VRc9TRWav1J4quVOTvWkbMkkwUwzXFPovM/9mg+F3B+vd7t2oGs4HzJyAbZfemzq6VHqOqhHQbf2Dm19xLsp9lX63d8wzD48D9bmEcn3YJY3HahNvimHbjpAu3ZXBQxWEPDmuYymDSjY0qNrgwDkwwLHNjuQtLMuhm6Myg142QC+834Iwb5zx4ICNg93G/KOeICYdjOBjHYTd2BURRVQavH5oNWpysEckLRfIpWlixzFpgb46TXm9Vnn9kdRslPzcZ6UtkcyBqzvr2c2/TZdWevYXmXGnl1GBlS7o5viA3sCHoVKz20I7+3FCyuSUcbW2pHF+54pZLOqPhTr820JfetKAyHBse8vq7aYQKSaySJLqJgpfXlp8w4KwBh2UckXCESMMnCRe6Jl28JKMkoTmDJicKKgpeRCUEVVh5xIJo2BfxxbSoZiWVY7HPgcQLVmJf33fM2lwuXYpmp4TzxAgvBlOuVL45vG5duNKSU/z+8VLa31odyHzkRH6g0hrs78WKlVNFLegwvPpt2RkNFC6rbb0pF0xqZv6EZPO6m06LdTsSkn/SR1pPLWnhzMwNssxhI8Ofa9CsFpPZIsmyA7JFn9Ro9D83pwBmTeeqK2lKeqvetDfpncAhW/1HP/0pWvkTI98f/a9RNlvH31EdFtZRixvAZXAz4xbjrHqRDUajwyQzwGEWNhgNuJfmKSN0jF64K+1K4qn6b196aT0psanvL6o/S0XOSniWP8Xy7FjtouNRHCN8FsZJLzpV5CLIhuEP41kFt8o4ZsdRG44SMFWnVF5Ue9UxVcqEkTPC5815OT2Y4Vk5AckDh+RzaGoml0mnbTk5oJj1zjeWdeca9mbCqkumLphd3TSdzAlmY1WI7KLuznS+6uLZ7J7OW9c0Fzu6lu67YnW2aVFhYMIbDvrtmeZKYLh5Wv1RMRDZVH7r+I47y8Mj1WigZ2Dl9pGplCvssYb/caT+qQZdeVTn3apa2WwC5zDJgnOST/ZZNYtm1Aya2WK1W2ST0Wg3yFyS7LIMZhPwUX1u/k+n9pyue67cnnQlidzEVwr4Sv3lNWuQXIcV9Y/yJ+rfR5xUdKN29jjVLrFwTZVm5YXbyUyzs7ldBiEcVdfEGl2tz46mlP6Ohb23tv9RE+4z4J0mvNuI0zR5WXCGMLURd5rxVhmHDbjJiMMW3GjGfhm7DNhtwS4Z42ZslnGRESuNWGrACjOWWtBvRMgspq+QASEjDD6jT7RJyJdol4lzEmGpIcKv9fdPomXRfA/SFlQn1m+He7L+e5gnRW9wef009mHRnHx/nHplYMVawMAZjR+xwiRRzQaiBpdlooqQnvPGexGCvniy/vg6OKnAd1A5w0QdMRazkGofvzmBm8O4OY6rIjdH3haR0hkE4whGELMhZse9YZxL4WwS77ThgQQeiOM2K6YdOBY6HeLTKRxO49oQjqawN4PdIexNYQv2gm/JYpN3t5ePujEa2BzYE5B6pKUS75HRY0CfDU1BFDI9maUZqWBDIAB/BpEggqS57bAEYbUhS1S1+ew+n8/vy2hpLagFtIiP2X1MSJ5fyjJpzjKZ2wP5M2n9OqWflFqldoJiUlpqRXtZTxFsiFGaFCKlOOF8DsDkMN675A6Xi3MJudUD192gOCSTxfjWw/34rVrMBHiiw1dn+FHfilhTi1oPkBDXKks88aIXXySxxLxURtnf1U7WnPCr8LrwPoYzwH0q7nXjtAf3OvEAw80evNWNYyrulnFSOifx6yVc6xHmxlGcBN+titlyq4wNHmx0YpxhtROL1Y0qF9bDlCQtdm9073JLARUBJ6JEMLvP4YtoYc2pKVqUEhFHWFYcBrvd4ZD10ahHf67Asjm6lck889AQ9sQoTWaDRAP5qy+5Il67JEnyxkv+1RFwWSkprcV74/1tkWTXomT9GqLMslC1GMoOLM/iANGGk3b9Jd/BnyUcVmX31vZ+PoPPK3gmhhNmHPcJQhzP0HR4QuYHXQI9fFPBCxK+mUBvEzrb0J0Qi+09BBpiyJiRL6JJRpMkwFnJj6yKrBP5LDSkjGhO+Zo1u5KXI7Mr7n8QGJyPDF5bd5+d+iXdgpIaFpTACLk5m4kmTW5SXe7lA+XVmtsV0KyJfLO3acjv8NiNF5U7cuu6hran1+WW7B5qWdyRsydiqK2YyuR6StnU1tqqG9Oke22xqJqrDHftOFZOx9sKF//Vulu2tnuTBXf56Jx+M5Ik2VmEbastOB7BUY4p2xEbb6wg2bCCY7mNLGqzSSNlbTByv5eGUkDza5rXa/fpitovOxqLSPO6WX3luVnVjPl+z+PN7q5GZylHHCcs5MP7C96B9s3XNre60lsjqfSqzuvW/G3PwsgWvLg85VbrP3Vp+WWdq+Nxf/1n/AnF3cd0Th/jH9U5vZTtrY0dG8DxBTjehRMl3DqEmztxqowDbqGVD1rROYROwuMLkBtCsam3iec6USz3lrnGCmZUCr6Kpigtct+fYmUD5bweuQ1KHt3apVv513GwseSu42fxJDe7XOZQtj1ZXql53EFNW9vSNEgMdRjf7i6qmXTC7o4mcpHaRfH1gq+FRV1FJRFv7/D6vVVNaYsrfllnsisVVpszDTanCtGYWqjU6gctNifBcbfDbuhIX/JhwWp3vOirHF6/49Z2j2KK2syaxOf4rhDfFdbBPlK76UUVZ+jPibdXcaYdt3Xc18FvreLWdhwv4w5SFx042I6DFR0hd2BXBZdWsbOMffwoP8mlKfsR+7RdmuAYt2NTGRtbdrXwDa3YWELJ7Pczs4mHqj6nL+AL+sJaSPMHAo6gbDaZHCE567hQcgTpX3rlzVb738CGOXEahIfo3oq8Lk6C6pJY6ilBUF32475c1FWMrB5r7dNcbrc51tKX0uLBdQ+lu4pxuz3gcYTKi4rWQMpmyZVoPsEvU312k2mGudxqZ73N1b14bXPQjfqX+RNGu2q1hDMt4aYl7WGtpNi7a0NZrq/t/5Kb+CdYBZ7aF2iSu6dZbAlNp8+m+bSGuzw46ceJIE4GcJBwuEcs4F3VjKtK2O/H4aJYeNsjYW8Rm1Vs0bDFvNd8nVla4sKiZrEd0GseM/NMCxLNSHlpQkOOzMW0WM0h2fWmEDTDl4AvCWM7zCkYrbivDXdpuMOMI6pA/jSoqdJ9pOxVdLUjE0AhBXMEsv58HnEHjL64lnIza6ipSQnJTHHPr9cRW950MPwxc+fCIzuv27obo6Crs6MNDY8BMVAIc0qNJZI4YhI3tbemlpYH91UzpfGOfNnXfnSx8BZI9K2rXHFZJtiUz/uGl3YuWC2cBj5T3hL1abFELR5xuLILtma9SsjburB5aUd0Yb/qU4wDnU0VLTjnKaBR9CLJvYmVayHZwI1gxDdCXiaBPWWJESa1S2Iie+k1UE/mtFBkAtGnO5P4xrOrn8bn+eSr7+CTYsd948yveITKTLCra8t3ufEIx5kgzoZwj4aTIdym4UgIkyHs0K7V+C4PdrthlGDgsJjh54LuMZpLHVo05lTsDvqTWTxuj80C3tdm0zczul6jLAF4AmivTSh6lke88cTGoTWXRBTjWoPJ4Em0JkqDqsuy9vODi0PhULka7Pfj3+uVUK9qC2h2n9ffnsZ36wFBKyvpiBZdR3ys9tabzbjWjA0WPGAXO3oPcLzbgUeNeK8BDyl4yIGTdgGBb5ZwSJ5dUp9kuJLjSgnrFbEzukHCRi7W2dc7sNmMTSYskTEqoY2jTUKgsckp6CBgKCEi1QGvAguYxIktDYy2bVuJ/ZngTEcWaUkACuGH4Kli4dofjb/dpNhMZNC7791A2GGJv62UNDmW9eCzDdmQCGuxJv7tZ5kqnBpKz7ImVtTPUbpSpBaI1cTobArzKeP83ch8ykGpiJ5S5lPe+VRq9l32DFWgDT1DFYnYqceqHkf0OKrHCvNR7NBj6DGjmIhQMzBFVfi2mmRwgWLuEbHkptjgZHEm7tgK4potr8c5ERsdIrZAj6MiNsdE7NLLcIu3pZj+VlR/K6jfDXE91p/3RETsDYvYZxdxr/5Mn/5Mn/5Mh/5Mt/5Mp/5Ml1VP2/RnJP0tWb+iiPZ2aCMatdfUy8bYOJPoSg+Wgq4Yliib9D6O6u0ey4p4aVHEo6qIl+g1jep1jOilj+ktGNPrGNPbtFV/d2tQxON6myZC+pWAiDfqtNig92FDXMTrEyLe4tSv6FTd3KQ/r1Nwl06jSb203fqTUzqlJvUad4o2GI4EpgOi9UeC00HR+gP6c0f01tyg9/uATo+79Tqm9TeP6a0/qd+9Rb97XK/pmN6mW/QSTug1PaC/dVZQTrqfific3qvTeh/eSW1lJAgWXRwMImZGiBEhwHbDSmmA7YZtMp/iR/dttCgW2WBxmDdMPbxls0mxGk02xbR12yUWh0U22pzmjetwQ3G0M5boWJyp30AD6crUsgWFQv+SGM7VN+VH2iPZgRV53CW0iFg79gibH9Xa93eYsNGEsglpE3wm3GvEu81iVY/slnNkvVjF1tV9NuELMm04a+DTZtxheaeF32rFrTYyX3CjAdebxXrd9fIpmR/iIGvmKMNh4JANkyZcacblFlxmxC5CsQxbLNhqxnojNljFvtUGUj/yXpmPWrHCglVmjBgxasAYwyjHmIwuI3rM6LWgYELJiixBfDNKlLWhABQYfDL8BljNsFlgtsFibSw70fQhzHaxePHnG+p/6mjMrkkb0h6p6u+uSh48+fh7Lv3KU9tP3HrpB15Af/1zuAqD9Xr9i/VphpnfMDGTPUEaZHGtOK2AROiQ83YnB7VP2GoGTdYUp5NZJGZwyoQCAQdvWGwvNTz9XrlAtQpsLiSE+8tdQnsWNn5n1y0WFyEN57GLYav/GireVf+9J6uhLQYjcbpA7H6eWuBl76tdY3DAynHSgFMy7pRwq4JbnLhFxU3EMBk3GHCNgqucuEoFjZjdCnY5sUvFVgnjMsYNYsrY4MRaFd1OdCvoVmFUYCQzlzOVU8k2A7eL+cKu2TQHs0JMEPMzpb76/YdMsfMJXEQnBqE7RXQnvdCdIqpJfLI+iawWyCdmmB+31//FFcz4oQ7h3xIpLF5Sj0ez0H37Z47hVzNPMYl5dA+wZ3X//zn/MW+SoMKv6vbrhZMr6575n/g6ZkiP97F/rz16tiRWZaeNYoHvoB3XOm518ENuHHDhsBE7HUgZIPxgIkhEkYgj2CKWnd4awbQHJ1RMO3GnCyfduN6Jm1y43o39EexzYsqFKTf2tmBRO/oCKEVQNKBoRJMZbWFk2zvbudYOrYxwoBTgLfG2PNqsxC4Wl72+NrWzozlkdjAzgXIW77DFZwGI7mHScA5Q/yQufwMmzApbV+FejUDJEOF05Bs+F42tq/bOWWtYuCh16d4WThhJ5nCp1W1rW1hwb8otdEnLQ2lTZrjUOph1Tt+oegw2e8LvbS1l7fmdV0wlzDJkcIuqvAqosZbI1lL6lvrPzflQJh4qVIMbL0Y6mbQrDqvLa+tZtErrSb03Vgi7TYIvy2d+hd/wf2IusoOura08YcMdVhyx4YQkqEyI/QYnDmsnNE4g+nDoRIiTtE6QkDonnVyu0gjyG3k55ouSDVtWkymrpbmpWLSnZPesVUPHeUL5xhXY11YDXAIZkwbWt65zRCVTWmC4jlx7t/De0Yztsw4V73aoSjWaHdG0kXSoEvFujnSMteRXerlsXmU2OVY5eHZhoWW0EoahMujT/NnIDAulAoWVfQM7luRzEdXvdHdX8ZP2TueC3v2eTCaeXnjx7Lo0/oJkNM5O16ZO+XEqgCPAQSeOkI1iPWHl+0PHQnxfQCzPjfsn/XyxCysYVgAdTlRUVFzCp6yJJJwnVGRdeNCFO2O424VJF7bEsNWFlS6EYogwgnhiuVoNigX0N1rbb2pZvG6d2nf+QnW10u3HX6SXtO3rCPrJUFjZNhguhQrRaFWJDA0MtpS60/HlmY096y5L9CV8XT53LNRx5cYtXZXGOsL/wg/5i4T/2tmB2ooDNhxO4GgcB7M4COxnuK4FkwbQnD5hwEYb9gdAzxyUccBAKiMsId3u8/rCvrTa2uKh4ROTWa5Qam6xNcsNCbhQM8328/w+QvTkwgUFtPvLOX2skI3Ure/m+jtotCyAX4B8Msp7WxYk3IrZ7iqn48WAqihWhBc0b5/aExDmqR+rTGbcHgp1RAc7m2u+eNjpsgVvimvx8LHrHhp1KckYt1pzrn90vPr88DJfgw6/xv8g/ltYnl1X20BKOe9BzouchrOexz3Per7sedHzE8/vPaaz2uPas9qXtRe1n2i/10yEXI7GsNuEvNUFv5EEyGxlZtnvi6selsvlzVYrmZT52SGhaxR23jblHOPZLCn0AWGqmuaoIRyKGpvd+v5ETIqCBNNmc5SjVxj/9V+tDu9wbrQaS24u7z0QSfRPrO9QfQ4j8sUOtxY4129Pd2mJaFsllrjz+qHNucVdrky51mITEt9E0Wb+PAuyZbXWohclD6yekIdbvLAzp2xhRrII/bLVZ/OpTrvDbjNbDEaL1Wq3NTjb2Ft6TteO874TOjOjqJoa9mJVd0IRDUefUtBGpgPXrCuXbS6POd25sJnfbHZuWdlfH8PH+ldFClGPMRQVvGiZWYJX8QrxYhsKtZdCSxFegvBiREah0QDKYmAUA1mcGsWxBAgbnqR0ATeM4i2j2J7Ajjguz+KiUaxLgNDsRGGqwMcKqMQXxnk1sSjBl2e3ZvlwK2rtaG8ZbuHlVrRXhiu8vR3lcq3My221Nt7kET4jpQxc8WSca4lMgreOItWCVCtSZSTb4G+Gt5KtcE8ZWnumnXtboLXC04Yc65aHEJX9JAcSNrT5Nqgle6USGks7+vqYK+QYkw3KatnV2FZ8zSmo4d8qaPlHZu4/dED4PjS0ga4rfN6GdSwGlvB1bKzGzc4+QxCXG8Nt1vFPOG2lZiXtNa8hfWcWy5oqF5fLEx/x5/xeRyyf9rSFq/nCmuZOfzS0b2v/umrAYFhnsETdnbG+obDSljXbrD6r1djSnq9WF4TLI8VILp7wa3F3GVuq7XvK1eddDmOurWnMZ7e6w46AXdU6E2tXaKnWsBJwmsxBe8FtsxfamtyEN3OpWF5VV1Wygy0Bi9VitZdJeleS9H6d5q4U21zr/gqhJcIK0XK0FpXcUTJNwSIw8XBqdvsjTNOT5vVbHXarzWa3y2ApGztvjWF+ohoSKqpB0KTrtekp/fqpSCenC8fNNkvMk1mZWEVTj5TaUG5ZajNI2BJJ2yy25kL9XXx3Y6YJNCfamtPOWLh+Tkj4KGGlT/MvCf95dmVt5YcUfMiODzmwRr5Uvkq+WZZrsphX0nJV5m5qrYxfyfi6/C8yf6/8UfmzsmQyOWR9F0+1gpv0TdrXNvMu2M+ZHZkNzw6hZaqubpfnPA+aibW+dCiUFgE/d9QlfooywVAmTe0UVP4dUdlC7XygNrmCcK0dDgfe7sA9TrxNwa0OHHPiNgUHHDhixUEFe6y41ClWQHbqKHaLAxN2bFSEq3qvFTknAnYEHLA5oBAz7Fanz+qzUE8cql0laoJZGtudDc0ijkZf/rjTd3WWQV5XVequmjwTxI8f/+BvL/vsf85y4NVzCL3wQv2HQvOtInvhZf4tFmCBmt3DJJ/qsphd4iuYxlxFItA9N9/Ol9v4rsP/stOjZn12u32Vw+biZhUm2WtLhrmq11KXol5e1f0zic1fpPmkxA7WVo8ZsSyLpRnhmbm4iMUZdBTRkYG3mC1ybyab4QpPSjxfIjqYfGaf2+fx5dWkykso2dk7TTDZYRbWotnttnnErvu2ba9se+mVbfMW14UYgjXEd15087nzpDjGvcIV3OfX5djr4tfVUv351UazKxXtzy93r4DZYk/li57ssJLZlOtZjKvGipn6aVzmj9m0eLp+Crd0dbihZVxK2OsoFXItywfqd1CfhfvAF1FnCba+1jESRSWCTARaNBPlhog3wg1RmHkoQZ1UfE6fpobUeMLOVKdDAVMUm1N0TB+O6iuvLYfP94o61MrzDY/+17qigyHqBQ5z2WRwZ53ZbMaxwiM6sXJNcaHTjrVayO0wem2ekFK/Ezd3Vb3QstrKQjIcrx/XrZNf4l85yBTIsQ213n0JTMZxWRo7s8IfZncQu0LYF8FkFBvNmOCY8AhxVhEmMyXsS6o2wjy+P7Zz8iY+w3OfmswOQeEgctKv9seTPQ673RXpTuR7HGbTeKJ3Vdueq67eMbQu5Pf/dE9bMh4N+gdbOje3hvMZf7k0eEktedfRIycr2dQE9WT1TBm/JJlTSeqmaxdPBzEZPBzk+yyYCGIiNBXiExbcIYuvMMhGPuHACQ1HJRyhWMENDkzRWJWmJD7uwDgZnEXYCNDwnJ86mVMz5mjEbAmFw/bIrMdkQ202FOcfcIuZ7X9Dj5q8F/hLCqDvNc2NrzkyPGS32Sux5X29w+leh8263uMNr2rPjYVW2dYYBjfmRgOR8M9blvtD0SX/NZyKBAY6Blalmtvi4f9w8k29FSXVNJaIpS9uILpOna8+sm5urm3e3yI+rjmcx36nML9JO00asaMVUz6x97tZxSZFfHmysQUbW7GB7WR8PIpxL8Z9CMc9iEu8FPdV1JIaCtscWZn5opGwLdKAQqXXiKC+9KazN2YlQNKddBsw11M2el0ml3HONaprDukSZKpiWhvNJLqcRouRmx0ui5qL5oa0Cefyng3prmC25AtGVrQURxJckvhK7E/kE1F7KJYNFruzfotTKWSxb3jo1a+rkIY93vilK5ZtTeWaUhmDU3WKj/6ZcCJfRdQJEPorv5/jAxLOsscZP8vFAtQiDzo96JBHaPpRzKSfFdWruk3Cv8hst8jnr8yf32HkGkh+fnjqfvC+drRa7W0Zl32VaxXMZoSX5wgrv0/OuOLx+hdxczcNSW9ey7UY9K/hZv4Tj3ADK7Knarc9o+LjTjziwCNF3Bd9JMrPmd5v4ied55z8hAmHndgZ3R89FpU2RjFhmjIdMUljJiwzo8+zzMMz/g7/iF8iYJstImvptPBiFIEoPlHA+2M4E8N0TGD3qRjGY0iykJvlZRsz+EJqTGEWbyZj98rMrlywF1R6vZ5945GdtWK7fTq6yonNngsd3/S9Hh8eCVcWN5W352Ir17Z250uDg7VQ23A+vzYRTfYnS9VEYeFtpzMDpUDY39fWWkjlLQ7H0o5UT8Hvc/epWks2mjYrEaJYcObnyPNbWJK11agTXgIMCdnmS8Dr9rhdbuIyYHPrn4QJvr3U4J2O2dONea3bPyeFArRTW2lMKtxbhXNvOOvy2my5ihZXQr7hYZK3dZIs3YCP1T/ZlDAYKhpBkXIOm/rdzbmQO9RUDeq7Qj/FT/Eqi7KRWmmThBUmLDDpa4emsKlkkjQYfAGa8ENBfyBoC8iKw2anv9f5UMxTWnyaOt/Abte8odhI45lgwNC7q7xINRlXOlz2hCu7RFvp9Hi1dHBFLqnkfHiLs342nbWbLe1FbHfU72trc4k9npn/wuf0dal31CbfIb1P4tda8D6ODxrwmBEfZPhL4C9lPGbHYwoe0z/muk/COQcetOOQEQel4xLf5cDlHOuVHQrf7MAWCdu4gGtEcUkBHI19HZWrCjOB8cZSXeOzYvanVkpJugxpqdtDs1y1sQxpXfmRNftNNiO3269Zb8AvXfXHtKKPV1JYLkaOc+YV/i7qT4b9vvZh1QfVC9WFiB2PpvGoH9NhAdT2KzicPpHmkyHsjWIyjKkI9gJ7OfYGsUch7IrNGQHbtgTE4vCYDb0u9KnI+7v9vMmGIkNUFYZ/1IyIjKgNIbvYQo07EVcQd0EoSy/iPsStsNhgcsJMWoYLQggonlHTakD1qw41QNOLDbINNvv8Htfr/ZD+zz+QMOgOSGlNgVjl7xYeSA3q6XTs0HfDuGvlDpOJS0a+c2x61RqzVbKYxzdulqwmbjDz1etkXByMwRU019+FX7jqH0uXW8IYrf+1I+Pj3J+yYnXjX5/gS/gFs0Gq/c3NJvE1zWGgZkIv4Dfhfo77jZi24qQZJ2Qcs4mV4DtMuAO4yoj9BhzRXeUOM+GgcciEQxDrLJMyJm3Yx7CLH+C8sYS/xohNBmy2iS8bNgObOBbLGDNguRWjFozYsJRjhYSqEV1Al75036Qv3QesBSv3GRE3I25BiDjFhL/4/LK9alaNqkH9v1u2n9WBybwp7cmLtXqTB1O3337Rs2cv3XHp1tOffP55aN999NHv1v9dH3FVfIxGQYJ9oHZDIYBCGGet2GbHVpvwBd9Gg8khvgUVH91MK9K0HSfiwqfwkAeTVqGop+JH4nxLHFut2BrDmBVjMSyLYjRBI0CUVgiBiiWl6HGBa26NcxessVCMKwzmBOy+uJpQ3Qm3VGJtf2aPxTCc/bTD1CrlhUhJ7VV9uwh2ezTXnvQHUiGvNcHtimrct3K30WoR+9tO42Xj7tyCcsLcafKlOnIGe0tH1YOeUP0T7lI2xBNVDaO6HBkYroy/YLzEOfBrFpR+LCTrqZXN+v/T+dv6PX81s2+mLJ+VHnzDf9d5cKbMuuTP0P0p+ez8P+OZO37Ml4j/MUGP/oqiH+r/tWcN7mYTvJnFeC+rSGtYgT/MBtgrbAL9Isz8M/6erqmsggAbQJrObrYEn2ADvMwqFNZTWEVhcDZ/KYVhCksp9FBoRY7q6KKwgGn8LfT+ICvgA6wg3U1lLGAGvpnqr1L6SqqvTuFDlD9A+UOUlik4Zuo8RefPsQkpQmVfT/ffTfdDs2e6J9qOz1P5V7ONvJtZpWZKB5gBr878Bq9QfR56/zTT6Kzxa6gf1H9cTO01sW5eYcvxFSqnna7PUMiyJvyOtfAWthIGNkptcFJ+JSS2SkpRv1JslIfpvTa2msIA+tgYvkX1d7EgT7ON+Adm5R+kd75EdXyaWSkUMMwK7DfUhmGqf4TqupWelyn0zfwWX6Z2fJdo+jV67mUKb2cx+TNEc9GnDjYg7aE6ZD1UqMwBChXO2Ro+RvxqpmtvZZr0TRbT29BB10ZZN/X7tzpvRb+K9DzVx13Ec2oD0W2AgF1FupTOf0Hv/Aed/20230c0uofaIMq6iM7bKQg6Fem91VRHmsoM0L1+uv6o/m5FEry8l8K3dL5WdJ6+SZD2CU98qqM+y+e5IM98nfo2SOdvU/gxyYFxnsevD77Z64LP5wXRX10eupmq8/RNguQiunxllsfnB8PMfxCf43R+kcKveEGXqQaPXx+2UP/Emfh8fhB8JnlZrp+bWBO938R+QWWniCYPE90EzYivVM8EsjP79PTnmJ9kKqDLB8mo4CdxqOW8373I6L9B7Mfj4scNPMAv5vv576Rt0g/lQ/JPDDcZ48aTpuvMTeZT87/nzc9bdlmt1sdtIdsK21Hb7+zd9rX0+xv79xwPKE3Kp51x5yfUXa5e1w7XP7hHPb/QHtV+5L1k/nfAe8A37PuCf5H/C4Fa4M7gu4P10Dr6fSL0s/Bk+LORYuTJ6FD0VCwbe0vs0/Fi/HuJI0n5///e7NfQy+zH7BJm0nUw12NNJPi3RVLslX6czqq4VSp9gpnZctZP8hNkZspZ2BLWNZ+zsmGSkhjz6Dkbaeq2+Zyd9Z6Xc7BOemsup9Bb+fmck5VZdj7HLqgPF9TH9frmcpJe31xOpvqaad7W9JxBr28uZ9Trm8uZ9PoaOeFKZGCmEvvfvPH/vwplbmRzdHJlYW0KZW5kb2JqCjIzIDAgb2JqCjw8L0Rlc2NlbnQgLTIzNS9DYXBIZWlnaHQgNzAwL1N0ZW1WIDgwL1R5cGUvRm9udERlc2NyaXB0b3IvRm9udEZpbGUyIDIyIDAgUi9GbGFncyAzMi9Gb250QkJveFstMTQ5IC0yMDggMTE3OCA5MjVdL0ZvbnROYW1lL1pFRVNMVytNZXRhTEZCTm9ybWFsL0l0YWxpY0FuZ2xlIDAvQXNjZW50IDkzMj4+CmVuZG9iago1IDAgb2JqCjw8L1N1YnR5cGUvVHJ1ZVR5cGUvRmlyc3RDaGFyIDMyL1R5cGUvRm9udC9CYXNlRm9udC9aRUVTTFcrTWV0YUxGQk5vcm1hbC9Gb250RGVzY3JpcHRvciAyMyAwIFIvRW5jb2Rpbmc8PC9UeXBlL0VuY29kaW5nL0RpZmZlcmVuY2VzWzMyL3NwYWNlIDM1L251bWJlcnNpZ24vZG9sbGFyIDM5L3F1b3Rlc2luZ2xlL3BhcmVubGVmdC9wYXJlbnJpZ2h0IDQ0L2NvbW1hL2h5cGhlbi9wZXJpb2Qvc2xhc2gvemVyby9vbmUvdHdvL3RocmVlL2ZvdXIvZml2ZS9zaXgvc2V2ZW4vZWlnaHQvbmluZS9jb2xvbiA2My9xdWVzdGlvbiA2NS9BL0IvQy9EL0UvRi9HL0gvSSA3NS9LL0wvTS9OL08vUC9RL1IvUy9UL1UvVi9XL1gvWS9aIDk1L3VuZGVyc2NvcmUgOTcvYS9iL2MvZC9lL2YvZy9oL2kgMTA3L2svbC9tL24vby9wL3Evci9zL3QvdS92L3cveC95XT4+L0xhc3RDaGFyIDEyMS9XaWR0aHNbMjMwIDAgMCA1NTYgNTY5IDAgMCAyNTIgMzE1IDMxNSAwIDAgMjgwIDI5MSAyNzQgNDQ1IDU2MCA1NjAgNTYwIDU2MCA1NjAgNTYwIDU2MCA1NjAgNTYwIDU2MCAyOTQgMCAwIDAgMCA0MjggMCA1NzMgNTk5IDU2MyA2MjMgNTEwIDQzMSA2MjkgNjMxIDI2MCAwIDU0NiA0NTMgNzk2IDYzNiA2NzEgNTM4IDY3MSA1NDYgNTY5IDQ1NCA2MjUgNTY3IDgxMiA1MzYgNTA5IDUwOCAwIDAgMCAwIDUwMCAwIDUxOSA1NjIgNDU5IDU2MSA1MTEgMjkzIDUwNCA1NTcgMjYxIDAgNTA0IDI1OSA4MDIgNTQ2IDUzOSA1NTkgNTU5IDMxNCA0NzEgMzEwIDU0NyA0NjMgNjg1IDQ1NyA0NDldPj4KZW5kb2JqCjI0IDAgb2JqCjw8L0xlbmd0aDEgODY5Mi9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDU5ODY+PnN0cmVhbQp4nO05CXtc1XXn3Pdm1+xv9pk3+75oNKOZ0T5jW5ItS16wZFuSLSwbZMsblg2WLROWECDgLCxtQguEOGlJQ8hiSEqcQAItJUAogZC0QGK2pCR8CeRrQ0I/wGh63tNIGAfyCzpvzrv33PfuvWc/584AAoAWrgQOAhfs3z5z+wvPf5xGvg7A9l0we0lA+2fFbQCck8Z6d87s2v/4iXskfCsADu/aN7ez9N3rnwXgTwLY/jA9tf3C5y6pNwG4T9D75WkaUP2UTxL+HOGR6f2XHP2xOvIS4WdojRP7Dlyw/cl3HqP9xNtpvWf2bz86w95hxwACMyDdDk3N/ObmfQ7q3kjPL6AxBhK9AALRSz10EyihMUgtRzcOPvihhxyvUKrUGq2uSW8wmswWq2CzO5wut8frE/2BYCgcicbiiWQqncnmmvMthWJrqVxpa+/o7OruqdaWLV/R29e/ctXA6sGhNWvXrT9vw/DIxk2bR8fGt2ydOH/b5HbYARdcOLVz1/TuPXv37Zf2vOjAzMFDF19yePbI0TmAY3Dpxy67/IorP37VJ66+5lrTJ6+7/vinPv2Zz95w4003/83ffu7zt/zd39962+1fuOOLJ770Ze4f7/zKP331rq/d/fVvfPNbJ++599vf+ef7vnvqe9+//4Ef/PDBh/7lXx/+t0d+9Ohjj//4iX9/8idPPf3TZ3728//4z2efAx430K49EAAeRLic9PlpuB1OkB6/AXVsweU4jNvxKF6ON7PH2Qvc5dwDASEQCsQC+UBH4OvBUMgWcoUeeZfV65Lkaf5VcBN8Eb5E879J85fhBpyk+ZfR/B+x5+T51oBTnt8uz7fI87Fer/8aoP5K/QW6/4Lg+frJ+pclkdQZwPwzUu/MiTPPvDwJ8HLt5Q6Al+57aQTgxSMvPKF4TlZkl6y1tgZInwK00n0BOhpjPfJ9rXwfWtL1DpI1BybYD3OwF47Ab+BV2A0v05Pr4JdwGl6Al+AwvAgXwQGQ7GkGDsIUtbtgGg7BPjgKv5XX+RWsh40wDtvgQpp/EVxMTy4jmXzyo8bxbRCYA1zsXrJNBQj15SCeAsgMngLN+tF7ED87dgrr15yCq33fAw1w287PngLMBAJ9u3tP4iQhLEMDqSD1uEyg/yQX7d8wGh4LHA8cH7jweKA/ML39wpN8VG7pwdTxsebASRge3U33kdHgydqYZ6k7NTbWQevw0jq8vM7xMVphT2OFPfIKtMB79JIiMxg4ycXWj543evLKXs/JWu+YJxgM9J18aP3oyYd6PcGxMXpLuUQptZftdjZoVhHNyhR11AurDNMatMTY8eMNjMWCJx86ftxznDiRR8LBUwiNAeJUeoeL9p3C2nr5US0c9EgD4WA4SHSM9dLamszg8GgfURIcy0oRoEA3gX2LpKwBbY1TiBw0V/MtQSsX5jxMh3jeww9PPvhg9uqrr8RR3Dx/1/xX2Zb59+bPIE/2VYAAizEn6CBTs2s1nBI0CqXCrVGqlIwDjjMp+bTpadNpup6G6ukzT0tIviVaLIVLYVvYVrQVcdujj+6k72vTjz02/Zi0Zn03XsveIcuz1rQcwybGQxPHp9PQTNHEgWHyO/Wa+bdv4W46c4A4yNX/hzF2AmyQgjW18hE1HtHjRg5Xx3E13QVc7cJyuD/Myr5+H2t3YXscPWBWYJQ3iwGblgfBLq1eTZvO5Ftw4v0PtuZYOGRgNkHki4UeFhdUBgyHYvFYqbXSg8WC3SEiY47K+IrCxmpYa48U+zKtm6uhY1ZDPhAoGAwOQ9YXLBjUKlxdnptaXhy/bCCybs3KUqAwfsXQdDXbUuzq3ravmsjkuwKriPduCrDvEC8p6K4l+sObwszmiDqYwoq8A+cSuDs0F2I2zssjA9GVUvhtJj7VnDZb2qF6Ji1TDxLlwUo4pFT1sCr2IFFakhlRqhwy0TbBwFQGTmXDXzmfdLcZeYtD7/a1C+mit9ifWnGo0hboz6Q69GJLVEin48YtjHFGu94ttruKu1uSfUVvS36ZK1Eq2wodvQlHLhVuGiDa8/VX8A2ivQIX1FaOK3FbAreacSCB65LbkmxAiR1J7EhgTIspM7pMSROzaRG0mIYCL2KQ13N23iWo+aoLt7lQATEbSMYzMZGGdDr9Qc1MTESJpfgiZ8TP+6yl0VYsiEzqSroiFRULZRICviGEc+5ip1gezPq6O8r2tkhvc77VGcvZO4Lezry/ZWA0bnda3Xa3UNke69/8Y19L3Ku29nVFerLuJoff0usKFeLesFW1mROSsfyyuJnxQxyaTb7dxH+8/iLj2C3QArO1tZPaGS2b1M3oGOhMuoCOA61JG9By1/px1oezIu4ScdI/42eTgZkAK/vJqoM2RsbEa1OBQMjvsoYMKU0a02lzig81pyUHoktyIHIDwp+m3vsykZUueVXIiKVyyWxEA9oEmyMcSyMNVXpYxRrmbMFCFVWCDyul1mZ02PGZlhaVVoubmhiHKpXmaq1Gw/N6o/Jrl/wclSaNFnczVKsv24qo1dEb9hu8JQuv0SCiSu3w6vHgfOtaRJ1We5V12qylYCHVB8P1l/FVdhOEyYaT4zbsEFYL4wKXFFDJoQNEXsdpeQUaebdeawGNQ08OaCEHJCMm5t5XdbRhtJViJciVG8r9oEZfDbavSZsEwfSEef6HgVXL2vt9pVUpZ8TtcPgc+dFI16pnsysLbsZ9eWL+FndnR7gz7VSqNyg4weo/AI3YgexhCFEu/EJtZkqNu5pxowo3qXFTbmeOzRpxlsdZDme1OFvA2SjuDM4G2a4IbjTguAnHo7gpgZs92G/Efh32a7HdNGBiSXO7ecDMuc1InqsyYQxFBeZ5UTR7TWFQp9OCi5gOE9Omp2Xf/cvY8/4HJs6WRg9HjFfiBiQPprjEFuKSMhzKsVJruVKWTJ9hoG0oHVnmUDYZrYbE7i5X18G8xHu4OlrKj3SHj3S1BjNuHcNqxRN365/ODFb8ZC5B0WvKd9uHzF0pcfXIluaeqdWJUNeGbHW1L9fmNfj0KWswQxpeVX+WaUjDVmirRa8FPG7C6yw4bTxqvM7IbbbiZgsiR4ZksJFuTSoeObJYCt9LTgwTWAmqJGuU+EBVRY5S+Cfb/KgL1TabzV+LFdc4L9cUgpFNLDO1HTEyuWrykT2BFZ/pG7xRqkqj9TeYjqJNEJqhCs/Wbr1Wgcfpm8FrYjinxGN+PBrAwwzn7HhUgXsyuMeFUzEc78GNXbgth1syuC2Lgzlcm8WBKg70YH8X9qc2pVhHFnsyazOsnWGy0F5gSSUmsxiroL0t1lZu45xZdBbQqURNFjUZTJe4KPh4PXTzto5Eh2BxJgQnz+VDeUFNtXVICmJnSMuN5sOC2Ud8gLQei/dwpaU8ZHdIfn2W1mOS0kXyZU4K6jl6VfIUpts44M5H7f62dc3J85YlunPtK5MrRjLpsVW5uVq5q7c52tJtDdosnrDJkgg7OvLFyn0960StN5r3R7syTiG1Il8ctgpbugsrc3ZPcdCXrXSXs52CEDC6MWULeexqleAJ2+J5uYpYT7dO9h0wgw+qteSliIdNOMfjNE9ZFqN6dHnQJaCDqWycRlR57VYND3aLnHarZ86yfZCybjzHpdEs8UhWQWzKiJxrOwedleXr8jfenR/oyNnDsSvXbR0ayHc4PWYc/YfN3qhD+96QxhH1Zi9dMXsU3/UEo1JkJurwEaJOAY5aE51qRN5LkdaqIM2clvancqcSNIfNeK+1Doe3sY3vDTV4csg8eaCrFh9W4DCPwxwOM9xkwk1GdHBqG9PzOlHttet0ViokbBbZpc/xZyQlLnBkbugtjaViwWFnjpacQUyUY6cOHW7e0BW+oqs2Pr7M78VX28c9Gr2Kf28I1f72DZlq55sh76Kck0STB+Kwt7Z6C+AWD55N2VgMx/w4glhifYyKnQEXSwLGeFRqUWnACGdW2WwszAsiUDAKC4LVyjcJ3kWyq1K19iGRKFgkDuJEvGSKMidkc/S1CkryX5kdO564fqfB53NpE7Wsw1PZ2PGzg2aL1luOJMwGU2u2s7qm+hV7NuZVBFr7oolV5QBxl0at1re2hld50gaT/pclicds/ff4IlNBDErwzdrHVkewy4zdJsxYuiys2YwZE7otSLFVZ0aNCbdGcZzCcRMOwVZggzEcjOJgBFf7MW1Cjz/t7/RzHh8KOhSaIk2tTb1NvNGESh0qmlBLL0HQA0XezvRBUe8Ed8Ik5qjcUyuVloTkuZJAKOVWq6YzHxGj4f2uokIeSMmVKkS53KK8S4LxUc2oJN+VkpaI8YUastQqPXPgne1Gi4YKLEX4SPtUe4vekC57sq7QpVectzm13K+JrmTmvDsYNQQrSaXK1q8V3GH3TXl/QD8SjKzTaOyWTNYzOrp8kyfsDL9z1OlLRZwRZxPKdfSbeDfJ0Qvn15Zt8eIaD64GXOvFXtMI1V4YxRJycy48ZsBjiDttuAVxi2Gvga0AXG2gstvCoVu0OFWCgwcjRfBzXHWJ82icAlE81kg/jkoOG8GKGMa7WwutSXdIq3NVkplKqJZKndcTu2h3cai8ouCLTuX7HcX4QMAR7N3Z97HrAvKJpP5n/D17BdxkAR210G4jzglSLI+yJj+zo2gXm5xKpzckmHmll7d4G1qSdERKahAIZLI5LJnjNsGPsUYRTwInBXDmhgKIWLx2Sq3ejyxby61p83vLa/KrC4wTdM3+nnS+XLZuGGQB8/eCLlcu3r+jq3OyN1IwGfHYfN3qGxmZ2ChT+yY+z5QU9yKwsdZGAW9vE+404RZBOnp0CWizR+1sj/2YnYVQ7USrCMhbRbXTLxjsTGRWkSoBQ6P8ObMQORZ4WLSrSlAyKEmokq812IgvZPxKWI73d2NOOmXG8kP+oBjs2lTetS7duSKa9+keuD+fT7W85BjdKfocwXBsYkX3jr7oihUFV7LVPVbt7ZUkXqMz+k/Zc5RVjRCt2VU6PW9UoVGPKDKnluMZmQK/4AjVKtXj+ZZONKsqrbElu15/4MDH0z5/Kin600wp3B5Ipf0iBXeEftImspdJm0JNy8Al6jUWgadagPRFbJLtSMGxZCYWVSJnM8vsIVq0NgXHc8gpD9L+yNmyqQj2KpQqXiv43fiN4jKHy+QPJ9zz55laskGp7uwhVn4ix41v166m818fJR5NScMUaps6qi6p+9QKhQYVnI2LciWOV/A2noWZws10ftFG4dDk1DkVTr9Z5GsiGkV8SsSaH41+fMqPdrONP2nDk3ZEM/AmwBnAKuBTgEbAG+TvFYA1GkG8AfEKRCPiwUOkvUMTBw9OHCShHTyYltqDE5IfVc+21UaENcvyVC0oWpIG2ijOLhxrljpsx76+ampV1NsZCveJnPFCjWavt+QIOHa7S16fAw+t64vEMmLQn3R6ha78/OfwiNsZzM7fiTOuYCADjTPmM0wNTmivJSUpRPkS38dv5JVLsrFxCjNJRJKH2WLimcW5kKklomWSl6htPYdQnNq/qiu1MqXRXKhT73P1OANuPNbfHw4LYbPXU0rMfxoPuAKRVjnOv4mvk+dY6aRQqAXmgjgWwjE7bhJxkwv9qHCQFhTEhnRCF/6iVIi+fzg/pwiWSmB8XewYKRc2dPj9HcPF4nCnf661xRVzG3JlZ8xjfLdrx8pYrG9Ht+QN0d4dnZMjlsxA5fwNlvRKySP660l8m2zJB0norWXoAHA4gVMc7kzgTs+sh40FMM4UQXRZAi5ZTBGr1WwhOsWlTPpB/WKjfM/hgpnLJzD7+2VcvCT5Mb7tL/XFEiudVD5PSXa/hxL5ivxg0TNbaHe1CLnucgLVyye7Rb/P4dZbDC5f2PHevZ67bAZjMrZqqrhlxCWcaR2T41ICf0scuCAB2Zo7xqlEZrQFyK6dKiclEZIq73aY3YvR8yyKw1SR5xZylhxrVEo6MZpbyyqbFDgbIrbjb7fxPAr98e17g22rk71FxGnlAWPB50x4jfk2byu+oxWsvuCN1yzbVvUXBcHzgIfFs0N7/+gImJP9paFxT8Nr8XyyRgGcNb1FwRkoNHJOg0XPywYnE1RZMLUPWBp2hSy2aI9onVJrL1K3hjUGvHWnOp7Kht2OZfn523CTPyP9QhWu/xHvZTrIwtW1rXvix+KsN4rlKNqi0SjbGscRM46YcJ/xY0bWbsd2FwocChnU2t12pnW73UzropiBygyOGzGEySa0ehVKPilanVGN0+gHDRX7YPUvRPClw9y5pcJiMJesthkpM8kyPCtX+pGYWzAFlXS+7WF4b5OvEKvGxFRY7AxHAsOtqb6Cp9benY7nIunNoXjL9nxusCzeYU9FRa1J63H4IhbrkNnjTHVGygNNek3QF4gHhXUenytbk2TtJI+rsJupemytea1gVoGZA7dodmqceotZQDr+a1WCRSXZxGn5OH5azqpnFviR83ysZC5KWaci5aKwrdgI2BWD2xZxT/jmFPxBHtvaDP5CtFWzXBtqRu9QbNjv8dUCQ/OPepojgkTJYP2/Zd8XIV3zNjm8joyjy8E3oRtEt6hyWpwWi5VXWdQfDDuKipQHS2aVuWGI1BPs8s8BlNfxS4OoVJn26Du8TtGo2Kc3Wmvp0TaVYGpFtSYXxcuE+c974iZfwonDwvxd+bLF4emx+yR6FOQtLzIF0XNlbex6xDkPznlxzofT4lGRTftwN8zB9cBN41Fk0zYc9qIT0eFFhw81NtSJqNCjlkMNohbQHRBEg5M5BZXVqDEYLEZexVBgS+Vkw90+/BSosIY5AzNiPMfinMpKRXbRwKkcPRymvz07ojJoeY7jNFpuYuZinVsX1rmDSXck250O6Jhi/g+OloS+SW+Ixpqw1Tp/d2h9OtISFFQr1YFC38K/N3QKwt8p/uuz24xdfwYX95r0A/19axKXSO0P52/+U32ynuBv5e74i3997qgnoMyfoueT/K1LfxItfl5j/dJvyfTqn+j2CrUi9OP90M8yIFJJmeCWU3sPvfNHGm+GAubrr+Nj4GKj1HcTVAls0IWnoMDykCNYS9BNkCeIEww3xlcRRAkKGKe12glmQGCXEn4AEpSUE9wNtMay+jwbg/WsQv39sB7fJPgW4VcQfoj6dgJP/X9ZJ7VPwXouD13sMD3/Cj1PN9puagchi78AIz0bZLn6W/SegnnrbzFl/XVmpf2CNP9W0FIryLwQ/9hBvHKQZVlYgY/L6xUYVeRYhCBzQoK1Es1GqBE0YR360AT9nAg9LET8umhemuRGc3AEemjvMA6Ck4kwiM/T3t+nvV6ov4W/BgU+Qc+2goA80UAtybrALiaaKN5hld75Fa31Dsn0NIj4O4IbwcWfItoknnwgcsO0h06GLL5CLb1P7/WzlVDgkoRfDwL3PM2TaKCCgGul9YhvmtuPT9Je/bTXg1BiOeKZaGAJwh+guato7u00513aS9/Ax8BF64k0VsBZek+iU5JTiOZtpHGB6BTpmbTmbTT3DO33A9LlvQS/p7EnST+STj8EuEHSBelZ1vHZYKc4Y4c0tacJXiI7wCUdnwtDjXFJz4vwSRiR9ZyW7UAr6/RDgFOR3B5f0PHZgEaSlZFoN9Z/QXCabFZc0vG5MEFznNSSns8GSc+yHUltO9lPGYJ4FeX1n5DMTASPEEhyI93SXusxWp8ke3bSs5hkH5KNSvokr/T/lWugcW2HR+FROhveyZLsCs7NbeXDH3EdOuf6TON6ln9WMfBXrzcUbygHlCdUrHHl5WtK9ba6W/0p9euammZG85a2U/uJxvW//3992NWI56+RzlRyDGbyXSt1mEPq3k/glIbT6R9AE7RDhlAjqAnTQwlSS5iB7CO+hBmB4usSZqLaKbyEmSENoSXMQvVxYAkjSyNLWsQEOomJS5iNzsfeJcxOK3qWMAet6F7CQKbTTTtJGMp0LmJMpnMR42Q6FzFepnMRU8h0LmJKmc5FTCXTuYipZToXMY1M5yKmlelcxHTyigvY94leBajS8H8CPKzTCmVuZHN0cmVhbQplbmRvYmoKMjUgMCBvYmoKPDwvRGVzY2VudCAtMjM1L0NhcEhlaWdodCA3MDAvU3RlbVYgODAvVHlwZS9Gb250RGVzY3JpcHRvci9Gb250RmlsZTIgMjQgMCBSL0ZsYWdzIDMyL0ZvbnRCQm94Wy0xNjAgLTIxNiAxMTc4IDkyNl0vRm9udE5hbWUvWUJOQUJNK01ldGFMRkJNZWRpdW0vSXRhbGljQW5nbGUgMC9Bc2NlbnQgOTMyPj4KZW5kb2JqCjYgMCBvYmoKPDwvU3VidHlwZS9UcnVlVHlwZS9GaXJzdENoYXIgMzIvVHlwZS9Gb250L0Jhc2VGb250L1lCTkFCTStNZXRhTEZCTWVkaXVtL0ZvbnREZXNjcmlwdG9yIDI1IDAgUi9FbmNvZGluZzw8L1R5cGUvRW5jb2RpbmcvRGlmZmVyZW5jZXNbMzIvc3BhY2UgNDMvcGx1cyA0Ny9zbGFzaC96ZXJvIDUwL3R3by90aHJlZS9mb3VyL2ZpdmUvc2l4L3NldmVuL2VpZ2h0IDY4L0QgNzYvTCA4MC9QIDgyL1IgOTcvYSA5OS9jL2QvZSAxMDUvaSAxMDgvbC9tL24vby9wL3Evci9zL3QvdSAxMjEveV0+Pi9MYXN0Q2hhciAxMjEvV2lkdGhzWzIyNSAwIDAgMCAwIDAgMCAwIDAgMCAwIDYwMCAwIDAgMCA0NDAgNTYwIDAgNTYwIDU2MCA1NjAgNTYwIDU2MCA1NjAgNTYwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCA2MjEgMCAwIDAgMCAwIDAgMCA0NjMgMCAwIDAgNTU1IDAgNTczIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCA1MTUgMCA0NTcgNTU1IDUxMyAwIDAgMCAyNzEgMCAwIDI3MiA3OTggNTQ2IDUzNyA1NTUgNTU1IDM0MCA0NzQgMzMxIDU0MiAwIDAgMCA0ODNdPj4KZW5kb2JqCjkgMCBvYmoKPDwvS2lkc1sxIDAgUiAxMCAwIFIgMTIgMCBSIDE0IDAgUl0vVHlwZS9QYWdlcy9Db3VudCA0Pj4KZW5kb2JqCjI2IDAgb2JqCjw8L05hbWVzWyhKUl9QQUdFX0FOQ0hPUl8wXzEpIDE2IDAgUihKUl9QQUdFX0FOQ0hPUl8wXzIpIDE3IDAgUihKUl9QQUdFX0FOQ0hPUl8wXzMpIDE4IDAgUihKUl9QQUdFX0FOQ0hPUl8wXzQpIDE5IDAgUl0+PgplbmRvYmoKMjcgMCBvYmoKPDwvRGVzdHMgMjYgMCBSPj4KZW5kb2JqCjI4IDAgb2JqCjw8L05hbWVzIDI3IDAgUi9UeXBlL0NhdGFsb2cvUGFnZXMgOSAwIFIvVmlld2VyUHJlZmVyZW5jZXM8PC9QcmludFNjYWxpbmcvQXBwRGVmYXVsdD4+Pj4KZW5kb2JqCjI5IDAgb2JqCjw8L0NyZWF0b3IoSmFzcGVyUmVwb3J0cyBMaWJyYXJ5IHZlcnNpb24gNi4yMS4yLTg0MzRhMGJkN2MzYmJjMzdjYmY5MTZmMjk2OGQzNWU0YjE2NTgyMWEpL0NyZWF0aW9uRGF0ZShEOjIwMjUwMjE5MTgyMTExLTA2JzAwJykvUHJvZHVjZXIoT3BlblBERiAxLjMuMzIpPj4KZW5kb2JqCnhyZWYKMCAzMAowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDM5NTYgMDAwMDAgbiAKMDAwMDAxMTk2MiAwMDAwMCBuIAowMDAwMDAwMDE1IDAwMDAwIG4gCjAwMDAwMjIyMTcgMDAwMDAgbiAKMDAwMDAzNjU5NiAwMDAwMCBuIAowMDAwMDQzNjM2IDAwMDAwIG4gCjAwMDAwMDE2MjIgMDAwMDAgbiAKMDAwMDAwMTg4NyAwMDAwMCBuIAowMDAwMDQ0MTkxIDAwMDAwIG4gCjAwMDAwMDY3ODEgMDAwMDAgbiAKMDAwMDAwNDIxMyAwMDAwMCBuIAowMDAwMDA5NjI1IDAwMDAwIG4gCjAwMDAwMDcwMjkgMDAwMDAgbiAKMDAwMDAxMTU2NyAwMDAwMCBuIAowMDAwMDA5ODczIDAwMDAwIG4gCjAwMDAwMTE4MTUgMDAwMDAgbiAKMDAwMDAxMTg1MSAwMDAwMCBuIAowMDAwMDExODg4IDAwMDAwIG4gCjAwMDAwMTE5MjUgMDAwMDAgbiAKMDAwMDAxMjA1MCAwMDAwMCBuIAowMDAwMDIyMDMyIDAwMDAwIG4gCjAwMDAwMjI5MTcgMDAwMDAgbiAKMDAwMDAzNjQwOSAwMDAwMCBuIAowMDAwMDM3MzgxIDAwMDAwIG4gCjAwMDAwNDM0NDkgMDAwMDAgbiAKMDAwMDA0NDI2MyAwMDAwMCBuIAowMDAwMDQ0NDAwIDAwMDAwIG4gCjAwMDAwNDQ0MzQgMDAwMDAgbiAKMDAwMDA0NDUzOSAwMDAwMCBuIAp0cmFpbGVyCjw8L0luZm8gMjkgMCBSL0lEIFs8MzMwMzkwZmJhNDU4YmI2MWIxNWQwYjk2NDc0NmRiMmQ+PDMzMDM5MGZiYTQ1OGJiNjFiMTVkMGI5NjQ3NDZkYjJkPl0vUm9vdCAyOCAwIFIvU2l6ZSAzMD4+CnN0YXJ0eHJlZgo0NDcxMAolJUVPRgo=</embeddedData></product></creditBureau>"}]}, "formatted_responses": {"Sentilink": {"data": {"scores": {"sentilink_abuse_score": 68, "sentilink_first_party_synthetic_score": 68, "sentilink_third_party_synthetic_score": 28, "sentilink_id_theft_score": 35}, "reasonCodes": {"allReasonCodes": ["R014", "R011", "R010", "R004", "R022", "R029", "R034"], "moreFraudyCodes": ["R004"], "lessFraudyCodes": ["R014", "R011", "R010", "R022", "R029", "R034"]}, "consumer_history": {"shared_ssn_count": null, "number_of_ssns": 0}, "years_since_filing_date": -1, "error": null}}, "TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 8, "revolving_count": 5, "open_count": 0, "line_of_credit_count": 0, "installment_count": 3, "mortgage_count": 0, "past_due_count": 1, "percentage_past_due": 0.13, "past_due_total_dollar_amount": 3323, "revolving_accounts_total_current_balance": 6009, "line_of_credit_accounts_total_current_balance": 0}, "collections": {"count": 3, "revolving_count": 0, "open_count": 3, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 2, "percentage_past_due": 0.67, "past_due_total_dollar_amount": 2229}, "consumer_statement_indicator": false, "public_records": {"count": 0, "public_records_within_last_year": 0, "public_records_within_last_three_years": 0, "public_records_within_last_five_years": 0, "public_records_within_last_ten_years": 0, "most_recent_public_record": "1000-01-01", "public_records_reason_codes": []}, "inquiries": {"count": 7, "inquiries_within_last_year": 6, "inquiries_within_last_three_years": 7, "inquiries_within_last_five_years": 7, "inquiries_within_last_ten_years": 7, "most_recent_inquiry": "2024-09-03"}, "vantagescore40": {"exists": true, "score": 564}, "vantagescore30": {"exists": true, "score": 573}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "ficoscore4": {"exists": false, "score": -1}, "syntheticidfraudscore": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": true, "condition": "mismatch", "type": "address", "inquiries_last_60_days": 0, "address_status": "current"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 3, "AT28B": 32170, "AT32S": 20522, "AT36S": 1, "AT104S": 38, "AU20S": 5, "AU21S": 5, "AU34S": 101, "AU36S": 999, "AU51A": 72, "AU101S": 20522, "BC34S": 96, "BC103S": 2003, "BC110S": 3323, "BI09S": -1, "BI34S": -1, "BR02S": 2, "BR34S": 96, "FI25S": -6, "FI34S": 101, "FI101S": 20522, "FR101S": -1, "G202B": 677, "G213A": 1911, "G215B": 2, "G216S": 1, "G224C": 2, "G228S": -1, "IN20S": 124, "OF27S": -1, "RE03S": 2, "RE06S": 1, "RE24S": 1, "RE28S": 2800, "S061S": 1, "S071A": 1, "ST01S": 2, "ST24S": 2, "ST27S": 2, "ST28S": 9033, "ST99S": -5, "S207S": -4, "G242F": 0, "G243F": 3, "US21S": -1, "US36S": -1, "JT20S": -1, "JT21S": -1, "G404S": 0, "G411S": 2, "G208B": 100, "RE21S": -99999, "G408S": -9, "RE101S": null, "BKC324": null, "G209S": -999, "BKC122": -9, "BKC14": -1, "RT25S": -999, "HR101S": null, "ST50S": -999999, "G215A": -9, "G960S": -9}, "factor_code_4": null, "total_amount_in_collections": 2229, "vantage_v4_score_factor_3": 10, "fico_9_bankcard_score": null, "max_credit_limit_open_credit_trades": 2000, "ficoauto8": {"score": null}, "mla_status": "delivered", "00W16": {"income_range_low_end": null, "income_range_high_end": null}, "factor_code_1": null, "credit_vision_link_score_FA2": [null], "months_on_file": 126, "truvalidate_fraud_alert_codes": null, "employment": {"employer": "THE PIER HOUSE"}, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "credit_vision_alternative_checking_attributes": {"LINKA006": 2, "LINKA014": 0, "LINKA027": 99999}, "trade": "[{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"0235064B\",\"name\":{\"unparsed\":\"SYNCB/ONDC\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2017-05-17\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2019-05-29\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"09\",\"remark\":{\"code\":\"PRL\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2019-11-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"C\",\"memberCode\":\"01NZ8326\",\"name\":{\"unparsed\":\"CCB/FE21CC\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2017-07-13\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2022-05-28\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2022-05-21\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"9P\",\"remark\":{\"code\":\"SET\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CH\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2022-05-21\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"********\",\"name\":{\"unparsed\":\"DISCOVERBANK\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2016-07-28\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2022-05-15\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-03-20\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2022-04-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"9P\",\"remark\":{\"code\":\"SET\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2022-04-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"01DTV001\",\"name\":{\"unparsed\":\"CAPITAL ONE\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2023-11-16\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-02-05\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentScheduleMonthCount\":\"MIN\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2025-01-05\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"************11\"},\"historicalCounters\":{\"monthsReviewedCount\":\"14\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2025-01-14\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"E\",\"memberCode\":\"039ED388\",\"name\":{\"unparsed\":\"EDFINANCIAL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2014-10-23\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"120\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"11XX11X11X11X************************11111111111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"48\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2023-08-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"F\",\"memberCode\":\"01W2K001\",\"name\":{\"unparsed\":\"CAP ONE AUTO\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-09-02\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"072\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"AU\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"1111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"04\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2025-01-16\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"E\",\"memberCode\":\"039ED388\",\"name\":{\"unparsed\":\"EDFINANCIAL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2014-10-23\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"120\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"11XX11X11X11X************************11111111111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"48\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2023-08-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"01DTV001\",\"name\":{\"unparsed\":\"CAPITAL ONE\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-09-03\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-25\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentScheduleMonthCount\":\"MIN\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-25\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"03\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2025-01-20\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"}]", "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "credit_vision_new_account_v2": {"score": null}, "number_foreclosure_bankruptcy_last_4y": 0, "military_leads": {"has_military_association": false}, "credit_vision_premeium_algorithms": {"paymnt01": null, "mnpmag01": null}, "factor_code_2": null, "military_lending_act_search_match": false, "count_60_days_late_payments": 0, "vantage_v_4_factor_1": 68, "vantage_v4_score_factor_2": 4, "consumer_statement_text": null, "months_since_most_recent_deliquency": 0, "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": null, "count_90_days_late_payments": 0, "total_monthly_debt_payment": 684, "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "number_deduped_bank_inquiries_past_3_months": -1, "number_charge_offs_last_24Mo": 0, "number_trades_verified_past_12_months": null, "tot_month_obligation_joint_and_indv_accts_verified_past_12_months": null, "number_of_deduplicated_inquiries_in_past_6_months": null, "credit_vision_revolver_6": {"RVLR61": null}, "tot_month_obligation_contractually_liable_accts_verified_past_12_months": null, "applicant_age_years": 28, "total_current_balance_mortgage_accounts": 0, "number_deduped_finance_inquiries_past_3_months": 0, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": 29}, "matching": {"address": {"score": 0.85, "matched": false}, "name": {"score": 1, "matched": true}, "dob": {"score": 0, "matched": false}, "ssn": {"score": 1, "matched": true}}}}, "audit_archive": null}