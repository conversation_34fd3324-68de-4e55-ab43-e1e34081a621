{"status_code": 201, "error": null, "timestamp": 1740585371523, "evaluation_token": "L-bynX6B5PhadNrt5ClWNE", "entity_token": "P-sBR7u4fnv9CHKk1FQtYf", "parent_entity_token": null, "application_token": "5hiyzkaF3x0B9XPxKY6l4WI3i6diVaHt", "application_version_id": 20, "champion_challenger_id": null, "summary": {"result": "success", "score": 0.93, "tags": [], "outcome_reasons": [], "outcome": null, "services": {"TransUnion Credit": "executed", "Sentilink": "executed"}, "custom_fields": {"financial_partner_id": 2}, "alloy_fraud_score": null}, "supplied": {"meta": {"customer_id": 4502094}, "name_first": "<PERSON><PERSON>", "name_last": "<PERSON><PERSON>", "address_line_1": "2107 W. Broadway Rd", "address_line_2": "148", "address_city": "Mesa", "address_state": "AZ", "address_postal_code": "85202", "birth_date": "1997-08-09", "document_ssn": "764529168", "email_address": "<EMAIL>", "phone_number": "+14802766928", "financial_partner_id": 2}, "formatted": {"name_first": "<PERSON><PERSON>", "name_last": "<PERSON><PERSON>", "address_line_1": "2107 W. Broadway Rd", "address_line_2": "148", "address_city": "Mesa", "address_state": "AZ", "address_postal_code": "85202", "birth_date": "1997-08-09", "document_ssn": "764529168", "email_address": "<EMAIL>", "phone_number": "+14802766928", "financial_partner_id": 2, "phone_country_code": "US", "age": "27"}, "meta": {"customer_id": 4502094}, "matching": {"address": {"score": 0.72, "matched": [], "unmatched": ["TransUnion Credit"]}, "name": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}, "dob": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}, "ssn": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}}, "diligence": {"watchlists": null, "fraud": {"average_score": 0.06, "flags": [], "sources": {"Sentilink": {"normalized_score": 0.06, "raw_score": 61, "attribute": "Abuse score"}}, "score": 0.06}, "financial": {"credit": [{"source": "TransUnion Credit", "models": {"vantagescore30": {"name": "VantageScore 3.0", "codes": ["vantagescore30-12", "vantagescore30-04", "vantagescore30-63", "vantagescore30-06"], "score": 635, "model_codes": [{"code": "vantagescore30-04", "description": "The balances on your accounts are too high compared to loan amounts"}, {"code": "vantagescore30-06", "description": "You have too many accounts that were opened recently"}, {"code": "vantagescore30-12", "description": "The date that you opened your oldest account is too recent"}, {"code": "vantagescore30-63", "description": "Lack of sufficient relevant real estate account information"}], "bureau": "TransUnion Credit"}, "vantagescore40": {"name": "VantageScore 4.0", "codes": ["vantagescore40-68", "vantagescore40-04", "vantagescore40-10", "vantagescore40-53"], "score": 561, "model_codes": [{"code": "vantagescore40-04", "description": "Balances on accounts too high compared to credit limits and loan amounts"}, {"code": "vantagescore40-10", "description": "Too few accounts paid as agreed"}, {"code": "vantagescore40-53", "description": "Not enough balance paid down over time on installment accounts"}, {"code": "vantagescore40-68", "description": "Lack of real estate secured loan information"}], "bureau": "TransUnion Credit"}}}]}, "identity_questions": null, "device": null}, "related_data": {}, "raw_responses": {"Sentilink": [{"transaction_id": "01JN1FHZ-K331-M55YGX5C", "application_id": "L-bynX6B5PhadNrt5ClWNE", "scores": [{"name": "sentilink_abuse_score", "version": "1.7.1", "score": 61, "reason_codes": [{"code": "R014", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R010", "rank": 2, "direction": "less_fraudy", "explanation": "The depth of the consumer's history with this information"}, {"code": "R011", "rank": 3, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}]}, {"name": "sentilink_third_party_synthetic_score", "version": "1.7.1", "score": 81, "reason_codes": [{"code": "R022", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the email domain or structure of the handle are suspicious"}, {"code": "R017", "rank": 2, "direction": "more_fraudy", "explanation": "Attributes of addresses that the consumer is tied to"}, {"code": "R011", "rank": 3, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}]}, {"name": "sentilink_first_party_synthetic_score", "version": "1.7.1", "score": 38, "reason_codes": [{"code": "R014", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R011", "rank": 2, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}, {"code": "R010", "rank": 3, "direction": "less_fraudy", "explanation": "The depth of the consumer's history with this information"}]}, {"name": "sentilink_id_theft_score", "version": "1.6.3", "score": 20, "reason_codes": [{"code": "R029", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the applicant appears to be the best owner of the phone"}, {"code": "R034", "rank": 2, "direction": "less_fraudy", "explanation": "Length of history of the email"}, {"code": "R028", "rank": 3, "direction": "less_fraudy", "explanation": "Whether there is unusual geographic activity associated with the phone number"}]}], "customer_id": "01EFND5G6EZXKKT7PVY904YPBY", "environment": "PROD", "notes": "", "timestamp": "2025-02-26T15:56:09.441016963Z", "latency_ms": 248}], "TransUnion Credit": [{"creditBureau": {"$": {"xmlns": "http://www.transunion.com/namespace", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.transunion.com/namespace"}, "document": "response", "version": "2.26", "transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "********", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2025-02-26T09:56:09.071-06:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "false", "market": "29", "submarket": "AZ", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "2018-02-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "indicative": {"name": {"$": {"source": "file"}, "person": {"first": "ADEY", "middle": "I", "last": "ABDI"}}, "address": [{"$": {"source": "file"}, "status": "current", "qualifier": "personal", "street": {"number": "4839", "name": "DARROW", "preDirectional": "S", "type": "DR", "unit": {"number": "F148"}}, "location": {"city": "TEMPE", "state": "AZ", "zipCode": "85282"}, "dateReported": {"_": "2023-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "428", "name": "CARTER", "preDirectional": "E", "type": "DR"}, "location": {"city": "TEMPE", "state": "AZ", "zipCode": "85282"}, "dateReported": {"_": "2024-10-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "1339", "name": "14TH", "preDirectional": "W", "type": "ST"}, "location": {"city": "TEMPE", "state": "AZ", "zipCode": "85281"}}], "socialSecurity": {"$": {"source": "file"}, "number": "764529168"}, "dateOfBirth": {"_": "1997-08-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false", "source": "file"}}}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "B", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2024-02-03", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2025-01-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "***********"}, "historicalCounters": {"monthsReviewedCount": "11", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2025-02-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "02JC8001", "name": {"unparsed": "DPT ED/AIDV"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2023-08-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "***********111111"}, "historicalCounters": {"monthsReviewedCount": "17", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-06-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "02JC8001", "name": {"unparsed": "DPT ED/AIDV"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2024-04-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "*********"}, "historicalCounters": {"monthsReviewedCount": "09", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-06-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "02JC8001", "name": {"unparsed": "DPT ED/AIDV"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2024-04-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "*********"}, "historicalCounters": {"monthsReviewedCount": "09", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-06-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "02JC8001", "name": {"unparsed": "DPT ED/AIDV"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2023-08-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "***********111111"}, "historicalCounters": {"monthsReviewedCount": "17", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-06-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}], "inquiry": [{"ECOADesignator": "individual", "subscriber": {"industryCode": "F", "memberCode": "********", "inquirySubscriberPrefixCode": "17NY", "name": {"unparsed": "SYNCB"}}, "date": {"_": "2025-02-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "33PC", "name": {"unparsed": "CAPITAL ONE"}}, "date": {"_": "2024-02-03", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}]}}, "addOnProduct": [{"code": "06400", "status": "delivered", "idMismatchAlert": {"type": "address", "condition": "mismatch", "inquiriesInLast60Count": "00", "addressStatus": "current"}}, {"code": "07030", "status": "delivered", "phoneAppend": {"$": {"searchStatus": "notFound"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "0000000018"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "0000000033"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "0000000033"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "*********2"}, {"algorithmID": "00WBO", "id": "P02F", "value": "*********2"}, {"algorithmID": "00WBO", "id": "P02H", "value": "*********2"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********5"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000016186"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "0000000999"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000100"}, {"algorithmID": "00WR3", "id": "AU20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU36S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "0000000092"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "*********4"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "0000000103"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "BR34S", "value": "0000000092"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FI101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G202B", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "G213A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G215B", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "0000000018"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "RE03S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "RE24S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "RE28S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "S061S", "value": "0000000999"}, {"algorithmID": "00WR3", "id": "S071A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********4"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "*********4"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "-000000006"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "0000015886"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000005"}, {"algorithmID": "00WR3", "id": "S207S", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G242F", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G243F", "value": "*********1"}, {"algorithmID": "00WR3", "id": "US21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US36S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G404S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G208B", "value": "0000000100"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000267"}}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "0*********"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "-000000002"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "-000002.00"}, {"algorithmID": "00H91", "id": "PAYMNT10", "value": "*********2"}]}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000001"}}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "-000000009"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "0000000056"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "*********0"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "0000000127"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "0000000027"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "0000000056"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "*********7"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "0000000010"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG208", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG218", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG223", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG407", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG504", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG518", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG602", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG801", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG803", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG807", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********1"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000083.50"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000098.33"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "PRTRANS"}}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "*********9"}, {"algorithmID": "00WG9", "id": "REV255", "value": "0000000029"}, {"algorithmID": "00WG9", "id": "STD255", "value": "-000000002"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "0000001.36"}, {"algorithmID": "00WG8", "id": "REV222", "value": "0000001.36"}, {"algorithmID": "00WG8", "id": "REV225", "value": "0000002.17"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "-000001.00"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "REV322", "value": "*********0"}, {"algorithmID": "00WH1", "id": "BKC320", "value": "*********5"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "*********0"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "0000000021"}]}}, {"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+635", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "12"}, {"rank": "2", "code": "04"}, {"rank": "3", "code": "63"}, {"rank": "4", "code": "06"}]}, "scoreCard": "08"}}}, {"code": "001NN", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+561", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "false", "factors": {"factor": [{"rank": "1", "code": "68"}, {"rank": "2", "code": "04"}, {"rank": "3", "code": "10"}, {"rank": "4", "code": "53"}]}, "scoreCard": "04"}}}, {"code": "00H87", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H87", "id": "TRV07", "value": "*********1"}, {"algorithmID": "00H87", "id": "TRV08", "value": "*********2"}]}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00Z23", "id": "LINKF001", "value": "CLEAR"}}}, {"code": "07051", "status": "delivered", "militaryLendingActSearch": {"$": {"searchStatus": "noMatch"}}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "9999.99999"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}, "printImage": "                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           29 AZ      2/18      02/26/25    09:56CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> AB<PERSON>, ADEY  I.                                     ***********   8/97        <CURRENT ADDRESS>                                               <DATE RPTD>  4839 S. DARROW DR., #F148. TEMPE AZ. 85282                       8/23        <FORMER ADDRESS>                                                             428 E. CARTER DR., TEMPE AZ. 85282                              10/24        1339 W. 14TH ST., TEMPE AZ. 85281                                            ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVALIDATE ID MISMATCH ALERT: CURRENT INPUT ADDRESS DOES NOT MATCH FILE                                    ADDRESS(ES)***                             ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +635  : 12, 04, 63, 06 SCORECARD :08*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +561  : 68, 04, 10, 53 SCORECARD :04***              ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   CAPITAL ONE  B 1DTV001  2/24   $347     MIN25              ***********  R01                          2/25A  $300     $0                                   I    CREDIT CARD               $276                           11   0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  8/23   $3500                       ***********1 I01                          1/25A           $0                 11111             I    DEFERRED ********         $3500    PAYMENT DEFERRED      17   0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  4/24   $4667                       *********    I01                          1/25A           $0                                   I    DEFERRED ********         $4832    PAYMENT DEFERRED      9    0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  4/24   $3500                       *********    I01                          1/25A           $0                                   I    DEFERRED ********         $3500    PAYMENT DEFERRED      9    0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  8/23   $4219                       ***********1 I01                          1/25A           $0                 11111             I    DEFERRED ********         $4501    PAYMENT DEFERRED      17   0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        2/14/25 FNY4853553(EAS) SYNCB                                                2/03/24 BPC3575459(NTL) CAPITAL ONE                                         ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ", "embeddedData": {"_": "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", "$": {"type": "pdf", "contentTypeEncoding": "base64"}}}}, "xml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><creditBureau xmlns=\"http://www.transunion.com/namespace\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.transunion.com/namespace\"><document>response</document><version>2.26</version><transactionControl><subscriber><industryCode>F</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>1702</inquirySubscriberPrefixCode></subscriber><options><country>us</country><language>en</language><productVersion>standard</productVersion></options><tracking><transactionTimeStamp>2025-02-26T09:56:09.071-06:00</transactionTimeStamp></tracking></transactionControl><product><code>07000</code><subject><number>1</number><subjectRecord><fileNumber>01</fileNumber><fileSummary><fileHitIndicator>regularHit</fileHitIndicator><ssnMatchIndicator>exact</ssnMatchIndicator><consumerStatementIndicator>false</consumerStatementIndicator><market>29</market><submarket>AZ</submarket><creditDataStatus><suppressed>false</suppressed><doNotPromote><indicator>false</indicator></doNotPromote><freeze><indicator>false</indicator></freeze><minor>false</minor><disputed>false</disputed></creditDataStatus><inFileSinceDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2018-02-19</inFileSinceDate></fileSummary><indicative><name source=\"file\"><person><first>ADEY</first><middle>I</middle><last>ABDI</last></person></name><address source=\"file\"><status>current</status><qualifier>personal</qualifier><street><number>4839</number><name>DARROW</name><preDirectional>S</preDirectional><type>DR</type><unit><number>F148</number></unit></street><location><city>TEMPE</city><state>AZ</state><zipCode>85282</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-08-31</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>428</number><name>CARTER</name><preDirectional>E</preDirectional><type>DR</type></street><location><city>TEMPE</city><state>AZ</state><zipCode>85282</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-10-31</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>1339</number><name>14TH</name><preDirectional>W</preDirectional><type>ST</type></street><location><city>TEMPE</city><state>AZ</state><zipCode>85281</zipCode></location></address><socialSecurity source=\"file\"><number>764529168</number></socialSecurity><dateOfBirth estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\" source=\"file\">1997-08-09</dateOfBirth></indicative><custom><credit><trade><subscriber><industryCode>B</industryCode><memberCode>01DTV001</memberCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-02-03</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-18</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><terms><paymentScheduleMonthCount>MIN</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-18</startDate><text>***********</text></paymentPattern><historicalCounters><monthsReviewedCount>11</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-15</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>02JC8001</memberCode><name><unparsed>DPT ED/AIDV</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-08-28</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>***********111111</text></paymentPattern><historicalCounters><monthsReviewedCount>17</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-06-26</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>02JC8001</memberCode><name><unparsed>DPT ED/AIDV</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-04-12</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>*********</text></paymentPattern><historicalCounters><monthsReviewedCount>09</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-06-26</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>02JC8001</memberCode><name><unparsed>DPT ED/AIDV</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-04-12</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>*********</text></paymentPattern><historicalCounters><monthsReviewedCount>09</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-06-26</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>02JC8001</memberCode><name><unparsed>DPT ED/AIDV</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-08-28</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>***********111111</text></paymentPattern><historicalCounters><monthsReviewedCount>17</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-06-26</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>F</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>17NY</inquirySubscriberPrefixCode><name><unparsed>SYNCB</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-14</date></inquiry><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>33PC</inquirySubscriberPrefixCode><name><unparsed>CAPITAL ONE</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-02-03</date></inquiry></credit></custom><addOnProduct><code>06400</code><status>delivered</status><idMismatchAlert><type>address</type><condition>mismatch</condition><inquiriesInLast60Count>00</inquiriesInLast60Count><addressStatus>current</addressStatus></idMismatchAlert></addOnProduct><addOnProduct><code>07030</code><status>delivered</status><phoneAppend searchStatus=\"notFound\"/></addOnProduct><addOnProduct><code>00WBN</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBN</algorithmID><id>AD06C</id><value>0000000018</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD09A</id><value>*********0</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14A</id><value>0000000033</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14H</id><value>0000000033</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WBO</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBO</algorithmID><id>P02E</id><value>*********2</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02F</id><value>*********2</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02H</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WR3</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WR3</algorithmID><id>AT24S</id><value>*********5</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT28B</id><value>0000016186</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT32S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT36S</id><value>0000000999</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT104S</id><value>0000000100</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU20S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU34S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU36S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU51A</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU101S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC34S</id><value>0000000092</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC103S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC110S</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI09S</id><value>*********4</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI34S</id><value>0000000103</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR02S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR34S</id><value>0000000092</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI25S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI34S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI101S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FR101S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G202B</id><value>-000000003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G213A</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G215B</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G216S</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G224C</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G228S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>IN20S</id><value>0000000018</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>OF27S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE03S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE06S</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE24S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE28S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S061S</id><value>0000000999</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S071A</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST01S</id><value>*********4</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST24S</id><value>*********4</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST27S</id><value>-000000006</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST28S</id><value>0000015886</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST99S</id><value>-000000005</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S207S</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G242F</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G243F</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US36S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT20S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G404S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G411S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G208B</id><value>0000000100</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H88</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H88</algorithmID><id>BALMAG01</id><value>0000000267</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V26</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V26</algorithmID><id>CV23</id><value>0*********</value></characteristic><characteristic><algorithmID>00V26</algorithmID><id>CV28</id><value>*********0</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H91</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT02</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT07</id><value>-000002.00</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT10</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG4</algorithmID><id>PLATTR04</id><value>-000000001</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V92</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V92</algorithmID><id>RVDEX02</id><value>-000000009</value></characteristic><characteristic><algorithmID>00V92</algorithmID><id>RVDEXQ2</id><value>0000000056</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V53</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V53</algorithmID><id>AGGS106</id><value>*********0</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>AGGS109</id><value>0000000127</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ1</id><value>0000000027</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ2</id><value>0000000056</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ3</id><value>*********7</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ4</id><value>0000000010</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H86</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H86</algorithmID><id>AGG205</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG208</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG218</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG223</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG407</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG504</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG518</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG602</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG801</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG803</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG807</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG901</id><value>*********1</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG906</id><value>0000083.50</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG910</id><value>0000098.33</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WP4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WP4</algorithmID><id>RVLR80</id><value>PRTRANS</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG9</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG9</algorithmID><id>ALL252</id><value>*********9</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>REV255</id><value>0000000029</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>STD255</id><value>-000000002</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG8</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG8</algorithmID><id>REV202</id><value>0000001.36</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV222</id><value>0000001.36</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV225</id><value>0000002.17</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>AUT225</id><value>-000001.00</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WH1</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WH1</algorithmID><id>REV322</id><value>*********0</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC320</id><value>*********5</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC322</id><value>*********0</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC326</id><value>0000000021</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V60</code><status>defaultDelivered</status><scoreModel><score><results>+635</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>true</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>12</code></factor><factor><rank>2</rank><code>04</code></factor><factor><rank>3</rank><code>63</code></factor><factor><rank>4</rank><code>06</code></factor></factors><scoreCard>08</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>001NN</code><status>defaultDelivered</status><scoreModel><score><results>+561</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>false</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>68</code></factor><factor><rank>2</rank><code>04</code></factor><factor><rank>3</rank><code>10</code></factor><factor><rank>4</rank><code>53</code></factor></factors><scoreCard>04</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>00H87</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H87</algorithmID><id>TRV07</id><value>*********1</value></characteristic><characteristic><algorithmID>00H87</algorithmID><id>TRV08</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00Z23</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z23</algorithmID><id>LINKF001</id><value>CLEAR</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>07051</code><status>delivered</status><militaryLendingActSearch searchStatus=\"noMatch\"/></addOnProduct><addOnProduct><code>00Z17</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z17</algorithmID><id>LINKA004</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA002</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA023</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA024</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA048</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA049</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA001</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA050</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA051</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA052</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA029</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA030</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA053</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA022</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA026</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA027</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA028</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA021</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA020</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA006</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA003</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA025</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA011</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA005</id><value>0000.00000</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA010</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA040</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA033</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA034</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA035</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA036</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA007</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA037</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA008</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA009</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA039</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA042</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA043</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA044</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA015</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA045</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA046</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA047</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA017</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA018</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA019</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA041</id><value>9999.99999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA012</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA014</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA016</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA013</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA038</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA054</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA055</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA056</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA057</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA058</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA059</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA031</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA080</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA060</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA061</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA062</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA063</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA064</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA065</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA066</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA082</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA067</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA068</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA069</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA070</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA071</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA072</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA032</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA081</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA084</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA085</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA073</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA074</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA075</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA076</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA077</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA078</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA079</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA083</id><value>0000000.00</value></characteristic></scoreModel></addOnProduct></subjectRecord></subject><printImage><![CDATA[                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           29 AZ      2/18      02/26/25    09:56CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> ABDI, ADEY  I.                                     ***********   8/97        <CURRENT ADDRESS>                                               <DATE RPTD>  4839 S. DARROW DR., #F148. TEMPE AZ. 85282                       8/23        <FORMER ADDRESS>                                                             428 E. CARTER DR., TEMPE AZ. 85282                              10/24        1339 W. 14TH ST., TEMPE AZ. 85281                                            ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVALIDATE ID MISMATCH ALERT: CURRENT INPUT ADDRESS DOES NOT MATCH FILE                                    ADDRESS(ES)***                             ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +635  : 12, 04, 63, 06 SCORECARD :08*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +561  : 68, 04, 10, 53 SCORECARD :04***              ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   CAPITAL ONE  B 1DTV001  2/24   $347     MIN25              ***********  R01                          2/25A  $300     $0                                   I    CREDIT CARD               $276                           11   0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  8/23   $3500                       ***********1 I01                          1/25A           $0                 11111             I    DEFERRED ********         $3500    PAYMENT DEFERRED      17   0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  4/24   $4667                       *********    I01                          1/25A           $0                                   I    DEFERRED ********         $4832    PAYMENT DEFERRED      9    0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  4/24   $3500                       *********    I01                          1/25A           $0                                   I    DEFERRED ********         $3500    PAYMENT DEFERRED      9    0/ 0/ 0                                                                                DPT ED/AIDV  B 2JC8001  8/23   $4219                       ***********1 I01                          1/25A           $0                 11111             I    DEFERRED ********         $4501    PAYMENT DEFERRED      17   0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        2/14/25 FNY4853553(EAS) SYNCB                                                2/03/24 BPC3575459(NTL) CAPITAL ONE                                         ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ]]></printImage><embeddedData type=\"pdf\" contentTypeEncoding=\"base64\">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</embeddedData></product></creditBureau>"}]}, "formatted_responses": {"Sentilink": {"data": {"scores": {"sentilink_abuse_score": 61, "sentilink_first_party_synthetic_score": 38, "sentilink_third_party_synthetic_score": 81, "sentilink_id_theft_score": 20}, "reasonCodes": {"allReasonCodes": ["R014", "R010", "R011", "R022", "R017", "R029", "R034", "R028"], "moreFraudyCodes": ["R017"], "lessFraudyCodes": ["R014", "R010", "R011", "R022", "R029", "R034", "R028"]}, "consumer_history": {"shared_ssn_count": null, "number_of_ssns": 0}, "years_since_filing_date": -1, "error": null}}, "TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 5, "revolving_count": 1, "open_count": 0, "line_of_credit_count": 0, "installment_count": 4, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0, "revolving_accounts_total_current_balance": 276, "line_of_credit_accounts_total_current_balance": 0}, "collections": {"count": 0, "revolving_count": 0, "open_count": 0, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0}, "consumer_statement_indicator": false, "public_records": {"count": 0, "public_records_within_last_year": 0, "public_records_within_last_three_years": 0, "public_records_within_last_five_years": 0, "public_records_within_last_ten_years": 0, "most_recent_public_record": "1000-01-01", "public_records_reason_codes": []}, "inquiries": {"count": 2, "inquiries_within_last_year": 1, "inquiries_within_last_three_years": 2, "inquiries_within_last_five_years": 2, "inquiries_within_last_ten_years": 2, "most_recent_inquiry": "2025-02-14"}, "vantagescore40": {"exists": true, "score": 561}, "vantagescore30": {"exists": true, "score": 635}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "ficoscore4": {"exists": false, "score": -1}, "syntheticidfraudscore": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": true, "condition": "mismatch", "type": "address", "inquiries_last_60_days": 0, "address_status": "current"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 5, "AT28B": 16186, "AT32S": 4832, "AT36S": 999, "AT104S": 100, "AU20S": -1, "AU21S": -1, "AU34S": -1, "AU36S": -1, "AU51A": -1, "AU101S": -1, "BC34S": 92, "BC103S": 276, "BC110S": 0, "BI09S": 4, "BI34S": 103, "BR02S": 1, "BR34S": 92, "FI25S": -1, "FI34S": -1, "FI101S": -1, "FR101S": -1, "G202B": -3, "G213A": -4, "G215B": -4, "G216S": 0, "G224C": 0, "G228S": -1, "IN20S": 18, "OF27S": -1, "RE03S": 1, "RE06S": 0, "RE24S": 1, "RE28S": 300, "S061S": 999, "S071A": -4, "ST01S": 4, "ST24S": 4, "ST27S": -6, "ST28S": 15886, "ST99S": -5, "S207S": -4, "G242F": 1, "G243F": 1, "US21S": -1, "US36S": -1, "JT20S": -1, "JT21S": -1, "G404S": 1, "G411S": 1, "G208B": 100, "RE21S": -99999, "G408S": -9, "RE101S": null, "BKC324": null, "G209S": -999, "BKC122": -9, "BKC14": -1, "RT25S": -999, "HR101S": null, "ST50S": -999999, "G215A": -9, "G960S": -9}, "factor_code_4": null, "total_amount_in_collections": null, "vantage_v4_score_factor_3": 10, "fico_9_bankcard_score": null, "max_credit_limit_open_credit_trades": 300, "ficoauto8": {"score": null}, "mla_status": "delivered", "00W16": {"income_range_low_end": null, "income_range_high_end": null}, "factor_code_1": null, "credit_vision_link_score_FA2": [null], "months_on_file": 85, "truvalidate_fraud_alert_codes": null, "employment": {"employer": null}, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "credit_vision_alternative_checking_attributes": {"LINKA006": 1, "LINKA014": 0, "LINKA027": 99999}, "trade": "[{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"01DTV001\",\"name\":{\"unparsed\":\"CAPITAL ONE\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-02-03\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-02-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentScheduleMonthCount\":\"MIN\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2025-01-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"***********\"},\"historicalCounters\":{\"monthsReviewedCount\":\"11\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2025-02-15\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"02JC8001\",\"name\":{\"unparsed\":\"DPT ED/AIDV\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2023-08-28\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"***********111111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"17\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-06-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"02JC8001\",\"name\":{\"unparsed\":\"DPT ED/AIDV\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-04-12\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"*********\"},\"historicalCounters\":{\"monthsReviewedCount\":\"09\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-06-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"02JC8001\",\"name\":{\"unparsed\":\"DPT ED/AIDV\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-04-12\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"*********\"},\"historicalCounters\":{\"monthsReviewedCount\":\"09\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-06-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"02JC8001\",\"name\":{\"unparsed\":\"DPT ED/AIDV\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2023-08-28\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"***********111111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"17\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-06-26\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"}]", "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "credit_vision_new_account_v2": {"score": null}, "number_foreclosure_bankruptcy_last_4y": 0, "military_leads": {"has_military_association": false}, "credit_vision_premeium_algorithms": {"paymnt01": null, "mnpmag01": null}, "factor_code_2": null, "military_lending_act_search_match": false, "count_60_days_late_payments": 0, "vantage_v_4_factor_1": 68, "vantage_v4_score_factor_2": 4, "consumer_statement_text": null, "months_since_most_recent_deliquency": 0, "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": null, "count_90_days_late_payments": 0, "total_monthly_debt_payment": 25, "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "number_deduped_bank_inquiries_past_3_months": -1, "number_charge_offs_last_24Mo": 0, "number_trades_verified_past_12_months": null, "tot_month_obligation_joint_and_indv_accts_verified_past_12_months": null, "number_of_deduplicated_inquiries_in_past_6_months": null, "credit_vision_revolver_6": {"RVLR61": null}, "tot_month_obligation_contractually_liable_accts_verified_past_12_months": null, "applicant_age_years": 27, "total_current_balance_mortgage_accounts": 0, "number_deduped_finance_inquiries_past_3_months": 1, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": 53}, "matching": {"address": {"score": 0.72, "matched": false}, "name": {"score": 1, "matched": true}, "dob": {"score": 1, "matched": true}, "ssn": {"score": 1, "matched": true}}}}, "audit_archive": null}