{"status_code": 201, "error": null, "timestamp": 1655829952597, "evaluation_token": "L-3PT7ShMHPXU75qzFlvjt", "entity_token": "P-p9CotoHDjSJAnWw982RU", "parent_entity_token": null, "application_token": "5hiyzkaF3x0B9XPxKY6l4WI3i6diVaHt", "application_version_id": 2, "champion_challenger_id": null, "summary": {"result": "success", "score": 1, "tags": ["KYC SSN Match", "Address Matched", "KYC Address Match"], "outcome_reasons": [], "outcome": "Approved", "services": {"Lexis Nexis Instant ID": "executed", "TransUnion Credit": "executed"}, "alloy_fraud_score": null}, "supplied": {"name_first": "first", "name_last": "last", "address_line_1": "address line 1", "address_city": "city", "address_state": "NV", "address_postal_code": "89108", "birth_date": "2000-01-01", "address_line_2": "2", "email_address": "<EMAIL>", "document_ssn": "*********", "phone_number": "+11234567890"}, "formatted": {"name_first": "first", "name_last": "last", "address_line_1": "address line 1", "address_city": "city", "address_state": "NV", "address_postal_code": "89108", "birth_date": "2000-01-01", "address_line_2": "2", "email_address": "<EMAIL>", "document_ssn": "*********", "phone_number": "+11234567890", "address_country_code": "US", "phone_country_code": "US", "age": "64"}, "matching": {"address": {"score": 0.99, "matched": ["TransUnion Credit", "Lexis Nexis Instant ID"], "unmatched": []}, "name": {"score": 1, "matched": ["TransUnion Credit", "Lexis Nexis Instant ID"], "unmatched": []}, "dob": {"score": 1, "matched": ["TransUnion Credit", "Lexis Nexis Instant ID"], "unmatched": []}, "ssn": {"score": 1, "matched": ["TransUnion Credit", "Lexis Nexis Instant ID"], "unmatched": []}, "phone": {"score": 1, "matched": ["Lexis Nexis Instant ID"], "unmatched": []}}, "diligence": {"watchlists": {"lists": ["BES", "CFTC", "DTC", "EUDT", "FBI", "FCEN", "FAR", "IMW", "OFAC", "OCC", "OSFI", "PEP", "SDT", "UNNT", "BIS", "WBIF"], "matches": []}, "fraud": null, "financial": {"credit": [{"source": "TransUnion Credit", "models": {"vantagescore30": {"name": "VantageScore 3.0", "codes": ["vantagescore30-98", "vantagescore30-15", "vantagescore30-63", "vantagescore30-95"], "score": 575, "model_codes": [{"code": "vantagescore30-15", "description": "Newest delinquent or derogatory payment status on your accounts is too recent"}, {"code": "vantagescore30-63", "description": "Lack of sufficient relevant real estate account information"}, {"code": "vantagescore30-95", "description": "You have too many collection agency accounts that are unpaid"}, {"code": "vantagescore30-98", "description": "There is a bankruptcy on your credit report"}], "bureau": "TransUnion Credit"}}}]}, "identity_questions": null, "device": null}, "related_data": {}, "raw_responses": {"TransUnion Credit": [{"creditBureau": {"$": {"xmlns": "http://www.transunion.com/namespace", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.transunion.com/namespace"}, "document": "response", "version": "2.26", "transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "4601416", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2022-06-21T11:45:49.510-05:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "false", "market": "12", "submarket": "LV", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "1993-01-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "indicative": {"socialSecurity": {"$": {"source": "file"}, "number": "*********"}}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "B", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2019-12-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2020-07-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "DRG", "type": "affiliate"}, "account": {"type": "CC"}, "paymentHistory": {"paymentPattern": {"startDate": {"_": "2020-06-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "XXXXX1"}, "historicalCounters": {"monthsReviewedCount": "06", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "updateMethod": "manual"}, {"subscriber": {"industryCode": "FF", "memberCode": "02CXY002", "name": {"unparsed": "OPORTUNPROG"}}, "portfolioType": "installment", "accountNumber": "4145579", "ECOADesignator": "individual", "dateOpened": {"_": "2020-03-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2020-12-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2020-12-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "9P", "remark": {"code": "PPL", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "007"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2021-12-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FZ", "memberCode": "02GYN001", "name": {"unparsed": "TBOM/ASPIRE"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2021-07-04", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-05-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "111111"}, "historicalCounters": {"monthsReviewedCount": "06", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-06-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "01TGU001", "name": {"unparsed": "MERRICK BK"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2021-06-20", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-05-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "***********"}, "historicalCounters": {"monthsReviewedCount": "11", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-05-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "054MR013", "name": {"unparsed": "CREDITONEBNK"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2019-10-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-05-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************1111111"}, "historicalCounters": {"monthsReviewedCount": "31", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-06-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FY", "memberCode": "0537D051", "name": {"unparsed": "FB&T/MERCURY"}}, "portfolioType": "revolving", "accountNumber": "**********", "ECOADesignator": "individual", "dateOpened": {"_": "2020-03-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-05-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************11"}, "historicalCounters": {"monthsReviewedCount": "26", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-06-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FP", "memberCode": "02GB1001", "name": {"unparsed": "UPGRADE INC"}}, "portfolioType": "open", "accountNumber": "14214", "ECOADesignator": "individual", "dateOpened": {"_": "2022-01-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "account": {"type": "LC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-04-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "1111"}, "historicalCounters": {"monthsReviewedCount": "04", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-05-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FP", "memberCode": "02FNE002", "name": {"unparsed": "OLLO/TBOM"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2020-01-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-04-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************1111"}, "historicalCounters": {"monthsReviewedCount": "28", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-05-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "054MR013", "name": {"unparsed": "CREDITONEBNK"}}, "portfolioType": "revolving", "accountNumber": "***********", "ECOADesignator": "individual", "dateOpened": {"_": "2021-04-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-04-26", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************"}, "historicalCounters": {"monthsReviewedCount": "12", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-05-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FF", "memberCode": "02AQE003", "name": {"unparsed": "TBOM/CONTFIN"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2019-12-11", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-05-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-04-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************1111"}, "historicalCounters": {"monthsReviewedCount": "28", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-05-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "0282E024", "name": {"unparsed": "CITI SHELL"}}, "portfolioType": "revolving", "accountNumber": "115342", "ECOADesignator": "individual", "dateOpened": {"_": "1996-07-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2018-03-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2016-11-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2014-11-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "CBG", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2018-02-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************************************"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2014-11-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "AZ", "memberCode": "082WT002", "name": {"unparsed": "BMW FIN SVC"}}, "portfolioType": "installment", "accountNumber": "**********", "ECOADesignator": "individual", "dateOpened": {"_": "2014-12-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2017-12-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2017-12-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "FTO", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "039"}, "account": {"type": "AL"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2017-11-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "***********************************"}, "historicalCounters": {"monthsReviewedCount": "35", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2017-12-23", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "01TGU001", "name": {"unparsed": "MERRICK BK"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2005-06-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2017-07-11", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2017-04-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2015-10-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "INA", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2017-06-11", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************************************"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2015-10-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "AZ", "memberCode": "082WT002", "name": {"unparsed": "BMW FIN SVC"}}, "portfolioType": "installment", "accountNumber": "**********", "ECOADesignator": "individual", "dateOpened": {"_": "2011-09-15", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2015-01-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2015-01-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "CLO", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "076"}, "account": {"type": "AU"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2014-12-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************************11"}, "historicalCounters": {"monthsReviewedCount": "39", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2015-01-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "0908N664", "name": {"unparsed": "WELLS FARGO"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2016-04-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-08-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "064DB002", "name": {"unparsed": "CITI"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2018-03-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-11-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-07-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "********", "name": {"unparsed": "BK OF AMER"}}, "portfolioType": "revolving", "accountNumber": "2806", "ECOADesignator": "individual", "dateOpened": {"_": "2016-05-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-10-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-08-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "026QK001", "name": {"unparsed": "JPMCB CARD"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2001-02-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-10-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-07-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-06-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2002-08-20", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-10-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2019-01-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BZ", "memberCode": "01ZZB001", "name": {"unparsed": "BRCLYSBANKDE"}}, "portfolioType": "revolving", "accountNumber": "***********", "ECOADesignator": "individual", "dateOpened": {"_": "2008-03-29", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-10-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-09-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FZ", "memberCode": "02BQT001", "name": {"unparsed": "LENDING CLUB"}}, "portfolioType": "installment", "accountNumber": "********", "ECOADesignator": "individual", "dateOpened": {"_": "2015-10-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-07-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-10-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-04-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "********", "name": {"unparsed": "DISCOVERBANK"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2018-02-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2019-07-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-11-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "creditLimit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-07", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "QU", "memberCode": "********", "name": {"unparsed": "AMER FST CU"}}, "portfolioType": "installment", "accountNumber": "034386268320141017", "ECOADesignator": "terminated", "dateOpened": {"_": "2014-10-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2018-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-05-04", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "QU", "memberCode": "********", "name": {"unparsed": "AMER FST CU"}}, "portfolioType": "installment", "accountNumber": "034386268220130813", "ECOADesignator": "terminated", "dateOpened": {"_": "2013-08-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2018-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2018-08-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "highCredit": "*********", "accountRating": "UR", "remark": {"code": "CBL", "type": "affiliate"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-08-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}], "collection": {"subscriber": {"industryCode": "YC", "memberCode": "02BRL001", "name": {"unparsed": "AD ASTRA REC"}}, "portfolioType": "open", "accountNumber": "8794353", "ECOADesignator": "individual", "account": {"type": "AG"}, "dateOpened": {"_": "2022-01-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-06-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "RAPIDCASHINC COM 93 NV"}, "creditorClassification": "financial", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "remark": {"code": "CLA", "type": "affiliate"}, "updateMethod": "automated"}, "publicRecord": {"type": "7X", "subscriber": {"industryCode": "ZP", "memberCode": "031XH053"}, "docketNumber": "1914343", "attorney": "THOMAS E CROWE", "dateFiled": {"_": "2019-07-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "datePaid": {"_": "2019-10-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "ECOADesignator": "individual", "source": {"type": "usBankruptcy"}}, "inquiry": [{"ECOADesignator": "individual", "subscriber": {"industryCode": "FZ", "memberCode": "********", "inquirySubscriberPrefixCode": "12NC", "name": {"unparsed": "CRB/UPGRADE"}}, "date": {"_": "2022-01-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"ECOADesignator": "individual", "subscriber": {"industryCode": "BC", "memberCode": "********", "inquirySubscriberPrefixCode": "29UT", "name": {"unparsed": "MERRICK BK"}}, "date": {"_": "2021-06-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}]}}, "addOnProduct": [{"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+575", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "98"}, {"rank": "2", "code": "15"}, {"rank": "3", "code": "63"}, {"rank": "4", "code": "95"}]}, "scoreCard": "01"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "0000000126"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "*********0"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02F", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02H", "value": "*********1"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0000010150"}, {"algorithmID": "00H86", "id": "AGG208", "value": "0000005950"}, {"algorithmID": "00H86", "id": "AGG218", "value": "0000004100"}, {"algorithmID": "00H86", "id": "AGG223", "value": "0000004424"}, {"algorithmID": "00H86", "id": "AGG407", "value": "0000000244"}, {"algorithmID": "00H86", "id": "AGG504", "value": "0000005187"}, {"algorithmID": "00H86", "id": "AGG518", "value": "0000002003"}, {"algorithmID": "00H86", "id": "AGG602", "value": "0000005850"}, {"algorithmID": "00H86", "id": "AGG801", "value": "0000000211"}, {"algorithmID": "00H86", "id": "AGG803", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG807", "value": "0000000152"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********3"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000080.88"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000088.65"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000450"}}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "-000000002"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "0000001.25"}]}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "0000000228"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********7"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000013649"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000017"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "0000000090"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "0000000039"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "-000000002"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "0000000090"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "0000001304"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "BR34S", "value": "0000000087"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "0000005326"}, {"algorithmID": "00WR3", "id": "G202B", "value": "-000000005"}, {"algorithmID": "00WR3", "id": "G213A", "value": "0*********"}, {"algorithmID": "00WR3", "id": "G215B", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********3"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "0000000129"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "S061S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "S071A", "value": "*********1"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G242F", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G243F", "value": "*********1"}, {"algorithmID": "00WR3", "id": "US21S", "value": "0000000027"}, {"algorithmID": "00WR3", "id": "US36S", "value": "0000000018"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G404S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G208B", "value": "0*********"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "*********8"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "0000000200"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "0000000032"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "0000000024"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "0000000017"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "0000000027"}]}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "0000000427"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "0000000016"}]}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "0000000218"}, {"algorithmID": "00WG9", "id": "REV255", "value": "0000000090"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "REV322", "value": "0000001025"}, {"algorithmID": "00WH1", "id": "BKC320", "value": "0000000017"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "0000001025"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "0000001774"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "0000001.70"}, {"algorithmID": "00WG8", "id": "REV222", "value": "0000001.73"}, {"algorithmID": "00WG8", "id": "REV225", "value": "0000001.41"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "-000002.00"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "PRREVLR"}}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000003"}}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z23", "id": "LINKF001", "value": "HIT"}, {"algorithmID": "00Z23", "id": "LINKF002", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF003", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF004", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF005", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF006", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF007", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF008"}, {"algorithmID": "00Z23", "id": "LINKF009"}, {"algorithmID": "00Z23", "id": "LINKF010"}, {"algorithmID": "00Z23", "id": "LINKF011"}, {"algorithmID": "00Z23", "id": "LINKF012"}, {"algorithmID": "00Z23", "id": "LINKF013"}, {"algorithmID": "00Z23", "id": "LINKF014"}, {"algorithmID": "00Z23", "id": "LINKF015", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF016"}, {"algorithmID": "00Z23", "id": "LINKF017"}, {"algorithmID": "00Z23", "id": "LINKF018", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF019", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF020", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF021", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF022", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF023", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF024"}, {"algorithmID": "00Z23", "id": "LINKF025"}, {"algorithmID": "00Z23", "id": "LINKF026"}, {"algorithmID": "00Z23", "id": "LINKF027"}, {"algorithmID": "00Z23", "id": "LINKF028"}, {"algorithmID": "00Z23", "id": "LINKF029"}, {"algorithmID": "00Z23", "id": "LINKF030"}, {"algorithmID": "00Z23", "id": "LINKF031", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF032", "value": "N"}, {"algorithmID": "00Z23", "id": "LINKF033"}, {"algorithmID": "00Z23", "id": "LINKF034"}, {"algorithmID": "00Z23", "id": "LINKF035", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF036", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF037", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF038", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF039", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF040", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF041", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF042", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF043", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF044", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF045"}, {"algorithmID": "00Z23", "id": "LINKF046", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF047", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF048", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF049"}, {"algorithmID": "00Z23", "id": "LINKF050", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF051", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF052", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF053", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF054", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF055", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF056", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF057", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF058", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF059", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF060"}, {"algorithmID": "00Z23", "id": "LINKF061", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF062", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF063", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF064", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF065", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF066", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF067", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF068", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF069", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF070", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF071", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF072", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF073", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF074", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF075", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF076", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF077", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF078", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF079", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF080"}, {"algorithmID": "00Z23", "id": "LINKF081"}, {"algorithmID": "00Z23", "id": "LINKF082", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF083", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF084", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF085", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF086", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF087", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF088", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF089", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF090", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF091", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF092", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF093", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF094", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF095", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF096", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF097", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF098", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF099", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF100", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF101", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF102", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF103", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF104", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF105", "value": "0000000824"}, {"algorithmID": "00Z23", "id": "LINKF106"}, {"algorithmID": "00Z23", "id": "LINKF107", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF108", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF109", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF110", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF111", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF112", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF113", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF114", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF115", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF116", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF117", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF118", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF119", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF120", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF121", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF122", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF123"}, {"algorithmID": "00Z23", "id": "LINKF124"}, {"algorithmID": "00Z23", "id": "LINKF125", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF126", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF127", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF128", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF129", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF130", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF131", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF132", "value": "0000001.00"}, {"algorithmID": "00Z23", "id": "LINKF133", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF134", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF135"}, {"algorithmID": "00Z23", "id": "LINKF136"}, {"algorithmID": "00Z23", "id": "LINKF137", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF138", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF139", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF140", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF141", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF142", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF143", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF144", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF145", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF146", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF147", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF148", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF149", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF150", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF151", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF152", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF153", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF154", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF155", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF156", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF157", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF158", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF159", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF160", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF161", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF162", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF163", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF164", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF165", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF166", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF167", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF168", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF169", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF170", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF171", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF172", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF173", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF174", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF175", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF176", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF177", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF178", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF179", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF180", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF181", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF182"}, {"algorithmID": "00Z23", "id": "LINKF183"}, {"algorithmID": "00Z23", "id": "LINKF184"}, {"algorithmID": "00Z23", "id": "LINKF185", "value": "0000000997"}, {"algorithmID": "00Z23", "id": "LINKF186"}, {"algorithmID": "00Z23", "id": "LINKF187"}, {"algorithmID": "00Z23", "id": "LINKF188"}, {"algorithmID": "00Z23", "id": "LINKF189", "value": "0000000824"}, {"algorithmID": "00Z23", "id": "LINKF190"}, {"algorithmID": "00Z23", "id": "LINKF191"}, {"algorithmID": "00Z23", "id": "LINKF192"}, {"algorithmID": "00Z23", "id": "LINKF193", "value": "0000000824"}, {"algorithmID": "00Z23", "id": "LINKF194"}, {"algorithmID": "00Z23", "id": "LINKF195", "value": "0000000998"}, {"algorithmID": "00Z23", "id": "LINKF196"}, {"algorithmID": "00Z23", "id": "LINKF197", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF198", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF199", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF200", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF201", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF202", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF203", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF204", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF205", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF206", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF207", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF208", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF209", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF210", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF211", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF212", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF213"}]}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "0000000898"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000000838"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000000838"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "*********4"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "9999.99999"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}}}}], "Lexis Nexis Instant ID": [{"response": {"Header": {"Status": 0, "TransactionId": "160130863R125464"}, "Result": {"InputEcho": {"Name": {"First": "first", "Last": "last"}, "Address": {"StreetAddress1": "street address 1", "StreetAddress2": "street address 2", "City": "city", "State": "NV", "Zip5": "89108"}, "DOB": {"Year": "2000", "Month": "1", "Day": "01"}, "SSN": "*********", "HomePhone": "1234567890", "UseDOBFilter": true, "DOBRadius": 1, "Passport": {"Number": "", "ExpirationDate": null, "Country": "", "MachineReadableLine1": "", "MachineReadableLine2": ""}, "Email": "<EMAIL>", "Channel": null, "OwnOrRent": null, "ApplicationDateTime": {"Year": "2022", "Month": "6", "Day": "21", "Hour24": "16", "Minute": "45", "Second": "48"}}, "UniqueId": "1489369131", "NameAddressSSNSummary": 12, "AdditionalScore1": "0", "AdditionalScore2": "0", "PhoneOfNameAddress": "1234567890", "DOBVerified": true, "CurrentName": {"First": "first", "Last": "last"}, "SSNInfo": {"Valid": "G", "IssuedLocation": "New York", "IssuedStartDate": {"Year": "1971", "Month": "01", "Day": "01"}, "IssuedEndDate": {"Year": "1971", "Month": "12", "Day": "31"}}, "ReversePhone": {"Name": {"First": "first", "Last": "last"}, "Address": {"StreetAddress1": "street address 1", "City": "city", "State": "NV", "Zip5": "89108", "StreetName": "street", "StreetNumber": "3050", "UnitDesignation": "APT", "UnitNumber": "1025", "StreetPreDirection": "N"}}, "ComprehensiveVerification": {"ComprehensiveVerificationIndex": 50, "PotentialFollowupActions": {"FollowupAction": [{"RiskCode": "C", "Description": "Verify name with Address (via DL, utility bill, Directory Assistance, paycheck stub, or other Government Issued ID)"}]}, "RiskIndicators": {"RiskIndicator": [{"RiskCode": "PA", "Description": "Potential address discrepancy - the Input address may be previous address", "Sequence": 1}, {"RiskCode": "10", "Description": "The input phone number is a mobile number", "Sequence": 2}]}}, "NameAddressPhone": {"Summary": "12", "Type": "P"}, "AddressPOBox": false, "AddressCMRA": false, "DOBMatchLevel": 8, "PassportValidated": false, "SSNFoundForLexID": true, "InstantIDVersion": "1", "VerifiedInput": {"SSN": "12312xxxx", "HomePhone": "1234567890", "Name": {"First": "first", "Last": "last"}, "Address": {"StreetAddress1": "street address 1", "City": "city", "State": "NV", "Zip5": "89108", "County": "CLARK", "Zip4": "6552", "StreetPreDirection": "N", "UnitDesignation": "APT", "StreetNumber": "3050", "UnitNumber": "1025", "StreetSuffix": "BLVD", "StreetName": "JONES"}, "DOB": {"Year": "2000", "Month": "01", "Day": "XX"}}}}}]}, "formatted_responses": {"TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 23, "revolving_count": 16, "open_count": 1, "line_of_credit_count": 0, "installment_count": 6, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0}, "collections": {"count": 1, "revolving_count": 0, "open_count": 1, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 1, "percentage_past_due": 1, "past_due_total_dollar_amount": 805}, "consumer_statement_indicator": false, "public_records": {"count": 1, "public_records_within_last_year": 0, "public_records_within_last_three_years": 1, "public_records_within_last_five_years": 1, "public_records_within_last_ten_years": 1, "most_recent_public_record": "2019-07-08", "public_records_reason_codes": ["public-record-7X"]}, "inquiries": {"count": 2, "inquiries_within_last_year": 1, "inquiries_within_last_three_years": 2, "inquiries_within_last_five_years": 2, "inquiries_within_last_ten_years": 2, "most_recent_inquiry": "2022-01-08"}, "vantagescore40": {"exists": false, "score": -1}, "vantagescore30": {"exists": true, "score": 575}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": false, "condition": "none", "type": "none", "inquiries_last_60_days": -1, "address_status": "none"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 7, "AT28B": 13649, "AT32S": 2690, "AT36S": 0, "AT104S": 17, "AU21S": 90, "AU34S": -3, "AU51A": 39, "AU101S": -2, "BC34S": 90, "BC103S": 1304, "BC110S": 0, "BI09S": -1, "BI34S": -1, "BR02S": 3, "BR34S": 87, "FI25S": -3, "FI34S": -3, "FR101S": 5326, "G202B": -5, "G213A": 805, "G215B": 1, "G216S": 1, "G224C": 3, "G228S": -1, "IN20S": 129, "OF27S": -3, "RE06S": 0, "S061S": 0, "S071A": 1, "ST01S": 0, "ST24S": -1, "ST27S": -1, "ST28S": -1, "ST99S": -1, "G242F": 0, "G243F": 1, "US21S": 27, "US36S": 18, "JT20S": -1, "JT21S": -1, "G404S": 0, "G411S": 1, "G208B": 100}, "factor_code_4": null, "vantage_v4_score_factor_3": null, "fico_9_bankcard_score": null, "mla_status": null, "factor_code_1": null, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "military_leads": {"has_military_association": false}, "factor_code_2": null, "vantage_v_4_factor_1": null, "vantage_v4_score_factor_2": null, "consumer_statement_text": null, "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": null, "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "credit_vision_revolver_6": {"RVLR61": null}, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": null}, "matching": {"address": {"score": 0.99, "matched": true}, "name": {"score": 1, "matched": true}, "dob": {"score": 1, "matched": true}, "ssn": {"score": 1, "matched": true}}}, "Lexis Nexis Instant ID": {"matching": {"name": {"score": 1, "matched": true}, "address": {"score": 1, "matched": true}, "ssn": {"score": 1, "matched": true}, "dob": {"score": 1, "matched": true}, "phone": {"score": 1, "matched": true}, "passport": {"score": 0, "matched": false}}, "diligence": {"watchlists": {"lists": ["BES", "CFTC", "DTC", "EUDT", "FBI", "FCEN", "FAR", "IMW", "OFAC", "OCC", "OSFI", "PEP", "SDT", "UNNT", "BIS", "WBIF"], "matches": []}, "fraud": {"score": null, "flags": null}, "financial": {"credit": null, "banking": null}, "identity_questions": null}, "data": {"watchlists": {"pep": 0, "ofac": 0}, "identity_theft_risk": 1, "risk_codes": ["PA", "10"], "followup_codes": [], "address": {"po_box": false, "commercial_mail": false}, "ssn": {"issuance_start_date": "1971-01-01", "issuance_end_date": "1971-12-31", "issuance_state": "New York"}, "verification": {"dob_day": true, "dob_month": true, "dob_year": true, "name_first_last": true, "name_first_address": true, "name_last_address": true, "name_first_phone": true, "name_last_phone": true, "name_first_ssn": true, "name_last_ssn": true, "address_phone": true, "address_ssn": true, "name_first_last_address": true, "name_first_last_phone": true, "name_first_last_ssn": true, "name_first_address_phone": true, "name_last_address_phone": true, "name_first_last_address_phone": true, "name_first_address_ssn": true, "name_last_address_ssn": true, "name_first_last_address_ssn": true}}}}, "audit_archive": null}