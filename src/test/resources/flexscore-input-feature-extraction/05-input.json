{"raw_responses": {"TransUnion Credit": [{"creditBureau": {"transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "4601416", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2024-02-24T17:41:45.169-06:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "false", "market": "15", "submarket": "DF", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "2022-06-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "BC", "memberCode": "026QK001", "name": {"unparsed": "JPMCB CARD"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2023-09-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2024-02-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "datePaidOut": {"_": "2024-02-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "account": {"type": "FX"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-01-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "1111"}, "historicalCounters": {"monthsReviewedCount": "04", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-02-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "RR", "memberCode": "02H3A200", "name": {"unparsed": "CAFMGT"}}, "portfolioType": "open", "accountNumber": "***********", "ECOADesignator": "jointContractLiability", "dateOpened": {"_": "2022-08-04", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2024-01-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "datePaidOut": {"_": "2024-01-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "account": {"type": "RA"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2023-12-25", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "****************"}, "historicalCounters": {"monthsReviewedCount": "16", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-01-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FZ", "memberCode": "02FV9004", "name": {"unparsed": "VARO BANK NA"}}, "portfolioType": "open", "accountNumber": "4665", "ECOADesignator": "individual", "dateOpened": {"_": "2023-03-02", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2023-12-29", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "datePaidOut": {"_": "2023-12-04", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "account": {"type": "SC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2023-11-29", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "*********"}, "historicalCounters": {"monthsReviewedCount": "09", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2023-12-04", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}], "inquiry": {"ECOADesignator": "individual", "subscriber": {"industryCode": "BB", "memberCode": "********", "inquirySubscriberPrefixCode": "07DM", "name": {"unparsed": "WF PLL"}}, "date": {"_": "2022-11-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}}}, "addOnProduct": [{"code": "06400", "status": "delivered", "idMismatchAlert": {"type": "address", "condition": "mismatch", "inquiriesInLast60Count": "00", "addressStatus": "current"}}, {"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+741", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "12"}, {"rank": "2", "code": "14"}, {"rank": "3", "code": "63"}, {"rank": "4", "code": "06"}]}, "scoreCard": "10"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "0000000011"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "0000000050"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02F", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02H", "value": "*********1"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG208", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG218", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG223", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG407", "value": "0000000569"}, {"algorithmID": "00H86", "id": "AGG504", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG518", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG602", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG801", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG803", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG807", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000000.00"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000000.00"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000288"}}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "-000000001"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "-000002.00"}]}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "-000000002"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000002982"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "0000000999"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000100"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "-000000005"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "BR34S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FI101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G202B", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "G213A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G215B", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********0"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "S061S", "value": "0000000999"}, {"algorithmID": "00WR3", "id": "S071A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G242F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G243F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US36S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "0000000018"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "0000000018"}, {"algorithmID": "00WR3", "id": "G404S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G208B", "value": "-000000005"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "-000000002"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "-000000009"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "-000000009"}]}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "-000000009"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "-000000002"}]}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "-000000002"}, {"algorithmID": "00WG9", "id": "REV255", "value": "-000000002"}, {"algorithmID": "00WG9", "id": "STD255", "value": "-000000001"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "REV322", "value": "0*********"}, {"algorithmID": "00WH1", "id": "BKC320", "value": "*********5"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "0*********"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "0*********"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "-000002.00"}, {"algorithmID": "00WG8", "id": "REV222", "value": "-000002.00"}, {"algorithmID": "00WG8", "id": "REV225", "value": "-000002.00"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "-000001.00"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "NOACTBC"}}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000001"}}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z23", "id": "LINKF001", "value": "HIT"}, {"algorithmID": "00Z23", "id": "LINKF002", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF003", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF004", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF005", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF006", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF007", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF008"}, {"algorithmID": "00Z23", "id": "LINKF009"}, {"algorithmID": "00Z23", "id": "LINKF010"}, {"algorithmID": "00Z23", "id": "LINKF011"}, {"algorithmID": "00Z23", "id": "LINKF012"}, {"algorithmID": "00Z23", "id": "LINKF013"}, {"algorithmID": "00Z23", "id": "LINKF014"}, {"algorithmID": "00Z23", "id": "LINKF015", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF016"}, {"algorithmID": "00Z23", "id": "LINKF017"}, {"algorithmID": "00Z23", "id": "LINKF018", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF019", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF020", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF021", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF022", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF023", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF024"}, {"algorithmID": "00Z23", "id": "LINKF025"}, {"algorithmID": "00Z23", "id": "LINKF026"}, {"algorithmID": "00Z23", "id": "LINKF027"}, {"algorithmID": "00Z23", "id": "LINKF028"}, {"algorithmID": "00Z23", "id": "LINKF029"}, {"algorithmID": "00Z23", "id": "LINKF030"}, {"algorithmID": "00Z23", "id": "LINKF031", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF032", "value": "N"}, {"algorithmID": "00Z23", "id": "LINKF033"}, {"algorithmID": "00Z23", "id": "LINKF034"}, {"algorithmID": "00Z23", "id": "LINKF035", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF036", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF037", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF038", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF039", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF040", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF041", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF042", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF043", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF044", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF045", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF046", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF047", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF048", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF049"}, {"algorithmID": "00Z23", "id": "LINKF050", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF051", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF052", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF053", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF054", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF055", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF056", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF057", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF058", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF059", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF060", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF061", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF062", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF063", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF064", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF065", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF066", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF067", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF068", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF069", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF070", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF071", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF072", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF073", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF074", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF075", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF076", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF077", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF078", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF079", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF080"}, {"algorithmID": "00Z23", "id": "LINKF081"}, {"algorithmID": "00Z23", "id": "LINKF082", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF083", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF084", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF085", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF086", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF087", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF088", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF089", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF090", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF091", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF092", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF093", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF094", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF095", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF096", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF097", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF098", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF099", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF100", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF101", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF102", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF103", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF104", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF105", "value": "0000000495"}, {"algorithmID": "00Z23", "id": "LINKF106"}, {"algorithmID": "00Z23", "id": "LINKF107", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF108", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF109", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF110", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF111", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF112", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF113", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF114", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF115", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF116", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF117", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF118", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF119", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF120", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF121", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF122", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF123"}, {"algorithmID": "00Z23", "id": "LINKF124"}, {"algorithmID": "00Z23", "id": "LINKF125", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF126", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF127", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF128", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF129", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF130", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF131", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF132", "value": "0002500.00"}, {"algorithmID": "00Z23", "id": "LINKF133", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF134", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF135"}, {"algorithmID": "00Z23", "id": "LINKF136"}, {"algorithmID": "00Z23", "id": "LINKF137", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF138", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF139", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF140", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF141", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF142", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF143", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF144", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF145", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF146", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF147", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF148", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF149", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF150", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF151", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF152", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF153", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF154", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF155", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF156", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF157", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF158", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF159", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF160", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF161", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF162", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF163", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF164", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF165", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF166", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF167", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF168", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF169", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF170", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF171", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF172", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF173", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF174", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF175", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF176", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF177", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF178", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF179", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF180", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF181", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF182"}, {"algorithmID": "00Z23", "id": "LINKF183"}, {"algorithmID": "00Z23", "id": "LINKF184"}, {"algorithmID": "00Z23", "id": "LINKF185", "value": "0000000495"}, {"algorithmID": "00Z23", "id": "LINKF186"}, {"algorithmID": "00Z23", "id": "LINKF187"}, {"algorithmID": "00Z23", "id": "LINKF188"}, {"algorithmID": "00Z23", "id": "LINKF189"}, {"algorithmID": "00Z23", "id": "LINKF190"}, {"algorithmID": "00Z23", "id": "LINKF191"}, {"algorithmID": "00Z23", "id": "LINKF192"}, {"algorithmID": "00Z23", "id": "LINKF193", "value": "0000000495"}, {"algorithmID": "00Z23", "id": "LINKF194"}, {"algorithmID": "00Z23", "id": "LINKF195", "value": "0000000495"}, {"algorithmID": "00Z23", "id": "LINKF196"}, {"algorithmID": "00Z23", "id": "LINKF197", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF198", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF199", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF200", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF201", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF202", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF203", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF204", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF205", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF206", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF207", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF208", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF209", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF210", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF211", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF212", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF213"}]}}, {"code": "07051", "status": "delivered", "militaryLendingActSearch": {"$": {"searchStatus": "noMatch"}}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000000495"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "9999.99999"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}}}}]}, "formatted_responses": {"TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 3, "revolving_count": 1, "open_count": 2, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0, "revolving_accounts_total_current_balance": 0, "line_of_credit_accounts_total_current_balance": 0}, "collections": {"count": 0, "revolving_count": 0, "open_count": 0, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0}, "consumer_statement_indicator": false, "public_records": {"count": 0, "public_records_within_last_year": 0, "public_records_within_last_three_years": 0, "public_records_within_last_five_years": 0, "public_records_within_last_ten_years": 0, "most_recent_public_record": "1000-01-01", "public_records_reason_codes": []}, "inquiries": {"count": 1, "inquiries_within_last_year": 0, "inquiries_within_last_three_years": 1, "inquiries_within_last_five_years": 1, "inquiries_within_last_ten_years": 1, "most_recent_inquiry": "2022-11-22"}, "vantagescore40": {"exists": false, "score": -1}, "vantagescore30": {"exists": true, "score": 741}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "ficoscore4": {"exists": false, "score": -1}, "syntheticidfraudscore": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": true, "condition": "mismatch", "type": "address", "inquiries_last_60_days": 0, "address_status": "current"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 2, "AT28B": 2982, "AT32S": 0, "AT36S": 999, "AT104S": 100, "AU21S": -1, "AU34S": -1, "AU51A": -1, "AU101S": -1, "BC34S": 0, "BC103S": -5, "BC110S": 0, "BI09S": -1, "BI34S": -1, "BR02S": 1, "BR34S": 0, "FI25S": -1, "FI34S": -1, "FI101S": -1, "FR101S": -1, "G202B": -3, "G213A": -4, "G215B": -4, "G216S": 0, "G224C": 0, "G228S": -1, "IN20S": -1, "OF27S": -1, "RE06S": 1, "S061S": 999, "S071A": -4, "ST01S": 0, "ST24S": -1, "ST27S": -1, "ST28S": -1, "ST99S": -1, "G242F": -1, "G243F": -1, "US21S": -1, "US36S": -1, "JT20S": 18, "JT21S": 18, "G404S": -1, "G411S": 1, "G208B": -5, "RE21S": -99999, "G408S": -9, "RE101S": null, "BKC324": null, "G209S": -999, "BKC122": -9, "BKC14": -1, "RT25S": -999, "HR101S": null, "ST50S": -999999, "G215A": -9, "G960S": -9}, "factor_code_4": null, "vantage_v4_score_factor_3": null, "fico_9_bankcard_score": null, "max_credit_limit_open_credit_trades": 500, "mla_status": "delivered", "factor_code_1": null, "credit_vision_link_score_FA2": [null], "months_on_file": 21, "employment": {"employer": null}, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "credit_vision_alternative_checking_attributes": {"LINKA006": 3, "LINKA014": 0, "LINKA027": 495}, "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "credit_vision_new_account_v2": {"score": null}, "number_foreclosure_bankruptcy_last_4y": 0, "military_leads": {"has_military_association": false}, "credit_vision_premeium_algorithms": {"paymnt01": null, "mnpmag01": null}, "factor_code_2": null, "count_60_days_late_payments": 0, "vantage_v_4_factor_1": null, "consumer_statement_text": null, "vantage_v4_score_factor_2": null, "months_since_most_recent_deliquency": 0, "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": null, "count_90_days_late_payments": 0, "total_monthly_debt_payment": 0, "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "number_deduped_bank_inquiries_past_3_months": -1, "number_charge_offs_last_24Mo": 0, "number_trades_verified_past_12_months": null, "tot_month_obligation_joint_and_indv_accts_verified_past_12_months": null, "number_of_deduplicated_inquiries_in_past_6_months": null, "credit_vision_revolver_6": {"RVLR61": null}, "tot_month_obligation_contractually_liable_accts_verified_past_12_months": null, "applicant_age_years": 22, "total_current_balance_mortgage_accounts": 0, "number_deduped_finance_inquiries_past_3_months": -1, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": null}, "matching": {"address": {"score": 0.83, "matched": false}, "name": {"score": 1, "matched": true}, "dob": {"score": 1, "matched": true}, "ssn": {"score": 1, "matched": true}}}, "Sentilink": {"data": {"scores": {"sentilink_abuse_score": 12, "sentilink_first_party_synthetic_score": 19, "sentilink_third_party_synthetic_score": 55, "sentilink_id_theft_score": 38}, "reasonCodes": {"allReasonCodes": ["R004", "R011", "R016", "R014", "R029", "R034", "R028"], "moreFraudyCodes": [], "lessFraudyCodes": ["R004", "R011", "R016", "R014", "R029", "R034", "R028"]}, "error": null, "consumer_history": {"shared_ssn_count": null, "number_of_ssns": 0}, "years_since_filing_date": -1}}}}