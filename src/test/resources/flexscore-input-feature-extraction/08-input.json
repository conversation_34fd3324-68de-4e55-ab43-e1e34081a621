{"status_code": 201, "error": null, "timestamp": 1740883051660, "evaluation_token": "L-liFWsGk5psbinqKboiy6", "entity_token": "P-sOK53lmgE554jnNzAS74", "parent_entity_token": null, "application_token": "5hiyzkaF3x0B9XPxKY6l4WI3i6diVaHt", "application_version_id": 20, "champion_challenger_id": null, "summary": {"result": "success", "score": 0.99, "tags": [], "outcome_reasons": [], "outcome": null, "services": {"TransUnion Credit": "executed", "Sentilink": "executed"}, "custom_fields": {"financial_partner_id": 2}, "alloy_fraud_score": null}, "supplied": {"meta": {"customer_id": 1508377}, "name_first": "<PERSON>", "name_last": "<PERSON>", "address_line_1": "402 Tuscan Reserve Dr", "address_line_2": "516", "address_city": "Palm Coast", "address_state": "FL", "address_postal_code": "32164", "birth_date": "1995-04-30", "document_ssn": "607828822", "email_address": "<EMAIL>", "phone_number": "+13865695272", "financial_partner_id": 2}, "formatted": {"name_first": "<PERSON>", "name_last": "<PERSON>", "address_line_1": "402 Tuscan Reserve Dr", "address_line_2": "516", "address_city": "Palm Coast", "address_state": "FL", "address_postal_code": "32164", "birth_date": "1995-04-30", "document_ssn": "607828822", "email_address": "kennedy<PERSON><PERSON><PERSON>@gmail.com", "phone_number": "+13865695272", "financial_partner_id": 2, "phone_country_code": "US", "age": "29"}, "meta": {"customer_id": 1508377}, "matching": {"address": {"score": 0.96, "matched": ["TransUnion Credit"], "unmatched": []}, "name": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}, "dob": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}, "ssn": {"score": 1, "matched": ["TransUnion Credit"], "unmatched": []}}, "diligence": {"watchlists": null, "fraud": {"average_score": 0.02, "flags": [], "sources": {"Sentilink": {"normalized_score": 0.02, "raw_score": 21, "attribute": "Abuse score"}}, "score": 0.02}, "financial": {"credit": [{"source": "TransUnion Credit", "models": {"vantagescore30": {"name": "VantageScore 3.0", "codes": ["vantagescore30-12", "vantagescore30-04", "vantagescore30-07", "vantagescore30-63"], "score": 591, "model_codes": [{"code": "vantagescore30-04", "description": "The balances on your accounts are too high compared to loan amounts"}, {"code": "vantagescore30-07", "description": "You have too many delinquent or derogatory accounts"}, {"code": "vantagescore30-12", "description": "The date that you opened your oldest account is too recent"}, {"code": "vantagescore30-63", "description": "Lack of sufficient relevant real estate account information"}], "bureau": "TransUnion Credit"}, "vantagescore40": {"name": "VantageScore 4.0", "codes": ["vantagescore40-68", "vantagescore40-04", "vantagescore40-87", "vantagescore40-07"], "score": 578, "model_codes": [{"code": "vantagescore40-04", "description": "Balances on accounts too high compared to credit limits and loan amounts"}, {"code": "vantagescore40-07", "description": "Too many delinquent or derogatory accounts"}, {"code": "vantagescore40-68", "description": "Lack of real estate secured loan information"}, {"code": "vantagescore40-87", "description": "Unpaid collections"}], "bureau": "TransUnion Credit"}}}]}, "identity_questions": null, "device": null}, "related_data": {}, "raw_responses": {"TransUnion Credit": [{"creditBureau": {"$": {"xmlns": "http://www.transunion.com/namespace", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.transunion.com/namespace"}, "document": "response", "version": "2.26", "transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "********", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2025-03-01T20:37:29.276-06:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "false", "market": "16", "submarket": "NF", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "2013-05-14", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "indicative": {"name": {"$": {"source": "file"}, "person": {"first": "KENNEDY", "middle": "C", "last": "WILLIAMS"}}, "address": [{"$": {"source": "file"}, "status": "current", "qualifier": "personal", "street": {"number": "500", "name": "TUSCAN RESERVE", "type": "DR", "unit": {"number": "516"}}, "location": {"city": "PALM COAST", "state": "FL", "zipCode": "32164"}, "dateReported": {"_": "2022-11-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "700", "name": "TUSCAN RESERVE", "type": "DR", "unit": {"number": "722"}}, "location": {"city": "PALM COAST", "state": "FL", "zipCode": "32164"}, "dateReported": {"_": "2022-06-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, {"$": {"source": "file"}, "status": "previous", "qualifier": "personal", "street": {"number": "33", "name": "COUNTRY", "preDirectional": "S", "type": "RD"}, "location": {"city": "WESTHAMPTON", "state": "NY", "zipCode": "11977"}}], "socialSecurity": {"$": {"source": "file"}, "number": "607828822"}, "dateOfBirth": {"_": "1995-04-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false", "source": "file"}}, "employment": {"$": {"source": "file"}, "employer": {"unparsed": "HILTON HOTEL"}, "occupation": "RECEPTIONIST", "dateOnFileSince": {"_": "2020-03-07", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2020-03-07", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "F", "memberCode": "0259237L", "name": {"unparsed": "ALLY FINCL"}}, "portfolioType": "installment", "ECOADesignator": "jointContractLiability", "dateOpened": {"_": "2020-03-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-08-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "09", "remark": {"code": "DRC", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "060"}, "account": {"type": "AU"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2022-11-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "02GSZ001", "name": {"unparsed": "GS BANK USA"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2021-11-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "09", "remark": {"code": "CBG", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2024-01-10", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "D", "memberCode": "********", "name": {"unparsed": "TARGET/TD"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2020-11-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2024-08-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2023-02-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2024-08-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "9P", "remark": {"code": "SET", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2024-08-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "manual"}, {"subscriber": {"industryCode": "B", "memberCode": "01DTV241", "name": {"unparsed": "CAP1/KOHLS"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2015-12-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-02-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CH"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2025-01-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************************************"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2021-12-06", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "E", "memberCode": "039ED388", "name": {"unparsed": "EDFINANCIAL"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2021-05-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "240"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "XXXXX111XXXX************************1XXXXXX1"}, "historicalCounters": {"monthsReviewedCount": "44", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-07-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-05-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "E", "memberCode": "039ED388", "name": {"unparsed": "EDFINANCIAL"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2020-09-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "240"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "XXXXX111XXXX************************1"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-07-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-05-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "E", "memberCode": "039ED388", "name": {"unparsed": "EDFINANCIAL"}}, "portfolioType": "installment", "ECOADesignator": "individual", "dateOpened": {"_": "2020-09-16", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "PDE", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "240"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "XXXXX111XXXX************************1"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-07-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "additionalTradeAccount": {"payment": {"specializedType": "deferred", "dateDue": {"_": "2025-05-18", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "portfolio": {"name": ""}, "original": {"creditGrantor": {"person": ""}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "F", "memberCode": "0999206T", "name": {"unparsed": "SYNCB/CARECR"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2024-06-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CH"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2024-12-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "1111111"}, "historicalCounters": {"monthsReviewedCount": "07", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2024-12-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "R", "memberCode": "02JQ6238", "name": {"unparsed": "FLEX"}}, "portfolioType": "open", "ECOADesignator": "individual", "dateOpened": {"_": "2024-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2024-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2024-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2024-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remark": {"code": "CLO", "type": "affiliate"}, "account": {"type": "RA"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2024-05-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "R", "memberCode": "01NZ8368", "name": {"unparsed": "CCB/CARTERS"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2020-09-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-12-23", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-11-03", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2022-02-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "CBG", "type": "affiliate"}, "account": {"type": "CH"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-11-23", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************111"}, "historicalCounters": {"monthsReviewedCount": "27", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-02-12", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "0235065N", "name": {"unparsed": "SYNCB/TJXDC"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2020-08-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-09-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2022-06-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "datePaidOut": {"_": "2022-02-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "CBG", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-08-09", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "************************1"}, "historicalCounters": {"monthsReviewedCount": "25", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-02-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "B", "memberCode": "********", "name": {"unparsed": "DISCOVERBANK"}}, "portfolioType": "revolving", "ECOADesignator": "individual", "dateOpened": {"_": "2020-11-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2021-02-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2021-01-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remark": {"code": "CBG", "type": "affiliate"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2021-01-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11"}, "historicalCounters": {"monthsReviewedCount": "02", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "updateMethod": "automated"}], "collection": [{"subscriber": {"industryCode": "Y", "memberCode": "028J3001", "name": {"unparsed": "JEFFCAPSYS"}}, "portfolioType": "open", "ECOADesignator": "individual", "account": {"type": "FC"}, "dateOpened": {"_": "2023-12-07", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "SPRINT"}, "creditorClassification": "financial", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "remark": {"code": "AID", "type": "affiliate"}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "Y", "memberCode": "01KSE003", "name": {"unparsed": "PORTFOLIO RC"}}, "portfolioType": "open", "ECOADesignator": "individual", "account": {"type": "FC"}, "dateOpened": {"_": "2024-05-17", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2025-01-19", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "CAPITAL ONE N A"}, "creditorClassification": "banking", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "remark": {"code": "DRC", "type": "affiliate"}, "updateMethod": "automated"}], "inquiry": {"ECOADesignator": "individual", "subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "17NY", "name": {"unparsed": "SYNCB"}}, "date": {"_": "2024-06-24", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}}}, "addOnProduct": [{"code": "07030", "status": "delivered", "phoneAppend": {"$": {"searchStatus": "notFound"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "**********"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "**********"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02F", "value": "*********1"}, {"algorithmID": "00WBO", "id": "P02H", "value": "*********2"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********5"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000009695"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000017"}, {"algorithmID": "00WR3", "id": "AU20S", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "AU36S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "BR34S", "value": "0000000075"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "FI101S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "G202B", "value": "0000000267"}, {"algorithmID": "00WR3", "id": "G213A", "value": "0*********"}, {"algorithmID": "00WR3", "id": "G215B", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********4"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "RE03S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "RE24S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "RE28S", "value": "0000000810"}, {"algorithmID": "00WR3", "id": "S061S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "S071A", "value": "*********1"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "*********3"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "0000008885"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000005"}, {"algorithmID": "00WR3", "id": "S207S", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G242F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G243F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US36S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "0000000060"}, {"algorithmID": "00WR3", "id": "G404S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G208B", "value": "0000000100"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000363"}}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "-000000002"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "*********0"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "-000002.00"}, {"algorithmID": "00H91", "id": "PAYMNT10", "value": "*********2"}]}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000001"}}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "-000000009"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "0000000052"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "-000000002"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "-000000002"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "-000000002"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0000001100"}, {"algorithmID": "00H86", "id": "AGG208", "value": "0000009985"}, {"algorithmID": "00H86", "id": "AGG218", "value": "0000009185"}, {"algorithmID": "00H86", "id": "AGG223", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG407", "value": "*********0"}, {"algorithmID": "00H86", "id": "AGG504", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG518", "value": "0000000255"}, {"algorithmID": "00H86", "id": "AGG602", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG801", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG803", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG807", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********2"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000046.60"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000046.60"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "NOACTBC"}}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "*********0"}, {"algorithmID": "00WG9", "id": "REV255", "value": "*********0"}, {"algorithmID": "00WG9", "id": "STD255", "value": "-000000002"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "0000000.86"}, {"algorithmID": "00WG8", "id": "REV222", "value": "0000000.85"}, {"algorithmID": "00WG8", "id": "REV225", "value": "0000000.93"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "-000002.00"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "REV322", "value": "0000000075"}, {"algorithmID": "00WH1", "id": "BKC320", "value": "-000000002"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "-000000002"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "-000000002"}]}}, {"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+591", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "12"}, {"rank": "2", "code": "04"}, {"rank": "3", "code": "07"}, {"rank": "4", "code": "63"}]}, "scoreCard": "06"}}}, {"code": "001NN", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+578", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "false", "factors": {"factor": [{"rank": "1", "code": "68"}, {"rank": "2", "code": "04"}, {"rank": "3", "code": "87"}, {"rank": "4", "code": "07"}]}, "scoreCard": "05"}}}, {"code": "00H87", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H87", "id": "TRV07", "value": "*********0"}, {"algorithmID": "00H87", "id": "TRV08", "value": "*********0"}]}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z23", "id": "LINKF001", "value": "HIT"}, {"algorithmID": "00Z23", "id": "LINKF002", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF003", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF004", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF005", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF006", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF007", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF008"}, {"algorithmID": "00Z23", "id": "LINKF009"}, {"algorithmID": "00Z23", "id": "LINKF010"}, {"algorithmID": "00Z23", "id": "LINKF011"}, {"algorithmID": "00Z23", "id": "LINKF012"}, {"algorithmID": "00Z23", "id": "LINKF013"}, {"algorithmID": "00Z23", "id": "LINKF014"}, {"algorithmID": "00Z23", "id": "LINKF015", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF016"}, {"algorithmID": "00Z23", "id": "LINKF017"}, {"algorithmID": "00Z23", "id": "LINKF018", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF019", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF020", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF021", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF022", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF023", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF024"}, {"algorithmID": "00Z23", "id": "LINKF025"}, {"algorithmID": "00Z23", "id": "LINKF026"}, {"algorithmID": "00Z23", "id": "LINKF027"}, {"algorithmID": "00Z23", "id": "LINKF028"}, {"algorithmID": "00Z23", "id": "LINKF029"}, {"algorithmID": "00Z23", "id": "LINKF030"}, {"algorithmID": "00Z23", "id": "LINKF031", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF032", "value": "N"}, {"algorithmID": "00Z23", "id": "LINKF033"}, {"algorithmID": "00Z23", "id": "LINKF034"}, {"algorithmID": "00Z23", "id": "LINKF035", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF036", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF037", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF038", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF039", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF040", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF041", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF042", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF043", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF044", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF045", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF046", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF047", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF048", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF049"}, {"algorithmID": "00Z23", "id": "LINKF050", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF051", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF052", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF053", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF054", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF055", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF056", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF057", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF058", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF059", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF060", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF061", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF062", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF063", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF064", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF065", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF066", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF067", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF068", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF069", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF070", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF071", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF072", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF073", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF074", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF075", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF076", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF077", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF078", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF079", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF080"}, {"algorithmID": "00Z23", "id": "LINKF081"}, {"algorithmID": "00Z23", "id": "LINKF082", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF083", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF084", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF085", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF086", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF087", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF088", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF089", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF090", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF091", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF092", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF093", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF094", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF095", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF096", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF097", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF098", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF099", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF100", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF101", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF102", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF103", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF104", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF105", "value": "0000001039"}, {"algorithmID": "00Z23", "id": "LINKF106", "value": "0000001039"}, {"algorithmID": "00Z23", "id": "LINKF107", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF108", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF109", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF110", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF111", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF112", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF113", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF114", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF115", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF116", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF117", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF118", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF119", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF120", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF121", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF122", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF123"}, {"algorithmID": "00Z23", "id": "LINKF124"}, {"algorithmID": "00Z23", "id": "LINKF125", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF126", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF127", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF128", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF129", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF130", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF131", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF132", "value": "0001700.00"}, {"algorithmID": "00Z23", "id": "LINKF133", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF134", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF135"}, {"algorithmID": "00Z23", "id": "LINKF136"}, {"algorithmID": "00Z23", "id": "LINKF137", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF138", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF139", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF140", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF141", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF142", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF143", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF144", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF145", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF146", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF147", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF148", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF149", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF150", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF151", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF152", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF153", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF154", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF155", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF156", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF157", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF158", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF159", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF160", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF161", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF162", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF163", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF164", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF165", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF166", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF167", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF168", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF169", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF170", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF171", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF172", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF173", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF174", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF175", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF176", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF177", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF178", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF179", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF180", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF181", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF182"}, {"algorithmID": "00Z23", "id": "LINKF183"}, {"algorithmID": "00Z23", "id": "LINKF184"}, {"algorithmID": "00Z23", "id": "LINKF185", "value": "0000001039"}, {"algorithmID": "00Z23", "id": "LINKF186"}, {"algorithmID": "00Z23", "id": "LINKF187"}, {"algorithmID": "00Z23", "id": "LINKF188"}, {"algorithmID": "00Z23", "id": "LINKF189"}, {"algorithmID": "00Z23", "id": "LINKF190"}, {"algorithmID": "00Z23", "id": "LINKF191"}, {"algorithmID": "00Z23", "id": "LINKF192"}, {"algorithmID": "00Z23", "id": "LINKF193", "value": "0000001039"}, {"algorithmID": "00Z23", "id": "LINKF194"}, {"algorithmID": "00Z23", "id": "LINKF195", "value": "0000001039"}, {"algorithmID": "00Z23", "id": "LINKF196"}, {"algorithmID": "00Z23", "id": "LINKF197", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF198", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF199", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF200", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF201", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF202", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF203", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF204", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF205", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF206", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF207", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF208", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF209", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF210", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF211", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF212", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF213"}]}}, {"code": "07051", "status": "delivered", "militaryLendingActSearch": {"$": {"searchStatus": "noMatch"}}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********2"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "*********6"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "9999.99999"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}, "printImage": "                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           16 NF      5/13      03/01/25    20:37CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> WILLIAMS, KENNEDY  C.                              ***********   4/95        <CURRENT ADDRESS>                                               <DATE RPTD>  500 TUSCAN RESERVE DR., #516. PALM COAST FL. 32164              11/22        <FORMER ADDRESS>                                                             700 TUSCAN RESERVE DR., #722. PALM COAST FL. 32164               6/22        33 S. COUNTRY RD., WESTHAMPTON NY. 11977                                                                              <POSITION>                          <CURRENT EMPLOYER AND ADDRESS>                     <VERF> <RPTD>             HILTON HOTEL                             RECEPTIONIST                                                                            3/20A  3/20              ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +591  : 12, 04, 07, 63 SCORECARD :06*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +578  : 68, 04, 87, 07 SCORECARD :05***              ---------------------------------------------------------------------------- C O L L E C T I O N S                                                        SUBNAME       SUBCODE   ECOA OPENED  CLOSED $PLACED  CREDITOR           MOP  ACCOUNT#                     VERIFIED       BALANCE  REMARKS                 JEFFCAPSYS    Y 28J3001 I    12/23          $1804    SPRINT             O9B                                1/25A         $1804    ACCT INFO DSP BY CSM                                                                                 PORTFOLIO RC  Y 1KSE003 I     5/24          $620     CAPITAL ONE N A    O9B                                1/25A         $620     DISP INV CMP-CNS DSG    ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   ALLY FINCL   F 259237L  3/20   $16.0K   060M                            I09                          2/25A           $4436                                C    AUTOMOBILE         8/22F  $4436    DISP INV CMP-CNS DSGR 0                                                                                           GS BANK USA  B 2GSZ001 11/21   $392                                     R09                          1/25A  $500     $233                                 I    CREDIT CARD       12/22F  $233     CLSD BY CRDT GRANTOR  0                                                                                           TARGET/TD    D 6476007 11/20   $574                                     R9P                          8/24V  $400     $0                                   I    CREDIT CARD        2/23F  $0       SETTLED < FULL BLNC   0                                                                                           CAP1/KOHLS   B 1DTV241 12/15   $256     MIN29              ************ R01                          2/25A  $300     $0                 ************      I    CHARGE ACCOUNT            $225                           48   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  5/21   $541     240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $551     PAYMENT DEFERRED      44   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  9/20   $4844    240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $4935    PAYMENT DEFERRED      48   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  9/20   $3500    240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $3566    PAYMENT DEFERRED      48   0/ 0/ 0                                                                                SYNCB/CARECR F 999206T  6/24   $492     MIN53              1111111      R01                          1/25A  $510     $0                                   I    CHARGE ACCOUNT            $492                           7    0/ 0/ 0                                                                                FLEX         R 2JQ6238  5/24   $2125                                    O01                          5/24A           $0                                   I    RENTAL AGREEMENT   5/24C  $0       CLOSED                0                                                                                           CCB/CARTERS  R 1NZ8368  9/20   $156                        ************ R01                         12/22A  $200     $0                 ************      I    CHARGE ACCOUNT    11/22C  $0       CLSD BY CRDT GRANTOR  27   0/ 0/ 0                                                                                SYNCB/TJXDC  B 235065N  8/20   $521                        ************ R01                          9/22A  $460     $0                 ************      I    CREDIT CARD        6/22C  $0       CLSD BY CRDT GRANTOR  25   0/ 0/ 0                                                                                DISCOVERBANK B 9616003 11/20   $0                          11           R01                          2/21A  $750     $0                                   I    CREDIT CARD        1/21C  $0       CLSD BY CRDT GRANTOR  2    0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        6/24/24 BNY4368310(EAS) SYNCB                                               ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ", "embeddedData": {"_": "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", "$": {"type": "pdf", "contentTypeEncoding": "base64"}}}}, "xml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><creditBureau xmlns=\"http://www.transunion.com/namespace\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://www.transunion.com/namespace\"><document>response</document><version>2.26</version><transactionControl><subscriber><industryCode>F</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>1702</inquirySubscriberPrefixCode></subscriber><options><country>us</country><language>en</language><productVersion>standard</productVersion></options><tracking><transactionTimeStamp>2025-03-01T20:37:29.276-06:00</transactionTimeStamp></tracking></transactionControl><product><code>07000</code><subject><number>1</number><subjectRecord><fileNumber>01</fileNumber><fileSummary><fileHitIndicator>regularHit</fileHitIndicator><ssnMatchIndicator>exact</ssnMatchIndicator><consumerStatementIndicator>false</consumerStatementIndicator><market>16</market><submarket>NF</submarket><creditDataStatus><suppressed>false</suppressed><doNotPromote><indicator>false</indicator></doNotPromote><freeze><indicator>false</indicator></freeze><minor>false</minor><disputed>false</disputed></creditDataStatus><inFileSinceDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2013-05-14</inFileSinceDate></fileSummary><indicative><name source=\"file\"><person><first>KENNEDY</first><middle>C</middle><last>WILLIAMS</last></person></name><address source=\"file\"><status>current</status><qualifier>personal</qualifier><street><number>500</number><name>TUSCAN RESERVE</name><type>DR</type><unit><number>516</number></unit></street><location><city>PALM COAST</city><state>FL</state><zipCode>32164</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-11-06</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>700</number><name>TUSCAN RESERVE</name><type>DR</type><unit><number>722</number></unit></street><location><city>PALM COAST</city><state>FL</state><zipCode>32164</zipCode></location><dateReported estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-06-01</dateReported></address><address source=\"file\"><status>previous</status><qualifier>personal</qualifier><street><number>33</number><name>COUNTRY</name><preDirectional>S</preDirectional><type>RD</type></street><location><city>WESTHAMPTON</city><state>NY</state><zipCode>11977</zipCode></location></address><socialSecurity source=\"file\"><number>607828822</number></socialSecurity><dateOfBirth estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\" source=\"file\">1995-04-30</dateOfBirth><employment source=\"file\"><employer><unparsed>HILTON HOTEL</unparsed></employer><occupation>RECEPTIONIST</occupation><dateOnFileSince estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-03-07</dateOnFileSince><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-03-07</dateEffective></employment></indicative><custom><credit><trade><subscriber><industryCode>F</industryCode><memberCode>0259237L</memberCode><name><unparsed>ALLY FINCL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>jointContractLiability</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-03-10</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-24</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-08-24</dateClosed><closedIndicator>normal</closedIndicator><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>09</accountRating><remark><code>DRC</code><type>affiliate</type></remark><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>060</paymentScheduleMonthCount></terms><account><type>AU</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-11-17</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>02GSZ001</memberCode><name><unparsed>GS BANK USA</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-11-09</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-12-31</dateClosed><closedIndicator>normal</closedIndicator><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>09</accountRating><remark><code>CBG</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-01-10</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>D</industryCode><memberCode>********</memberCode><name><unparsed>TARGET/TD</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-11-08</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-30</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-02-05</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-30</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>9P</accountRating><remark><code>SET</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-08-30</date></mostRecentPayment><updateMethod>manual</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>01DTV241</memberCode><name><unparsed>CAP1/KOHLS</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2015-12-05</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-02-08</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><terms><paymentScheduleMonthCount>MIN</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>CH</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-08</startDate><text>************************************************</text></paymentPattern><historicalCounters><monthsReviewedCount>48</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-12-06</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>E</industryCode><memberCode>039ED388</memberCode><name><unparsed>EDFINANCIAL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-05-19</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>240</paymentScheduleMonthCount></terms><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>XXXXX111XXXX************************1XXXXXX1</text></paymentPattern><historicalCounters><monthsReviewedCount>44</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-07-18</date></mostRecentPayment><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-05-18</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>E</industryCode><memberCode>039ED388</memberCode><name><unparsed>EDFINANCIAL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-09-16</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>240</paymentScheduleMonthCount></terms><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>XXXXX111XXXX************************1</text></paymentPattern><historicalCounters><monthsReviewedCount>48</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-07-18</date></mostRecentPayment><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-05-18</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>E</industryCode><memberCode>039ED388</memberCode><name><unparsed>EDFINANCIAL</unparsed></name></subscriber><portfolioType>installment</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-09-16</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-31</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>PDE</code><type>affiliate</type></remark><terms><paymentFrequency>monthly</paymentFrequency><paymentScheduleMonthCount>240</paymentScheduleMonthCount></terms><account><type>ST</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</startDate><text>XXXXX111XXXX************************1</text></paymentPattern><historicalCounters><monthsReviewedCount>48</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-07-18</date></mostRecentPayment><additionalTradeAccount><payment><specializedType>deferred</specializedType><dateDue estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-05-18</dateDue></payment><portfolio><name/></portfolio><original><creditGrantor><person/></creditGrantor></original></additionalTradeAccount><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>F</industryCode><memberCode>0999206T</memberCode><name><unparsed>SYNCB/CARECR</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-06-24</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-24</dateEffective><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><terms><paymentScheduleMonthCount>MIN</paymentScheduleMonthCount><scheduledMonthlyPayment>*********</scheduledMonthlyPayment></terms><account><type>CH</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-24</startDate><text>1111111</text></paymentPattern><historicalCounters><monthsReviewedCount>07</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-12-31</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>R</industryCode><memberCode>02JQ6238</memberCode><name><unparsed>FLEX</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-01</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-01</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-01</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-01</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><accountRating>01</accountRating><remark><code>CLO</code><type>affiliate</type></remark><account><type>RA</type></account><pastDue>*********</pastDue><paymentHistory/><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-01</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>R</industryCode><memberCode>01NZ8368</memberCode><name><unparsed>CCB/CARTERS</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-09-21</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-12-23</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-11-03</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-02-12</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><remark><code>CBG</code><type>affiliate</type></remark><account><type>CH</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-11-23</startDate><text>************************111</text></paymentPattern><historicalCounters><monthsReviewedCount>27</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-02-12</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>0235065N</memberCode><name><unparsed>SYNCB/TJXDC</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-08-21</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-09-09</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-06-09</dateClosed><closedIndicator>normal</closedIndicator><datePaidOut estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-02-13</datePaidOut><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><remark><code>CBG</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-08-09</startDate><text>************************1</text></paymentPattern><historicalCounters><monthsReviewedCount>25</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><mostRecentPayment><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2022-02-13</date></mostRecentPayment><updateMethod>automated</updateMethod></trade><trade><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><name><unparsed>DISCOVERBANK</unparsed></name></subscriber><portfolioType>revolving</portfolioType><ECOADesignator>individual</ECOADesignator><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2020-11-30</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-02-19</dateEffective><dateClosed estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-01-21</dateClosed><closedIndicator>normal</closedIndicator><currentBalance>*********</currentBalance><highCredit>*********</highCredit><creditLimit>*********</creditLimit><accountRating>01</accountRating><remark><code>CBG</code><type>affiliate</type></remark><account><type>CC</type></account><pastDue>*********</pastDue><paymentHistory><paymentPattern><startDate estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2021-01-19</startDate><text>11</text></paymentPattern><historicalCounters><monthsReviewedCount>02</monthsReviewedCount><late30DaysTotal>00</late30DaysTotal><late60DaysTotal>00</late60DaysTotal><late90DaysTotal>00</late90DaysTotal></historicalCounters></paymentHistory><updateMethod>automated</updateMethod></trade><collection><subscriber><industryCode>Y</industryCode><memberCode>028J3001</memberCode><name><unparsed>JEFFCAPSYS</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>individual</ECOADesignator><account><type>FC</type></account><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2023-12-07</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-24</dateEffective><currentBalance>*********</currentBalance><original><creditGrantor><unparsed>SPRINT</unparsed></creditGrantor><creditorClassification>financial</creditorClassification><balance>*********</balance></original><pastDue>*********</pastDue><accountRating>9B</accountRating><remark><code>AID</code><type>affiliate</type></remark><updateMethod>automated</updateMethod></collection><collection><subscriber><industryCode>Y</industryCode><memberCode>01KSE003</memberCode><name><unparsed>PORTFOLIO RC</unparsed></name></subscriber><portfolioType>open</portfolioType><ECOADesignator>individual</ECOADesignator><account><type>FC</type></account><dateOpened estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-05-17</dateOpened><dateEffective estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2025-01-19</dateEffective><currentBalance>*********</currentBalance><original><creditGrantor><unparsed>CAPITAL ONE N A</unparsed></creditGrantor><creditorClassification>banking</creditorClassification><balance>*********</balance></original><pastDue>*********</pastDue><accountRating>9B</accountRating><remark><code>DRC</code><type>affiliate</type></remark><updateMethod>automated</updateMethod></collection><inquiry><ECOADesignator>individual</ECOADesignator><subscriber><industryCode>B</industryCode><memberCode>********</memberCode><inquirySubscriberPrefixCode>17NY</inquirySubscriberPrefixCode><name><unparsed>SYNCB</unparsed></name></subscriber><date estimatedDay=\"false\" estimatedMonth=\"false\" estimatedCentury=\"false\" estimatedYear=\"false\">2024-06-24</date></inquiry></credit></custom><addOnProduct><code>07030</code><status>delivered</status><phoneAppend searchStatus=\"notFound\"/></addOnProduct><addOnProduct><code>00WBN</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBN</algorithmID><id>AD06C</id><value>**********</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD09A</id><value>*********0</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14A</id><value>*********0</value></characteristic><characteristic><algorithmID>00WBN</algorithmID><id>AD14H</id><value>**********</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WBO</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WBO</algorithmID><id>P02E</id><value>*********1</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02F</id><value>*********1</value></characteristic><characteristic><algorithmID>00WBO</algorithmID><id>P02H</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WR3</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WR3</algorithmID><id>AT24S</id><value>*********5</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT28B</id><value>0000009695</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT32S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT36S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AT104S</id><value>0000000017</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU20S</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU21S</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU34S</id><value>-000000003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU36S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU51A</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>AU101S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC34S</id><value>-000000003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC103S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BC110S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI09S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BI34S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR02S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>BR34S</id><value>0000000075</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI25S</id><value>-000000003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI34S</id><value>-000000003</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FI101S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>FR101S</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G202B</id><value>0000000267</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G213A</id><value>0*********</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G215B</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G216S</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G224C</id><value>*********4</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G228S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>IN20S</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>OF27S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE03S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE06S</id><value>*********0</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE24S</id><value>*********2</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>RE28S</id><value>0000000810</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S061S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S071A</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST01S</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST24S</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST27S</id><value>*********3</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST28S</id><value>0000008885</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>ST99S</id><value>-000000005</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>S207S</id><value>-000000004</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G242F</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G243F</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US21S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>US36S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT20S</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>JT21S</id><value>0000000060</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G404S</id><value>-000000001</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G411S</id><value>*********1</value></characteristic><characteristic><algorithmID>00WR3</algorithmID><id>G208B</id><value>0000000100</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H88</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H88</algorithmID><id>BALMAG01</id><value>0000000363</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V26</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V26</algorithmID><id>CV23</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V26</algorithmID><id>CV28</id><value>*********0</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H91</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT02</id><value>*********0</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT07</id><value>-000002.00</value></characteristic><characteristic><algorithmID>00H91</algorithmID><id>PAYMNT10</id><value>*********2</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG4</algorithmID><id>PLATTR04</id><value>-000000001</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V92</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V92</algorithmID><id>RVDEX02</id><value>-000000009</value></characteristic><characteristic><algorithmID>00V92</algorithmID><id>RVDEXQ2</id><value>0000000052</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V53</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00V53</algorithmID><id>AGGS106</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>AGGS109</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ1</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ2</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ3</id><value>-000000002</value></characteristic><characteristic><algorithmID>00V53</algorithmID><id>INDEXQ4</id><value>-000000002</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00H86</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H86</algorithmID><id>AGG205</id><value>0000001100</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG208</id><value>0000009985</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG218</id><value>0000009185</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG223</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG407</id><value>*********0</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG504</id><value>0*********</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG518</id><value>0000000255</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG602</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG801</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG803</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG807</id><value>-000000002</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG901</id><value>*********2</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG906</id><value>0000046.60</value></characteristic><characteristic><algorithmID>00H86</algorithmID><id>AGG910</id><value>0000046.60</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WP4</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WP4</algorithmID><id>RVLR80</id><value>NOACTBC</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG9</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG9</algorithmID><id>ALL252</id><value>*********0</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>REV255</id><value>*********0</value></characteristic><characteristic><algorithmID>00WG9</algorithmID><id>STD255</id><value>-000000002</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WG8</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WG8</algorithmID><id>REV202</id><value>0000000.86</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV222</id><value>0000000.85</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>REV225</id><value>0000000.93</value></characteristic><characteristic><algorithmID>00WG8</algorithmID><id>AUT225</id><value>-000002.00</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00WH1</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00WH1</algorithmID><id>REV322</id><value>0000000075</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC320</id><value>-000000002</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC322</id><value>-000000002</value></characteristic><characteristic><algorithmID>00WH1</algorithmID><id>BKC326</id><value>-000000002</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00V60</code><status>defaultDelivered</status><scoreModel><score><results>+591</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>true</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>12</code></factor><factor><rank>2</rank><code>04</code></factor><factor><rank>3</rank><code>07</code></factor><factor><rank>4</rank><code>63</code></factor></factors><scoreCard>06</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>001NN</code><status>defaultDelivered</status><scoreModel><score><results>+578</results><derogatoryAlert>false</derogatoryAlert><fileInquiriesImpactedScore>false</fileInquiriesImpactedScore><factors><factor><rank>1</rank><code>68</code></factor><factor><rank>2</rank><code>04</code></factor><factor><rank>3</rank><code>87</code></factor><factor><rank>4</rank><code>07</code></factor></factors><scoreCard>05</scoreCard></score></scoreModel></addOnProduct><addOnProduct><code>00H87</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00H87</algorithmID><id>TRV07</id><value>*********0</value></characteristic><characteristic><algorithmID>00H87</algorithmID><id>TRV08</id><value>*********0</value></characteristic></scoreModel></addOnProduct><addOnProduct><code>00Z23</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z23</algorithmID><id>LINKF001</id><value>HIT</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF002</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF003</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF004</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF005</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF006</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF007</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF008</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF009</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF010</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF011</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF012</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF013</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF014</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF015</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF016</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF017</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF018</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF019</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF020</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF021</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF022</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF023</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF024</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF025</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF026</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF027</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF028</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF029</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF030</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF031</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF032</id><value>N</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF033</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF034</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF035</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF036</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF037</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF038</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF039</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF040</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF041</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF042</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF043</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF044</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF045</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF046</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF047</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF048</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF049</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF050</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF051</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF052</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF053</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF054</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF055</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF056</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF057</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF058</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF059</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF060</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF061</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF062</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF063</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF064</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF065</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF066</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF067</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF068</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF069</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF070</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF071</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF072</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF073</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF074</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF075</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF076</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF077</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF078</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF079</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF080</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF081</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF082</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF083</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF084</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF085</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF086</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF087</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF088</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF089</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF090</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF091</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF092</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF093</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF094</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF095</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF096</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF097</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF098</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF099</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF100</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF101</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF102</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF103</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF104</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF105</id><value>0000001039</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF106</id><value>0000001039</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF107</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF108</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF109</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF110</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF111</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF112</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF113</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF114</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF115</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF116</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF117</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF118</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF119</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF120</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF121</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF122</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF123</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF124</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF125</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF126</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF127</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF128</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF129</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF130</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF131</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF132</id><value>0001700.00</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF133</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF134</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF135</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF136</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF137</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF138</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF139</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF140</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF141</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF142</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF143</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF144</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF145</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF146</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF147</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF148</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF149</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF150</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF151</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF152</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF153</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF154</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF155</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF156</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF157</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF158</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF159</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF160</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF161</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF162</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF163</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF164</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF165</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF166</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF167</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF168</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF169</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF170</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF171</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF172</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF173</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF174</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF175</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF176</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF177</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF178</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF179</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF180</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF181</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF182</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF183</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF184</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF185</id><value>0000001039</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF186</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF187</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF188</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF189</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF190</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF191</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF192</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF193</id><value>0000001039</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF194</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF195</id><value>0000001039</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF196</id></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF197</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF198</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF199</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF200</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF201</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF202</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF203</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF204</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF205</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF206</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF207</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF208</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF209</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF210</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF211</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF212</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z23</algorithmID><id>LINKF213</id></characteristic></scoreModel></addOnProduct><addOnProduct><code>07051</code><status>delivered</status><militaryLendingActSearch searchStatus=\"noMatch\"/></addOnProduct><addOnProduct><code>00Z17</code><status>defaultDelivered</status><scoreModel><characteristic><algorithmID>00Z17</algorithmID><id>LINKA004</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA002</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA023</id><value>*********2</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA024</id><value>*********6</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA048</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA049</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA001</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA050</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA051</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA052</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA029</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA030</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA053</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA022</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA026</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA027</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA028</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA021</id><value>*********1</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA020</id><value>*********3</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA006</id><value>*********3</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA003</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA025</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA011</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA005</id><value>0000.00000</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA010</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA040</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA033</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA034</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA035</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA036</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA007</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA037</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA008</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA009</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA039</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA042</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA043</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA044</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA015</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA045</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA046</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA047</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA017</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA018</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA019</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA041</id><value>9999.99999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA012</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA014</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA016</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA013</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA038</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA054</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA055</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA056</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA057</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA058</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA059</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA031</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA080</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA060</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA061</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA062</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA063</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA064</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA065</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA066</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA082</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA067</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA068</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA069</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA070</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA071</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA072</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA032</id><value>*********0</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA081</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA084</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA085</id><value>0000099999</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA073</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA074</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA075</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA076</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA077</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA078</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA079</id><value>0000000.00</value></characteristic><characteristic><algorithmID>00Z17</algorithmID><id>LINKA083</id><value>0000000.00</value></characteristic></scoreModel></addOnProduct></subjectRecord></subject><printImage><![CDATA[                                                                                                        TRANSUNION TRUVISION CREDIT REPORT                                                                                              <FOR>          <SUB NAME>          <MKT SUB>  <INFILE>   <DATE>      <TIME>  (I) F NY7673450 LEAD/FLEX           16 NF      5/13      03/01/25    20:37CT                                                                              <SUBJECT>                                          <SSN>        <BIRTH DATE> WILLIAMS, KENNEDY  C.                              ***********   4/95        <CURRENT ADDRESS>                                               <DATE RPTD>  500 TUSCAN RESERVE DR., #516. PALM COAST FL. 32164              11/22        <FORMER ADDRESS>                                                             700 TUSCAN RESERVE DR., #722. PALM COAST FL. 32164               6/22        33 S. COUNTRY RD., WESTHAMPTON NY. 11977                                                                              <POSITION>                          <CURRENT EMPLOYER AND ADDRESS>                     <VERF> <RPTD>             HILTON HOTEL                             RECEPTIONIST                                                                            3/20A  3/20              ---------------------------------------------------------------------------- S P E C I A L   M E S S A G E S                                              ***TRUVISION MILITARY LENDING ACT SEARCH: NO MATCH FOUND***                  ---------------------------------------------------------------------------- M O D E L   P R O F I L E          * * * A L E R T * * *                     ***VANTAGESCOR3 SCORE +591  : 12, 04, 07, 63 SCORECARD :06*** IN ADDITION TO    THE FACTORS LISTED ABOVE, THE NUMBER OF INQUIRIES ON THE CONSUMER'S          CREDIT FILE HAS ADVERSELY AFFECTED THE CREDIT SCORE.                      ***VANTAGESCORE 4 SCORE +578  : 68, 04, 87, 07 SCORECARD :05***              ---------------------------------------------------------------------------- C O L L E C T I O N S                                                        SUBNAME       SUBCODE   ECOA OPENED  CLOSED $PLACED  CREDITOR           MOP  ACCOUNT#                     VERIFIED       BALANCE  REMARKS                 JEFFCAPSYS    Y 28J3001 I    12/23          $1804    SPRINT             O9B                                1/25A         $1804    ACCT INFO DSP BY CSM                                                                                 PORTFOLIO RC  Y 1KSE003 I     5/24          $620     CAPITAL ONE N A    O9B                                1/25A         $620     DISP INV CMP-CNS DSG    ---------------------------------------------------------------------------- T R A D E S                                                                  SUBNAME      SUBCODE   OPENED  HIGHCRED TERMS     MAXDELQ  PAYPAT  1-12 MOP  ACCOUNT#               VERFIED CREDLIM  PASTDUE   AMT-MOP  PAYPAT 13-24      ECOA COLLATRL/LOANTYPE CLSD/PD BALANCE  REMARKS                MO 30/60/90   ALLY FINCL   F 259237L  3/20   $16.0K   060M                            I09                          2/25A           $4436                                C    AUTOMOBILE         8/22F  $4436    DISP INV CMP-CNS DSGR 0                                                                                           GS BANK USA  B 2GSZ001 11/21   $392                                     R09                          1/25A  $500     $233                                 I    CREDIT CARD       12/22F  $233     CLSD BY CRDT GRANTOR  0                                                                                           TARGET/TD    D 6476007 11/20   $574                                     R9P                          8/24V  $400     $0                                   I    CREDIT CARD        2/23F  $0       SETTLED < FULL BLNC   0                                                                                           CAP1/KOHLS   B 1DTV241 12/15   $256     MIN29              ************ R01                          2/25A  $300     $0                 ************      I    CHARGE ACCOUNT            $225                           48   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  5/21   $541     240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $551     PAYMENT DEFERRED      44   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  9/20   $4844    240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $4935    PAYMENT DEFERRED      48   0/ 0/ 0                                                                                EDFINANCIAL  E 39ED388  9/20   $3500    240M               XXXXX111XXXX I01                          1/25A           $0                 ************      I    DEFERRED ********         $3566    PAYMENT DEFERRED      48   0/ 0/ 0                                                                                SYNCB/CARECR F 999206T  6/24   $492     MIN53              1111111      R01                          1/25A  $510     $0                                   I    CHARGE ACCOUNT            $492                           7    0/ 0/ 0                                                                                FLEX         R 2JQ6238  5/24   $2125                                    O01                          5/24A           $0                                   I    RENTAL AGREEMENT   5/24C  $0       CLOSED                0                                                                                           CCB/CARTERS  R 1NZ8368  9/20   $156                        ************ R01                         12/22A  $200     $0                 ************      I    CHARGE ACCOUNT    11/22C  $0       CLSD BY CRDT GRANTOR  27   0/ 0/ 0                                                                                SYNCB/TJXDC  B 235065N  8/20   $521                        ************ R01                          9/22A  $460     $0                 ************      I    CREDIT CARD        6/22C  $0       CLSD BY CRDT GRANTOR  25   0/ 0/ 0                                                                                DISCOVERBANK B 9616003 11/20   $0                          11           R01                          2/21A  $750     $0                                   I    CREDIT CARD        1/21C  $0       CLSD BY CRDT GRANTOR  2    0/ 0/ 0   ---------------------------------------------------------------------------- I N Q U I R I E S                                                            DATE     SUBCODE         SUBNAME        TYPE    AMOUNT                        6/24/24 BNY4368310(EAS) SYNCB                                               ---------------------------------------------------------------------------- T R U V I S I O N  C R E D I T  R E P O R T  S E R V I C E D  B Y :          TRANSUNION                                                    800-888-4213   2 BALDWIN PLACE, P.O. BOX 1000 CHESTER, PA 19016                             CONSUMER DISCLOSURES CAN BE OBTAINED ONLINE THROUGH TRANSUNION AT:                HTTP://WWW.TRANSUNION.COM                                                                                                                                                        END OF TRANSUNION REPORT                        ]]></printImage><embeddedData type=\"pdf\" contentTypeEncoding=\"base64\">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</embeddedData></product></creditBureau>"}], "Sentilink": [{"transaction_id": "01JNABEE-JMFR-6Q2HFY93", "application_id": "L-liFWsGk5psbinqKboiy6", "scores": [{"name": "sentilink_third_party_synthetic_score", "version": "1.7.1", "score": 52, "reason_codes": [{"code": "R004", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the supplied SSN aligns with the consumer's DOB"}, {"code": "R010", "rank": 2, "direction": "less_fraudy", "explanation": "The depth of the consumer's history with this information"}, {"code": "R011", "rank": 3, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}]}, {"name": "sentilink_abuse_score", "version": "1.7.1", "score": 21, "reason_codes": [{"code": "R004", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the supplied SSN aligns with the consumer's DOB"}, {"code": "R014", "rank": 2, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R011", "rank": 3, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}]}, {"name": "sentilink_first_party_synthetic_score", "version": "1.7.1", "score": 22, "reason_codes": [{"code": "R014", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the consumer appears to have a better SSN"}, {"code": "R004", "rank": 2, "direction": "less_fraudy", "explanation": "Whether the supplied SSN aligns with the consumer's DOB"}, {"code": "R011", "rank": 3, "direction": "less_fraudy", "explanation": "How well the start time of the consumer's history aligns with the expected start time"}]}, {"name": "sentilink_id_theft_score", "version": "1.6.3", "score": 58, "reason_codes": [{"code": "R029", "rank": 1, "direction": "less_fraudy", "explanation": "Whether the applicant appears to be the best owner of the phone"}, {"code": "R028", "rank": 2, "direction": "less_fraudy", "explanation": "Whether there is unusual geographic activity associated with the phone number"}, {"code": "R022", "rank": 3, "direction": "less_fraudy", "explanation": "Whether the email domain or structure of the handle are suspicious"}]}], "customer_id": "01EFND5G6EZXKKT7PVY904YPBY", "environment": "PROD", "notes": "", "timestamp": "2025-03-02T02:37:29.23565363Z", "latency_ms": 187}]}, "formatted_responses": {"TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 12, "revolving_count": 7, "open_count": 1, "line_of_credit_count": 0, "installment_count": 4, "mortgage_count": 0, "past_due_count": 2, "percentage_past_due": 0.17, "past_due_total_dollar_amount": 4669, "revolving_accounts_total_current_balance": 950, "line_of_credit_accounts_total_current_balance": 0}, "collections": {"count": 2, "revolving_count": 0, "open_count": 2, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 2, "percentage_past_due": 1, "past_due_total_dollar_amount": 2424}, "consumer_statement_indicator": false, "public_records": {"count": 0, "public_records_within_last_year": 0, "public_records_within_last_three_years": 0, "public_records_within_last_five_years": 0, "public_records_within_last_ten_years": 0, "most_recent_public_record": "1000-01-01", "public_records_reason_codes": []}, "inquiries": {"count": 1, "inquiries_within_last_year": 1, "inquiries_within_last_three_years": 1, "inquiries_within_last_five_years": 1, "inquiries_within_last_ten_years": 1, "most_recent_inquiry": "2024-06-24"}, "vantagescore40": {"exists": true, "score": 578}, "vantagescore30": {"exists": true, "score": 591}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "ficoscore4": {"exists": false, "score": -1}, "syntheticidfraudscore": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": false, "condition": "none", "type": "none", "inquiries_last_60_days": -1, "address_status": "none"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 5, "AT28B": 9695, "AT32S": 4935, "AT36S": 1, "AT104S": 17, "AU20S": 60, "AU21S": 60, "AU34S": -3, "AU36S": 1, "AU51A": 60, "AU101S": 4436, "BC34S": -3, "BC103S": 233, "BC110S": 233, "BI09S": -1, "BI34S": -1, "BR02S": 1, "BR34S": 75, "FI25S": -3, "FI34S": -3, "FI101S": 4436, "FR101S": 492, "G202B": 267, "G213A": 620, "G215B": 1, "G216S": 3, "G224C": 4, "G228S": -1, "IN20S": 60, "OF27S": -1, "RE03S": 2, "RE06S": 0, "RE24S": 2, "RE28S": 810, "S061S": 1, "S071A": 1, "ST01S": 3, "ST24S": 3, "ST27S": 3, "ST28S": 8885, "ST99S": -5, "S207S": -4, "G242F": -1, "G243F": -1, "US21S": -1, "US36S": -1, "JT20S": 60, "JT21S": 60, "G404S": -1, "G411S": 1, "G208B": 100, "RE21S": -99999, "G408S": -9, "RE101S": null, "BKC324": null, "G209S": -999, "BKC122": -9, "BKC14": -1, "RT25S": -999, "HR101S": null, "ST50S": -999999, "G215A": -9, "G960S": -9}, "factor_code_4": null, "total_amount_in_collections": 2424, "vantage_v4_score_factor_3": 87, "fico_9_bankcard_score": null, "max_credit_limit_open_credit_trades": 510, "ficoauto8": {"score": null}, "mla_status": "delivered", "00W16": {"income_range_low_end": null, "income_range_high_end": null}, "factor_code_1": null, "credit_vision_link_score_FA2": [null], "months_on_file": 144, "truvalidate_fraud_alert_codes": null, "employment": {"employer": "HILTON HOTEL"}, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "credit_vision_alternative_checking_attributes": {"LINKA006": 3, "LINKA014": 0, "LINKA027": 99999}, "trade": "[{\"subscriber\":{\"industryCode\":\"F\",\"memberCode\":\"0259237L\",\"name\":{\"unparsed\":\"ALLY FINCL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"jointContractLiability\",\"dateOpened\":{\"_\":\"2020-03-10\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-02-24\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-08-24\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"09\",\"remark\":{\"code\":\"DRC\",\"type\":\"affiliate\"},\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"060\"},\"account\":{\"type\":\"AU\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2022-11-17\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"02GSZ001\",\"name\":{\"unparsed\":\"GS BANK USA\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2021-11-09\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"09\",\"remark\":{\"code\":\"CBG\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2024-01-10\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"D\",\"memberCode\":\"********\",\"name\":{\"unparsed\":\"TARGET/TD\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-11-08\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2024-08-30\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2023-02-05\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2024-08-30\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"9P\",\"remark\":{\"code\":\"SET\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2024-08-30\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"manual\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"01DTV241\",\"name\":{\"unparsed\":\"CAP1/KOHLS\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2015-12-05\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-02-08\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentScheduleMonthCount\":\"MIN\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"CH\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2025-01-08\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"************************************************\"},\"historicalCounters\":{\"monthsReviewedCount\":\"48\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2021-12-06\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"E\",\"memberCode\":\"039ED388\",\"name\":{\"unparsed\":\"EDFINANCIAL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2021-05-19\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"240\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"XXXXX111XXXX************************1XXXXXX1\"},\"historicalCounters\":{\"monthsReviewedCount\":\"44\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2024-07-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-05-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"E\",\"memberCode\":\"039ED388\",\"name\":{\"unparsed\":\"EDFINANCIAL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-09-16\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"240\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"XXXXX111XXXX************************1\"},\"historicalCounters\":{\"monthsReviewedCount\":\"48\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2024-07-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-05-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"E\",\"memberCode\":\"039ED388\",\"name\":{\"unparsed\":\"EDFINANCIAL\"}},\"portfolioType\":\"installment\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-09-16\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"PDE\",\"type\":\"affiliate\"},\"terms\":{\"paymentFrequency\":\"monthly\",\"paymentScheduleMonthCount\":\"240\"},\"account\":{\"type\":\"ST\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"XXXXX111XXXX************************1\"},\"historicalCounters\":{\"monthsReviewedCount\":\"48\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2024-07-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"additionalTradeAccount\":{\"payment\":{\"specializedType\":\"deferred\",\"dateDue\":{\"_\":\"2025-05-18\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"portfolio\":{\"name\":\"\"},\"original\":{\"creditGrantor\":{\"person\":\"\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"F\",\"memberCode\":\"0999206T\",\"name\":{\"unparsed\":\"SYNCB/CARECR\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-06-24\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2025-01-24\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"terms\":{\"paymentScheduleMonthCount\":\"MIN\",\"scheduledMonthlyPayment\":\"*********\"},\"account\":{\"type\":\"CH\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2024-12-24\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"1111111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"07\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2024-12-31\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"R\",\"memberCode\":\"02JQ6238\",\"name\":{\"unparsed\":\"FLEX\"}},\"portfolioType\":\"open\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2024-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2024-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2024-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2024-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"CLO\",\"type\":\"affiliate\"},\"account\":{\"type\":\"RA\"},\"pastDue\":\"*********\",\"paymentHistory\":\"\",\"mostRecentPayment\":{\"date\":{\"_\":\"2024-05-01\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"R\",\"memberCode\":\"01NZ8368\",\"name\":{\"unparsed\":\"CCB/CARTERS\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-09-21\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2022-12-23\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-11-03\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2022-02-12\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"CBG\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CH\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2022-11-23\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"************************111\"},\"historicalCounters\":{\"monthsReviewedCount\":\"27\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2022-02-12\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"0235065N\",\"name\":{\"unparsed\":\"SYNCB/TJXDC\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-08-21\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2022-09-09\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2022-06-09\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"datePaidOut\":{\"_\":\"2022-02-13\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"CBG\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2022-08-09\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"************************1\"},\"historicalCounters\":{\"monthsReviewedCount\":\"25\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"mostRecentPayment\":{\"date\":{\"_\":\"2022-02-13\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}}},\"updateMethod\":\"automated\"},{\"subscriber\":{\"industryCode\":\"B\",\"memberCode\":\"********\",\"name\":{\"unparsed\":\"DISCOVERBANK\"}},\"portfolioType\":\"revolving\",\"ECOADesignator\":\"individual\",\"dateOpened\":{\"_\":\"2020-11-30\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateEffective\":{\"_\":\"2021-02-19\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"dateClosed\":{\"_\":\"2021-01-21\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"closedIndicator\":\"normal\",\"currentBalance\":\"*********\",\"highCredit\":\"*********\",\"creditLimit\":\"*********\",\"accountRating\":\"01\",\"remark\":{\"code\":\"CBG\",\"type\":\"affiliate\"},\"account\":{\"type\":\"CC\"},\"pastDue\":\"*********\",\"paymentHistory\":{\"paymentPattern\":{\"startDate\":{\"_\":\"2021-01-19\",\"$\":{\"estimatedDay\":\"false\",\"estimatedMonth\":\"false\",\"estimatedCentury\":\"false\",\"estimatedYear\":\"false\"}},\"text\":\"11\"},\"historicalCounters\":{\"monthsReviewedCount\":\"02\",\"late30DaysTotal\":\"00\",\"late60DaysTotal\":\"00\",\"late90DaysTotal\":\"00\"}},\"updateMethod\":\"automated\"}]", "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "credit_vision_new_account_v2": {"score": null}, "number_foreclosure_bankruptcy_last_4y": 0, "military_leads": {"has_military_association": false}, "credit_vision_premeium_algorithms": {"paymnt01": null, "mnpmag01": null}, "factor_code_2": null, "military_lending_act_search_match": false, "count_60_days_late_payments": 0, "vantage_v_4_factor_1": 68, "vantage_v4_score_factor_2": 4, "consumer_statement_text": null, "months_since_most_recent_deliquency": 0, "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": null, "count_90_days_late_payments": 0, "total_monthly_debt_payment": 82, "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "number_deduped_bank_inquiries_past_3_months": -1, "number_charge_offs_last_24Mo": 0, "number_trades_verified_past_12_months": null, "tot_month_obligation_joint_and_indv_accts_verified_past_12_months": null, "number_of_deduplicated_inquiries_in_past_6_months": null, "credit_vision_revolver_6": {"RVLR61": null}, "tot_month_obligation_contractually_liable_accts_verified_past_12_months": null, "applicant_age_years": 29, "total_current_balance_mortgage_accounts": 0, "number_deduped_finance_inquiries_past_3_months": -1, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": 7}, "matching": {"address": {"score": 0.96, "matched": true}, "name": {"score": 1, "matched": true}, "dob": {"score": 1, "matched": true}, "ssn": {"score": 1, "matched": true}}}, "Sentilink": {"data": {"scores": {"sentilink_abuse_score": 21, "sentilink_first_party_synthetic_score": 22, "sentilink_third_party_synthetic_score": 52, "sentilink_id_theft_score": 58}, "reasonCodes": {"allReasonCodes": ["R004", "R010", "R011", "R014", "R029", "R028", "R022"], "moreFraudyCodes": [], "lessFraudyCodes": ["R004", "R010", "R011", "R014", "R029", "R028", "R022"]}, "consumer_history": {"shared_ssn_count": null, "number_of_ssns": 0}, "years_since_filing_date": -1, "error": null}}}, "audit_archive": null}