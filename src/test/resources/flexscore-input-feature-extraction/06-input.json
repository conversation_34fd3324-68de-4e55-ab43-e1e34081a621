{"status_code": 201, "error": null, "timestamp": 1651668841373, "evaluation_token": "L-rApSuLzWCdgEEDOhkdZT", "entity_token": "P-q26LzR4oh9Zr2SO8zvNQ", "parent_entity_token": null, "application_token": "5hiyzkaF3x0B9XPxKY6l4WI3i6diVaHt", "application_version_id": 2, "champion_challenger_id": null, "summary": {"result": "success", "score": 0.81, "tags": ["KYC SSN Match", "Address Matched", "KYC Address Match"], "outcome_reasons": [], "outcome": "Approved", "services": {"Lexis Nexis Instant ID": "executed", "TransUnion Credit": "executed"}, "alloy_fraud_score": null}, "supplied": {"name_first": "Name", "name_last": "Last Name", "address_line_1": "Address line 1", "address_city": "City", "address_state": "TX", "address_postal_code": "77449", "birth_date": "2000-01-01", "address_line_2": "2", "email_address": "<EMAIL>", "document_ssn": "*********", "phone_number": "+1123456789"}, "formatted": {"name_first": "Name", "name_last": "Last Name", "address_line_1": "Address line 1", "address_city": "City", "address_state": "TX", "address_postal_code": "77449", "birth_date": "2000-01-01", "address_line_2": "2", "email_address": "<EMAIL>", "document_ssn": "*********", "phone_number": "+1123456789", "address_country_code": "US", "phone_country_code": "US", "age": "34"}, "matching": {"name": {"score": 0.75, "matched": ["Lexis Nexis Instant ID"], "unmatched": ["TransUnion Credit"]}, "address": {"score": 0.99, "matched": ["Lexis Nexis Instant ID", "TransUnion Credit"], "unmatched": []}, "ssn": {"score": 1, "matched": ["Lexis Nexis Instant ID", "TransUnion Credit"], "unmatched": []}, "dob": {"score": 0.33, "matched": [], "unmatched": ["Lexis Nexis Instant ID", "TransUnion Credit"]}, "phone": {"score": 1, "matched": ["Lexis Nexis Instant ID"], "unmatched": []}}, "diligence": {"watchlists": {"lists": ["BES", "CFTC", "DTC", "EUDT", "FBI", "FCEN", "FAR", "IMW", "OFAC", "OCC", "OSFI", "PEP", "SDT", "UNNT", "BIS", "WBIF"], "matches": []}, "fraud": null, "financial": {"credit": [{"source": "TransUnion Credit", "models": {"vantagescore30": {"name": "VantageScore 3.0", "codes": ["vantagescore30-12", "vantagescore30-07", "vantagescore30-63", "vantagescore30-11"], "score": 573, "model_codes": [{"code": "vantagescore30-07", "description": "You have too many delinquent or derogatory accounts"}, {"code": "vantagescore30-11", "description": "The total of your delinquent or derogatory account balances is too high"}, {"code": "vantagescore30-12", "description": "The date that you opened your oldest account is too recent"}, {"code": "vantagescore30-63", "description": "Lack of sufficient relevant real estate account information"}], "bureau": "TransUnion Credit"}}}]}, "identity_questions": null, "device": null}, "related_data": {}, "raw_responses": {"TransUnion Credit": [{"creditBureau": {"$": {"xmlns": "http://www.transunion.com/namespace", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.transunion.com/namespace"}, "document": "response", "version": "2.26", "transactionControl": {"subscriber": {"industryCode": "F", "memberCode": "4601416", "inquirySubscriberPrefixCode": "1702"}, "options": {"country": "us", "language": "en", "productVersion": "standard"}, "tracking": {"transactionTimeStamp": "2022-05-04T07:53:58.725-05:00"}}, "product": {"code": "07000", "subject": {"number": "1", "subjectRecord": {"fileNumber": "01", "fileSummary": {"fileHitIndicator": "regularHit", "ssnMatchIndicator": "exact", "consumerStatementIndicator": "true", "market": "15", "submarket": "HO", "creditDataStatus": {"suppressed": "false", "doNotPromote": {"indicator": "false"}, "freeze": {"indicator": "false"}, "minor": "false", "disputed": "false"}, "inFileSinceDate": {"_": "2000-07-01", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "indicative": {"socialSecurity": {"$": {"source": "file"}, "number": "*********"}}, "consumerFileData": {"consumerStatement": {"type": "initialFraud<PERSON><PERSON><PERSON>", "text": "#HK#IFCRA INITIAL FRAUD ALERT: ACTION MAY BE REQUIRED UNDER FCRA BEFORE OPENING OR MODIFYING AN ACCOUNT. CONTACT CONSUMER AT (347) 981-9795"}}, "custom": {"credit": {"trade": [{"subscriber": {"industryCode": "BZ", "memberCode": "01G7N002", "name": {"unparsed": "1ST NB TX"}}, "portfolioType": "installment", "accountNumber": "********", "ECOADesignator": "individual", "dateOpened": {"_": "2018-09-11", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-04-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2019-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "09", "remark": {"code": "PRL", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "004"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": "", "mostRecentPayment": {"date": {"_": "2018-10-11", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "FZ", "memberCode": "051GV179", "name": {"unparsed": "SUN LOAN"}}, "portfolioType": "installment", "accountNumber": "6551", "ECOADesignator": "individual", "dateOpened": {"_": "2018-12-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateClosed": {"_": "2019-06-30", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "closedIndicator": "normal", "currentBalance": "*********", "highCredit": "*********", "accountRating": "09", "remark": {"code": "DRC", "type": "affiliate"}, "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "006"}, "account": {"type": "NT"}, "pastDue": "*********", "paymentHistory": "", "updateMethod": "automated"}, {"subscriber": {"industryCode": "FZ", "memberCode": "02GNH001", "name": {"unparsed": "POSSIBLEFINA"}}, "portfolioType": "installment", "accountNumber": "JRP5H6PGTI82AKGF8P", "ECOADesignator": "individual", "dateOpened": {"_": "2022-01-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "02", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "002", "scheduledMonthlyPayment": "*********"}, "account": {"type": "US"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-02-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11"}, "historicalCounters": {"monthsReviewedCount": "02", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-01-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "QU", "memberCode": "0692N001", "name": {"unparsed": "NAVY FCU"}}, "portfolioType": "revolving", "accountNumber": "**********", "ECOADesignator": "authorizedUser", "dateOpened": {"_": "2004-10-22", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-04-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-03-27", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "**************************1111111111111111111111"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BC", "memberCode": "01DTV001", "name": {"unparsed": "CAPITAL ONE"}}, "portfolioType": "revolving", "accountNumber": "************", "ECOADesignator": "individual", "dateOpened": {"_": "2021-10-21", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-04-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "terms": {"paymentScheduleMonthCount": "MIN", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CC"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-03-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11111"}, "historicalCounters": {"monthsReviewedCount": "05", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-04-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "BI", "memberCode": "02ANW003", "name": {"unparsed": "SELF / LEAD"}}, "portfolioType": "installment", "accountNumber": "A*********18175870", "ECOADesignator": "individual", "dateOpened": {"_": "2021-10-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "012", "scheduledMonthlyPayment": "*********"}, "account": {"type": "SE"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-02-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "11111"}, "historicalCounters": {"monthsReviewedCount": "05", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-02-08", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}, {"subscriber": {"industryCode": "RR", "memberCode": "02EHD068", "name": {"unparsed": "RR/BIGLADDER"}}, "portfolioType": "open", "accountNumber": "*********00011", "ECOADesignator": "individual", "dateOpened": {"_": "2019-12-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "dateEffective": {"_": "2022-03-31", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "datePaidOut": {"_": "2022-03-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "terms": {"paymentFrequency": "monthly", "paymentScheduleMonthCount": "001", "scheduledMonthlyPayment": "*********"}, "account": {"type": "RA"}, "pastDue": "*********", "paymentHistory": {"paymentPattern": {"startDate": {"_": "2022-02-28", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}, "text": "**************************"}, "historicalCounters": {"monthsReviewedCount": "26", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "mostRecentPayment": {"date": {"_": "2022-03-05", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}, "updateMethod": "automated"}], "inquiry": {"ECOADesignator": "individual", "subscriber": {"industryCode": "BC", "memberCode": "********", "inquirySubscriberPrefixCode": "33PC", "name": {"unparsed": "CAPITAL ONE"}}, "date": {"_": "2021-10-13", "$": {"estimatedDay": "false", "estimatedMonth": "false", "estimatedCentury": "false", "estimatedYear": "false"}}}}}, "addOnProduct": [{"code": "00V60", "status": "defaultDelivered", "scoreModel": {"score": {"results": "+573", "derogatoryAlert": "false", "fileInquiriesImpactedScore": "true", "factors": {"factor": [{"rank": "1", "code": "12"}, {"rank": "2", "code": "07"}, {"rank": "3", "code": "63"}, {"rank": "4", "code": "11"}]}, "scoreCard": "08"}}}, {"code": "00WBN", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBN", "id": "AD06C", "value": "*********7"}, {"algorithmID": "00WBN", "id": "AD09A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14A", "value": "*********0"}, {"algorithmID": "00WBN", "id": "AD14H", "value": "*********0"}]}}, {"code": "00WBO", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WBO", "id": "P02E", "value": "-000000004"}, {"algorithmID": "00WBO", "id": "P02F", "value": "-000000004"}, {"algorithmID": "00WBO", "id": "P02H", "value": "-000000004"}]}}, {"code": "00H86", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H86", "id": "AGG205", "value": "0000001239"}, {"algorithmID": "00H86", "id": "AGG208", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG218", "value": "0*********"}, {"algorithmID": "00H86", "id": "AGG223", "value": "-000000002"}, {"algorithmID": "00H86", "id": "AGG407", "value": "0000000693"}, {"algorithmID": "00H86", "id": "AGG504", "value": "*********6"}, {"algorithmID": "00H86", "id": "AGG518", "value": "0000000288"}, {"algorithmID": "00H86", "id": "AGG602", "value": "0000000700"}, {"algorithmID": "00H86", "id": "AGG801", "value": "0000000040"}, {"algorithmID": "00H86", "id": "AGG803", "value": "0000000100"}, {"algorithmID": "00H86", "id": "AGG807", "value": "0000000693"}, {"algorithmID": "00H86", "id": "AGG901", "value": "*********1"}, {"algorithmID": "00H86", "id": "AGG906", "value": "0000053.03"}, {"algorithmID": "00H86", "id": "AGG910", "value": "0000102.57"}]}}, {"code": "00H88", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00H88", "id": "BALMAG01", "value": "0000000370"}}}, {"code": "00H91", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00H91", "id": "PAYMNT02", "value": "*********0"}, {"algorithmID": "00H91", "id": "PAYMNT07", "value": "0000002.00"}]}}, {"code": "00V26", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V26", "id": "CV23", "value": "0000000045"}, {"algorithmID": "00V26", "id": "CV28", "value": "*********0"}]}}, {"code": "00WR3", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WR3", "id": "AT24S", "value": "*********4"}, {"algorithmID": "00WR3", "id": "AT28B", "value": "0000003064"}, {"algorithmID": "00WR3", "id": "AT32S", "value": "0*********"}, {"algorithmID": "00WR3", "id": "AT36S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "AT104S", "value": "0000000043"}, {"algorithmID": "00WR3", "id": "AU21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU34S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU51A", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "AU101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "BC34S", "value": "0000000094"}, {"algorithmID": "00WR3", "id": "BC103S", "value": "0000000330"}, {"algorithmID": "00WR3", "id": "BC110S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "BI09S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "BI34S", "value": "0000000068"}, {"algorithmID": "00WR3", "id": "BR02S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "FI25S", "value": "-000000006"}, {"algorithmID": "00WR3", "id": "FI34S", "value": "0000000062"}, {"algorithmID": "00WR3", "id": "FR101S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G202B", "value": "-000000003"}, {"algorithmID": "00WR3", "id": "G213A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G215B", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "G216S", "value": "*********2"}, {"algorithmID": "00WR3", "id": "G224C", "value": "*********2"}, {"algorithmID": "00WR3", "id": "G228S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "IN20S", "value": "0000000044"}, {"algorithmID": "00WR3", "id": "OF27S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "RE06S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "S061S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "S071A", "value": "-000000004"}, {"algorithmID": "00WR3", "id": "ST01S", "value": "*********0"}, {"algorithmID": "00WR3", "id": "ST24S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST27S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST28S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "ST99S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G242F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G243F", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "US21S", "value": "*********4"}, {"algorithmID": "00WR3", "id": "US36S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "JT20S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "JT21S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G404S", "value": "-000000001"}, {"algorithmID": "00WR3", "id": "G411S", "value": "*********1"}, {"algorithmID": "00WR3", "id": "G208B", "value": "0000000093"}]}}, {"code": "00V53", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V53", "id": "AGGS106", "value": "*********1"}, {"algorithmID": "00V53", "id": "AGGS109", "value": "0000000237"}, {"algorithmID": "00V53", "id": "INDEXQ1", "value": "*********7"}, {"algorithmID": "00V53", "id": "INDEXQ2", "value": "0000000024"}, {"algorithmID": "00V53", "id": "INDEXQ3", "value": "0000000041"}, {"algorithmID": "00V53", "id": "INDEXQ4", "value": "0000000029"}]}}, {"code": "00V92", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00V92", "id": "RVDEX02", "value": "-000000009"}, {"algorithmID": "00V92", "id": "RVDEXQ2", "value": "0000000024"}]}}, {"code": "00WG9", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG9", "id": "ALL252", "value": "*********0"}, {"algorithmID": "00WG9", "id": "REV255", "value": "0000000169"}]}}, {"code": "00WH1", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WH1", "id": "BKC320", "value": "*********3"}, {"algorithmID": "00WH1", "id": "BKC322", "value": "*********0"}, {"algorithmID": "00WH1", "id": "BKC326", "value": "0000000110"}]}}, {"code": "00WG8", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00WG8", "id": "REV202", "value": "0000002.00"}, {"algorithmID": "00WG8", "id": "REV222", "value": "0000002.00"}, {"algorithmID": "00WG8", "id": "REV225", "value": "0000008.84"}, {"algorithmID": "00WG8", "id": "AUT225", "value": "-000001.00"}]}}, {"code": "00WP4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WP4", "id": "RVLR80", "value": "MIXBHVR"}}}, {"code": "00WG4", "status": "defaultDelivered", "scoreModel": {"characteristic": {"algorithmID": "00WG4", "id": "PLATTR04", "value": "-000000005"}}}, {"code": "00Z23", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z23", "id": "LINKF001", "value": "HIT"}, {"algorithmID": "00Z23", "id": "LINKF002", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF003", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF004", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF005", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF006", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF007", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF008", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF009", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF010", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF011", "value": "0000001099"}, {"algorithmID": "00Z23", "id": "LINKF012", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF013", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF014", "value": "0000002234"}, {"algorithmID": "00Z23", "id": "LINKF015", "value": "*********7"}, {"algorithmID": "00Z23", "id": "LINKF016", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF017", "value": "0000000045"}, {"algorithmID": "00Z23", "id": "LINKF018", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF019", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF020", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF021", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF022", "value": "*********7"}, {"algorithmID": "00Z23", "id": "LINKF023", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF024", "value": "0000002128"}, {"algorithmID": "00Z23", "id": "LINKF025", "value": "0000002128"}, {"algorithmID": "00Z23", "id": "LINKF026", "value": "0000002128"}, {"algorithmID": "00Z23", "id": "LINKF027", "value": "0000000586"}, {"algorithmID": "00Z23", "id": "LINKF028", "value": "0000001718"}, {"algorithmID": "00Z23", "id": "LINKF029", "value": "0000002128"}, {"algorithmID": "00Z23", "id": "LINKF030", "value": "0000002367"}, {"algorithmID": "00Z23", "id": "LINKF031", "value": "0000000011"}, {"algorithmID": "00Z23", "id": "LINKF032", "value": "N"}, {"algorithmID": "00Z23", "id": "LINKF033", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF034", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF035", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF036", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF037", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF038", "value": "0*********"}, {"algorithmID": "00Z23", "id": "LINKF039", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF040", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF041", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF042", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF043", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF044", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF045", "value": "0000001521"}, {"algorithmID": "00Z23", "id": "LINKF046", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF047", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF048", "value": "0000001521"}, {"algorithmID": "00Z23", "id": "LINKF049", "value": "0000000225"}, {"algorithmID": "00Z23", "id": "LINKF050", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF051", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF052", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF053", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF054", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF055", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF056", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF057", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF058", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF059", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF060"}, {"algorithmID": "00Z23", "id": "LINKF061", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF062", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF063", "value": "0*********"}, {"algorithmID": "00Z23", "id": "LINKF064", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF065", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF066", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF067", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF068", "value": "0000000039"}, {"algorithmID": "00Z23", "id": "LINKF069", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF070", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF071", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF072", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF073", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF074", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF075", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF076", "value": "0000001521"}, {"algorithmID": "00Z23", "id": "LINKF077", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF078", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF079", "value": "0000001771"}, {"algorithmID": "00Z23", "id": "LINKF080", "value": "0001521.00"}, {"algorithmID": "00Z23", "id": "LINKF081", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF082", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF083", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF084", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF085", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF086", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF087", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF088", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF089", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF090", "value": "0000000010"}, {"algorithmID": "00Z23", "id": "LINKF091", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF092", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF093", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF094", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF095", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF096", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF097", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF098", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF099", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF100", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF101", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF102", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF103", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF104", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF105", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF106", "value": "0000001460"}, {"algorithmID": "00Z23", "id": "LINKF107", "value": "0000000021"}, {"algorithmID": "00Z23", "id": "LINKF108", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF109", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF110", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF111", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF112", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF113", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF114", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF115", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF116", "value": "0000000017"}, {"algorithmID": "00Z23", "id": "LINKF117", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF118", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF119", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF120", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF121", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF122", "value": "0000000016"}, {"algorithmID": "00Z23", "id": "LINKF123", "value": "0001521.00"}, {"algorithmID": "00Z23", "id": "LINKF124", "value": "0001521.00"}, {"algorithmID": "00Z23", "id": "LINKF125", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF126", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF127", "value": "*********7"}, {"algorithmID": "00Z23", "id": "LINKF128", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF129", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF130", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF131", "value": "0000000014"}, {"algorithmID": "00Z23", "id": "LINKF132", "value": "0004000.00"}, {"algorithmID": "00Z23", "id": "LINKF133", "value": "0000885.50"}, {"algorithmID": "00Z23", "id": "LINKF134", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF135", "value": "0001771.00"}, {"algorithmID": "00Z23", "id": "LINKF136", "value": "0000874.00"}, {"algorithmID": "00Z23", "id": "LINKF137", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF138", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF139", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF140", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF141", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF142", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF143", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF144", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF145", "value": "0000000015"}, {"algorithmID": "00Z23", "id": "LINKF146", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF147", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF148", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF149", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF150", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF151", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF152", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF153", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF154", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF155", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF156", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF157", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF158", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF159", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF160", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF161", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF162", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF163", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF164", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF165", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF166", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF167", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF168", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF169", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF170", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF171", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF172", "value": "*********5"}, {"algorithmID": "00Z23", "id": "LINKF173", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF174", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF175", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF176", "value": "*********3"}, {"algorithmID": "00Z23", "id": "LINKF177", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF178", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF179", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF180", "value": "*********1"}, {"algorithmID": "00Z23", "id": "LINKF181", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF182", "value": "0000000105"}, {"algorithmID": "00Z23", "id": "LINKF183", "value": "0000000194"}, {"algorithmID": "00Z23", "id": "LINKF184", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF185", "value": "0000000019"}, {"algorithmID": "00Z23", "id": "LINKF186", "value": "0000000105"}, {"algorithmID": "00Z23", "id": "LINKF187", "value": "0000000194"}, {"algorithmID": "00Z23", "id": "LINKF188", "value": "*********8"}, {"algorithmID": "00Z23", "id": "LINKF189", "value": "0000000067"}, {"algorithmID": "00Z23", "id": "LINKF190"}, {"algorithmID": "00Z23", "id": "LINKF191", "value": "0000001385"}, {"algorithmID": "00Z23", "id": "LINKF192", "value": "0000001362"}, {"algorithmID": "00Z23", "id": "LINKF193", "value": "0000000019"}, {"algorithmID": "00Z23", "id": "LINKF194", "value": "0000000105"}, {"algorithmID": "00Z23", "id": "LINKF195", "value": "0000001460"}, {"algorithmID": "00Z23", "id": "LINKF196", "value": "0000001399"}, {"algorithmID": "00Z23", "id": "LINKF197", "value": "0000000012"}, {"algorithmID": "00Z23", "id": "LINKF198", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF199", "value": "0003072.00"}, {"algorithmID": "00Z23", "id": "LINKF200", "value": "0000586.00"}, {"algorithmID": "00Z23", "id": "LINKF201", "value": "*********0"}, {"algorithmID": "00Z23", "id": "LINKF202", "value": "*********4"}, {"algorithmID": "00Z23", "id": "LINKF203", "value": "0000000.00"}, {"algorithmID": "00Z23", "id": "LINKF204", "value": "0001099.00"}, {"algorithmID": "00Z23", "id": "LINKF205", "value": "*********2"}, {"algorithmID": "00Z23", "id": "LINKF206", "value": "*********6"}, {"algorithmID": "00Z23", "id": "LINKF207", "value": "0000586.00"}, {"algorithmID": "00Z23", "id": "LINKF208", "value": "0002009.00"}, {"algorithmID": "00Z23", "id": "LINKF209", "value": "*********9"}, {"algorithmID": "00Z23", "id": "LINKF210", "value": "*********7"}, {"algorithmID": "00Z23", "id": "LINKF211", "value": "0002628.00"}, {"algorithmID": "00Z23", "id": "LINKF212", "value": "0002234.00"}, {"algorithmID": "00Z23", "id": "LINKF213", "value": "0000007.87"}]}}, {"code": "00Z17", "status": "defaultDelivered", "scoreModel": {"characteristic": [{"algorithmID": "00Z17", "id": "LINKA004", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA002", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA023", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA024", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA048", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA049", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA001", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA050", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA051", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA052", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA029", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA030", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA053", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA022", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA026", "value": "*********6"}, {"algorithmID": "00Z17", "id": "LINKA027", "value": "0000000124"}, {"algorithmID": "00Z17", "id": "LINKA028", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA021", "value": "*********9"}, {"algorithmID": "00Z17", "id": "LINKA020", "value": "0000000024"}, {"algorithmID": "00Z17", "id": "LINKA006", "value": "0000000024"}, {"algorithmID": "00Z17", "id": "LINKA003", "value": "*********2"}, {"algorithmID": "00Z17", "id": "LINKA025", "value": "*********3"}, {"algorithmID": "00Z17", "id": "LINKA011", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA005", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA010", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA040", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA033", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA034", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA035", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA036", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA007", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA037", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA008", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA009", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA039", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA042", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA043", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA044", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA015", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA045", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA046", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA047", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA017", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA018", "value": "0000001179"}, {"algorithmID": "00Z17", "id": "LINKA019", "value": "0000001179"}, {"algorithmID": "00Z17", "id": "LINKA041", "value": "0000.00000"}, {"algorithmID": "00Z17", "id": "LINKA012", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA014", "value": "*********1"}, {"algorithmID": "00Z17", "id": "LINKA016", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA013", "value": "0000335.66"}, {"algorithmID": "00Z17", "id": "LINKA038", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA054", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA055", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA056", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA057", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA058", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA059", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA031", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA080", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA060", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA061", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA062", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA063", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA064", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA065", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA066", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA082", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA067", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA068", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA069", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA070", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA071", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA072", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA032", "value": "*********0"}, {"algorithmID": "00Z17", "id": "LINKA081", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA084", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA085", "value": "0000099999"}, {"algorithmID": "00Z17", "id": "LINKA073", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA074", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA075", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA076", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA077", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA078", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA079", "value": "0000000.00"}, {"algorithmID": "00Z17", "id": "LINKA083", "value": "0000000.00"}]}}]}}}}}]}, "formatted_responses": {"TransUnion Credit": {"data": {"file_frozen": false, "credit_data_suppressed": false, "credit_data_disputed": false, "subject_is_minor": false, "do_not_promote": false, "file_hit_flag": true, "trades": {"count": 7, "revolving_count": 2, "open_count": 1, "line_of_credit_count": 0, "installment_count": 4, "mortgage_count": 0, "past_due_count": 3, "percentage_past_due": 0.43, "past_due_total_dollar_amount": 952}, "collections": {"count": 0, "revolving_count": 0, "open_count": 0, "line_of_credit_count": 0, "installment_count": 0, "mortgage_count": 0, "past_due_count": 0, "percentage_past_due": 0, "past_due_total_dollar_amount": 0}, "consumer_statement_indicator": true, "public_records": {"count": 0, "public_records_within_last_year": 0, "public_records_within_last_three_years": 0, "public_records_within_last_five_years": 0, "public_records_within_last_ten_years": 0, "most_recent_public_record": "1000-01-01", "public_records_reason_codes": []}, "inquiries": {"count": 1, "inquiries_within_last_year": 1, "inquiries_within_last_three_years": 1, "inquiries_within_last_five_years": 1, "inquiries_within_last_ten_years": 1, "most_recent_inquiry": "2021-10-13"}, "vantagescore40": {"exists": false, "score": -1}, "vantagescore30": {"exists": true, "score": 4}, "creditvisionbankruptcyscore": {"exists": false, "score": -1}, "ficoscore9": {"exists": false, "score": -1}, "ficoscore8": {"exists": false, "score": -1}, "id_vision_alerts": {"exists": false, "reason_codes": []}, "credit_vision_credit_summary": {"exists": false, "counts": {"public_record": -1, "collection": -1, "total_trade": -1, "negative_trade": -1, "historical_negative_trade": -1, "historical_negative_occurrences": -1, "revolving_trade": -1, "installment_trade": -1, "mortgage_trade": -1, "open_trade": -1, "total_inquiry": -1, "open_revolving_trade": -1, "open_installment_trade": -1, "open_mortgage_trade": -1, "other_trade": -1, "open_other_trade": -1}, "revolving_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "installment_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "mortgage_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "other_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "total_amount": {"credit_limit": -1, "current_balance": -1, "past_due": -1, "estimated_spend": -1, "current_payment_due": -1, "prior_payment_due": -1, "most_recent_payment_amount": -1}, "account_delinquency": {"most_recent_delinquency_date": "1000-01-01", "most_recent_delinquency_account_rating": "No delinquencies detected in available data", "revolving": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "installment": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "mortgage": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}, "other": {"late_30_days": -1, "late_60_days": -1, "late_90_days": -1}}}, "id_mismatch_alert": {"exists": false, "condition": "none", "type": "none", "inquiries_last_60_days": -1, "address_status": "none"}, "credit_vision_enriched_attributes_v2": {"exists": true, "AT24S": 4, "AT28B": 3064, "AT32S": 456, "AT36S": 1, "AT104S": 43, "AU21S": -1, "AU34S": -1, "AU51A": -1, "AU101S": -1, "BC34S": 94, "BC103S": 330, "BC110S": 0, "BI09S": 1, "BI34S": 68, "BR02S": 1, "FI25S": -6, "FI34S": 62, "FR101S": -1, "G202B": -3, "G213A": -4, "G215B": -4, "G216S": 2, "G224C": 2, "G228S": -1, "IN20S": 44, "OF27S": 1, "RE06S": 0, "S061S": 1, "S071A": -4, "ST01S": 0, "ST24S": -1, "ST27S": -1, "ST28S": -1, "ST99S": -1, "G242F": -1, "G243F": -1, "US21S": 4, "US36S": 1, "JT20S": -1, "JT21S": -1, "G404S": -1, "G411S": 1, "G208B": 93}, "factor_code_4": null, "vantage_v4_score_factor_3": null, "fico_9_bankcard_score": null, "mla_status": null, "factor_code_1": null, "FICO9score_factor_code_1": null, "fico_9_bankcard_score_returned": 0, "is_deceased_count": 0, "FICO9score_factor_code_3": null, "file_inquiries_impacted_FICO_9_score": null, "military_leads": {"has_military_association": false}, "factor_code_2": null, "vantage_v_4_factor_1": null, "vantage_v4_score_factor_2": null, "consumer_statement_text": "#HK#IFCRA INITIAL FRAUD ALERT: ACTION MAY BE REQUIRED UNDER FCRA BEFORE OPENING OR MODIFYING AN ACCOUNT. CONTACT CONSUMER AT (347) 981-9795", "credit_summary": {"monthly_payment_due": null}, "fraud_statement_type": "initialFraud<PERSON><PERSON><PERSON>", "credit_vision_alg_spend": {"AGGS903": null}, "factor_code_3": null, "ofac_name_screen_result": null, "FICO9score_factor_code_2": null, "credit_vision_revolver_6": {"RVLR61": null}, "FICO9score_factor_code_4": null, "vantage_v4_score_factor_4": null}, "matching": {"address": {"score": 0.98, "matched": true}, "name": {"score": 0.5, "matched": false}, "dob": {"score": 0, "matched": false}, "ssn": {"score": 1, "matched": true}}}}, "audit_archive": null}