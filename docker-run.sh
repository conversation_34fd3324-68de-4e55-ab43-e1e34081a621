#!/bin/bash

# https://dzone.com/articles/difference-between-initialrampercentage-minramperc
# Set the JVM memory as 50% of the total memory by default
export JVM_OPTS="-XX:InitialRAMPercentage=75.0 -XX:MaxRAMPercentage=75.0 -XX:MinRAMPercentage=75.0 $JVM_OPTS"

# If ENV variable JAVA_OPTS(-Xmx, -Xms) is setup, then JAVA_OPTS will overwrite the above memory config
echo "JAR=$1"
echo "JVM_OPTS=$JVM_OPTS"
echo "JAVA_OPTS=$JAVA_OPTS"

cmd="java $JVM_OPTS $JAVA_OPTS -jar $1"
echo "$cmd"

exec $cmd
