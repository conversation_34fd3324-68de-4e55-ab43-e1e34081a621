all: up

lint:
	@./gradlew checkstyleMain checkstyleTest

build:
	@set -a; . .env; set +a; ./gradlew build

test:
	@set -a; . .env; set +a; ./gradlew test --fail-fast

run:
	@set -a; . .env; set +a; ./gradlew bootRun

clean:
	@./gradlew clean

refresh-dependencies:
	@./gradlew --refresh-dependencies

# cleaning daemons, all gradle cache and build files on Mac OS
fullclean:
	@./gradlew --stop && ./gradlew clean && rm -rf .gradle bin build ~/.gradle/caches

# ============================================================
# Docker commands
# ============================================================
up:
	@docker-compose up -d --no-recreate

down:
	@docker compose down
