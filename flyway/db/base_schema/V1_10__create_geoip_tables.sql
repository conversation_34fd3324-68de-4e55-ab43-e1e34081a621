-- Flyway migration file for GeoIP database schema

-- Table to track imported data files
CREATE TABLE geoip_import_log (
  import_id BIGINT AUTO_INCREMENT PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  record_count INT,
  import_type ENUM('network', 'location') NOT NULL,
  INDEX idx_import_date (import_date),
  INDEX idx_file_name (file_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Network table for IP address ranges
CREATE TABLE geoip_network (
  network_start VARBINARY(16) NOT NULL,
  network_end VARBINARY(16) NOT NULL,
  network VARCHAR(50),
  geoname_id INT,
  registered_country_geoname_id INT,
  represented_country_geoname_id INT,
  is_anonymous_proxy BOOLEAN,
  is_satellite_provider BOOLEAN,
  postal_code VARCHAR(20),
  latitude FLOAT,
  longitude FLOAT,
  accuracy_radius INT,
  is_anycast BOOLEAN,
  import_id BIGINT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_network_start (network_start),
  INDEX idx_network_end (network_end),
  INDEX idx_network (network),
  INDEX idx_geoname_id (geoname_id),
  CONSTRAINT fk_network_import FOREIGN KEY (import_id) REFERENCES geoip_import_log(import_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Location table for geographic information
CREATE TABLE geoip_location (
  geoname_id INT NOT NULL,
  locale_code VARCHAR(10) NOT NULL,
  continent_code VARCHAR(10),
  continent_name VARCHAR(100),
  country_iso_code VARCHAR(10),
  country_name VARCHAR(100),
  subdivision_1_iso_code VARCHAR(10),
  subdivision_1_name VARCHAR(100),
  subdivision_2_iso_code VARCHAR(10),
  subdivision_2_name VARCHAR(100),
  city_name VARCHAR(100),
  metro_code INT,
  time_zone VARCHAR(50),
  is_in_european_union BOOLEAN,
  import_id BIGINT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (geoname_id, locale_code(5)),
  CONSTRAINT fk_location_import FOREIGN KEY (import_id) REFERENCES geoip_import_log(import_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create additional index for faster lookups
CREATE INDEX idx_country_iso ON geoip_location(country_iso_code);
