CREATE TABLE IF NOT EXISTS us_addresses (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    latitude FLOAT,
    longitude FLOAT,
    number VARCHAR(128),
    street_directional_prefix VARCHAR(9),
    street VARCHAR(1024),
    street_type VARCHAR(20),
    street_directional_suffix VARCHAR(9),
    unit VARCHAR(255),
    city VARCHAR(100),
    state CHAR(2),
    zip CHAR(5),
    PRIMARY KEY (id),
    KEY idx_zip_city_lon_lat_desc (zip, city ASC, longitude DESC, latitude DESC)
);
