use `feature_store`;

create table if not exists `events`
(
  `id`              bigint                                not null auto_increment comment 'Primary Key' primary key,
  `name`            varchar(256)                          not null comment 'Event Name',
  `category`        varchar(256)                          not null comment 'Event Category',
  `entity_id`       varchar(256)                          null comment 'Entity ID',
  `entity_type`     varchar(256)                          null comment 'Entity Type',
  `customer_id`     varchar(256)                          null comment 'Entity ID',
  `metadata`        json                                  not null comment 'Event Metadata',
  `dt_arrived`      timestamp                             not null comment 'Timestamp when event arrives in Flex cloud',
  `dt_created`      timestamp default CURRENT_TIMESTAMP   not null comment 'Timestamp when event is written into DB'
);
