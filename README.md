# Feature Store Service
LTV team's feature store service

### Local Development
1. To setup a local DB: Execute `docker-compose up -d` to setup/update tables owned by this github repo, flyway should handle creating the tables
2. Make sure you have active SSO session and set `AWS_PROFILE` environment variable. This is necessary for gradle to login to CodeArtifacts and service logic to invoke AWS services. You can reference [here](https://getflex.atlassian.net/wiki/spaces/SEC/pages/797999108/RDS+User+Guide)
3. Open `build.gradle` as a project with IntelliJ, let IntelliJ link gradle project and download all dependencies
4. Setup run configuration
    1. Right click [FeatureStoreApplication.java](src/main/java/com/getflex/featurestore/FeatureStoreApplication.java) and click `More Run/Debug` -> `Modify Run Configuration`
    2. Modify `Active profiles` field to include the environment you'd like to test against, followed by `,developer`. For example, `qa,developer`
        * `qa` profile will provide configurations specific to qa environment
        * `developer` profile will override logging format to pure text logging (rather than JSON format which is not quite readable without DataDog)
5. To run the application
    * `Run` menu -> `Run FeatureStoreApplication`

### MySql Connection
To connect to mysql in the docker container from the host, use the following command:
```
mysql -h localhost -P 3411 --protocol=tcp -u feature_store -p
```
Then enter the password `password` when prompted.

### Kubernetes

```bash
# Login to ECR
aws ecr get-login-password | docker login --username AWS --password-stdin 775586162990.dkr.ecr.us-east-1.amazonaws.com

# Change directory to /kubernetes/service/dev
cd /kubernetes/service/dev

# Build K8s manifests
kustomize build --enable-helm -o resources.yaml

# Diff K8s manifests
kubectl diff -f resources.yaml
```

### GHA
- `kubectl-diff-*` - Pre Merge Check, posts diff comment to PR
- `terraform-plan-*` - Pre Merge Check, posts the terraform plan to a PR comment



### Adding a Feature

Features are registered with the `@RegisterFeature` annotation. This annotation will automatically make it available
to be used in the system. The class may be annotated with one or more `@RegisterFeature` annotations to register the same
feature with different names or parameters. For each annotation a compatible constructor must present, if not an error
will be raised at startup (also in unit tests).

For instance:

```java
@RegisterFeature
@RegisterFeature(
    value = "MyFeature_{exampleParam}",
    parameterType = MyFeature.Parameters.class
)
class MyFeature extends BaseFeature {

  record Parameters(String exampleParam) {

  }

  // constructor with no parameters for feature name `MyFeature`
  public MyFeature(VerificationService verificationService) {
    // verificationService will be injected automatically
  }

  // constructor with parameters for the feature name `MyFeature_{exampleParam}`.
  // params are serialized using the user provided feature name. For example
  // MyFeature_foo will result in Parameters("foo") provided to this constructor
  public MyFeature(VerificationService verificationService, Parameters params) {
    // verificationService will be injected automatically
  }
  
  // ... omitting other BaseFeature methods for brevity
}
```

This code will register this class as `MyFeature` and `MyFeature_{exampleParam}`. If no value is provided
to the `@RegisterFeature` annotation, the class name will be used as the feature name. Each feature request by the client
will result in a new instance of the feature being created.

Unit tests will ensure that all features get registered, have proper constructor parameters and can be initialized.

Services and other Spring components will be injected in the constructor automatically when the feature is initialized.
Simply add the required service as a constructor parameter.


