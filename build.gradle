import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
	id 'java'
	id 'org.springframework.boot' version '3.2.2'
	id 'io.spring.dependency-management' version '1.1.4'
	id 'checkstyle'
	id "org.openapi.generator" version "7.4.0"
	id 'jacoco'
}

group = 'com.getflex'
version = '0.0.1'

java {
	sourceCompatibility = '17'
}

bootJar {
	archiveFileName = "feature-store-latest.jar"
	mainClass = "com.getflex.featurestore.FeatureStoreApplication"
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

def codeArtifactToken = System.getenv().getOrDefault("CODEARTIFACT_AUTH_TOKEN", null)
if (codeArtifactToken == null) {
	// token wasn't passed in, lets generate it via AWS
	logger.info("Generating codeartifact token via aws cli")
	codeArtifactToken = "aws codeartifact get-authorization-token --domain flex-development --domain-owner 775586162990 --query authorizationToken --output text".execute().text
}

repositories {
	mavenCentral()
	maven {
		url 'https://flex-development-775586162990.d.codeartifact.us-east-1.amazonaws.com/maven/flex-development/'
		credentials {
			username "aws"
			password codeArtifactToken
		}
	}
}

dependencies {
    annotationProcessor 'org.projectlombok:lombok'
	testAnnotationProcessor 'org.projectlombok:lombok'

	implementation 'com.getflex.flex2:api-client:1.3.2026'
	implementation 'com.getflex.flex2:decision-engine-api-client:1.0.88'
	implementation 'com.getflex.flex2:billPaymentMethod-api-client:0.0.208'
	implementation 'com.getflex.identity:identity-api-client-v2:v0.4.266'
	implementation 'com.getflex.flex2:ds-app-rent-credibility-api-client:1.0.20'
	implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
	implementation 'com.datadoghq:java-dogstatsd-client:4.1.0'
	implementation 'com.getflex.flex2:dispute-api-client:1.0.7'
	implementation 'com.getflex.flex2:verification-api-client:1.0.30'
	implementation 'com.getflex.partnerhub:private-api-client:0.0.540'
	implementation 'com.getflex.flex2:billing-api-client:1.0.3923'
	implementation 'com.getflex.flex2:common-o11y:2.0.16'
	implementation 'com.getflex.flex2:db-router:1.0.+'
	implementation 'com.getflex.flex2:offer-api-client:1.0.23'
	implementation 'com.getflex.risk:credit-management-api-client:0.0.10'
	implementation 'com.google.guava:guava:33.1.0-jre'
	implementation 'com.googlecode.json-simple:json-simple:1.1.1'
	implementation 'io.github.resilience4j:resilience4j-spring-boot2:2.1.0'
	implementation 'joda-time:joda-time:2.12.6'
	implementation 'net.logstash.logback:logstash-logback-encoder:6.6'
	implementation 'org.apache.commons:commons-text:1.4'
	implementation 'org.openapitools:jackson-databind-nullable:0.2.6'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.18.2'
	implementation 'org.projectlombok:lombok'
	implementation 'org.reflections:reflections:0.10.2'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.retry:spring-retry'
	implementation 'software.amazon.awssdk:dynamodb-enhanced'
	implementation 'software.amazon.awssdk:sagemakerruntime:'
	implementation 'software.amazon.awssdk:sqs'
	implementation 'software.amazon.awssdk:s3'
	implementation platform('software.amazon.awssdk:bom:2.20.142')
	implementation 'com.getflex.cipher:common-cipher:2.1.+'
	implementation 'com.plaid:plaid-java:37.0.0'
	implementation 'commons-codec:commons-codec:1.18.0'
    implementation "org.apache.commons:commons-collections4:4.5.0"

	runtimeOnly 'software.amazon.awssdk:sso:'
	runtimeOnly 'software.amazon.awssdk:sts:'

	runtimeOnly 'com.mysql:mysql-connector-j'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.mockito:mockito-inline:5.2.0'  // Added version
	testImplementation 'junit:junit:4.13.1'
	testImplementation 'software.amazon.awssdk:s3'
	testImplementation 'software.amazon.awssdk:sso'
}

tasks.named('bootBuildImage') {
	builder = 'paketobuildpacks/builder-jammy-base:latest'
}

tasks.named('test') {
	useJUnitPlatform()
	finalizedBy jacocoTestReport
}

test {
	filter {
		excludeTestsMatching 'FlexScoreFiveFeatureIntegTest' // only meant for developer local testing
	}
}

jacoco {
	toolVersion = "0.8.12"
	reportsDirectory = layout.buildDirectory.dir('reports/coverage')
}

jacocoTestReport {
	dependsOn test
	reports {
		xml.required.set(true)
	}

	afterEvaluate {
		classDirectories.setFrom(files(classDirectories.files.collect {
			fileTree(dir: it,
					exclude: [
							'com/getflex/featurestore/*.class',
							'com/getflex/featurestore/config/*.class',
							'com/getflex/featurestore/constant/*.class',
							'com/getflex/featurestore/controller/**/*.class',
							'com/getflex/featurestore/dao/**/*.class',
							'com/getflex/featurestore/exception/**/*.class',
							'com/getflex/featurestore/filter/*.class',
							'com/getflex/featurestore/integration/socure/model/*.class',
							'com/getflex/featurestore/integration/flex/flexscore/model/*.class',
							'com/getflex/featurestore/integration/zendesk/model/*.class',
							'com/getflex/featurestore/model/*.class',
							'com/getflex/featurestore/model/flexscore/alloy/**/*.class',
							'com/getflex/featurestore/model/feature/income/model/**/*.class',
					])
		}))
	}
}


tasks.named('checkstyleMain') {
	dependsOn 'compileTestJava'
}

tasks.register('generateFeatureStoreServerCode', GenerateTask) {
	generatorName = "spring"
	inputSpec = layout.projectDirectory.dir("api").dir("spec").file("spec.yml").toString()
	outputDir = layout.buildDirectory.dir("generated/server").get().toString()
	apiPackage = 'com.getflex.featurestore.controller.stub'
	invokerPackage = 'com.getflex.featurestore.controller.stub'
	modelPackage = 'com.getflex.featurestore.model'
	configOptions = [
			useSpringBoot3: "true",
			interfaceOnly: "true",
			openApiNullable: "false"
	]
}

tasks.named('compileJava') {
	dependsOn 'generateFeatureStoreServerCode'
}

sourceSets {
	main {
		java {
			srcDirs layout.buildDirectory.dir("generated/server/src/main/java").get().toString()
		}
	}
}

checkstyle {
	toolVersion '10.12.1'
	maxWarnings = 0
	ignoreFailures false
}
