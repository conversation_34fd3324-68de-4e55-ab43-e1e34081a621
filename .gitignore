HELP.md
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/
.tmp/*

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
.run/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

.env
.DS_Store
cdk/

## Terraform
**/.terraform/*
.terraform.lock.hcl

# .tfstate files
*.tfstate
*.tfstate.*

# Terraform plan files
*tfplan*

# development
__pycache__
**/scripts/**/*.csv
**/scripts/**/outputs/
**/geoip2-csv-converter
