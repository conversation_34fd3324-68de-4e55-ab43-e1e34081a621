locals {
  datadog   = jsondecode(data.aws_secretsmanager_secret_version.feature-store-datadog.secret_string)
  pagerduty = jsondecode(data.aws_secretsmanager_secret_version.pagerduty.secret_string)
  cipher-secret = jsondecode(data.aws_secretsmanager_secret_version.cipher-secret.secret_string)
}

data "aws_secretsmanager_secret" "feature-store-datadog" {
  name = "${var.env}/flex2/datadog"
}

data "aws_secretsmanager_secret_version" "feature-store-datadog" {
  secret_id = data.aws_secretsmanager_secret.feature-store-datadog.id
}

data "aws_secretsmanager_secret" "pagerduty" {
  name = "${var.env}/flex2/pagerduty"
}

data "aws_secretsmanager_secret_version" "pagerduty" {
  secret_id = data.aws_secretsmanager_secret.pagerduty.id
}

data "aws_secretsmanager_secret" "cipher-secret" {
  name = "cipher-secret"
}

data "aws_secretsmanager_secret_version" "cipher-secret" {
  secret_id = data.aws_secretsmanager_secret.cipher-secret.id
}
