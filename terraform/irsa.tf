# Get AWS identity of caller (ie. IAM Role used to execute terraform)
data "aws_caller_identity" "current" {}

# Get EKS cluster data
data "aws_eks_cluster" "cluster" {
  name = var.eks_cluster_b_name
}

data "aws_s3_bucket" "alloy" {
  bucket = var.alloy_bucket_name
}

data "aws_s3_bucket" "datalake_shared" {
  bucket = var.datalake_shared_bucket_name
}

data "aws_iam_policy_document" "policy" {
  dynamic "statement" {
    for_each = var.events_enabled ? [1] : []

    content {
      sid       = "PutEvents"
      effect    = "Allow"
      actions   = ["events:PutEvents"]
      resources = [one(module.eventbridge[*].eventbridge_bus_arn)]
    }
  }

  dynamic "statement" {
    for_each = concat(module.dynamodb.feature_store_table_arns, [])
    content {
      effect    = "Allow"
      actions    = module.dynamodb.dynamodb_actions
      resources  = [statement.value, format("%s/%s", statement.value, "index/*")]
    }
  }

  statement {
    sid       = "Sagemaker"
    effect    = "Allow"
    actions   = ["sagemaker:InvokeEndpoint"]
    resources = ["*"]
  }

  statement {
    sid       = "ReceiveSQSMessage"
    effect    = "Allow"
    actions   = ["sqs:ReceiveMessage"]
    resources = ["*"]
  }

  statement {
      sid       = "DeleteSQSMessage"
      effect    = "Allow"
      actions   = ["sqs:DeleteMessage"]
      resources = ["*"]
    }

  statement {
    sid       = "DecryptKMSKey"
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = ["*"]
  }

  statement {
    sid       = "SocureCache"
    effect    = "Allow"
    actions   = ["s3:PutObject", "s3:GetObject", "s3:ListBucket"]
    resources = [
      aws_s3_bucket.socure_cache.arn,
      "${aws_s3_bucket.socure_cache.arn}/*"
    ]
  }

  statement {
    sid       = "AlloyReport"
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    resources = [
      data.aws_s3_bucket.alloy.arn,
      "${data.aws_s3_bucket.alloy.arn}/*"
    ]
  }

  statement {
    sid       = "PlaidReport"
    effect    = "Allow"
    actions   = ["s3:GetObject", "s3:ListBucket"]
    resources = [
      data.aws_s3_bucket.datalake_shared.arn,
      "${data.aws_s3_bucket.datalake_shared.arn}/internal-data/verification-service/*"
    ]
  }

  statement {
    sid       = "CipherKMSActions"
    effect    = "Allow"
    actions   = [
      "kms:Decrypt",
      "kms:Encrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*"
    ]
    resources = [local.cipher-secret.kms_master_key]
}

}

# Create IAM Policy
resource "aws_iam_policy" "policy" {
  name_prefix = var.application_tag
  description = "Feature Store Access Policy"
  policy      = data.aws_iam_policy_document.policy.json

  tags = module.flextags.tags
}

# Create IAM Role
module "iam_assumable_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"
  version = "5.34.0"

  create_role = true

  role_name_prefix = var.application_tag

  provider_url = data.aws_eks_cluster.cluster.identity[0].oidc[0].issuer

  role_policy_arns = [
    aws_iam_policy.policy.arn
  ]

  oidc_fully_qualified_subjects = ["system:serviceaccount:flex2:feature-store-service-account"]

  tags = module.flextags.tags
}

output "iam_role_arn" {
  value       = module.iam_assumable_role.iam_role_arn
  description = "The ARN of IRSA IAM Role"
}
