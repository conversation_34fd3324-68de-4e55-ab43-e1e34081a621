resource "datadog_monitor" "post_event_high_error_rate" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] PostEvent endpoint has high error rate"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"POST /v1/events\" service:feature-store status:error"), ")",
    " to find error traces and see what has caused the unexpected exception"
  ])

  query = "sum(last_10m):( sum:trace.servlet.request.errors{env:${var.env},resource_name:post_/v1/events,service:feature-store}.as_count() / sum:trace.servlet.request.hits{env:${var.env},resource_name:post_/v1/events,service:feature-store}.as_count() ) > 0.05"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "post_event_high_latency" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] PostEvent endpoint has high latency"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"POST /v1/events\" service:feature-store"), "&sort=desc&sort_by=@duration&sort_order=desc)",
    " to find slow requests and see what has caused the delay"
  ])

  query = "percentile(last_15m):p95:trace.servlet.request{env:${var.env},resource_name:post_/v1/events,service:feature-store} > 0.5"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "get_features_high_error_rate" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] GetFeatures endpoint has high error rate"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"GET /v1/features\" service:feature-store status:error"), ")",
    " to find error traces and see what has caused the unexpected exception"
  ])

  query = "sum(last_10m):( sum:trace.servlet.request.errors{env:${var.env},resource_name:get_/v1/features,service:feature-store}.as_count() / sum:trace.servlet.request.hits{env:${var.env},resource_name:get_/v1/features,service:feature-store}.as_count() ) > 0.05"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "get_features_high_latency" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] GetFeatures endpoint has high latency"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"GET /v1/features\" service:feature-store"), "&sort=desc&sort_by=@duration&sort_order=desc)",
    " to find slow requests and see what has caused the delay"
  ])

  query = "percentile(last_15m):p95:trace.servlet.request{env:${var.env},resource_name:get_/v1/features,service:feature-store} > 7"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "fetch_feature_values_high_error_rate" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FetchFeatureValues endpoint has high error rate"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"POST /v1/features\" service:feature-store status:error"), ")",
    " to find error traces and see what has caused the unexpected exception"
  ])

  query = "sum(last_10m):( sum:trace.servlet.request.errors{env:${var.env},resource_name:post_/v1/features,service:feature-store}.as_count() / sum:trace.servlet.request.hits{env:${var.env},resource_name:post_/v1/features,service:feature-store}.as_count() ) > 0.05"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "fetch_feature_values_high_latency" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FetchFeatureValues endpoint has high latency"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Check [APM](https://app.datadoghq.com/apm/traces?query=", urlencode("env:${var.env} operation_name:servlet.request resource_name:\"POST /v1/features\" service:feature-store"), "&sort=desc&sort_by=@duration&sort_order=desc)",
    " to find slow requests and see what has caused the delay"
  ])

  query = "percentile(last_15m):p95:trace.servlet.request{env:${var.env},resource_name:post_/v1/features,service:feature-store} > 7"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}


resource "datadog_monitor" "flex_score_feature_low_availability" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore feature has low availability - feature_name: {{feature_name.name}}"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Please investigate the availability of the FlexScore feature: {{feature_name.name}}"
  ])

  query = "avg(last_1h):100 * avg:flex2.feature_store.FlexScoreModel.flex_score_feature_availability{env:${var.env}, !feature_name:utilization_authorized_user} by {feature_name} <= 5"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "flex_score_feature_no_availability" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore feature has no availability - feature_name: {{feature_name.name}}"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Please investigate the availability of the FlexScore feature: {{feature_name.name}}"
  ])

  query = "avg(last_4h):100 * avg:flex2.feature_store.FlexScoreModel.flex_score_feature_availability{env:${var.env}, feature_name:utilization_authorized_user} by {feature_name} == 0"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "flex_score_feature_anomaly_detected" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore feature {{feature_name}} availability decreased by more than 50% in the last hour"
  type    = "query alert"
  message = "Please investigate why the availability of feature {{feature_name}} has decreased by more than **50%** in the past hour compared to the previous hour."

  query = "avg(last_1d):anomalies(100 * ewma_20(avg:flex2.feature_store.FlexScoreModel.flex_score_feature_availability{env:${var.env}} by {feature_name}), 'robust', 5, direction='below', interval=300, alert_window='last_1h', count_default_zero='true', seasonality='daily') >= 0.8"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false
  renotify_interval = 0

  evaluation_delay = 60
  priority         = 4

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "flex_score_feature_high_enough_traffic" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore feature call has high enough traffic"
  type    = "query alert"
  message = "This is part of composite monitor to justify a PagerDuty alert (to address low traffic time issue)."

  query = "sum(last_1h):sum:flex2.feature_store.FlexScoreModel.flex_score_feature_count{env:${var.env}} > 300"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false

  evaluation_delay = 60

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "flex_score_feature_anomaly_detected_critical" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore feature {{feature_name}} availability decreased by more than 50% in the last hour (CRITICAL)"
  type    = "composite"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Please investigate why the availability of feature {{feature_name}} has decreased by more than **50%** in the past hour compared to the previous hour.",
    "This alert is only triggered with high enough traffic."
  ])

  query = "${datadog_monitor.flex_score_feature_anomaly_detected[0].id} && ${datadog_monitor.flex_score_feature_high_enough_traffic[0].id}"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false
  renotify_interval = 0

  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "flex_score_model_low_success_rate" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] FlexScore model output availability decreased by more than 5% in the last hour"
  type    = "query alert"
  message = join("", [
    var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Please investigate why the availability of the FlexScore model output has decreased by more than **5%** in the past hour compared to the previous hour."
  ])

  query = "avg(last_1h):avg:flex2.feature_store.FlexScoreModel.flex_score_model_availability{env:${var.env}} < 0.95"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false
  renotify_interval = 0

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}

resource "datadog_monitor" "proxy_rent_model_low_success_rate" {
  count   = var.monitoring_enabled ? 1 : 0
  name    = "[${var.application_tag}][${var.env}] ProxyRentModelPredictionFeature error rate is above 3% in the last hour"
  type    = "query alert"
  message = join("", [
      var.alert_enabled ? "@pagerduty-${local.service_name}\n" : "",
    "Please investigate why the error rate of the ProxyRentModelPredictionFeature has increased to above 3% in the past hour compared to the previous hour."
  ])

  query = "avg(last_1h):sum:flex2.feature_store.FeatureService.getFeatureValues_error{feature_name:proxyrentmodelpredictionfeature} by {feature_name}.as_rate() > 3"

  require_full_window = false
  notify_no_data      = false
  notify_audit        = false
  renotify_interval = 0

  evaluation_delay = 60
  priority         = 1

  include_tags = true
  tags         = local.tags
}
