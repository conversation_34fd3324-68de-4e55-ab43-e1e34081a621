locals {
  app = var.application_tag
}

# ---------------------------------------------------------------------
# Event Bus
# ---------------------------------------------------------------------

module "eventbridge" {
  source  = "terraform-aws-modules/eventbridge/aws"
  version = "v3.2.0"

  count = var.events_enabled ? 1 : 0

  bus_name = local.app

  attach_sqs_policy = var.events_enabled
  sqs_target_arns = [
    one(module.risk_events[*].queue_arn),
    one(module.risk_events[*].dead_letter_queue_arn)
  ]

  rules = {
    risk_events = {
      state       = "ENABLED"
      description = "Capture risk events"
      event_pattern = jsonencode({
        "detail-type": ["FeatureStoreEvent"]
      })
    }
  }

  targets = {
    risk_events = [
      {
        name            = "send-risk-ltv-events-to-sqs"
        arn             = one(module.risk_events[*].queue_arn)
        dead_letter_arn = one(module.risk_events[*].dead_letter_queue_arn)
        target_id       = "send-risk-ltv-events-to-sqs"
      }
    ]
  }

  tags = module.flextags.tags
}


# ---------------------------------------------------------------------
# Event Queue
# ---------------------------------------------------------------------

module "kms" {
  source  = "terraform-aws-modules/kms/aws"
  version = "v2.2.0"

  count = var.events_enabled ? 1 : 0

  description = "Feature Store Encryption"
  key_usage   = "ENCRYPT_DECRYPT"

  aliases = ["flex/${local.app}"]

  key_statements = [
    {
      sid = "AllowDirectAccess",
      effect = "Allow",
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::986067545241:root"]
        }
      ]
      actions = ["kms:*"],
      resources = ["*"]
    },
    {
      sid = "AllowEventBridgeWriteAccess"
      actions = [
        "kms:Encrypt*",
        "kms:Decrypt*",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:Describe*"
      ]

      principals = [
        {
          type        = "Service"
          identifiers = ["events.amazonaws.com"]
        }
      ]

      resources = ["*"]
    }
  ]

  tags = module.flextags.tags
}

module "risk_events" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "4.1.0"

  count = var.events_enabled ? 1 : 0
  name  = "${local.app}-risk-ltv-events"

  create_queue_policy = true
  queue_policy_statements = {
    account = {
      sid = "AllowSendMessage"
      actions = [
        "sqs:SendMessage",
      ]
      principals = [
        {
          type        = "Service"
          identifiers = ["events.amazonaws.com"]
        }
      ]
    }
  }

  # Dead Letter Queue
  create_dlq              = true
  create_dlq_queue_policy = true
  dlq_queue_policy_statements = {
    account = {
      sid = "AllowSendMessage"
      actions = [
        "sqs:SendMessage",
      ]
      principals = [
        {
          type        = "Service"
          identifiers = ["events.amazonaws.com"]
        }
      ]
    }
  }

  # Encryption
  kms_master_key_id = module.kms[0].key_id

  tags = module.flextags.tags
}
