terraform {
  cloud {
    organization = "getflex"

    workspaces {
      tags = ["cloud:aws", "feature-store", "owner:risk"]
    }
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.7"
    }
    datadog = {
      source = "DataDog/datadog"
    }
    pagerduty = {
      source  = "pagerduty/pagerduty"
      version = "3.7.0"
    }
    mysql = {
      source = "petoju/mysql"
      version = "3.0.47"
    }
  }
}

provider "aws" {
  region     = var.aws_region
}


data "aws_eks_cluster" "b" {
  name = var.eks_cluster_b_name
}


provider "kubernetes" {
  host                   = data.aws_eks_cluster.b.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.b.certificate_authority[0].data)

  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    args        = ["eks", "--region", "us-east-1", "get-token", "--cluster-name", var.eks_cluster_b_name]
    command     = "aws"
  }

  alias = "cluster_b"
}

provider "datadog" {
  api_key = local.datadog.api_key
  app_key = local.datadog.application_key
}

provider "pagerduty" {
  token      = local.pagerduty.token
  user_token = local.pagerduty.user_token
}

provider "mysql" {
  endpoint = data.aws_db_proxy.proxy.endpoint
  username = local.admin_user.username
  password = local.admin_user.password
}
