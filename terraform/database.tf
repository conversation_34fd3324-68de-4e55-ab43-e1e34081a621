# database.tf
# Generate random password
# Add user(s) and/or database(s) on shared RDS database
# Add grants on user(s)
# Add username and password to AWS Secrets Manager Secret

# WARNING! REQUIRED ACTION!:
# All users must be added to RDS proxy's users.tfvars files
# TODO: add link to users.tfvars after https://github.com/flexapp/infra-rds/pull/80 is merged

# Get shared database endpoint
data "aws_db_proxy" "proxy" {
  name = "risk-ltv"
}

# Get admin username and password
data "aws_secretsmanager_secret" "admin_user" {
  name = "risk-ltv/rds/credentials"
}

data "aws_secretsmanager_secret_version" "admin_user" {
  secret_id = data.aws_secretsmanager_secret.admin_user.id
}

locals {
  admin_user = jsondecode(data.aws_secretsmanager_secret_version.admin_user.secret_string)
}

# Generate random password
resource "random_password" "password" {
  length            = 40
  special           = true
  min_special       = 5
  override_special  = "!#$%^&*()-_=+[]{}<>:?"

  # modify keeprs content to re-create password
  keepers           = {
    pass_version  = 1
  }
}

# Create AWS Secret Manager Secret
resource "aws_secretsmanager_secret" "db-pass" {
  name = "${var.application_tag}/rds/credentials"
}

resource "aws_secretsmanager_secret_version" "db-pass-val" {
  secret_id     = aws_secretsmanager_secret.db-pass.id
  secret_string = jsonencode({
    username = var.application_tag
    password = random_password.password.result
  })
}

# Create Mysql user
resource "mysql_user" "user" {
  user               = var.application_tag
  host               = "%"
  plaintext_password = random_password.password.result
}

# Create Mysql database
resource "mysql_database" "app" {
  name = replace(var.application_tag, "-", "_")
  default_collation = "utf8mb4_0900_ai_ci"

  # Prevent destruction or recreation of database
  lifecycle {
    prevent_destroy = true
  }
}

# Create Mysql user grant
resource "mysql_grant" "grant" {
  user       = mysql_user.user.user
  host       = mysql_user.user.host
  database   = replace(var.application_tag, "-", "_")
  privileges = ["SELECT", "INSERT", "UPDATE"]
}

output "mysql_aws_secret_id" {
  value = aws_secretsmanager_secret_version.db-pass-val.version_id
  description = "The Version Id of database Secret"
}
