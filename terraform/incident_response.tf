locals {
  service_name = "${title(var.application_tag)}${title(var.env)}"
  tags = [for k, v in module.flextags.tags : "${replace(k, ":", ".")}:${v}"]
}

#
# Pagerduty Service
#
resource "pagerduty_service" "feature_store_service" {
  count   = var.alert_enabled ? 1 : 0

  name = local.service_name
  escalation_policy = "P1ZT69X" # Use "Core Platform - Risk Engineering" policy
}

data "pagerduty_vendor" "datadog" {
  name = "Datadog"
}

resource "pagerduty_service_integration" "datadog" {
  count   = var.alert_enabled ? 1 : 0

  name    = data.pagerduty_vendor.datadog.name
  service = pagerduty_service.feature_store_service[0].id
  vendor  = data.pagerduty_vendor.datadog.id
}

#
# Datadog default monitors
#
module "datadog_default_monitoring" {
  source                 = "app.terraform.io/getflex/default-monitors/datadog"
  version                = "0.0.17"
  count                  = var.monitoring_enabled ? 1 : 0
  alert_enabled          = var.alert_enabled
  application            = var.application_tag
  pagerduty_service_name = local.service_name
  pagerduty_service_key  = var.alert_enabled ? pagerduty_service_integration.datadog[0].integration_key : ""
  env                    = var.env
  tags                   = module.flextags.tags
}
