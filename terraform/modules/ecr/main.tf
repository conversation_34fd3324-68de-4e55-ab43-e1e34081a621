# Create ECR repository for service image
resource "aws_ecr_repository" "service" {
  name                 = var.application_tag
  image_tag_mutability = "IMMUTABLE"
}

# Create ECR repository for flyway image
resource "aws_ecr_repository" "flyway" {
  name                 = "${var.application_tag}-flyway"
  image_tag_mutability = "IMMUTABLE"
}

# Create repository policy
data "aws_iam_policy_document" "ecr" {
  # Allow all AWS identities from AWS Org
  statement {
    sid    = "new policy"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    actions = [
      "ecr:*",
    ]

    condition {
      test = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values = ["o-a9wc1qtpn5"]
    }
  }
}

# Add repository policy to service repository
resource "aws_ecr_repository_policy" "service" {
  repository = aws_ecr_repository.service.name
  policy     = data.aws_iam_policy_document.ecr.json
}

# Add repository policy to flyway repository
resource "aws_ecr_repository_policy" "flyway" {
  repository = aws_ecr_repository.flyway.name
  policy     = data.aws_iam_policy_document.ecr.json
}
