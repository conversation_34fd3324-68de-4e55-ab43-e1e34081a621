data "aws_caller_identity" "current" {}

locals {
  feature_store_events_table_name = "events_store"

  initial_table_names = [
    local.feature_store_events_table_name,
  ]

  table_indexes = {
    events_store = ["entity_id_name_timestamp_id_index"]
  }

  initial_table_index_names = flatten(
    [
      for table, indexes in local.table_indexes: [
      for idx in indexes:
       "table/${table}/index/${idx}"
    ]
  ])

  table_names = local.is_lower_env ? [] : local.initial_table_names
  table_index_names = local.is_lower_env ? [] : local.initial_table_index_names
}

output "decision_engine_table_names" {
  value = local.initial_table_names
}

output "feature_store_table_arns" {
  value = formatlist("arn:aws:dynamodb:us-east-1:%s:table/%s",
    data.aws_caller_identity.current.account_id, local.initial_table_names)
}

output "dynamodb_actions" {
  value = [
    "dynamodb:*",
  ]
}
