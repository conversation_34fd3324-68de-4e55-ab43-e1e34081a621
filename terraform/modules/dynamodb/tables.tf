# Terraform only requires us to define attributes related to indices for DDB
# This is not meant to define the entire table schema
# See dao.logging.model classes for how we annotate Java objects to their corresponding ddb table columns

# Define a map of scale up/down schedules
locals {
  is_lower_env = var.env != "prod"
  current_date = tonumber(formatdate("DD", timestamp()))
}

resource "aws_dynamodb_table" "events_store" {
  name             = local.feature_store_events_table_name
  billing_mode     = "PAY_PER_REQUEST"
  hash_key         = "customer_id"
  range_key        = "name#timestamp#id"
  stream_enabled   = true
  stream_view_type = "NEW_AND_OLD_IMAGES"

  attribute {
    name = "customer_id"
    type = "S"
  }

  attribute {
    name = "name#timestamp#id"
    type = "S"
  }

  attribute {
    name = "entity_id"
    type = "S"
  }

  attribute {
    name = "category#timestamp#id"
    type = "S"
  }

  global_secondary_index {
    name            = "entity_id_name_timestamp_id_index"
    hash_key        = "entity_id"
    range_key       = "name#timestamp#id"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "customerId_category_timestamp_id_index"
    hash_key        = "customer_id"
    range_key       = "category#timestamp#id"
    projection_type = "ALL"
  }

  point_in_time_recovery {
    enabled = true
  }

  tags = {
    Name = var.application_tag
  }
}

output "current_date" {
  value = local.current_date
  description = "Current day of month"
}

output "is_lower_env_tag" {
  value = local.is_lower_env
  description = "If it is lower env"
}
