variable "env" {
  type        = string
  description = "Our internal name for the environment, such as 'development' or 'production'"
}

variable "aws_region" {
  type    = string
  default = "us-east-1"
}

variable "application_tag" {
  type    = string
  default = "feature-store"
}

variable "endpoints" {
  type = map(object({
    method = string
    uri    = string
  }))
  default = {
    root = {
      method = "*"
      uri    = "/v1/*"
    }
    # Complete Endpoints here.
  }
}

variable "monitoring_enabled" {
  type        = bool
  description = "Enable creation of incident response resources."
  default     = false
}

variable "alert_enabled" {
  type        = bool
  description = "Enable/disable Pagerduty Alerts"
  default     = true
}

variable "events_enabled" {
  type        = bool
  description = "Enable/disable risk events"
  default     = true
}

variable "eks_cluster_b_name" {
  type        = string
  description = "The name of the second EKS cluster to deploy to"
}

variable "create_ecr" {
  type        = bool
  description = "Create ECR registry - only for shared env"
  default     = false
}

variable "alloy_bucket_name" {
  type        = string
  description = "S3 bucket that stores Alloy reports"
}

variable "datalake_shared_bucket_name" {
  type        = string
  description = "S3 bucket that stores Datalake shared data. Used for Plaid reports here."
}
