resource "aws_s3_bucket" "socure_cache" {
  bucket = "flex2-feature-store-socure-cache-${var.env}"
}

resource "aws_s3_bucket_versioning" "scoure_cache_versioning" {
  bucket = aws_s3_bucket.socure_cache.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "scoure_cache_ttl" {
  bucket = aws_s3_bucket.socure_cache.id

  rule {
    id = "ttl"

    expiration {
      days = 60
    }

    status = "Enabled"
  }
}
